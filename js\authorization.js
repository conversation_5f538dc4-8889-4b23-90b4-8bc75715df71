/**
 * Create by Chelly
 * 2019/9/10
 */

var checkAuthConfirm = function (uid) {
    $("#importAuth").click();
    $("#importAuth").change(function (e) {
        let fileList = [];
        for (let i = 0; i < this.files.length; i++) {
            if (checkFileExt(['lic'], this.files[i].name)) {
                analysisFile(this.files[i]).then(info => {
                    if (info.now_crc && (info.crc === info.now_crc)) {
                        fileList.push(info);
                    }
                    if (i === (this.files.length - 1)) {
                        compareAuth(fileList, uid)
                    }
                }).catch(e => {
                    console.log(e)
                })
            }
        }
    });
};
var checkFileExt = function (arr, filename) {
    let flag = false; //状态
    //取出上传文件的扩展名
    let index = filename.lastIndexOf(".");
    let ext = filename.substr(index + 1);
    //循环比较
    for (let i = 0; i < arr.length; i++) {
        if (ext === arr[i]) {
            flag = true; //一旦找到合适的，立即退出循环
            break;
        }
    }
    //条件判断
    return flag
};
var checkFiles = function (files) {
    let newFiles = [];
    for (let i = 0; i < files.length; i++) {
        if (checkFileExt(['lic'], files[i].name)) {
            newFiles.push(files[i])
        }
    }
    return newFiles;
};
var compareAuth = function (fileList, uid, callback) {
    let result = {
        result: false
    };
    let check = 0, checkFiles = [];
    for (let i = 0; i < fileList.length; i++) {
        if (Array.isArray(fileList[i].uid)) {
            for (let j = 0; j < fileList[i].uid.length; j++) {
                if (fileList[i].uid[j] === uid) {
                    check++;
                    checkFiles.push(fileList[i])
                }
            }
        } else {
            if (fileList[i].uid === uid) {
                check++;
                checkFiles.push(fileList[i])
            }
        }
    }
    if (check === 0) {

        result.msg = langMessage.authorization.matchFail;


    } else if (check > 1) {
        let fileName = '';
        for (let i = 0; i < check; i++) {
            fileName += checkFiles[i].filename + " ";
        }
        result.msg = langMessage.authorization.matchMultiple(fileName);
    } else {
        result.result = true;
        result.base = checkFiles[0].base_64
        result.fileName = checkFiles[0].filename
    }
    if (callback) {
        callback(result)
    }
    // notice('匹配到合适授权码文件：' + checkFiles[0].filename, 'suc');
    // setDataset($("#authorize")[0],'authFileBase64');
};
// 旧一对一授权
// var analysisFile = function (ofile, callback, error, fileLen) {
//     let file = ofile;
//     let filename = file.name;
//     let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
//         fileInfo = {},
//         fileReader = new FileReader();
//     let start = 0;
//     let end = file.size;
//     let uidLength = 32,
//         crcLength = 4,
//         uidNumLength = 4;
//     let fileMinSize = fileLen || 368 ;
//     let baseLength = 0, lE = true;
//     let uid=[];
//     if (file.type === 'IE') {
//         file = file.file;
//         end = file.size
//     }
//     fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
//
//     fileReader.onload = function (e) {
//         let bf = e.target.result;
//         let bfLength = bf.byteLength;
//         if (bfLength >= fileMinSize) {
//             // let crc_bf = bf.slice( bfLength - 4, bfLength);
//             let ndv = new DataView(bf, bfLength - (crcLength+uidNumLength), 4);
//             let num = getUint32BigInt(ndv, 0, lE);
//             if((num * uidLength + (crcLength+uidNumLength)) < bfLength ){
//
//                 baseLength = (crcLength+uidNumLength);
//                 for(let i = 0;i<num;i++){
//                     let uid_bf = bf.slice(bfLength - (baseLength+uidLength), bfLength - baseLength);
//                     let id = ab2str(uid_bf);
//                     uid.push(id);
//                     baseLength = baseLength+uidLength;
//                 }
//             }else{
//                 baseLength = uidLength + crcLength;
//                 let uid_bf = bf.slice(bfLength - baseLength, bfLength - crcLength);
//                 uid = ab2str(uid_bf);
//             }
//             let token_bf = bf.slice(0, bfLength - crcLength);
//             let token = ab2str(token_bf);
//             let now_crc = crc32(token);
//             let dv = new DataView(bf, bfLength - crcLength, crcLength);
//             let crc = getUint32BigInt(dv, 0, lE);
//
//
//             let base_bf = bf.slice(0, bfLength - baseLength);
//             let base = ab2str(base_bf);
//
//             let base_64 = btoa(base);
//             fileInfo = {
//                 filename,
//                 uid,
//                 crc,
//                 now_crc,
//                 base_64
//             };
//         }
//         if (callback) {
//             callback(fileInfo);
//         }
//     };
//
//     fileReader.onerror = function (e) {
//         if (error) {
//             error(e);
//         }
//     };
//
// };


var analysisFile = function (ofile, callback, error, fileLen) {
    let file = ofile;
    let filename = file.name;
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
        fileInfo = {},
        fileReader = new FileReader();
    let start = 0;
    let end = file.size;
    let uidLength = 32,
        crcLength = 4,
        uidNumLength = 4;
    let fileMinSize = fileLen || 368;
    let baseLength = 0, lE = true;
    let uid = [];
    if (file.type === 'IE') {
        file = file.file;
        end = file.size
    }
    fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));

    fileReader.onload = function (e) {
        let bf = e.target.result;
        let bfLength = bf.byteLength;
        if (bfLength >= fileMinSize) {
            // let crc_bf = bf.slice( bfLength - 4, bfLength);
            let ndv = new DataView(bf, bfLength - (crcLength + uidNumLength), 4);
            let num = getUint32BigInt(ndv, 0, lE);
            if ((num * uidLength + (crcLength + uidNumLength)) < bfLength) {
                baseLength = (crcLength + uidNumLength);
                for (let i = 0; i < num; i++) {
                    let uid_bf = bf.slice(bfLength - (baseLength + uidLength), bfLength - baseLength);
                    let id = ab2str(uid_bf);
                    uid.push(id);
                    baseLength = baseLength + uidLength;
                }
            } else {
                baseLength = uidLength + crcLength;
                let uid_bf = bf.slice(bfLength - baseLength, bfLength - crcLength);
                uid = ab2str(uid_bf);
            }
            let token_bf = bf.slice(0, bfLength - crcLength);
            let token = ab2str(token_bf);
            let now_crc = crc32(token);
            let dv = new DataView(bf, bfLength - crcLength, crcLength);
            let crc = getUint32BigInt(dv, 0, lE);


            let base_bf = bf.slice(0, bfLength - baseLength);
            let blob = new Blob([base_bf]);
            let base = ab2str(base_bf);

            let base_64 = btoa(base);
            fileInfo = {
                filename,
                uid,
                crc,
                now_crc,
                base_64,
                blob
            };
        }
        if (callback) {
            callback(fileInfo);
        }
    };

    fileReader.onerror = function (e) {
        if (error) {
            error(e);
        }
    };
};