/**
 * Create by Chelly
 * 2020/7/29
 */
var ocx;
var getDate = function (timestamp, tick) {
  let time = new Date(parseInt(timestamp) * 1000),
    y = time.getFullYear(),
    m = time.getMonth() + 1,
    d = time.getDate(),
    h = time.getHours(),
    mm = time.getMinutes(),
    s = time.getSeconds();
  time = null;
  return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " "
    + (h < 10 ? "0" + h : h) + ":" + (mm < 10 ? "0" + mm : mm) + ":" + (s < 10 ? "0" + s : s)
    + "." + tick;
};
var timestampToTime = function (timestamp) {
  let date = new Date(timestamp),//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    Y = date.getFullYear() + '-',
    M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-',
    D = date.getDate() + ' ',
    h = date.getHours() + ':',
    m = date.getMinutes() + ':',
    s = date.getSeconds() + '.',
    ss = date.getUTCMilliseconds();
  return Y + M + D + h + m + s + ss;
};
var date2stamp = function (time) {
  let t = time.replace(/-/g, "/");
  let da = new Date(t);
  return parseInt(Date.parse(da) / 1000);
};
var getAccount = function () {
  let ip = location.hostname;
  let ret = ocx.account_password(ip);
  console.log(ret);
};
var initFilePath = function () {
  let ip = location.hostname;
  console.log("ocx.FIle_Path_Init");
  let ret = ocx.FIle_Path_Init(ip);
  console.log("ocx.FIle_Path_Init:" + ret)
};
var getCameraUrl = function (channel) {
  let auth = "";
  let stream = "";
  let ip = location.hostname;
  let projectConfig = getSe("projectConfig");
  channel = channel ? channel : projectConfig.camera_channel;

  if (projectConfig.camera_username && projectConfig.camera_password) {
    auth = projectConfig.camera_username + ":" + projectConfig.camera_password + "@"
  }
  if (projectConfig.camera_stream) {
    stream = projectConfig.camera_stream
  }
  return 'rtsp://' + auth + ip + '/' + stream + channel;
};
var ocxDialog = function () {
  console.log("ocx_JumpProtect");
  let ret = ocx.JumpProtect();
  console.log("JumpProtect:" + ret);
  return ret;
};
var ThreadStatus = function () {
  let result = false;
  try {
    console.log("ocx_ThreadStatus");
    let ret = ocx.ThreadStatus();
    console.log("ThreadStatus:" + ret);
    if (ret === "TRUE") {
      result = true
    }

  } catch (e) {
    result = e;
    console.log(e)
  } finally {
    return result
  }
};
var stopThread = function () {
  console.log("ocx.STOPThread");
  let ret = ocx.STOPThread();
  console.log("STOPThread:" + ret);
};

var saveAuthcode = function (path, code) {
  console.log("ocx.Save_authcode");
  // path = path.myReplace("\\", "\\\\");
  let ret = ocx.Save_authcode(path, code);
  console.log("saveAuth:" + ret);
  return ret
};
var readAuthFile = function (unique_id, callback) {
  let path = ocxPopOut();
  let result = {
    msg: '当前文件夹下没有匹配文件或授权码文件打开错误！',
    result: false
  };
  if (path) {
    if (path !== 'error') {
      console.log("ocx.Read_authfile");
      let base = ocx.Read_authfile(path, unique_id);
      if (base !== 'FALSE') {
        try {
          base = JSON.parse(base);
          if (base['Executive Outcomes'] === 'TRUE') {
            result.base = base['Base64'];
            result.result = true;
          } else if (base['Filecount']) {
            let msg = '匹配到' + base['Filecount'] + '个相同的授权文件：' + base['File_alike_name'];
            msg = msg.slice(0, -1);
            result.msg = msg;
            result.result = false;
          }
        } catch (e) {
          console.log(e);
        }
      }
    }
    if (callback) {
      callback(result);
    }
  }

};
var objGETValue = function (obj, key) {
  for (let key in obj) {
    return obj
  }
};
var base642pic = function (data) {
  if (data !== 'FALSE') {
    let imgSrc = "data:image/jpeg;base64," + data;
    return imgSrc
  } else {
    return '../../img/404.png'
  }
};
var sendPath = function (path) {
  console.log("ocx.get_picBase64");
  // path = path.replace(/\\/g, "\\\\")
  let base = ocx.get_picBase64(path);
  let pic = '../../img/404.png';
  let blob;
  if (base !== 'FALSE') {
    let baseOBJ = JSON.parse(base);
    let pathData = baseOBJ.Base_data;
    pic = base642pic(pathData);
    blob = base64ToBlob(pic);
    base = "";
    pathData = "";
    baseOBJ = null;
  }
  return blob
};
var getRGBA = function (path, w, h, callback) {
  console.log('ocx.Get_PicRgba');
  let rgba = ocx.Get_PicRgba(path, w, h);
  let width = w, height = h;
  let c = document.createElement('canvas');
  if (rgba !== 'FALSE') {
    let rgbaOBJ = JSON.parse(rgba);
    let pathData = rgbaOBJ.Rgba_data;
    pathData = str2array(pathData);
    width = rgbaOBJ.Jpeg_width;
    height = rgbaOBJ.Jpeg_height;
    c.width = w;
    c.height = h;
    let ctx = c.getContext("2d");
    let d = ctx.getImageData(0, 0, w, h);
    let imageData = d.data;
    imageData.set(pathData);
    ctx.putImageData(d, 0, 0);
    callback(c, width, height);
    rgba = "";
    imageData = null;
    d = null;
    c = null;
    ctx = null;
    rgbaOBJ = null;
    pathData = null;
  } else {
    c.width = w;
    c.height = h;
    let ctx = c.getContext("2d");
    let bigImg = document.createElement("img"); //创建一个img元素
    bigImg.src = "../img/404.png";   //给img元素的src属性赋值
    bigImg.onload = function () {
      let width = bigImg.width;
      let height = bigImg.height;
      ctx.clearRect(0, 0, w, h);
      ctx.drawImage(bigImg, 0, 0, width, height, 0, 0, w, h);
      callback(c, width, height);
      c = null;
      ctx = null;
    };
  }

};
// var imageCanvas = function (type, path, x, y, w, h) {
//     let picDom = $("#" + type)[0];
//     let picW = parseInt(picDom.clientWidth);
//     let picH = parseInt(picDom.clientHeight);
//     //图像宽高默认为4的倍数
//     picW = parseInt(picW / 4) * 4;
//     picH = parseInt(picH / 4) * 4;
//     let can = $("<canvas id=\"" + type + "Can\" height='" + picH + "' width='" + picW + "'>");
//     $("#" + type).html(can);
//     let c = $("#" + type + "Can")[0];
//     let ctx = c.getContext('2d');
//     ctx.clearRect(0, 0, picW, picH);
//     // let paths = sendPath(path);
//     getRGBA(path, picW, picH, function (img, width, height) {
//         ctx.drawImage(img, 0, 0, picW, picH, 0, 0, picW, picH);
//         if (x >= 0 && y >= 0 && w > 0 && h > 0) {
//             let rectX = parseInt(x / width * picW);
//             let rectY = parseInt(y / height * picH);
//             let rectW = parseInt(w / width * picW);
//             let rectH = parseInt(h / height * picH);
//             ctx.strokeStyle = 'red';
//             ctx.lineWidth = "1";
//             ctx.rect(rectX, rectY, rectW, rectH);
//             rectX = null, rectY = null;
//             rectH = null;
//             rectW = null;
//         }
//         ctx.stroke();
//         let multipleX = width / picW;
//         let multipleY = height / picH;
//         if (type === 'prePic' || type === 'eventPic' || type === 'lastPic') {
//             let magCan = $("<canvas class=\"magCanvas\" id=\"" + type + "MagCan\" height=200 width=200>");
//             $("#" + type + "Can").after(magCan);
//             let mc = document.getElementById(type + "MagCan");
//             let mctx = mc.getContext("2d");
//             c.onmouseout = function () {
//                 mc.style.display = "none";
//             };
//             c.onmousemove = function (e) {
//                 if (img) {
//                     let bigImg = document.createElement('img');
//                     // let imgSrc = sendPath(path);
//                     // let blob = base64ToBlob(imgSrc);
//                     // bigImg.onload = function(){
//                     //     window.URL.revokeObjectURL(bigImg.src);
//                     // };
//                     // bigImg.src = window.URL.createObjectURL(blob);
//                     let blob = sendPath(path);
//                     bigImg.onload = function () {
//                         window.URL.revokeObjectURL(bigImg.src);
//                     };
//                     bigImg.src = window.URL.createObjectURL(blob);
//                     mc.style.display = "block";
//                     mctx.clearRect(0, 0, 300, 300);
//                     let location = getLocation(type + "Can", e.clientX, e.clientY);// getLocation()
//                     let centerPoint = {
//                         x: location.x,
//                         y: location.y
//                     };
//                     let originalRadius = 300;
//                     mctx.drawImage(bigImg, (centerPoint.x * multipleX - 50 < 0 ? 0 : centerPoint.x * multipleX - 50),// biggerImg:规定要使用的图像、画布或视频;
//                         (centerPoint.y * multipleY - 50 < 0 ? 0 : centerPoint.y * multipleY - 50), originalRadius,// centerPoint.y
//                         originalRadius, 0, 0, originalRadius, originalRadius);// originalRadius:被剪切图像的高度；
//                     mc.style.left = e.pageX + "px";
//                     mc.style.top = e.pageY + "px";
//                 } else {
//                     mctx.clearRect(0, 0, 300, 300);// clearRect() 方法清空给定矩形内的指定像素。
//                     mc.style.display = "none";
//                 }
//             };
//         }
//         can = null;
//         c = null;
//         ctx = null;
//
//     });
// };

var imageCanvas = function (type, path, x, y, w, h, showClick, drawRect, amplify) {
  let picD = $("#" + type);
  let picDom = picD[0];
  let picW = parseInt(picDom.clientWidth);
  let picH = parseInt(picDom.clientHeight);
  //图像宽高默认为4的倍数
  picW = parseInt(picW / 4) * 4;
  picH = parseInt(picH / 4) * 4;
  let s = showClick || false;
  let d = drawRect || false;
  let a = amplify || false;
  let can = $("<canvas id=\"" + type + "Can\" height='" + picH + "' width='" + picW + "'>");
  picD.html(can);
  let c = $("#" + type + "Can")[0];
  let ctx = c.getContext('2d');
  ctx.clearRect(0, 0, picW, picH);
  if (s) {
    picD.off("click").on("click", function () {
      $(".pic-show").removeClass("show");
      $(".time-show").removeClass("show");
      let index = $(this).index();
      $(this).addClass("show");
      $($(".time-show")[index]).addClass("show");
      let am = false;
      if (type === 'platePic') {
        am = true
      }
      imageCanvas('currentPic', path, x, y, w, h, null, true, am)
    })
  }
  // let paths = sendPath(path);
  getRGBA(path, picW, picH, function (img, width, height) {
    ctx.drawImage(img, 0, 0, picW, picH, 0, 0, picW, picH);
    if (d && x >= 0 && y >= 0 && w > 0 && h > 0) {
      let rectX = parseInt(x / width * picW);
      let rectY = parseInt(y / height * picH);
      let rectW = parseInt(w / width * picW);
      let rectH = parseInt(h / height * picH);
      ctx.strokeStyle = 'red';
      ctx.lineWidth = "1";
      ctx.rect(rectX, rectY, rectW, rectH);
      rectX = null, rectY = null;
      rectH = null;
      rectW = null;
    }
    ctx.stroke();
    let multipleX = width / picW;
    let multipleY = height / picH;
    if (type === 'currentPic') {
      let magCan = $("<canvas class=\"magCanvas\" id=\"" + type + "MagCan\" height=200 width=200>");
      $("#" + type + "Can").after(magCan);
      let mc = document.getElementById(type + "MagCan");
      let mctx = mc.getContext("2d");
      c.onmouseout = function () {
        mc.style.display = "none";
      };
      c.onmousemove = function (e) {
        if (img && !a) {
          let bigImg = document.createElement('img');
          // let imgSrc = sendPath(path);
          // let blob = base64ToBlob(imgSrc);
          // bigImg.onload = function(){
          //     window.URL.revokeObjectURL(bigImg.src);
          // };
          // bigImg.src = window.URL.createObjectURL(blob);
          let blob = sendPath(path);
          bigImg.onload = function () {
            window.URL.revokeObjectURL(bigImg.src);
          };
          bigImg.src = window.URL.createObjectURL(blob);
          mc.style.display = "block";
          mctx.clearRect(0, 0, 300, 300);
          let location = getLocation(type + "Can", e.clientX, e.clientY);// getLocation()
          let centerPoint = {
            x: location.x,
            y: location.y
          };
          let originalRadius = 300;
          mctx.drawImage(bigImg, (centerPoint.x * multipleX - 50 < 0 ? 0 : centerPoint.x * multipleX - 50),// biggerImg:规定要使用的图像、画布或视频;
            (centerPoint.y * multipleY - 50 < 0 ? 0 : centerPoint.y * multipleY - 50), originalRadius,// centerPoint.y
            originalRadius, 0, 0, originalRadius, originalRadius);// originalRadius:被剪切图像的高度；
          mc.style.left = e.pageX + "px";
          mc.style.top = e.pageY + "px";
        } else {
          mctx.clearRect(0, 0, 300, 300);// clearRect() 方法清空给定矩形内的指定像素。
          mc.style.display = "none";
        }
      };
    }
    can = null;
    c = null;
    ctx = null;

  });
};

/**
 * 获得放大的坐标
 * @param x
 * @param y
 * @returns {{x: number, y: number}}
 */
var getLocation = function (id, x, y) {
  let c = document.getElementById(id);
  let bbox = c.getBoundingClientRect();// getBoundingClientRect()方法返回元素的大小及其相对于视口的位置。
  return {
    x: (x - bbox.left) * (c.width / bbox.width),
    y: (y - bbox.top) * (c.height / bbox.height)
  };
};
var displayImage = function (url, callback) {
  var j = new JpegImage();
  j.onload = function () {
    let c = document.createElement('canvas');
    c.width = j.width;
    c.height = j.height;
    let ctx = c.getContext("2d");
    let d = ctx.getImageData(0, 0, j.width, j.height);
    j.copyToImageData(d);
    ctx.putImageData(d, 0, 0);
    callback(c, j.width, j.height);
    c = null;
  };
  j.load(url);
};

var image404 = function (ctx, w, h) {
  // let c = $("#" + type)[0];
  // let ctx = c.getContext('2d');
  let bigImg = document.createElement("img"); //创建一个img元素
  bigImg.src = "../img/404.png";   //给img元素的src属性赋值
  bigImg.onload = function () {
    let width = bigImg.width;
    let height = bigImg.height;
    ctx.clearRect(0, 0, w, h);
    ctx.drawImage(bigImg, 0, 0, width, height, 0, 0, w, h);
  };
};
var initOCX = function () {
  let ret = ocx.test_ocx_Ini();
  console.log("init:" + ret);
};


var initListen = function (callback) {
  let ip = location.hostname;
  let sdc = getSe("SDC");
  let port = 60000;
  if (sdc) {
    port = getSe("projectConfig")['newEventPort']
  } else {
    port = getSe("projectConfig")['oldEventPort']
  }
  console.log("ocx.LISTEN_INI");
  let result = ocx.LISTEN_INI(ip, port);
  if (result !== 'FALSE') {
    callback(true)
  } else {
    callback(false)
  }
};
var initRTSP = function (flag, type, w, h) {
  try {
    console.log("ocx.command:init");
    let play = "H264";

    if (!flag) {
      play = "H265"
    }
    let ret = ocx.command('INIT', '[xnn| 11]', play, w, h);
    console.log("rtsp-init:" + ret);
    showRTSP(type, play, w, h);
  } catch (e) {
    console.log(e)
  }
};
var showRTSP = function (channel, play, w, h) {
  console.log("ocx.command:play");
  let ret = ocx.command('PLAY', '[channal|0][url|' + getCameraUrl(channel) + ']', play, w, h);
  console.log("rtsp-play:" + ret);
};
var initStreamType = function (type) {
  initWebService(13, null, getStreamTypeCallback, type);
};
var getStreamTypeCallback = function (res, type) {
  let flag = false, width = 0, height = 0;
  if (res.stream_type === 1) {
    flag = true
  }
  ;
  if (res.stream_w && res.stream_h) {
    width = res.stream_w;
    height = res.stream_h;
  }

  initRTSP(flag, type, width, height)
};
var stopRTSP = function (channel) {
  console.log("ocx.command:stop");
  let ret = ocx.command('STOP', '[channal|0][url|' + getCameraUrl(channel) + ']', "", 0, 0);
  // console.log("ocx.command:close")
  // let cRet = ocx.command('CLOSE', '');
  console.log("rtsp-stop:" + ret);
};
// var initRTSP = function (flag, type) {
//     if (flag) {
//         console.log("ocx.command:init");
//         let ret = ocx.command('INIT', '[xnn| 11]');
//         console.log("rtsp-init:" + ret);
//         showRTSP(type);
//     } else {
//         errorRTSP();
//     }
// };
// var showRTSP = function (channel) {
//     console.log("ocx.command:play");
//     let ret = ocx.command('PLAY', '[channal|0][url|' + getCameraUrl(channel) + ']');
//     console.log("rtsp-play:" + ret);
// };
// var initStreamType = function (type) {
//     initWebService(13, null, getStreamTypeCallback,type);
// };
// var getStreamTypeCallback = function (res,type) {
//     let flag = false;
//     if (res.stream_type === '1') {
//         flag = true
//     };
//     initRTSP(flag,type)
// };
// var stopRTSP = function (channel) {
//     console.log("ocx.command:stop");
//     let ret = ocx.command('STOP', '[channal|0][url|' + getCameraUrl(channel) + ']');
//     // console.log("ocx.command:close")
//     // let cRet = ocx.command('CLOSE', '');
//     console.log("rtsp-stop:" + ret);
// };
var closeRTSP = function () {
  let ip = location.hostname;
  console.log("ocx.command:close");
  let cRet = ocx.command('CLOSE', '');
  console.log(cRet);
};
var initOnvif = function () {
  console.log("ocx.ONVIF_INI");
  let ip = location.hostname;
  let result = ocx.ONVIF_INI(ip, 'admin', '12345');
  if (result === "FALSE") {
    return false
  }
  return true;
};
var getOnvifTraficLight = function () {
  console.log("ocx.trafficLight_get");
  let data = ocx.trafficLight_get();
  //let data="[{\"x\":0.297136,\"y\":0.246815},{\"x\":0.297136,\"y\":0.283439},{\"x\":0.319809,\"y\":0.283439},{\"x\":0.319809,\"y\":0.246815},{\"x\":0.442200,\"y\":0.255000},{\"x\":0.442200,\"y\":0.264500},{\"x\":0.449500,\"y\":0.264500},{\"x\":0.449500,\"y\":0.255000},{\"x\":0.459000,\"y\":0.260300},{\"x\":0.459000,\"y\":0.279700},{\"x\":0.477800,\"y\":0.279700},{\"x\":0.477800,\"y\":0.260300},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000}]"
  let allPosition = [];
  if (data !== 'FALSE') {
    data = JSON.parse(data);
    allPosition = value2Coordinate(data);
    for (let i = 0; i < allPosition.length; i++) {
      let item = {};
      item.type = "fixed-rect";
      item.id = 'videoSignal' + fixName[i];
      item.position = allPosition[i];
      showGroupPoint(item, item.id, 'drawVideoSignal');
      // setSe('videoSignal'+fixName[i], item);
    }
  } else {
    return false;
  }
  return true;
};
var setOnvifTraficLight = function () {
  console.log("ocx.trafficLight_set");
  let data = getSe('videoSignalValue');
  data = JSON.stringify(data);
  let result = ocx.trafficLight_set(data);
  if (result === 'TRUE') {
    getOnvifTraficLight();
  }
  return result;
};
var getOnvifOSD = function () {
  console.log("ocx.OSD_get");
  let data = ocx.OSD_get();
  if (data !== 'FALSE') {
    data = JSON.parse(data);
    setSe('sOSD', data[0]);
    return true
  }
  return false
};
var setOnvifOSD = function () {
  console.log("ocx.OSD_set");
  let data = getSe('sOSD');
  data = JSON.stringify(data);
  let result = ocx.OSD_set(data);
  return result;
};
var find_ocx = function () {
  try {
    //插件ProgID
    let ocx = new ActiveXObject('TS_PLAYER_PLUGIN.TS_PLAYER_PLUGINCtrl.1');
  } catch (e) {
    console.log(e);
    return false;
  }
  return true;
};
var getVersion = function () {
  console.log("ocx.get_Current_version");
  let version = {
    main: 0,
    sub: 0,
    revise: 0
  };
  try {
    let nowVersion = ocx.get_Current_version();
    nowVersion = JSON.parse(nowVersion);
    version.main = parseInt(nowVersion.Main);
    version.sub = parseInt(nowVersion.Sub);
    version.revise = parseInt(nowVersion.Revise);
  } catch (e) {
    console.log(e);
  }
  return version;
};
var initialize_ocx = function (id) {
  let result = find_ocx();
  let flag = {
    result: false,
    msg: '没有安装插件无法进行操作！'
  };
  let version = 0;
  let href = '../setup.exe';
  if (id !== 'eventVideo' && id !== 'indexVideo' && id !== "authContainer") {
    href = '../../setup.exe'
  }
  container.append("<OBJECT ID=\"ocx\" width=\"100%\" height=\"100%\" classid=\"clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA\"></OBJECT>");
  ocx = document.getElementById("ocx");
  let container = $("#" + id);
  if (result === true) {

    try {
      version = getVersion();
      let nowVersion = getSe("projectConfig").index_ocx;
      if (compareVersion(version.main + "." + version.sub, nowVersion.main + "." + nowVersion.sub) >= 0) {
        initFilePath();
        if (id === 'indexVideo') {
          $("#ocx").attr({
            width: ($("#ocxContainer")[0].offsetWidth),
            height: ($("#ocxContainer")[0].offsetHeight),
          });

          let channel = getSe("projectConfig").camera_channel;
          initStreamType(channel)
        }
        else if (id === 'eventVideo') {
          $("#ocx").attr({
            width: (container[0].offsetWidth),
            height: (container[0].offsetHeight),
          });
          //initRTSP()
        }
        flag.result = true;
        flag.msg = "";
        return flag
      } else {
        flag.msg = "插件版本太低无法进行操作！"
      }
    } catch (e) {
      console.log(e);
    } finally {
      let projectConfig = getSe("projectConfig");
      projectConfig.ocx_version = version;
      setSe('projectConfig', projectConfig);
    }
  }
  let msg = "请点击此处下载插件，安装时请关闭浏览器";
  if (id !== 'pictureOCX') {
    container.html("");
    container.append("<a id='ocx_down' href=" + href + ">" + msg + "</a>");
  }
  return flag
};

var savePathConfig = function (path) {
  console.log("ocx.set_pic_save_dir");
  let result = false;
  if (find_ocx()) {
    result = ocx.set_pic_save_dir(path.img);
  }
  return result;
};
var initPathConfig = function () {
  console.log("ocx.getCurrentPicDir");
  let path = {
    img: "",
    record: "",
    log: ""
  };
  if (find_ocx()) {
    let imgPath = ocx.getCurrentPicDir();
    if (imgPath !== 'FALSE') {
      path.img = imgPath.myReplace("\\\\\\\\", "\\");
      path.record = "";
      path.log = ""
    }
  }
  return path;
};
var selectEvent = function (param) {
  let events = {};
  let getEvents = ocx.select_event(param);
  if (events !== "FALSE") {
    try {
      events = JSON.parse(getEvents)
    } catch (e) {
      console.log(e)
    }
  }
  return events
};
var getEventStatus = function () {
  let status = ocx.eventWeb();
  try {
    status = JSON.parse(status)
  } catch (e) {
    console.log(e)
  }
  return status
};
var selectDB = function (params) {
  if (sqlStatus === 0) {
    let path = initPathConfig();
    let openDB = ocx.sqlite_open(path.img + "sql.db");
    if (openDB == -1) {
      path = initPathConfig();
      openDB = ocx.sqlite_open(path.img + "sql.db");
      if (openDB == -1) {
        layer.msg('无法打开文件！请联系厂家。', {icon: 2});
        return
      }
    }
    console.log("open database " + openDB);
    if (openDB === 0) {
      sqlStatus = 1;
    }
  }
  let keyIndex = 0;
  let conditions = "";
  let condition = {
    'begin_time': 'EVENT_TIME>=',
    'end_time': 'EVENT_TIME<=',
    'plate_type': 'PLATE_TYPE=',
    'event_type': 'EVENT_TYPE=',
    'plate_string': 'PLATE_STRING like "%'
  };
  let limit = "";
  if (params.limit) {
    limit = " Limit " + params.limit;
  }
  let order = "Desc";
  if (params.order) {
    order = params.order
  }
  console.log(params);
  Object.keys(params).forEach(function (key) {
    if (params[key]) {
      if (key === 'limit' || key === "order") {
        return
      }
      if (keyIndex === 0) {
        conditions += " WHERE "
      } else {
        conditions += " AND "
      }
      keyIndex++;
      conditions += condition[key] + params[key];
      if (key === 'plate_string') {
        conditions += '%"'
      }
    }
  });
  let sql = "select " +
    "ID" +
    ",EVENT_TYPE" +
    ",PLATE_TYPE" +
    ",PLATE_STRING" +
    ",LANE_INDEX" +
    ",CAR_COLOR" +
    ",CAR_SPEED" +
    ",EVENT_TIME" +
    ",EVENT_TICK" +
    ",IMAGE_COUNT" +
    ",IMAGE_PRE_PATH" +
    ",IMAGE_PRE_X" +
    ",IMAGE_PRE_Y" +
    ",IMAGE_PRE_W" +
    ",IMAGE_PRE_H" +
    ",IMAGE_EVENT_PATH" +
    ",IMAGE_EVENT_X" +
    ",IMAGE_EVENT_Y" +
    ",IMAGE_EVENT_W" +
    ",IMAGE_EVENT_H" +
    ",IMAGE_LAST_PATH" +
    ",IMAGE_LAST_X" +
    ",IMAGE_LAST_Y" +
    ",IMAGE_LAST_W" +
    ",IMAGE_LAST_H" +
    ",IMAGE_PLATE_PATH" +
    ",TIMESTAMP" +
    " from 'EVENT_TBL' " +
    conditions + " Order By EVENT_TIME " + order + ",EVENT_TICK " + order + limit;
  console.log("ocx.sqlite_select");
  let eventData = ocx.sqlite_select(sql);
  console.log(eventData);
  let data = [];
  if (eventData !== 'FALSE') {
    let d = eventData.replace(/\\/g, "\\\\");
    // let JSONdata = eventData.replace(/\\\\/g, "////")

    try {
      data = JSON.parse(d);
      let wrapData = [];

      let eventConfig = getSe('eventConfig');
      $(data).each(function (i) {
        data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
        data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
        data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
        data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
      });
    } catch (e) {
      console.log(e);
      layer.msg('查询出错！', {icon: 2})
    }
  }


  //
  // var getTpl = dataTpl.innerHTML
  //     , view = document.getElementById('data');
  // laytpl(getTpl).render(data, function (html) {
  //     view.innerHTML = html;
  // });
  return data;
};
var ocxPath = function (callback) {
  let result = ocx.Modif_Path();
  if (result === "TRUE") {
    initPath();
    callback(true)
  } else {
    console.log(result);
    callback(false)
  }
};
var ocxAuth = function (u_id, callback) {
  console.log("ocx.GET_Auth");
  let status = ocx.GET_Auth(u_id);
  let result = {
    msg: '当前文件夹下没有匹配文件或授权码文件打开错误！',
    result: false
  };
  if (status !== 'FALSE') {
    try {
      let base = JSON.parse(status);
      if (base['Executive Outcomes'] === 'TRUE') {
        result.base = base['Base64'];
        result.result = true;
      } else if (base['Filecount']) {
        let msg = '匹配到' + base['Filecount'] + '个相同的授权文件：' + base['File_alike_name'];
        msg = msg.slice(0, -1);
        result.msg = msg;
        result.result = false;
      }
    } catch (e) {
      console.log(e)
    }
    if (callback) {
      callback(result)
    }
  }

};
var ocxDestroyAll = function (ocx) {
  console.log("ocx.DestroyAll");
  try {
    ocx.DestroyAll();
  } catch (e) {
    console.log(e);
  }
};
var appendOcx = function () {
  $("body").append("<div style='display: none'><OBJECT ID=\"ocx\" width=\"100%\" height=\"100%\" classid=\"clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA\"></OBJECT></div>");
};
var ocxUpgrade = function (type, ip, base) {
  console.log("ocx.UPDATE" + type);
  let status = ocx.UPDATE(type, ip, base);
  console.log("UPDATE" + type + ":" + status);
  return status
};
String.prototype.myReplace = function (f, e) {
  //把f替换成e
  var reg = new RegExp(f, "g"); //创建正则RegExp对象
  return this.replace(reg, e);
};
var ocxDestroy = function (win) {
  let w;
  if (win) {
    w = win
  } else {
    w = window;
  }
  let ocx = w.document.getElementById("ocx");
  ocxDestroyAll(ocx);
};

var base64ToBlob = function (urlData) {
  let arr = urlData.split(',');
  let mime = arr[0].match(/:(.*?);/)[1] || 'image/png';
  // 去掉url的头，并转化为byte
  let bstr = atob(arr[1]),
    n = bstr.length,
    // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
};

var exportPicture = function (type, path, data) {
  console.log("ocx.Export_pic");
  let status = ocx.Export_pic(type, path, data);
  console.log("Export_pic:" + status);
  return status
};

var ocxPopOut = function () {
  console.log("ocx.Popout");
  let path = "";
  let status = ocx.Popout();
  if (status === "FALSE") {
    path = false
  } else if (status === "FOLDER SELECT ERROR") {
    path = "error"
  } else {
    path = status;
  }
  console.log("Popout:" + status);
  return path
};
var ocxSQL = function (params) {
  let data = [];
  try {
    if (sqlStatus === 0) {
      let path = initPathConfig();
      let openDB = ocx.sqlite_open(path.img + "sql.db");
      if (openDB == -1) {
        path = initPathConfig();
        openDB = ocx.sqlite_open(path.img + "sql.db");
        if (openDB == -1) {
          layer.msg('无法打开文件！请联系厂家。', {icon: 2});
          return
        }
      }
      console.log("open database " + openDB);
      if (openDB === 0) {
        sqlStatus = 1;
      }
    }
    console.log("ocx.SQL_Select");
    let eventData = ocx.SQL_Select(JSON.stringify(params));
    //console.log(eventData);

    if (eventData !== 'FALSE') {
      let d = eventData.replace(/\\/g, "\\\\");
      // let JSONdata = eventData.replace(/\\\\/g, "////")


      data = JSON.parse(d);
      let eventConfig = getSe('eventConfig');
      $(data).each(function (i) {
        data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
        data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
        data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
        data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
      });

    }
  } catch (e) {
    console.log(e);
    layer.msg('查询出错！', {icon: 2})
  }
  return data;
};


var removeVideo = function (channel) {
  stopRTSP(channel)
};


// 4.22
// ocx新接口


var initFilePath = function () {
  let ip = location.hostname;
  let param = {
    Camera1: ip
  };
  console.log("ocx.FIle_Path_Init");
  let ret = ocx.FIle_Path_Init(JSON.stringify(param));
  let ocxMsg = getSe("projectConfig").ocxMsg || {};
  let result = ocxMsg[ret] || {result: false, msg: "未知错误"};
  console.log("ocx.FIle_Path_Init:" + ret);
  return result;
};

var commandRTSP = function (param) {
  console.log("ocx.command:" + param.CMD);
  let ret = ocx.command(JSON.stringify(param));
  return ret;
};

var initRTSP = function (flag, type, w, h) {
  try {
    let play = 1;

    if (!flag) {
      play = 2
    }
    let param = {
      CMD: 'INIT',
      PARAMS: "[channel|11]"
    };
    let ret = commandRTSP(param);
    console.log("rtsp-init:" + ret);
    if (ret !== 'FALSE') {
      $("#RTSPError").css({display: "none"});
      playRTSP(type, play, w, h);
    } else {
      RTSPError("视频初始化失败请尝试重播！")
    }
  } catch (e) {
    console.log(e)
  }
};

var playRTSP = function (channel, play, w, h) {
  let param = {
    CMD: 'PLAY',
    IP: location.hostname,
    PARAMS: '[channel|0][url|' + getCameraUrl(channel) + ']',
    STREAM_WIDTH: w,
    STREAM_HEIGHT: h,
    VIDEOTYPE: play,
  };
  let ret = commandRTSP(param);
  // if (ret === 'FALSE') {
  //     $("#ocx").html("视频播放失败请刷新重试！");
  //     stopRTSP(channel)
  // }
  console.log("rtsp-play:" + ret);
};

var stopRTSP = function () {
  let param = {
    CMD: 'STOP',
    PARAMS: "[channel|0]"
  };
  try {
    let ret = commandRTSP(param);
    console.log("rtsp-stop:" + ret);
  } catch (e) {
    console.log(e)
  }
};
var closeRTSP = function () {
  let param = {
    CMD: 'CLOSE',
  };
  try {
    let ret = commandRTSP(param);
    console.log("rtsp-close:" + ret);
  } catch (e) {
    console.log(e)
  }
};
var removeVideo = function () {
  stopRTSP();
  closeRTSP();
};
var ThreadStatus = function () {
  let result = false;
  try {
    console.log("ocx_ThreadStatus");
    let ip = location.hostname;
    let ret = ocx.ThreadStatus(ip);
    console.log("ThreadStatus:" + ret);
    if (ret === "TRUE") {
      result = true
    }

  } catch (e) {
    result = e;
    console.log(e)
  } finally {
    return result
  }
};
var stopThread = function () {
  console.log("ocx.STOPThread");
  let ip = location.hostname;
  let ret = ocx.STOPThread(ip);
  console.log("STOPThread:" + ret);
};
var getEventStatus = function () {
  let ip = location.hostname;
  let status = ocx.eventWeb(ip);
  try {
    status = JSON.parse(status)
  } catch (e) {
    console.log(e)
  }
  return status
};

var savePathConfig = function (path) {
  console.log("ocx.set_pic_save_dir");
  let result = false;
  let ip = location.hostname;
  if (find_ocx()) {
    result = ocx.set_pic_save_dir(path.img, ip);
  }
  return result;
};

var initPathConfig = function () {
  console.log("ocx.getCurrentPicDir");
  let path = {
    img: "",
    record: "",
    log: ""
  };
  if (find_ocx()) {
    let imgPath = getCommonConf("PICPATH");
    let dbPath = getCommonConf("DBPATH");
    if (imgPath !== 'FALSE') {
      path.img = imgPath.myReplace("\\\\\\\\", "\\");
      path.db = dbPath.myReplace("\\\\\\\\", "\\");
      path.record = "";
      path.log = ""
    }
  }
  return path;
};

var ocxSQL = function (params) {
  let data = [];
  let ip = location.hostname;
  try {
    if (sqlStatus === 0) {
      let path = initPathConfig();
      let openDB = ocx.sqlite_open(ip, path.db);
      if (openDB == -1) {
        openDB = ocx.sqlite_open(ip, path.db);
        if (openDB == -1) {
          layer.msg('无法打开文件！请联系开发人员。', {icon: 2});
          return
        }
      }
      console.log("open database " + openDB);
      if (openDB === 0) {
        sqlStatus = 1;
      }
    }
    console.log("ocx.SQL_Select");
    let p = {
      IP: ip
    };
    for (let index in params) {
      p[index] = params[index]
    }
    let eventData = ocx.SQL_Select(JSON.stringify(p));
    //console.log(eventData);

    if (eventData !== 'FALSE') {
      let d = eventData.replace(/\\/g, "\\\\");
      // let JSONdata = eventData.replace(/\\\\/g, "////")


      data = JSON.parse(d);
      let eventConfig = getSe('eventConfig');
      let carType = getSe("carDetailType");
      $(data).each(function (i) {
        data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
        data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
        data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
        data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
        data[i].CAR_TYPE = eventConfig.carType[data[i]['CAR_TYPE']];
        if (data[i]['CHE_XING']) {
          data[i].CHE_XING = carType.get(data[i]['CHE_XING']);
        }
        if (data[i]['IMAGE_PRE_PATH']) {
          data[i].preTime = getTime(data[i]['IMAGE_PRE_PATH'])
        }
        if (data[i]['IMAGE_LAST_PATH']) {
          data[i].lastTime = getTime(data[i]['IMAGE_LAST_PATH'])
        }
        if (data[i]['IMAGE_FEATURE_PATH']) {
          data[i].featureTime = getTime(data[i]['IMAGE_FEATURE_PATH'])
        }
      });

    }
  } catch (e) {
    console.log(e);
    layer.msg('查询出错！', {icon: 2})
  }
  return data;
};
var getTime = function (picString) {
  let index = picString.lastIndexOf("\\");
  let time = picString.slice(index + 1, index + 18);
  let newTime = time.slice(0, 4) + "/" + time.slice(4, 6) + "/" + time.slice(6, 8) + " "
    + time.slice(8, 10) + ":" + time.slice(10, 12) + ":" + time.slice(12, 14)
  return timestampToTime(new Date(newTime).getTime() + parseInt(time.slice(14)));
};
var initialize_ocx = function (id) {
  let result = find_ocx();
  let flag = {
    result: false,
    msg: '没有安装插件无法进行操作！'
  };
  let version = 0;
  let href = '../setup.exe';
  if (id !== 'eventVideo' && id !== 'indexVideo' && id !== "authContainer") {
    href = '../../setup.exe'
  }
  let container = $("#" + id);
  if (result === true) {
    container.append("<OBJECT ID=\"ocx\" width=\"100%\" height=\"100%\" classid=\"clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA\"></OBJECT>");
    ocx = document.getElementById("ocx");
    //getDeviceVersion();
    try {
      version = getVersion();
      let nowVersion = getSe("projectConfig").index_ocx;
      if (compareVersion(version.main + "." + version.sub, nowVersion.main + "." + nowVersion.sub) >= 0) {
        let fileInit = getSe("fileInit");
        if (!fileInit) {
          fileInit = initFilePath();
          setSe("fileInit", fileInit);
        }
        if (!fileInit.result) {
          flag.result = false;
          flag.msg = fileInit.msg;
          flag.offset = fileInit.offset || 'auto';
          ocxDestroy();
          container.html("");
          return flag
        }
        let streamType = getCommonConf("RTSPWAY");
        let type = ['UDP', 'TCP'];
        // let control = "<div class='video-control'>" +
        //     "<div class=\"video-control-item video-button\">" +
        //     "<i class=\"video-play\" id=\"playVideo\"></i>" +
        //     "</div>" +
        //     "<div class=\"video-control-item video-type\" id='videoT1C'>" +
        //     "<span><a href=\"javascript:;\" id='videoT1Value'>主码流</a></span>" +
        //     "<div class='video-select' style='display: none;'>" +
        //     "<iframe id=\"videoT1\" style=\"bottom:30px;position:absolute;background-color: #fff;width: 60px;height: 60px;\">" +
        //     "</iframe>" +
        //     "<ul id='videoT1Select'>" +
        //     "<li data-channel=\"1\" class='selected'><a href=\"javascript:;\">主码流</a></li>" +
        //     "<li data-channel=\"2\"><a href=\"javascript:;\">子码流</a></li>" +
        //     "</ul>" +
        //     "</div>"+
        //     "</div>" +
        //     "<div class=\"video-control-item video-type\" id='videoT2C'>" +
        //     "<span><a href=\"javascript:;\" id='videoT2Value'>UDP</a></span>" +
        //     "<div class='video-select'>" +
        //     "<iframe id=\"videoT2\" style=\"bottom:30px;position:absolute;background-color: #fff;width: 60px;height: 60px;\">" +
        //     "</iframe>" +
        //     "<ul id='videoT2Select'>" +
        //     "<li data-type=\"0\" class='selected'><a class='selected' href=\"javascript:;\">UDP</a></li>" +
        //     "<li data-type=\"1\"><a href=\"javascript:;\">TCP</a></li>" +
        //     "</ul>" +
        //     "</div>"+
        //     "</div>" +
        //     "</div>";
        let height = container[0].offsetHeight - 30;
        if (id === 'indexVideo') {
          $("#ocx").attr({
            width: (container[0].offsetWidth),
            height: (height),
          });
          // container.append(control);
          let channel = getSe("projectConfig").camera_channel;
          $("#videoT2Value").html(type[streamType]);
          setDataset($("#videoT2Value")[0], "type", streamType);
          setTimeout(function () {
            initStreamType(channel)
          }, 1000);
        }
        else if (id === 'eventVideo') {
          $("#ocx").attr({
            width: (container[0].offsetWidth),
            height: (height),
          });
          $("#videoT2Value").html(type[streamType]);
          setDataset($("#videoT2Value")[0], "type", streamType);
          // container.append(control);
          //initRTSP()
        }
        flag.result = true;
        flag.msg = "";
        return flag

      } else {
        flag.msg = "插件版本不匹配无法进行操作！"
      }
    } catch (e) {
      console.log(e);
    } finally {
      let projectConfig = getSe("projectConfig");
      projectConfig.ocx_version = version;
      setSe('projectConfig', projectConfig);
    }
  }
  let msg = "请点击此处下载插件，安装时请关闭浏览器";
  if (id !== 'pictureOCX') {
    container.html("");
    container.append("<a id='ocx_down' href=" + href + ">" + msg + "</a>");
  }
  return flag
};

var getCommonConf = function (key) {
  console.log("ocx.getCurrentconf");
  let ip = location.hostname;
  let value = ocx.getCurrentconf(ip, key);
  console.log("getCurrentconf", value);
  return value
};

var setRTSPWay = function (type, callback) {
  console.log("ocx.RTSP_Way");
  let ip = location.hostname;
  let ret = ocx.RTSP_Way(ip, type);
  let result = false;
  console.log("RTSP_Way", ret);
  if (ret === 'TRUE') {
    result = true
  }
  if (callback) {
    callback(result);
  }
};

var RTSPError = function (msg) {
  $("#RTSPError").css({display: "block"});
  $("#errorMsg").html(msg);
  $("#playVideo").removeClass('video-stop').addClass('video-play');
};

var changePic = function (that, type) {
  let index = parseInt(getDataset($(that).parents("#eventControl").siblings(".layui-layer-content").find("#eventPicture")[0]).index);
  if (type === "prev") {
    index = index - 1
  } else if (type === 'next') {
    index = index + 1
  }
  let data = $("div[lay-id='eventTable'] .layui-table-body").find("tr")[index];
  if (!data) {
    layer.msg('没有事件了！', {icon: 2});
    return;
  }
  setDataset($("#eventPicture")[0], "index", index);
  let changeData = getTDValue(data);
  showCanvas(changeData);
  showDetailTable(changeData);
  $(".layui-layer-title").html('事件ID：' + changeData.ID,);
  changeData = null;
};
var getTDValue = function (obj) {
  let TDs = $(obj).find("td");
  let data = {};
  TDs.each(function (i) {
    let key = getDataset(TDs[i]).field;
    let value = $(TDs[i]).find(".layui-table-cell").html();
    data[key] = value;
  });
  return data;
};

var getDeviceTime = function () {
  console.log("ocx.GetDeviceTime");
  let time = ocx.getDeviceTime(location.hostname);
  return date2stamp(time)
};

var getDeviceVersion = function () {
  console.log("ocx.getDeviceVersion");
  let version = ocx.getDeviceVersion(location.hostname);
  console.log("deviceVersion:", version);
  let v = version.slice(4);
  let newAgreement = false;
  if (compareVersion(v, "8.0.2") >= 0) {
    newAgreement = true;
  }
  setSe("SDC", newAgreement);
};
var uploadLicence = function (path) {
  console.log("ocx.UploadLicence");
  let result = ocx.UploadLicence(location.hostname, path);
  console.log("UploadLicence:", result);
  if (result === 'FALSE') {
    return false
  }
  return true
};

var getLicencePath = function () {
  console.log("ocx.SelectFileDialog");
  let result = ocx.SelectFileDialog();
  console.log("SelectFileDialog:", result);
  if (result === 'FALSE') {
    return false
  }
  return result
};

var showEventDetail = function (data, _index, w, h) {

  layer.open({
    title: '事件ID：' + data.ID,
    type: 1,
    area: [w, h],
    offset: 'auto',
    success: function (layero, index) {
      setDataset($("#eventPicture")[0], "index", _index);
      showCanvas(data);
      showDetailTable(data);
      setTimeout(function () {
        $(".layui-layer-page").append("<span id='eventControl' class='img-control'><a href=\"javascript:;\" class=\"layui-layer-iconext layui-layer-imgprev\"></a><a href=\"javascript:;\" class=\"layui-layer-iconext layui-layer-imgnext\"></a></span>");
        $("#eventControl .layui-layer-imgprev").off('click').on('click', function (e) {
          changePic(this, 'prev')
        });
        $("#eventControl .layui-layer-imgnext").off('click').on('click', function (e) {
          changePic(this, 'next')
        })
      }, 10);
      data = null;
    },
    end: function (layero, index) {
      clearDetailTable();
    },
    content: $('#eventPicture') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
  });
};
var showCanvas = function (data) {
  clearDetailTable();
  $(".layer-pic").addClass("event-height");
  imageCanvas('currentPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], null, true);

  if (data['IMAGE_PLATE_PATH'] !== '') {
    imageCanvas('platePic', data['IMAGE_PLATE_PATH'], 0, 0, 0, 0, true);
  }
  if (data['IMAGE_PRE_PATH'] !== '') {
    imageCanvas('prePic', data['IMAGE_PRE_PATH'], data['IMAGE_PRE_X'], data['IMAGE_PRE_Y'], data['IMAGE_PRE_W'], data['IMAGE_PRE_H'], true)
  }
  if (data['IMAGE_EVENT_PATH'] !== '') {
    // if (data['IMAGE_PRE_PATH'] === '') {
    //   imageCanvas('prePic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true);
    //   $("#prePic").addClass("show");
    // } else {
    //   imageCanvas('eventPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true);
    //   $("#eventPic").addClass("show");
    // }
    imageCanvas('eventPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true);
    $("#eventPic").addClass("show");
  }
  if (data['IMAGE_LAST_PATH'] !== '') {
    imageCanvas('lastPic', data['IMAGE_LAST_PATH'], data['IMAGE_LAST_X'], data['IMAGE_LAST_Y'], data['IMAGE_LAST_W'], data['IMAGE_LAST_H'], true)
  }
  if (data['IMAGE_FEATURE_PATH'] !== '') {
    imageCanvas('featurePic', data['IMAGE_FEATURE_PATH'], 0, 0, 0, 0, true);
  }
};
var showDetailTable = function (data) {
  let tHead = "";
  let $trTemp = $("<tr></tr>");
  tHead = "<th>事件类型</th><th>车道号</th><th>车牌类型</th><th>车牌号码</th><th>速度</th><th>车身颜色</th><th>车辆类型</th>";
  // 往行里面追加 td单元格
  if (data['CHE_XING']) {
    tHead += '<th>车型</th>'
  }
  // tHead+='<th>概述</th>';
  $trTemp.append("<td>" + data.EVENT_TYPE + "</td>");
  $trTemp.append("<td>" + data.LANE_INDEX + "</td>");
  $trTemp.append("<td>" + data.PLATE_TYPE + "</td>");
  $trTemp.append("<td>" + data.PLATE_STRING + "</td>");
  $trTemp.append("<td>" + data.CAR_SPEED + "</td>");
  $trTemp.append("<td>" + data.CAR_COLOR + "</td>");
  $trTemp.append("<td>" + data.CAR_TYPE + "</td>");
  if (data['CHE_XING']) {
    $trTemp.append("<td>" + data.CHE_XING + "</td>");
  }
  // $trTemp.append("<td> </td>");
  $("#detailTable tbody tr").remove();
  $trTemp.appendTo("#detailTable tbody");
  $("#detailTable thead tr").html(tHead);
  if (data['preTime']) {
    $("#preTime").html("第一张：" + data['preTime']);
  }
  if (data['time']) {
    if (!data['preTime']) {
      $("#preTime").html("第一张：" + data['time']).addClass("show");
    } else {
      $("#eventTime").html("第二张：" + data['time']).addClass("show");
    }
  }
  if (data['lastTime']) {
    $("#lastTime").html("第三张：" + data['lastTime']);
  }
  if (data['featureTime']) {
    $("#featureTime").html("特写：" + data['featureTime']);
  }
};
var clearDetailTable = function () {
  $(".layer-pic").removeClass("event-height");
  $(".pic-show").removeClass("show");
  $(".time-show").removeClass("show");
  $("#detailTable tbody").html("");
  $("#detailTable thead tr").html("");
  $("#currentPic").html("");
  $("#featurePic").html("").off("click");
  $("#eventPic").html("").off("click");
  $("#platePic").html("").off("click");
  $("#prePic").html("").off("click");
  $("#lastPic").html("").off("click");
  $("#timeList .layui-row div").html("")
};

var exportData = function (data, path) {
  let dataL = 0;
  let exportData = {};
  data.map(function (item) {
    let type = item.EVENT_TYPE;
    if (!exportData[type]) {
      dataL++;
      exportData[type] = [];
    }
    if (item['IMAGE_PLATE_PATH'] !== '') {
      exportData[type].push(item['IMAGE_PLATE_PATH'])
    }
    if (item['IMAGE_PRE_PATH'] !== '') {
      exportData[type].push(item['IMAGE_PRE_PATH'])
    }
    if (item['IMAGE_EVENT_PATH'] !== '') {
      exportData[type].push(item['IMAGE_EVENT_PATH'])
    }
    if (item['IMAGE_LAST_PATH'] !== '') {
      exportData[type].push(item['IMAGE_LAST_PATH'])
    }
  });
  let result = false;
  let exportL = 0;
  for (let key in exportData) {
    let r = exportPictureTask(path + "\\" + key, exportData[key]);
    if (!r) {
      break;
    } else {
      exportL++;
    }
  }
  if (exportL === dataL) {
    result = true;
  }
  return result;
};
var exportPictureTask = function (path, data) {
  let s = exportPicture(0, path, data.join());
  if (s === -1 || s < -1) {
    return false;
  }
  if (s === data.length) {
    return true
  }
  sleep(1);
  return exportPictureTask(path, data);
};
