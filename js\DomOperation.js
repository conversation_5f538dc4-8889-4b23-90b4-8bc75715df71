/**
 * Create by Chelly
 * 2020/9/27
 */


document.ready(function () {
  initInput();
});
var initInput = function () {
  let inputList = $(".input-range");
  if (inputList.length) {
    for (let i = 0; i < inputList.length; i++) {
      let item = $(inputList[i]);
      let itemData = getDataset(inputList[i]);
      let text = itemData.label;
      let labelId = itemData.labelId;
      let labelClass = itemData.labelClass;
      let tempId = itemData.id;
      let disabled = itemData.disabled;
      if (disabled !== "false") {
        disabled = ' disabled="disabled"'
      }
      let replaceContent = "";
      replaceContent += '<label ';
      if (labelId) {
        replaceContent += ('id="' + labelId + '"')
      }
      if (labelClass) {
        replaceContent += ('class="' + labelClass + '"')
      }
      replaceContent += '>' + text + '</label>';
      replaceContent += '<div class="input-handler-wrap"><button type="button" class="input-up"' + disabled + '><i class="button-edge button-up"></i></button><button type="button" class="input-down" ' + disabled + '><i class="button-edge button-down"></i></button></div><input ';
      if (tempId) {
        replaceContent += ('id="' + tempId + '"')
      }
      replaceContent += ' type="text" value="0" class="input-number" oninput="value=value.replace(/[^\\d]/g,\'\')"/>';
      item.html(replaceContent);
    }
  }
};