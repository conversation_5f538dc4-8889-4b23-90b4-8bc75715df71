var form;
$(document).ready(function () {
    initLogin();
});
var initLogin = function () {
    layui.use(['form'], function () {
        var $ = layui.$;
        form = layui.form;
        initCookie();
        onloadConfig('../config/config.json', 'projectConfig', function () {
            initCopy();//初始化logo大小和底部信息
            // // 获取SDC版本做网络协议兼容
            // let version = getSe("projectConfig").protocol;
            // console.log("deviceVersion:",version);
            // let newAgreement = false;
            // if(parseInt(version)===2){
            //     newAgreement = true;
            // }
            // setSe("SDC",newAgreement);
        });
        form.render();
        form.on('submit(user-login-submit)', handleLogin);//form.on('submit(user-login-submit)'监听submit提交,传参：表单数据
        $(document).keydown(function (event) {
            if (event.keyCode == "13") {// keyCode=13是回车键
                $("#login").trigger("click");//trigger 触发某事件
            }
        });
        $('#username').val('admin')
        $('#password').val('12345')
    });
};
var handleLogin = function (obj) {
    let data = obj.field;
    let sucMsg = langMessage.login.loginSuc, failMsg = langMessage.login.loginFail;
    if (data.username === 'admin' && data.password === '12345') {
        sessionStorage.setItem("loginUser", JSON.stringify(data));
        let rememberMe = $("#rememberMe")[0].checked === true ? 1 : 0;
        setCookie('username', data.username, 30);
        setCookie('password', data.password, 30);
        setCookie('rememberMe', rememberMe, 30);


        layer.msg(sucMsg, {
            offset: '15px'
            , icon: 1
            , time: 1000
        }, function () {
            setInfo();//根据当前屏幕大小判断当前需要显示的图片大小
            let top = getTopWindow();
            top.document.getElementById("pngContainer").style.display = "none";
            top.document.getElementById("index_frame").style.top = 0;
            window.location.href = 'content.html'; //后台主页
            parent.$("#select_i18n").hide();

        });
    } else {

        layer.msg(failMsg, {
            offset: '15px'
            , icon: 2
            , anim: 6
            , time: 1000
        })


    }
};
//记住密码相关
var initCookie = function () {
    let rememberMe = parseInt(getCookie('rememberMe'));
    if (rememberMe) {
        $("#username").val(getCookie('username'));
        $("#password").val(getCookie('password'));
        $("#rememberMe")[0].checked = true
    }
};
