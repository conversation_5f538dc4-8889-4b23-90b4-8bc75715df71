var element, layer, form, laydate, table, laypage;
var moduleStatusConfig;
var timer = null, time = 1000;
var threadArr = ['calculation', 'trace', 'encoding', 'vpss', 'video_record', 'forwarding', 'console', 'license', 'osd',
    'web', 'land_circle', 'light_signal', 'NTP', 'Debug', 'h264', 'h264_synthetic', 'video_maker', 'uploader', 'blacklist',
    'checkuid', 'command_processor', 'remote_access', 'vframe_receiver', 'forward_event', 'venc_h264', 'discovery',
    'free_frame', 'web_config', 'web_eventServer', 'merge', 'flash', 'event_forward']
var threadCol = []
var currPage = 1, limit = 10;
var threadName = null, errCodeName = null, info = [], lastClickTime = 0, brokenLineRadio = 'day';
$(document).ready(function () {
    var beginChange = 0, endChange = 0;
    $("#begin").change(function (e) {
        beginChange = 1;
    });
    $("#end").change(function (e) {
        endChange = 1;
    });
    layui.use(['element', 'layer', 'form', 'laydate', 'table', 'laypage'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, laydate = layui.laydate, table = layui.table, laypage = layui.laypage;
        laydate.render({
            elem: '#date_input' //指定元素
            // , value: dateStart + " - " + dateEnd
            , isInitValue: true //是否允许填充初始值，默认为 true
            // , min: dateStart
            // , max: dateEnd
            , range: true
            , type: 'datetime'
            // , done: function (value, date, endDate) {
            //     console.log(value); //得到日期生成的值，如：2017-08-18
            //     console.log(date); //得到日期时间对象：{year: 2017, month: 8, date: 18, hours: 0, minutes: 0, seconds: 0}
            //     console.log(endDate); //得结束的日期时间对象，开启范围选择（range: true）才会返回。对象成员同上。
            // }
        });
        laydate.render({
            elem: '#date_inputAnalysis' //指定元素
            , isInitValue: true //是否允许填充初始值，默认为 true
            , range: true
            , type: 'datetime'
        });
        let col = [
            {type: 'numbers', title: '序号'},//序号列
            // {
            //     field: 'id',
            //     title: 'ID',
            //     align: 'center'
            // },
            {
                field: 'time',
                title: '时间',
                align: 'center'
            }
        ]
        for (let i = 0; i < threadArr.length; i++) {
            col.push(
                {
                    field: threadArr[i],
                    title: langMessage.systemModuleStatus.text[threadArr[i] + "_"],
                    align: 'center'
                }
            )
        }
        table.render({
            elem: '#status_table'
            , cols: [col]
            , data: []
        });

        laypageRender(0)


        changeTag(1);
        form.on('switch(switchRefresh)', function (data) {
            // console.log(data.elem); //得到checkbox原始DOM对象
            // console.log(data.elem.checked); //开关是否开启，true或者false
            // console.log(data.value); //开关value值，也可以通过data.elem.value得到
            // console.log(data.othis); //得到美化后的DOM对象
            switchListener(data.elem.checked);
        });
    })
});
var changeTag = function (type) {
    if (type === 1) {
        let systemModuleStatusFresh = getCookie('systemModuleStatusFresh')
        if (systemModuleStatusFresh && systemModuleStatusFresh === 'true') {
            $('#fresh').addClass("layui-disabled").attr("disabled", true);
            timer = setInterval(getModuleStatus, time)
            $('#switchRefreshID').prop("checked", true);
        } else {
            $('#fresh').removeClass("layui-disabled").attr("disabled", false);
            clearInterval(timer)
            setCookie('systemModuleStatusFresh', 'false', 30)
            $('#switchRefreshID').prop("checked", false);
        }
        form.render('checkbox')
        getStatusConfig();
    } else if (type === 2) {
        clearInterval(timer)
        getCol();
    }
    // } else if (type === 3) {
    //     getRateConfig();
    // }
    else {
        getStatusAnalysis()
    }
}
var switchListener = function (checked) {
    if (checked) {
        $('#fresh').addClass("layui-disabled").attr("disabled", true);
        timer = setInterval(getModuleStatus, time)
        setCookie('systemModuleStatusFresh', 'true', 30)
    } else {
        $('#fresh').removeClass("layui-disabled").attr("disabled", false);
        clearInterval(timer)
        setCookie('systemModuleStatusFresh', 'false', 30)
    }
};
/**
 * 获取模块状态错误码配置文件
 */
var getStatusConfig = function () {
    onloadConfig('../../config/moduleStatus.json', 'moduleStatusConfig', getModuleStatusCallBack);
};
var getModuleStatusCallBack = function (flag) {
    if (!flag) {
        console.log('配置文件加载失败')
        return
    }
    // console.log(getSe('moduleStatusConfig'))
    moduleStatusConfig = getSe('moduleStatusConfig')
    getModuleStatus();

};
var getModuleStatus = function () {
    initWebService(webserviceCMD.CMD_GET_THRD_STATE, null, handleModuleStatus)
};
/**
 * @param data 请求返回数据
 * data形如
 data = {
        "ret": 0,
        "desc": "success",
        "data": [{
            "name": "calculation",
            "state": 268435456,
            "mask": 1,
            "desc": "calculation",
            "pid": 6659
        }]
    }
 * */
var handleModuleStatus = function (data) {
    for (let i = 0; i < threadArr.length; i++) {
        $("#" + threadArr[i] + "_").parent().addClass("status-display")
    }
    for (let i = 0; i < data.data.length; i++) {
        let name = data.data[i].name;
        $("#" + name + "_").parent().removeClass("status-display")
        let ret = handleOper(data.data[i])
        $("#" + name).text(langMessage.systemModuleStatus.text[ret.text])
        $("#" + name).css('color', ret.color)
        if (ret.errorCodeArr.length > 0) {
            let text = $("#" + name).text()
            $("#" + name).text(text + ',' + langMessage.systemModuleStatus.text.hasErrCode)
        }
        // tips显示#########################################
        let codeDiv = "<div>高32位：" + ret.state_16 + "</div><div>低32位：" + ret.mask_16 + "</div>";
        for (let i = 0; i < ret.errorCodeArr.length; i++) {
            codeDiv = codeDiv + "<div>" + ret.errorCodeArr[i] + "</div>"
        }
        $("#" + name).off('mouseenter')
        $("#" + name).off('mouseleave')
        var tips;
        $("#" + name).on({
            mouseenter: function () {
                tips = layer.tips(codeDiv, '#' + name, {
                    tips: [3],
                    time: 0
                });
            },
            mouseleave: function () {
                layer.close(tips);
            }
        });
    }
};

// 高32位那边有错误码才算有错误,低32位是留着的(编号可以都打出来).
// 只有故障状态才会有错误码
var handleOper = function (data) {
    // data形如{"name": "vpss", "state": 9, "mask": 9, "desc": "working", "pid": 21}
    // let num = 2562260489589490296n
    // let num = BigInt(2562260489589490296n)
    // let num = BigInt('2562260489589490296')
    // let s = ""
    // for (let i = 63; i >= 0; i--) {
    // const check_i = 1n << BigInt(i)
    // const check_i = BigInt(1n) << BigInt(i)
    // const check_i = BigInt(1) << BigInt(i)
    // const bit_i = num & check_i
    // if (bit_i) {
    //     s = s + '1'
    // } else {
    //     s = s + '0'
    // }
    // }
    // console.log(s)
    //0010 0011 1000 1110 1111 1010 0101 0111 0000 0000 0000 0000 0000 0010 0111 1000
    let result = {
        text: '',
        state_16: '',
        mask_16: '',
        errorCodeArr: [],
        color: ''
    }
    let name = data.name;
    let state = data.state;
    let state_16 = (parseInt(state, 10) >>> 0).toString(16).toLocaleUpperCase()// parseInt(state, 10) >>> 0将state转为无符号整数
    while (state_16.length < 8) {
        state_16 = '0' + state_16
    }
    let state_0x = '0x' + state_16
    state_0x = state_0x.substr(0, state_0x.length - 4) + ' ' + state_0x.substring(state_0x.length - 4)
    result.state_16 = state_0x

    let mask = data.mask;
    let mask_16 = (parseInt(mask, 10) >>> 0).toString(16).toLocaleUpperCase()// parseInt(mask, 10) >>> 0将smask转为无符号整数
    while (mask_16.length < 8) {
        mask_16 = '0' + mask_16
    }
    let mask_0x = '0x' + mask_16
    mask_0x = mask_0x.substr(0, mask_0x.length - 4) + ' ' + mask_0x.substring(mask_0x.length - 4)
    result.mask_16 = mask_0x
    if (state === 0) { // 未激活
        result.text = 'moduleStatus_unactive'
        result.color = '#909399'
        if (mask !== 0) {
            // mask错误码##################################
            let errorCodeArr_mask = []
            for (let i = 0; i < 32; i++) {
                if (checkI(mask, i)) {
                    let index = i
                    if (i < 10) {
                        index = '0' + i
                    }
                    errorCodeArr_mask.push(index + '：' + moduleStatusConfig[name]['mask'][i.toString()])
                }
            }
            result.errorCodeArr = errorCodeArr_mask

            if (errorCodeArr_mask.length > 0) {
                result.color = '#666666'
            }
        }
    } else {
        if (checkI(state, 28)) {// 就绪
            result.text = 'moduleStatus_ready'
            result.color = '#1E90FF'
        }
        if (checkI(state, 31)) { // 运行
            result.text = 'moduleStatus_success'
            result.color = '#67c23a'
        }
        if (checkI(state, 27)) {// 挂起
            result.text = 'moduleStatus_sleep'
            result.color = '#E6A23C'
        }
        if (checkI(state, 30)) {// 错误
            result.text = 'moduleStatus_error'
            result.color = 'rgb(252,16,3)'
        }
        // state错误码#######################################
        let errorCodeArr_state = []
        for (let i = 0; i < 27; i++) {
            if (checkI(state, i)) {
                let index = i + 32
                errorCodeArr_state.push(index + '：' + moduleStatusConfig[name]['state'][i.toString()])
            }
        }
        // mask错误码########################################
        let errorCodeArr_mask = []
        for (let i = 0; i < 32; i++) {
            if (checkI(mask, i)) {
                let index = i
                if (i < 10) {
                    index = '0' + i
                }
                errorCodeArr_mask.push(index + '：' + moduleStatusConfig[name]['mask'][i.toString()])
            }
        }
        //错误码合并###########################################
        result.errorCodeArr = errorCodeArr_mask.concat(errorCodeArr_state);
        if (errorCodeArr_state.length > 0) {
            if (checkI(state, 31)) { // 运行,有错误码
                result.color = 'rgb(180,220,52)'
            }
            if (checkI(state, 30)) {// 错误,有错误码
                result.color = '#FF5722'
            }
            if (checkI(state, 28)) {// 就绪,有错误码
                result.color = '#01AAED'
            }
            if (checkI(state, 27)) {// 挂起,有错误码
                result.color = '#EA7D7F'
            }
        }
    }
    return result;
};
var checkI = function (data, idx) {
    // 查看data的i位是否为0  若bit_i=0则第i位为0，若bit_i不为0，则第i位是1
    const check_i = 1 << idx
    const bit_i = data & check_i
    if (bit_i) {
        return true
    } else {
        return false
    }
};
var numberTo2 = function (state) {
    let state1 = state >>> 0 //转为无符号数
    let state_2 = state1.toString(2)
    while (state_2.length < 32) {
        state_2 = '0' + state_2
    }
}

// 获取有哪些线程，得到线程名称
var getCol = function () {
    let params = {
        page: 1,
        limit: 1
    };
    initWebService(webserviceCMD.CMD_GET_THRD_STATE_HISTORY, params, handleCol)
}
var handleCol = function (data) {
    let col = [
        {type: 'numbers', title: '序号'},//序号列
        // {
        //     field: 'id',
        //     title: 'ID',
        //     align: 'center'
        // },
        {
            field: 'time',
            title: '时间',
            align: 'center',
            width: 165,
            templet: function (d1) {
                // 返回的时间戳为0时区的
                return getFormatTime1(d1.time)
            }
        }
    ]
    if (data.data.length > 0) {
        let obj = data.data[0]
        for (let key in obj) {
            if (threadArr.indexOf(key) > -1) {
                col.push(
                    {
                        field: key,
                        title: langMessage.systemModuleStatus.text[key + "_"],
                        align: 'center',
                        templet: function (d1) {
                            let obj = {
                                name: key,
                                state: d1[key].state,
                                mask: d1[key].mask
                            }
                            let ret = handleOper(obj)
                            if (ret.text === "") {
                                console.log("###############################################")
                                console.log(d1.time)
                                console.log(obj)
                                console.log(ret)
                            }
                            let text = langMessage.systemModuleStatus.text[ret.text]
                            if (ret.errorCodeArr.length > 0) {
                                text = text + ',' + langMessage.systemModuleStatus.text.hasErrCode
                            }

                            let spanId = key + d1.LAY_TABLE_INDEX;
                            let span = "<span id='" + spanId + "' style='color: " + ret.color + ";'>" + text + "</span>";
                            return span;
                        }
                    }
                )
            }
        }
    }
    threadCol = col
    query();
}
var reset = function () {
    $("#date_input").val("");
    form.render();
}
var resetAnalysis = function () {
    $("#date_inputAnalysis").val("");
    form.render();
}
var submit = function () {
    currPage = 1
    laypageRender(0)
    query()
}
var query = function () {
    let params = {
        channel_id: 0,
        web_cmd: webserviceCMD.CMD_GET_THRD_STATE_HISTORY,
        page: currPage,
        limit: limit
    };
    let date_input = $('#date_input').val();//2021-12-15 00:00:00 - 2022-01-15 00:00:00
    if (date_input) {
        let start = date_input.substring(0, 19)
        let end = date_input.substring(22)
        params.begin_time = replaceTime(start) * 1000;
        params.end_time = replaceTime(end) * 1000;
    }
    let wsURL = getRouterUrl();
    let type = getSe("SDC");
    // 1旧网络协议，2新网络协议
    if (!type) {
        let wsData;
        //SOAP 1.1 请求报文格式，1.2在网上可以找到
        wsData = '<?xml version="1.0" encoding="utf-8"?>';
        wsData = wsData + '<SOAP-ENV:Envelope  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xmlns:xsd="http://www.w3.org/2001/XMLSchema"  xmlns:ns1="http://webservice.service.seisys.cn/">';
        wsData = wsData + '<SOAP-ENV:Body>';
        wsData = wsData + '<ns1:CommonOperate>'; //这里就是发布的方法名和xml文档中的命名空间地址（图中画线部分）
        wsData = wsData + '<arg0>';
        wsData = wsData + escapeHTML(JSON.stringify(params));
        wsData = wsData + '</arg0>';
        wsData = wsData + '</ns1:CommonOperate>';
        wsData = wsData + '</SOAP-ENV:Body>';
        wsData = wsData + '</SOAP-ENV:Envelope>';
        params = wsData
    }
    // table.reload('status_table', {
    table.render({
        elem: '#status_table'
        , url: wsURL //数据接口
        , method: "post"
        , where: params
        , contentType: "text/xml; charset=utf-8"
        , text: {
            none: '未查询到相关数据' //默认：无数据。注：该属性为 layui 2.2.5 开始新增
        }
        , even: true
        , cols: [threadCol]
        , done: function (response, curr, count) {
            laypageRender(count)

            let res = response
            if (!type) {
                res = response.childNodes[0].childNodes[0].childNodes[0].childNodes[0].childNodes[0].data;
                // 转义特殊符号
                res = unescapeHTML(res);
                if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                    res = res.replace(/\n/g, "\\n");
                }
            }
            for (let i = 0; i < res.data.length; i++) {
                let d1 = res.data[i]
                for (let key in  d1) {
                    let obj = {
                        name: key,
                        state: d1[key].state,
                        mask: d1[key].mask
                    }
                    let ret = handleOper(obj)
                    let spanId = key + d1.LAY_TABLE_INDEX;
                    let codeDiv = "<div>高32位：" + ret.state_16 + "</div><div>低32位：" + ret.mask_16 + "</div>";
                    for (let i = 0; i < ret.errorCodeArr.length; i++) {
                        codeDiv = codeDiv + "<div>" + ret.errorCodeArr[i] + "</div>"
                    }
                    $("#" + spanId).off('mouseenter')
                    $("#" + spanId).off('mouseleave')
                    var tips;
                    $("#" + spanId).on({
                        mouseenter: function () {
                            tips = layer.tips(codeDiv, '#' + spanId, {
                                tips: [3],
                                time: 0
                            });
                        },
                        mouseleave: function () {
                            layer.close(tips);
                        }
                    });
                }

            }
        }
        //parseData和response共同作用于数据显示
        , parseData: function (response) { //res 即为原始返回的数据
            console.log("返回数据1：", response)
            let res = response
            if (!type) {
                res = response.childNodes[0].childNodes[0].childNodes[0].childNodes[0].childNodes[0].data;
                // 转义特殊符号
                res = unescapeHTML(res);
                if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                    res = res.replace(/\n/g, "\\n");
                }
            }
            console.log("返回数据2：", res)
            return {
                "code": parseInt(res.ret), //解析接口状态
                "msg": res.desc, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        }
        , response: {
            statusName: 'code', //规定返回的状态码字段为code
            statusCode: 0 //规定成功的状态码为200
        }
    });
}
var laypageRender = function (count) {
    laypage.render({
        elem: 'status_table_laypage' //注意，这里的 status_table_laypage 是 ID，不用加 # 号
        , count: count //数据总数，从服务端得到
        , limit: limit
        , limits: [10, 20, 40, 80, 100]
        , curr: currPage
        , groups: 5
        , prev: '上一页'
        , next: '下一页'
        , layout: ['prev', 'page', 'next', 'skip', 'count', 'limit']
        , jump: function (obj, first) {
            //obj包含了当前分页的所有参数，比如：
            // console.log(obj.curr); //得到当前页，以便向服务端请求对应页的数据。
            // console.log(obj.limit); //得到每页显示的条数

            currPage = obj.curr
            limit = obj.limit
            //首次不执行
            if (!first) {
                //do something
                query()
            }
        }
    });
}

var getRateConfig = function () {
    $("#stateRate").val("")
    initWebService(webserviceCMD.CMD_GET_STATE_RATE, null, handleStatusRate)
}
var handleStatusRate = function (data) {
    $("#stateRate").val(data.set_rate)
}
var setRateConfig = function () {
    let rate = $("#stateRate").val();
    if (!/^[1-9]\d*$/.test(rate)) {
        layer.msg('输入频率值格式不规范', {icon: 2});
        return
    }
    initWebService(webserviceCMD.CMD_SET_STATE_RATE, {set_rate: rate}, getRateConfig)
}
// 转为时间戳（1970年开始）
var replaceTime = function (data) {
    const date = new Date(data.replace(/-/g, "/"))
    console.log('date: ', date)
    const year = date.getFullYear()// getFullYear() 方法可返回一个表示年份的 4 位数字。
    const month = date.getMonth() + 1// getMonth() 方法可返回表示月份的数字。返回值是 0（一月） 到 11（十二月）之间的一个整数。
    const day = date.getDate()// getDate() 方法可返回月份的某一天。
    const hours = date.getHours()// etHours() 方法可返回时间的小时字段
    const minute = date.getMinutes()// getMinutes() 方法可返回时间的分钟字段。
    const second = date.getSeconds()// getSeconds() 方法可返回时间的秒。返回值是 0 ~ 59 之间的一个整数。
    return setTime(year, month, day, hours, minute, second)
}
var setTime = function (year, month, day, hour, minute, second) {
    let i
    let days
    // let error_cnt = 1200
    days = day - 1
    for (i = 1970; i < year; i++) {
        if (year_is_heap(i)) {
            days += 366
        } else {
            days += 365
        }
    }
    const m_days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    for (i = 1; i < month; i++) {
        if (i === 2) {
            if (year_is_heap(year)) {
                days += 29
            } else {
                days += 28
            }
        } else {
            days += m_days_in_month[i - 1]
        }
    }

    return parseInt(days * 86400 + hour * 3600 + minute * 60 + second)
}
var year_is_heap = function (year) {
    if ((((year % 4) === 0) && ((year % 100) !== 0)) || ((year % 400) === 0)) {
        return 1
    } else {
        return 0
    }
}

var getStatusAnalysis = function () {
    let params = {}
    let date_input = $('#date_inputAnalysis').val();//2021-12-15 00:00:00 - 2022-01-15 00:00:00
    if (date_input) {
        let start = date_input.substring(0, 19)
        let end = date_input.substring(22)
        params.begin_time = replaceTime(start) * 1000;
        params.end_time = replaceTime(end) * 1000;
    }
    let index = layer.load()
    initWebService(webserviceCMD.CMD_GET_THRD_STATE_HISTORY, params, statusAnalysis, index)
}
var statusAnalysis = function (data, loadIndex) {
    layer.close(loadIndex)
    document.getElementById('err_code').style.display = 'none'
    $("#err_code_time").html('')
    const stat = {}
    const obj = {
        color: '',
        desc: '',
        info: []
    }
    for (let i = 0; i < threadArr.length; i++) {
        stat[threadArr[i]] = {
            name: langMessage.systemModuleStatus.text[threadArr[i] + '_'],
            moduleStatus_unactive: JSON.parse(JSON.stringify(obj)),
            moduleStatus_ready: JSON.parse(JSON.stringify(obj)),
            moduleStatus_success: JSON.parse(JSON.stringify(obj)),
            moduleStatus_sleep: JSON.parse(JSON.stringify(obj)),
            moduleStatus_error: JSON.parse(JSON.stringify(obj)),
        }
    }
    for (let i = 0; i < data.data.length; i++) {
        for (let key in  data.data[i]) {
            if (threadArr.indexOf(key) > -1) {
                let obj = {
                    name: key,
                    state: data.data[i][key].state,
                    mask: data.data[i][key].mask
                    // state: 1073741825,
                    // mask: 1073741825
                }
                let ret = handleOper(obj)
                stat[key][ret.text].color = ret.color
                stat[key][ret.text].desc = langMessage.systemModuleStatus.text[ret.text]
                stat[key][ret.text].info.push({
                    state_16: ret.state_16,
                    mask_16: ret.mask_16,
                    errorCodeArr: ret.errorCodeArr,
                    time: data.data[i].time
                })
            }
        }
    }
    let xData = []
    // let yData = []
    let errData = []
    let timeArr = []
    for (let key in stat) {
        if (stat[key].moduleStatus_error.info.length > 0) {
            errData.push({value: stat[key].moduleStatus_error.info.length, name: stat[key].name})
            xData.push(stat[key].name)
            // yData.push(stat[key].moduleStatus_error.info.length)

            for (let i = 0; i < stat[key].moduleStatus_error.info.length; i++) {
                timeArr.push(stat[key].moduleStatus_error.info[i].time)
            }
        }
    }
    timeArr = ascSort(timeArr)
    $("#err_time").html('')
    if (timeArr.length) {
        let start = getFormatTime1(timeArr[0])
        let end = getFormatTime1(timeArr[timeArr.length - 1])
        $("#err_time").html(start + ' -- ' + end)
    }

    let option = {
        color: 'rgb(84,112,198)',
        tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} 次'
        },
        xAxis: {
            type: 'category',
            data: xData,
            name: '线程名称',
            axisTick: {
                alignWithLabel: true
            }
        },
        yAxis: {
            type: 'value',
            name: '错误次数',
            minInterval: 1
        },
        series: [
            {
                data: errData,
                type: 'bar',
                // 显示数值
                // itemStyle: {
                //     normal: {
                //         label: {
                //             show: true,
                //             formatter: '{b} : {c} 次 '
                //         },
                //         labelLine: {show: true}
                //     }
                // }
            }
        ]
    };
    document.getElementById('err').style.height = '500px'
    let myChart = echarts.init(document.getElementById('err'));
    myChart.setOption(option);
    window.addEventListener('resize', () => {
        myChart.resize()
    })
    myChart.off('click')
    myChart.on('click', function (params) {
        leftEchartsClick(params, errData, myChart, option, stat)
    })
}
/**
 * @description 单击左边柱状图，右边展示具体错误码的柱状图；双击左边柱状图，弹出折线图，统计频率
 * @param params 图表的某一部分数据
 * @param chartsData 图表的data
 * @param myChart 图表
 * @param option 图表
 * @param stat 状态解析出的数据
 * */
var leftEchartsClick = function (params, chartsData, myChart, option, stat) {
    document.getElementById('err_code').style.display = 'block'
    //点击时时间戳
    let curT = new Date().getTime()
    //上一次时间戳
    let lastT = lastClickTime
    //对上一次时间戳重新赋值
    lastClickTime = curT
    //做差
    let diff = curT - lastT
    // console.log(params)
    threadName = params.name
    for (let i = 0; i < chartsData.length; i++) {
        if (chartsData[i].name === threadName) {
            chartsData[i].itemStyle = {
                color: '#5FB878'
            }
        } else {
            chartsData[i].itemStyle = {
                color: 'rgb(84,112,198)'
            }
        }
    }
    myChart.setOption(option);
    for (let key in stat) {
        if (stat[key].name === threadName) {
            info = stat[key].moduleStatus_error.info
            if (diff < 500) {
                showThreadEchartsLine()
            }
            let errCode = {}
            let timeArr = []
            for (let i = 0; i < info.length; i++) {
                for (let j = 0; j < info[i].errorCodeArr.length; j++) {
                    let key = info[i].errorCodeArr[j]
                    let name = key.split('：')[1]
                    errCode[name] = errCode[name] ? errCode[name] + 1 : 1
                    timeArr.push(info[i].time)
                }
            }
            timeArr = ascSort(timeArr)
            $("#err_code_time").html('')
            if (timeArr.length) {
                let start = getFormatTime1(timeArr[0])
                let end = getFormatTime1(timeArr[timeArr.length - 1])
                $("#err_code_time").html(start + ' -- ' + end)
            }
            let xdata = [], errCodeData = []
            for (let key in errCode) {
                xdata.push(key)
                errCodeData.push({value: errCode[key], name: key})
            }
            let option1 = {
                color: 'rgb(84,112,198)',
                tooltip: {
                    trigger: 'item',
                    formatter: '{b} : {c} 次'
                },
                xAxis: {
                    type: 'category',
                    data: xdata,
                    name: '错误码',
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '错误次数',
                    minInterval: 1
                },
                series: [
                    {
                        data: errCodeData,
                        type: 'bar',
                        // 显示数值
                        // itemStyle: {
                        //     normal: {
                        //         label: {
                        //             show: true,
                        //             formatter: '{b} : {c} 次 '
                        //         },
                        //         labelLine: {show: true}
                        //     }
                        // }
                    }
                ]
            };
            document.getElementById('err_code').style.height = '500px'
            let myChart1 = echarts.init(document.getElementById('err_code'));
            myChart1.setOption(option1);
            window.addEventListener('resize', () => {
                myChart1.resize()
            })
            myChart1.off('click')
            myChart1.on('click', function (params) {
                rightEchartsClick(params, errCodeData, myChart1, option1)
            })
            break
        }
    }
}
/**
 * 弹框折线图（每小时/每天 错误频率）
 */
var showThreadEchartsLine = function () {
    layer.open({
        title: '频率统计',
        type: 1,
        area: ['90%', '650px'],
        offset: ['50px', '5%'],
        success: function (layero, index) {
            showThreadEchartsLine1()
        },
        end: function (layero, index) {
        },
        content: $('#brokenLine')
    })
}
var showThreadEchartsLine1 = function () {
    let xData = [], seriesData = []
    for (let i = 0; i < info.length; i++) {
        let time = getFormatTime(new Date(info[i].time))
        let flag = true
        for (let i = 0; i < seriesData.length; i++) {
            if (seriesData[i].name === time) {
                flag = false
                seriesData[i].value = seriesData[i].value + 1
                break
            }
        }
        if (flag) {
            xData.push(time)
            seriesData.push({
                name: time,
                value: 1
            })
        }
    }
    let option = {
        tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} 次'
        },
        xAxis: {
            type: 'category',
            name: '时间段',
            data: xData
        },
        yAxis: {
            type: 'value',
            name: '错误次数',
            minInterval: 1
        },
        series: [
            {
                data: seriesData,
                type: 'line',
                smooth: true
            }
        ]
    };
    document.getElementById('brokenLineEcharts').style.height = '500px'
    let myChart1 = echarts.init(document.getElementById('brokenLineEcharts'));
    myChart1.setOption(option);
    window.addEventListener('resize', () => {
        myChart1.resize()
    })
    myChart1.off('click')
    myChart1.on('click', function (params) {
        threadLineEchartsClick(params)
    })
    form.on('radio(brokenLineRadio)', function (data) {
        brokenLineRadio = data.value
        showThreadEchartsLine1()
    })
}
/**
 *
 * */
var threadLineEchartsClick = function (params) {
    let tableData = []
    for (let i = 0; i < info.length; i++) {
        let time = getFormatTime(new Date(info[i].time))
        if (time === params.name) {
            tableData.push(info[i])
        }
    }
    showModuleDetail(tableData)
}
/**
 * @description 单击右边柱状图，弹出折线图，统计频率
 * @param params 图表的某一部分数据
 * @param chartsData 图表的data
 * @param myChart 图表
 * @param option 图表
 * */
var rightEchartsClick = function (params, chartsData, myChart, option) {
    errCodeName = params.name
    for (let i = 0; i < chartsData.length; i++) {
        if (chartsData[i].name === errCodeName) {
            chartsData[i].itemStyle = {
                color: '#5FB878'
            }
        } else {
            chartsData[i].itemStyle = {
                color: 'rgb(84,112,198)'
            }
        }
    }
    myChart.setOption(option);
    layer.open({
        title: '频率统计',
        type: 1,
        area: ['90%', '650px'],
        offset: ['50px', '5%'],
        success: function (layero, index) {
            showErrCodeEchartsLine()
        },
        end: function (layero, index) {
        },
        content: $('#brokenLine')
    })
}
var showErrCodeEchartsLine = function () {
    let xData = [], seriesData = []
    for (let i = 0; i < info.length; i++) {
        for (let j = 0; j < info[i].errorCodeArr.length; j++) {
            let key = info[i].errorCodeArr[j]
            let name = key.split('：')[1]
            if (errCodeName === name) {
                let time = getFormatTime(new Date(info[i].time))
                let flag = true
                for (let i = 0; i < seriesData.length; i++) {
                    if (seriesData[i].name === time) {
                        flag = false
                        seriesData[i].value = seriesData[i].value + 1
                        break
                    }
                }
                if (flag) {
                    xData.push(time)
                    seriesData.push({
                        name: time,
                        value: 1
                    })
                }
                break
            }
        }
    }

    let option1 = {
        tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} 次'
        },
        xAxis: {
            type: 'category',
            name: '时间段',
            data: xData
        },
        yAxis: {
            type: 'value',
            name: '错误次数',
            minInterval: 1
        },
        series: [
            {
                data: seriesData,
                type: 'line',
                smooth: true
            }
        ]
    };
    document.getElementById('brokenLineEcharts').style.height = '500px'
    let myChart1 = echarts.init(document.getElementById('brokenLineEcharts'));
    myChart1.setOption(option1);
    window.addEventListener('resize', () => {
        myChart1.resize()
    })
    myChart1.off('click')
    myChart1.on('click', function (params) {
        errCodeLineEchartsClick(params)
    })
    form.on('radio(brokenLineRadio)', function (data) {
        brokenLineRadio = data.value
        showErrCodeEchartsLine()
    })
}
var errCodeLineEchartsClick = function (params) {
    let tableData = []
    for (let i = 0; i < info.length; i++) {
        for (let j = 0; j < info[i].errorCodeArr.length; j++) {
            let key = info[i].errorCodeArr[j]
            let name = key.split('：')[1]
            if (errCodeName === name) {
                let time = getFormatTime(new Date(info[i].time))
                if (time === params.name) {
                    tableData.push(info[i])
                }
                break
            }
        }
    }
    showModuleDetail(tableData, errCodeName)
}
/**
 * 弹框表格
 * */
var showModuleDetail = function (data, type) {
    // let obj = data[0]
    // for (let i = 0; i < 500; i++) {
    //     data.push(obj)
    // }
    layer.open({
        title: '详情',
        type: 1,
        area: ['90%', '570px'],
        offset: ['50px', '5%'],
        success: function (layero, index) {
            table.render({
                elem: '#moduleDetailTable'
                , cols: [[
                    {type: 'numbers', title: '序号', align: 'center', width: 80},//序号列
                    {
                        field: 'time',
                        title: '时间',
                        align: 'center',
                        width: 180,
                        templet: function (d1) {
                            // 返回的时间戳为0时区的
                            return getFormatTime1(d1.time)
                        }
                    },
                    {
                        field: 'state_16',
                        title: 'state',
                        width: 180,
                        align: 'center'
                    },
                    {
                        field: 'mask_16',
                        title: 'mask',
                        width: 180,
                        align: 'center'
                    },
                    {
                        field: 'errorCodeArr',
                        title: '错误码',
                        align: 'center',
                        templet: function (d1) {
                            let msg = ''
                            for (let i = 0; i < d1.errorCodeArr.length; i++) {
                                msg = msg === '' ? '' : (msg + '，')
                                let span = ''
                                let name = d1.errorCodeArr[i].split('：')[1]
                                if (type && name === type) {
                                    span = "<span style='color: #FF5722;'>" + d1.errorCodeArr[i] + "</span>";
                                } else {
                                    span = "<span>" + d1.errorCodeArr[i] + "</span>";
                                }
                                msg = msg + span
                            }
                            return msg
                        }
                    }
                ]]
                , data: data
                , page: true
            });
        },
        end: function (layero, index) {
        },
        content: $('#moduleDetail')
    });
}
var getFormatTime = function (data) {
    try {
        let isoTime = new Date(data).toISOString()//2022-03-14T11:01:41.968Z
        let time = isoTime.substring(0, 10)
        if (brokenLineRadio === 'hour') {
            time = isoTime.substring(0, 10) + " " + isoTime.substring(11, 13)
        }
        return time
    } catch (e) {
        return ''
    }
}
var getFormatTime1 = function (data) {
    try {
        let isoTime = new Date(data).toISOString();//2022-03-14T11:01:41.968Z
        let formatTime = isoTime.substring(0, 10) + " " + isoTime.substring(11, 19)
        return formatTime
    } catch (e) {
        return ''
    }
}
