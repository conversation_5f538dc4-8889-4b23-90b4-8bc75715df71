var browseFolder = function (path) {
  try {
    var Message = "\u8bf7\u9009\u62e9\u6587\u4ef6\u5939"; //选择框提示信息
    var Shell = new ActiveXObject("Shell.Application");
    var Folder = Shell.BrowseForFolder(0, Message, 64, 17); //起始目录为：我的电脑
    //var Folder = Shell.BrowseForFolder(0, Message, 0); //起始目录为：桌面
    if (Folder != null) {
      Folder = Folder.items(); // 返回 FolderItems 对象
      Folder = Folder.item(); // 返回 Folderitem 对象
      Folder = Folder.Path; // 返回路径
      if (Folder.charAt(Folder.length - 1) != "\\") {
        Folder = Folder + "\\";
      }
      document.getElementById(path).value = Folder;
      return Folder;
    }
  }
  catch (e) {
    console.log(e.message);
    layer.msg(langMessage.common.browserNonsupport, {icon: 2});
  }
};
var initPath = function () {
  let path = initPathConfig();
  if (path) {
    $("#img").val(path.img);
    $("#record").val(path.record);
    $("#log").val(path.log);
  }
};
var form, element, layer;
$(document).ready(function () {
  layui.use(['form', 'element', 'layer'], function () {
    form = layui.form, element = layui.element, layer = layui.layer;
    let result = initialize_ocx('pathContainer');
    if (!result.result) {
      layer.open({
        title: langMessage.common.error
        , shade: 0.8
        , btn: false
        , content: result.msg
        , anim: 6
      });
    } else {
      initPath();
    }
  });
  $("#imgBtn").off("click").on("click", function () {
    // browseFolder('img');
    let status = ocxPopOut();
    if (status) {
      if (status === 'error') {
        layer.msg(langMessage.ocxGlobal.ocxError.SELECT_FAIL, {icon: 2});
        return
      }
      let path = {
        img: status,
        record: "",
        log: ""
      };
      if (savePathConfig(path) === 'TRUE') {
        layer.msg(langMessage.common.saveSuc, {icon: 1});
        initPath()
      } else {
        layer.msg(langMessage.common.saveFail, {icon: 2});
      }
    }
  });
  $("#recordBtn").off("click").on("click", function () {
    layer.msg(langMessage.common.undeveloped, {icon: 2});
    // browseFolder('record');
  });
  $("#logBtn").off("click").on("click", function () {
    layer.msg(langMessage.common.undeveloped, {icon: 2});
    // browseFolder('log');
  });
  $("#defaultConfig").off("click").on("click", function () {
    let ip = location.hostname;
    $("#img").val('C:\\TSPlayerFiles\\' + ip + '\\');
    //$("#record").val('C:\\Users\\<USER>\\WebStorage');
    //$("#log").val('C:\\Users\\<USER>\\WebStorage');
    saveConfig();
  });
  var saveConfig = function () {
    let img = $("#img").val().trim();
    let record = $("#record").val().trim();
    let log = $("#log").val().trim();
    if (!img /*|| !record || !log*/) {
      layer.msg(langMessage.setting.errorMsg.LOCAL_ERROR, {icon: 2});
      return
    }
    let path = {
      img: img,
      record: record,
      log: log
    };
    if (savePathConfig(path) === 'TRUE') {
      layer.msg(langMessage.common.saveSuc, {icon: 1});
    } else {
      layer.msg(langMessage.common.saveFail, {icon: 2});
    }
  };
});

