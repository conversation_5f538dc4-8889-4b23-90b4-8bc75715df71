var element, layer, form;
$(document).ready(function () {
    layui.use(['element', 'layer', 'form',], function () {
        element = layui.element, layer = layui.layer, form = layui.form;
        form.on('switch(switchBackup)', function (data) {
            // console.log(data.elem); //得到checkbox原始DOM对象
            // console.log(data.elem.checked); //开关是否开启，true或者false
            // console.log(data.value); //开关value值，也可以通过data.elem.value得到
            // console.log(data.othis); //得到美化后的DOM对象
            switchListener(data.elem.checked);
        });
        getBackup()
    })
});
var switchListener = function (checked) {
    let flag = 0
    if (checked) {
        flag = 1
    }
    initWebService(webserviceCMD.CMD_SET_SD_BACKUP, {sd_backup: flag}, getBackup)
};
var getBackup = function () {
    initWebService(webserviceCMD.CMD_GET_SD_BACKUP, null, handleBackup)
}
var handleBackup = function (data) {
    let flag = false
    if (data.sd_backup === 1) {
        flag = true
    }
    $('#switchBackupID').prop("checked", flag);
    form.render('checkbox')
}