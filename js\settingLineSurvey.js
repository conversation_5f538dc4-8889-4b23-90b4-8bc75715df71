/**
 * Create by Chelly
 * 2019/5/20
 */

var element, form, layer, colorpicker, laydate, layerIndex, slider
//JavaScript代码区域
$(document).ready(function () {
    // registerMyTab();
    let lineStartNum = 128;
    layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate', 'slider'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, colorpicker = layui.colorpicker,
            laydate = layui.laydate, slider = layui.slider;
        $(".input-number").attr({
            disabled: 'disabled'
        });
        for (let i = 1; i < 6; i++) {
            $("#peopleTips" + i).off("mouseover ").on("mouseover ", function () {
                showTips("peopleTips" + i, langMessage.setting.tipsMsg.people);
            });
            $("#floorTips" + i).off("mouseover ").on("mouseover ", function () {
                showTips("floorTips" + i, langMessage.setting.tipsMsg.floor);
            });
        }

        function showTips(id, msg) {
            layer.tips(msg, "#" + id + "", {
                tips: [4, "#000000"]
            });
        }

        //执行一个laydate实例
        laydate.render({
            elem: '#restartTime' //指定元素
        });
        for (let i = 1; i < 6; i++) {
            for (let j = 1; j < 4; j++) {
                laydate.render({
                    elem: '#line_' + i + "_" + j
                    , type: 'time'
                    , range: true
                });
            }

        }
        // 车道从1开始，页面显示从1开始，值仍然从0开始
        for (let i = 0; i < lineStartNum; i++) {
            $("#lineStartNumber").append("<option value='" + i + "'>" + (i + 1) + "</option>")
            form.on('select(type_' + i + ')', function (data) {
                drawPath("lineSurveyCan", 'drawLineSurvey')
                form.render();
            });
            if (i > 0) {
                form.on('select(direction_' + i + ')', function (data) {
                    drawPath("lineSurveyCan", 'drawLineSurvey')
                    form.render();
                });
            }
        }
        form.on('select(lineStartNumber)', function (data) {
            drawPath('lineSurveyCan', 'drawLineSurvey')
        });
        // 去掉
        // form.on('select(lineStart)', function (data) {
        //     drawPath('lineSurveyCan', 'drawLineSurvey')
        // });
        for (let x = 1; x < 6; x++) {
            form.on('select(special_' + x + ')', function (data) {
                // 公交车道和单行道，下面的时间段才起效
                if (parseInt(data.value) === 4 || parseInt(data.value) === 64) {
                    let num = (data.elem.id).substr(8, 1);
                    $($("#timeTable .timeNum")[num]).removeClass('dis');
                    $(".time-" + (num)).removeClass("dis");
                    $(".time-" + num).removeClass('common-none');
                    $(".time-" + num + " input").removeAttr('disabled')
                } else {
                    let num = (data.elem.id).substr(8, 1);
                    $($("#timeTable .timeNum")[num]).addClass('dis');
                    $(".time-" + (num)).addClass("dis");
                    $(".time-" + num).addClass('common-none');
                    $("#timeTable .time-" + num + " input").attr('disabled', 'disabled')
                }
                saveMyLineConfig()
                drawPath("lineSurveyCan", 'drawLineSurvey')
            });
            form.on('select(define_' + x + ')', function (data) {
                let num = (data.elem.id).substr(7, 1);
                if (parseInt(data.value) & 1) {
                    $("#leftWait" + (num)).removeClass("dis").removeAttr('disabled');
                } else {
                    $("#leftWait" + (num)).addClass("dis").attr('disabled', 'disabled').val(0);
                }
                saveMyLineConfig()
                drawPath("lineSurveyCan", 'drawLineSurvey')
                form.render();
            });
        }
        form.on('select(lineNumber)', function (data) {
            let number = data.value;
            changeLineNumber(number);
        });
        form.on('select(redStopType)', function (data) {
            let optionsNum = data.value;
            $("#redStop").remove();
            $("#redStopBtn").removeClass("layui-btn-normal");
            $(".redStop-signal").map(i => {
                $(".redStop-signal")[i].checked = false;
            });
            $(".control-point .redStop-point").find(".input-number").val(0);
            $(".control-point .redStop-point").find(".input-number").val(0);
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            drawPath(drawCanvas, drawContainer);
            if (optionsNum === '0') {
                $("#redStopBtn").attr('onclick', "commonLine('redStop',1,false)");
                $(".redStop-group1").removeClass("common-none").find(".input-number").attr({disabled: "disabled"});
                $(".redStop-group2").addClass("common-none").find(".input-number").attr({disabled: "disabled"});
                $(".redStop-group3").addClass("common-none").find(".input-number").attr({disabled: "disabled"});
                $(".redStop-group4").addClass("common-none").find(".input-number").attr({disabled: "disabled"});
                $(".redStop-group5").addClass("common-none").find(".input-number").attr({disabled: "disabled"})
            } else if (optionsNum === '1') {
                let groupNum = parseInt($("#lineNumber")[0].value);
                groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
                $("#redStopBtn").attr('onclick', "commonLine('redStop'," + parseInt(groupNum - 1) + ",false)");
                for (let i = 0; i < 5; i++) {
                    if (i < (groupNum - 1)) {
                        $(".redStop-group" + (i + 1)).removeClass("common-none").find(".input-number").attr({disabled: "disabled"})
                    } else {
                        $(".redStop-group" + (i + 1)).addClass("common-none").find(".input-number").attr({disabled: "disabled"})
                    }
                }
            }
            form.render();
        });
        form.on('select(peopleLineNumber)', function (data) {
            let optionsNum = data.value;
            $(".people1-group").addClass("common-none");
            $(".people1-group" + optionsNum).removeClass("common-none")
            $(".people2-group").addClass("common-none");
            $(".people2-group" + optionsNum).removeClass("common-none")
            $(".people3-group").addClass("common-none");
            $(".people3-group" + optionsNum).removeClass("common-none")
            $(".people4-group").addClass("common-none");
            $(".people4-group" + optionsNum).removeClass("common-none")
            $(".people5-group").addClass("common-none");
            $(".people5-group" + optionsNum).removeClass("common-none")
        });
        form.on('select(floorLineNumber)', function (data) {
            let optionsNum = data.value;
            $(".floor1-group").addClass("common-none");
            $(".floor1-group" + optionsNum).removeClass("common-none")
            $(".floor2-group").addClass("common-none");
            $(".floor2-group" + optionsNum).removeClass("common-none")
            $(".floor3-group").addClass("common-none");
            $(".floor3-group" + optionsNum).removeClass("common-none")
            $(".floor4-group").addClass("common-none");
            $(".floor4-group" + optionsNum).removeClass("common-none")
            $(".floor5-group").addClass("common-none");
            $(".floor5-group" + optionsNum).removeClass("common-none")
        });
        form.on('checkbox(redStopCheck)', function (data) {
            let arr = [];
            $(".redStop-checkbox input:checkbox.redStop-signal").each(function (i) {
                arr[i] = (this.checked === true ? 1 : 0);
            });
            for (let i = 0; i < arr.length; i++) {
                if (arr[i] === 0) {
                    $("#redStop .redStop-can" + (i + 1)).css({
                        display: 'none'
                    });
                    $(".redStop-point input").attr({
                        disabled: 'disabled'
                    })
                    // for (let j = 0; j < 2; j++) {
                    //     let num = 2 * (i) + j
                    //     $($(".redStop-point")[num]).find(".input-number")[0].value = 0;
                    //     $($(".redStop-point")[num]).find(".input-number")[1].value = 0;
                    // }

                } else {
                    $("#redStop .redStop-can" + (i + 1)).css({
                        display: 'block'
                    });
                    $(".redStop-point input").removeAttr('disabled')
                }
                drawPath("lineSurveyCan", 'drawLineSurvey')
            }
        });


        let redStopType = getSe("redStopType");
        if (redStopType === 0) {
            $("#redStopBtn").attr('onclick', "commonLine('redStop',1,false)");
        } else {
            let lineType = getSe('lineType');
            let groupNum = lineType.lineNum === 0 ? lineType.lineNum : (lineType.lineNum - 1)
            $("#redStopBtn").attr('onclick', "commonLine('redStop'," + parseInt(groupNum) + ",false)");
        }


        //survey################################################
        let checkArray = ['turnLeft', 'goStraight', 'turnRight', 'cartTurnRight', 'upCalibration', 'downCalibration'];
        for (let i = 0; i < checkArray.length; i++) {
            let nowId = checkArray[i];
            let type = nowId + 'Check';
            let nowBtn = $('#' + nowId + 'Btn');
            form.on('checkbox(' + type + ')', function (data) {
                if (data.elem.checked === false) {
                    $("#" + nowId).css({
                        display: 'none'
                    });
                    // $("#"+checkArray[i]+" ."+checkArray[i]+"-can").css({
                    //     display: 'none'
                    // });
                    // for (let j = 0; j < 2; j++) {
                    //     $($("."+checkArray[i]+"-point")[j]).find(".input-number")[0].value = 0;
                    //     $($("."+checkArray[i]+"-point")[j]).find(".input-number")[1].value = 0;
                    // }
                    $("." + nowId + '-point input').attr({
                        disabled: 'disabled'
                    });
                    $("#" + nowId + "Zone").css({
                        display: 'none'
                    });
                    nowBtn.addClass('layui-btn-disabled');
                    nowBtn.attr("disabled", 'disabled');
                    $("." + nowId + '-queueLength button').attr("disabled", 'disabled');
                } else {
                    $("." + nowId + '-point input').removeAttr('disabled');
                    nowBtn.removeClass('layui-btn-disabled');
                    nowBtn.removeAttr("disabled");
                    $("." + nowId + '-queueLength button').removeAttr("disabled");
                    // $("#"+checkArray[i]+" ."+checkArray[i]+"-can").css({
                    //     display: 'block'
                    // });
                    $("#" + nowId).css({
                        display: 'block'
                    });
                    $("#" + nowId + "Zone").css({
                        display: 'block'
                    });
                }
                drawPath("lineSurveyCan", 'drawLineSurvey')
            });
        }

        //渲染
        var ins1 = slider.render({
            elem: '#slideMinWidth'  //绑定元素
            , input: true //输入框
            , max: 200
        });
        var ins2 = slider.render({
            elem: '#slideMaxWidth'  //绑定元素
            , input: true //输入框
            , max: 300
        });
        ins1.setValue(getSe('plateMin'));
        ins2.setValue(getSe('plateMax'));
        disableColor(['lineColor', 'floor1Color', 'floor2Color', 'floor3Color', 'floor4Color', 'floor5Color', 'people1Color', 'people2Color', 'people3Color', 'people4Color', 'people5Color', 'redStopColor', 'surveyColor', 'noParkingColor', 'aroundColor', 'faceColor', 'turnLeftColor', 'goStraightColor', 'turnRightColor', 'cartTurnRightColor', 'upCalibrationColor', 'downCalibrationColor'])
        let globalInfo = checkGlobal();
        setSize(globalInfo.w, globalInfo.h);
        initPicture('drawLineSurvey');
        //survey################################################
        form.render();
        element.render();
    });
});
var show = function () {
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let dataArray = getSe("projectConfig").settingShow[drawContainer].show;
    showDouble(dataArray, drawContainer);
    let detects = getSe('detects');
    if (detects) {
        if (detects['detect-signal']) {
            // showCheckboxValue('detect-signal', detects['detect-signal']);
            let detectValue = detects['detect-signal'];
            //检测行人使能
            if (detectValue & '0x8000') {
                for (let i = 1; i < 6; i++) {
                    $("#people" + i + "Btn").removeClass('layui-btn-disabled');
                    $("#people" + i + "Btn").removeAttr('disabled');
                }
                $(".my-colla-tab-title>li:nth-child(3)").removeClass('common-none');
            } else {
                for (let i = 1; i < 6; i++) {
                    $("#people" + i + "Btn").addClass('layui-btn-disabled');
                    $("#people" + i + "Btn").attr('disabled', 'disabled');
                    $("#people" + i).css({
                        display: 'none'
                    });
                    $("#people" + i + "Zone").css({
                        display: 'none'
                    });
                }
                $(".my-colla-tab-title>li:nth-child(3)").addClass('common-none');
            }
            //检测地感线圈使能
            if (detectValue & '0x40000') {
                for (let i = 1; i < 6; i++) {
                    $("#floor" + i + "Btn").removeClass('layui-btn-disabled');
                    $("#floor" + i + "Btn").removeAttr('disabled');
                }
                $(".my-colla-tab-title>li:nth-child(2)").removeClass('common-none');
            } else {
                for (let i = 1; i < 6; i++) {
                    $("#floor" + i + "Btn").addClass('layui-btn-disabled');
                    $("#floor" + i + "Btn").attr('disabled', 'disabled');
                    $("#floor" + i).css({
                        display: 'none'
                    });
                    $("#floor" + i + "Zone").css({
                        display: 'none'
                    });
                }
                $(".my-colla-tab-title>li:nth-child(2)").addClass('common-none');
            }
        }
    }

    let lineType = getSe('lineType');
    if (lineType) {
        $("#lineNumber").val(lineType.lineNum === 0 ? lineType.lineNum : (lineType.lineNum - 1));
        $("#lineStartNumber").val(lineType.lineStartNum);
        // $("#lineStart").val(lineType.lineStart);
        $("#lineStart").val(0);
        let lines = lineType.lines;
        for (let i = 0; i < lineType.lineNum; i++) {
            $($(".my-colla-item")[i]).removeClass('common-none');
            $($(".tab-item")[i]).removeClass('common-none1');
            $("#type_" + i).val(lines[i].type);
            if (i >= 1) {
                $("#direction_" + i).val(lines[i].direction);
                $("#define_" + i).vals(lines[i].define);
                $("#special_" + i).vals(lines[i].special);
                if (parseInt(lines[i].define) & 1) {
                    $("#leftWait" + i).removeClass("dis").removeAttr("disabled");
                } else {
                    $("#leftWait" + i).addClass("dis").attr("disabled", "disabled");
                }
                $("#leftWait" + i).vals(lines[i].leftWait);
                let index = i + 1
                index = index === 0 ? index : (index - 1)
                $($("#lineNumber option")[index]).attr("disabled", false);
                if (parseInt(lines[i].special) === 4) {
                    $(".time-" + i).removeClass('common-none');
                    $("#line_" + i + "_1").val(value2Date(lines[i]['time1start'], 1) + ' - ' + value2Date(lines[i]['time1end'], 1));
                    $("#line_" + i + "_2").val(value2Date(lines[i]['time2start'], 1) + ' - ' + value2Date(lines[i]['time2end'], 1));
                    $("#line_" + i + "_3").val(value2Date(lines[i]['time3start'], 1) + ' - ' + value2Date(lines[i]['time3end'], 1));
                }

            }
        }
    }

    let redStopType = getSe('redStopType');
    let checkRed = getSe('redStop');
    if (checkRed) {
        checkRed = checkRed.use;
        if (redStopType == 1) {
            // $("#redStopBtn").removeClass('layui-btn-disabled');
            // $("#redStopBtn").removeAttr('disabled');
            $(checkRed).each(function (i) {
                if (checkRed[i] === 1) {
                    $(".redStop-signal")[i].checked = true;
                } else {
                    $("#redStop .redStop-can" + (i + 1)).css({
                        display: 'none'
                    })
                }
            });
            for (let j = 0; j < 5; j++) {
                if (j < (lineType.lineNum - 1)) {
                    $(".redStop-group" + (j + 1)).removeClass('common-none');
                }
            }
        } else {
            if (checkRed === 1) {
                $(".redStop-signal")[0].checked = true;
            }
            //     $("#redStopBtn").removeClass('layui-btn-disabled');
            //     $("#redStopBtn").removeAttr('disabled');
            // } else {
            //     $("#redStopBtn").addClass('layui-btn-disabled');
            //     $("#redStopBtn").attr('disabled', 'disabled');
            // }
        }
        if (redStopType) {
            $("#redStopType").val(redStopType)
        }
    }
    //survey##################################
    let noParkingType = getSe('noParkingType');
    if (noParkingType) {
        $("#noParkingType").val(noParkingType)
    }
    leftStraightRightCheck();
    //survey##################################
    drawPath(drawCanvas, drawContainer);
    form.render();
};
var saveMyLineConfig = function () {
    let lineType = {};
    let groupNum = parseInt($("#lineNumber option:selected").val())
    groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
    lineType.lineNum = groupNum
    // lineType.lineStart = parseInt($("#lineStart option:selected").val());
    lineType.lineStart = 0;
    lineType.lineStartNum = parseInt($("#lineStartNumber option:selected").val());
    let lines = [];
    for (let i = 0; i < parseInt(lineType.lineNum); i++) { // parseInt(lineType.lineNum) + 1
        let line = {};
        line.type = parseInt($("#type_" + i + " option:selected").val());
        if (i >= 1) {
            line.direction = parseInt($("#direction_" + i + " option:selected").val());
            line.define = parseInt($("#define_" + i + " option:selected").val());
            line.special = parseInt($("#special_" + i + " option:selected").val());
            line.leftWait = parseInt($("#leftWait" + i + " option:selected").val());
            // 公交车道和单行道，下面的时间段才起效
            if (parseInt(line.special) === 4 || parseInt(line.special) === 64) {
                let time1 = $("#line_" + i + "_1").val().split(' - ');
                let time2 = $("#line_" + i + "_2").val().split(' - ');
                let time3 = $("#line_" + i + "_3").val().split(' - ');
                if (time1.length === 2) {
                    line.time1start = date2Value(time1[0].length >= 0 ? time1[0] : 0, 1);
                    line.time1end = date2Value(time1[1].length >= 0 ? time1[1] : 0, 1);
                }
                if (time2.length === 2) {
                    line.time2start = date2Value(time2[0].length >= 0 ? time2[0] : 0, 1);
                    line.time2end = date2Value(time2[1].length >= 0 ? time2[1] : 0, 1);
                }
                if (time3.length === 2) {
                    line.time3start = date2Value(time3[0].length >= 0 ? time3[0] : 0, 1);
                    line.time3end = date2Value(time3[1].length >= 0 ? time3[1] : 0, 1);
                }
            }
        }
        lines.push(line)
    }
    lineType.lines = lines;
    setSe('lineType', lineType);
}

var inputAdd = function (type) {
    let value = $("." + type + "-queueLength input").val();
    let numberValue = Number(value) + 1;
    $("." + type + "-queueLength input").val(numberValue);
}
var inputReduce = function (type) {
    let value = $("." + type + "-queueLength input").val();
    let numberValue = Number(value);
    if (numberValue > 0) {
        numberValue--
    }
    $("." + type + "-queueLength input").val(numberValue);
}
