/**
 * Create by Chelly
 * 2019/5/24
 */

var form, colorpicker, laydate, element, layer;
//JavaScript代码区域
$(document).ready(function () {
    layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate'], function () {
        element = layui.element, layer = layui.layer;
        form = layui.form, colorpicker = layui.colorpicker,
            laydate = layui.laydate;
        laydate.render({
            elem: '#StrongStartTime' //指定元素
            , type: 'time'
        });
        laydate.render({
            elem: '#StrongEndTime' //指定元素
            , type: 'time'
        });
        laydate.render({
            elem: '#YellowStartTime' //指定元素
            , type: 'time'
        });
        laydate.render({
            elem: '#YellowEndTime' //指定元素
            , type: 'time'
        });
        let dataArray = ['signalStronger', 'signalYellow'];
        disableFun(dataArray);
        $(".input-number").attr({
            disabled: 'disabled'
        });
        //执行一个laydate实例
        form.on('select(extendSignal)', function (data) {
            let optionsNum = data.value; //得到被选中的值
            if (optionsNum === '0') {
                $("#signalLeftNumber").attr("disabled", 'disabled');
                $("#signalRightNumber").attr("disabled", 'disabled');
                $("#signalStraightNumber").attr("disabled", 'disabled');
                $("#signalPeopleNumber").attr("disabled", 'disabled');
            } else {
                $("#signalLeftNumber").removeAttr("disabled");
                $("#signalRightNumber").removeAttr("disabled");
                $("#signalStraightNumber").removeAttr("disabled");
                $("#signalPeopleNumber").removeAttr("disabled");
            }
            form.render();
        })
        form.on('select(signalNumber)', function (data) {
            // let lineN = document.getElementsByClassName("signal-can").length;
            let optionsNum = data.value; //得到被选中的值
            let lineNum = document.getElementsByClassName("signal-num");
            let disNum = parseInt(6 - $(".signal-num.dis").length);
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            if (optionsNum > disNum) {
                for (let i = 0; i < (optionsNum - disNum); i++) {
                    $(lineNum[(disNum + i)]).removeClass("dis");
                    $("#type_" + (disNum + i + 1) + " .signal-type").attr("disabled", false);
                    // $("#type_" + (disNum + i + 1) + " .signal-type").val(0);
                    $("#type_" + (disNum + i + 1) + " .signal-type").removeClass("dis");
                }
                form.render();
                // let add = 0;
                // for (let i = 0; i < (optionsNum - liNum); i++) {
                //     for (let j = 1; j < 3; j++) {
                //         add++;
                //         let lineDiv = $('<div></div>');
                //         lineDiv.attr({
                //             "class": "mini-box signal-can signal-can" + (liNum + i + 1),
                //             "id": "signal" + (lineN + add)
                //         });
                //         lineDivs.push(lineDiv);
                //     }
                //     form.render('select');
                // }
                // form.render();
                // if (lineN) {
                //     $("#line" + lineN).after(lineDivs);
                //     drawAll('line', drawCanvas, drawContainer);
                // } else {
                //     $("#line").html(lineDivs);
                //     drawAll('line', drawCanvas, drawContainer);
                // }

            } else if (optionsNum < disNum) {
                for (let i = 0; i < disNum - optionsNum; i++) {
                    $(".signalBig-can.fixed-rect-group" + (disNum - i)).remove();
                    $(".signal-can.fixed-rect-group" + (disNum - i)).remove();
                    drawAll('signal', drawCanvas, drawContainer);
                    $(lineNum[(disNum - i - 1)]).addClass("dis");
                    $("#type_" + (disNum - i) + " .signal-type").attr("disabled", true);
                    $("#type_" + (disNum - i) + " .signal-type").addClass("dis");
                    let signalPoint = $(".signal-point");
                    $(signalPoint[(2 * disNum - (2 * (i + 1) - 1))]).find(".input-up").attr("disabled", true);
                    $(signalPoint[(2 * disNum - (2 * (i + 1) - 1))]).find(".input-down").attr("disabled", true);
                    $(signalPoint[(2 * disNum - (2 * (i + 1) - 1))]).find(".input-number")[0].value = 0;
                    $(signalPoint[(2 * disNum - (2 * (i + 1) - 1))]).find(".input-number")[1].value = 0;
                    $(signalPoint[(2 * disNum - (2 * (i + 1)))]).find(".input-up").attr("disabled", true);
                    $(signalPoint[(2 * disNum - (2 * (i + 1)))]).find(".input-down").attr("disabled", true);
                    $(signalPoint[(2 * disNum - (2 * (i + 1)))]).find(".input-number")[0].value = 0;
                    $(signalPoint[(2 * disNum - (2 * (i + 1)))]).find(".input-number")[1].value = 0;
                    form.render('select');
                }
                // for (let i = 0; i < (liNum - optionsNum); i++) {
                //
                // }
                // for (let i = 0; i < (liNum - optionsNum); i++) {
                //     // var che = document.getElementsByClassName("check" + (liNum - i - 1));
                //     // for (let j = 0; j < 4; j++) {
                //     //     $(che[j]).attr("disabled", true);
                //     //     $(che[j]).prop("checked", false);
                //     // }
                //     // $(checkB[(liNum - i - 2)]).addClass("dis");
                //
                // }
            }
            let length = document.getElementsByClassName("line-can").length;
            if (length) {
                for (let i = 0; i < length; i++) {
                    for (let j = 0; j < 4; j++) {
                        $($(".line-point button[type=button]")[4 * i + j]).attr("disabled", false);
                    }
                }
            }
            form.render();
        });
        form.on('checkbox(check1)', function (data) {
            let arr;
            $("input:checkbox[name='check1']").each(function (i) {
                arr[i] = this.value;
            });
            console.log(arr);
            // console.log(data.value); //复选框value值，也可以通过data.elem.value得到
            // console.log(data.othis); //得到美化后的DOM对象
        });
        // colorpicker.render({
        //     elem: '#signalColorEle'
        //     , color: '#4909f9'
        //     , done: function (color) {
        //         $('#signalColor').val(color);
        //     }
        // });

        let globalInfo = checkGlobal();
        setSize(globalInfo.w, globalInfo.h);
        initPicture('drawSignal');
        disableColor(['signalColor']);
        element.render();
        form.render();

        // let globalInfo = checkGlobal();
        setSize(globalInfo.w, globalInfo.h);
        numberRange("signalTime", 0, 99);
        loadLightSelect("signalInfo");
        $(".signal-group .input-up").off("click").on("click", function (e) {
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let currentObj = getSe('signal-bigger');
            let v = $(e.currentTarget).parent().next();
            let num = $(e.currentTarget).parents('.index-point');
            let name = num[0].className.split(" ");
            name = name[0].split("-");
            name = name[0];
            let index = $(e.currentTarget).parents(".layui-colla-content").find("." + name + "-point").index(num);
            v.val(parseInt(v.val()) + 1 > pictureW ? pictureW : parseInt(v.val()) + 1);
            let x = parseInt(($(num).find('.input-number')[0].value));
            let y = parseInt(($(num).find('.input-number')[1].value));
            if ($("#amplifyImg").length) {
                let realX = getContainerPosition(x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW);
                let realY = getContainerPosition(y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH);
                $("#" + name + 'Big' + (index + 1)).css({
                    left: realX,
                    top: realY
                });

            }
            $("#" + name + (index + 1)).css({
                left: x - 5,
                top: y - 5
            });
            drawPath(drawCanvas, drawContainer);
        });
        $(".signal-group .input-down").off("click").on("click", function (e) {
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let currentObj = getSe('signal-bigger');
            let v = $(e.currentTarget).parent().next();
            v.val(parseInt(v.val()) - 1 < 0 ? 0 : parseInt(v.val()) - 1);
            let num = $(e.currentTarget).parents('.index-point');
            let name = num[0].className.split(" ");
            name = name[0].split("-");
            name = name[0];
            let index = $(e.currentTarget).parents(".layui-colla-content").find("." + name + "-point").index(num);
            let x = parseInt(($(num).find('.input-number')[0].value));
            let y = parseInt(($(num).find('.input-number')[1].value));
            if ($("#amplifyImg").length) {
                let realX = getContainerPosition(x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW);
                let realY = getContainerPosition(y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH);
                $("#" + name + 'Big' + (index + 1)).css({
                    left: realX,
                    top: realY
                });

            }
            $("#" + name + (index + 1)).css({
                left: x - 5,
                top: y - 5
            });
            drawPath(drawCanvas, drawContainer);
        });
        $(".signal-group .input-number").off('change').off('focus').on('focus', function (e) {
            $(this).attr("data-oval", $(this).val()); //将当前值存入自定义属性
        }).off('blur').on('blur', function (e) {
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let currentObj = getSe('signal-bigger');
            let oldVal = ($(this).attr("data-oval")); //获取原值
            let newVal = ($(this).val()); //获取当前值
            let v = $(this);
            let value = 0;
            let val = oldVal;
            if (val > 0) {
                value = newVal > pictureW ? pictureW : newVal
            } else if (val < 0) {
                value = newVal < 0 ? 0 : newVal
            } else {
                value = newVal == '' ? 0 : newVal
            }
            v.val(value);
            let num = $(this).parents('.index-point');
            let name = num[0].className.split(" ");
            name = name[0].split("-");
            name = name[0];
            let index = $(this).parents(".control-point").find(".index-point").index(num);
            let x = parseInt(($(num).find('.input-number')[0].value));
            let y = parseInt(($(num).find('.input-number')[1].value));
            if ($("#amplifyImg").length) {
                let realX = getContainerPosition(x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW);
                let realY = getContainerPosition(y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH);
                $("#" + name + 'Big' + (index + 1)).css({
                    left: realX,
                    top: realY
                });

            }
            $("#" + name + (index + 1)).css({
                left: parseInt((x / pictureW) * containerW),
                top: parseInt((y / pictureH) * containerH)
            });
            drawPath(drawCanvas, drawContainer);
        })
    });
});
var show = function () {
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let dataArray = getSe("projectConfig").settingShow[drawContainer].show;
    showDouble(dataArray, drawContainer);
    let signalType = getSe('signalType');
    if (signalType) {
        $("#RS485").val(signalType.RS485);
        if (signalType.extendSignal) {
            $("#extendSignal").val(signalType.extendSignal);
            $("#signalLeftNumber").removeAttr("disabled");
            $("#signalRightNumber").removeAttr("disabled");
            $("#signalStraightNumber").removeAttr("disabled");
            $("#signalPeopleNumber").removeAttr("disabled");
            $("#signalLeftNumber").val(signalType.signalLeftNumber);
            $("#signalRightNumber").val(signalType.signalRightNumber);
            $("#signalStraightNumber").val(signalType.signalStraightNumber);
            $("#signalPeopleNumber").val(signalType.signalPeopleNumber);
        }
        if (signalType.signal) {
            $("#signalJudge")[0].checked = true
        }
        if (signalType.signalStronger) {
            $("#signalStronger")[0].checked = true;
            $("#strongerNum").val(signalType.signalStronger);

            // $(".signalStronger input").removeAttr("disabled");
            // $(".signalStronger button").removeAttr("disabled");
            // $(".signalStronger button").removeClass("layui-btn-disabled");
            $("#StrongStartTime").val(value2Date((signalType.signalStrongStartTime), 1));
            $("#StrongEndTime").val(value2Date((signalType.signalStrongEndTime), 1));
            enableFun("signal-stronger");
        }
        if (signalType.signalYellow === 1) {
            $("#signalYellow")[0].checked = true;
            // $(".signalYellow input").removeAttr("disabled");
            // $(".signalYellow button").removeAttr("disabled");
            // $(".signalYellow button").removeClass("layui-btn-disabled");
            $("#YellowStartTime").val(value2Date((signalType.yellowStartTime), 30));
            $("#YellowEndTime").val(value2Date((signalType.yellowEndTime), 30))
            enableFun("signal-yellow");
        }

        $("#signalTime").val(signalType.signalTime);
        $("#signalIP").val(signalType.signalIP);
        $("#signalNumber").val(signalType.signalNum);
        let signals = signalType.signals;
        for (let i = 0; i < signalType.signalNum; i++) {
            let lineNum = document.getElementsByClassName("signal-num");
            $(lineNum[(i)]).removeClass("dis");
            $("#type_" + (i + 1) + " .signal-type").attr("disabled", false);
            $("#type_" + (i + 1) + " .signal-type").removeClass("dis");
            let type = $("#type_" + (i + 1) + " select");
            $(type[0]).val(signals[i].signalCategory);
            $(type[1]).vals(signals[i].signalDirection);
            $(type[2]).val(signals[i].signalShape);
            $(type[3]).val(signals[i].signalType);
        }
    }

    form.render();
};


/**
 *灯组加载
 * @param id 需要插入进的div id
 */
var loadLightSelect = function (id) {
    var str, lenght, str1, str2, str3, st4, typeLenght1, typeLenght2, typeLenght3, typeLenght4, list, typeList,
        typeList2,
        typeList3, typeList4;
    list = langMessage.settingSignal.lightList;
    typeList = langMessage.settingSignal.signalTypeList[0];
    typeList2 = langMessage.settingSignal.signalTypeList[1];
    typeList3 = langMessage.settingSignal.signalTypeList[2];
    typeList4 = langMessage.settingSignal.signalTypeList[3];

    lenght = list.length;

    for (var i = 0; i < lenght; i++) {
        str = "<tr id='" + list[i].id + "'> <td class='signal-num dis'>" + list[i].name + "</td> <td class='checkInfo'> <div> <select class='" + list[i].id + " signal-type dis' style='width: 50px' disabled='disabled' lay-filter='check1'>";
        typeLenght1 = typeList.length;
        for (var s = 0; s < typeLenght1; s++) {
            str1 = "<option value='" + typeList[s].value + "'>" + typeList[s].name + "</option>"
            str = str + str1;
        }
        ;
        str = str + "</select> </div> </td> <td> <select class='" + list[i].id + " signal-type dis' style='width: 50px' disabled='disabled' lay-filter='check1'>";
        typeLenght2 = typeList2.length;
        for (var p = 0; p < typeLenght2; p++) {
            str2 = "<option value='" + typeList2[p].value + "'>" + typeList2[p].name + "</option>";
            str = str + str2;
        }
        ;
        str = str + "</select> </td> <td> <select class='" + list[i].id + " signal-type dis' style='width: 50px' disabled='disabled' lay-filter='check1'>";
        typeLenght3 = typeList3.length;
        for (var n = 0; n < typeLenght3; n++) {
            str3 = "<option value='" + typeList3[n].value + "'>" + typeList3[n].name + "</option>";
            str = str + str3;
        }
        ;
        str = str + "</select> </td> <td> <select class='" + list[i].id + " signal-type dis' style='width: 50px' disabled='disabled' lay-filter='check1'>";
        typeLenght4 = typeList4.length;
        for (var k = 0; k < typeLenght4; k++) {
            str3 = "<option value='" + typeList4[k].value + "'>" + typeList4[k].name + "</option>";
            str = str + str3;
        }
        ;
        str = str + "</select> </td> </tr>";
        st4 = st4 + str;
    }
    var divA = $("#" + id);
    divA.append(st4);
};


