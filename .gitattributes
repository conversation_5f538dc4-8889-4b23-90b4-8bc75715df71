*  text=auto

###### Documentation ######
*.ipynb           text
*.markdown        text diff=markdown
*.md              text diff=markdown
*.mdwn            text diff=markdown
*.mdown           text diff=markdown
*.mkd             text diff=markdown
*.mkdn            text diff=markdown
*.mdtxt           text
*.mdtext          text
*.txt             text
AUTHORS           text
CHANGELOG         text
CHANGES           text
CONTRIBUTING      text
COPYING           text
copyright         text
*COPYRIGHT*       text
INSTALL           text
license           text
LICENSE           text
NEWS              text
readme            text
*README*          text
TODO              text

##### Sources #####
*.c     text diff=c
*.cc    text diff=cpp
*.cxx   text diff=cpp
*.cpp   text diff=cpp
*.c++   text diff=cpp
*.hpp   text diff=cpp
*.h     text diff=c
*.h++   text diff=cpp
*.hh    text diff=cpp

###### Compiled Object files ######
*.slo   binary
*.lo    binary
*.o     binary
*.obj   binary

###### Precompiled Headers ######
*.gch   binary
*.pch   binary

###### Compiled Dynamic libraries ######
*.so    binary
*.dylib binary
*.dll   binary

##### Compiled Static libraries ######
*.lai   binary
*.la    binary
*.a     binary
*.lib   binary

####### Executables ######
*.exe   binary
*.out   binary
*.app   binary

###### Scripts ######
*.bash     text eol=lf
*.fish     text eol=lf
*.sh       text eol=lf
# These are explicitly windows files and should use crlf
*.bat      text eol=crlf
*.cmd      text eol=crlf

###### Serialisation ######
*.json     text
*.toml     text
*.xml      text
*.yaml     text
*.yml      text

##### Archives ######
*.7z       binary
*.gz       binary
*.tar      binary
*.tgz      binary
*.zip      binary

# Text files where line endings should be preserved
*.patch    -text

# Java sources
*.java          text diff=java
*.gradle        text diff=java
*.gradle.kts    text diff=java

# These files are text and should be normalized (Convert crlf => lf)
*.css           text diff=css
*.df            text
*.htm           text diff=html
*.html          text diff=html
*.js            text
*.jsp           text
*.jspf          text
*.jspx          text
*.properties    text
*.tld           text
*.tag           text
*.tagx          text
*.xml           text

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class         binary
*.dll           binary
*.ear           binary
*.jar           binary
*.so            binary
*.war           binary
*.jks           binary

###### vim ######
*.vim text eol=lf
.vimrc text eol=lf
.gvimrc text eol=lf

###### VisualStudio prj #######
*.sln        text eol=crlf
*.csproj     text eol=crlf
*.vbproj     text eol=crlf
*.vcxproj    text eol=crlf
*.vcproj     text eol=crlf
*.dbproj     text eol=crlf
*.fsproj     text eol=crlf
*.lsproj     text eol=crlf
*.wixproj    text eol=crlf
*.modelproj  text eol=crlf
*.sqlproj    text eol=crlf
*.wwaproj    text eol=crlf

*.xproj      text eol=crlf
*.props      text eol=crlf
*.filters    text eol=crlf
*.vcxitems   text eol=crlf

#
# Exclude files from exporting
#

.gitattributes export-ignore
.gitignore     export-ignore
.gitkeep       export-ignore
