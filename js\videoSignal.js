var sliderObj = {};
var data = {
  colorenable: 0,
  coloryellowtime1: 0,
  coloryellowtime2: 0,
  coloryellowtime3: 0,
  coloryellowtime4: 0,
  coloryellowtime5: 0,
  coloryellowtime6: 0,
  colorhalorange: 0,
};
var fixName = ['One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven'];
var slider, form, layer;

$(document).ready(function () {
  layui.use(['layer', 'form'], function () {
    form = layui.form, layer = layui.layer;
    let result = initialize_ocx('videoContainer');
    if (!result.result) {
      layer.open({
        title: langMessage.common.error
        , shade: 0.8
        , btn: false
        , content: result.msg
        , anim: 6
      });
      return;
    } else {
      getRTSPWay();
      getRTSPInfo();
    }
    $("#submitVideo").off('click').on('click', function () {
      let way = $("#RTSPWay option:selected").text();
      changeRTSPWay(way);
    });
    $("#commonDefault").off('click').on('click', function () {
      let way = "TCP";
      changeRTSPWay(way);
    });
    $("#passwordClick").off("click").on("click", function () {
      let c = this.className;
      if (c.indexOf("icon-yanjing_kai") > -1) {
        this.className = "iconfont icon-yanjing_yincang";
        $("#cameraPassword").attr({type: "password"});
      } else {
        this.className = "iconfont icon-yanjing_kai";
        $("#cameraPassword").attr({type: "text"});
      }
    })
  });

});
var getRTSPInfo = function () {
  let projectConfig = getSe("projectConfig");
  initWebService(webserviceCMD.CMD_GET_VIDEO, null, function (res) {
    let user = projectConfig.camera_username;
    let pwd = projectConfig.camera_password;
    if (res.stream_username && res.stream_password) {
      user = res.stream_username;
      pwd = res.stream_password;
    }
    $("#cameraUsername").val(user);
    $("#cameraPassword").val(pwd);
  });
};
var getRTSPWay = function () {
  let value = getCommonConf("RTSPWAY");
  $("#RTSPWay").val(value);
  form.render();
};
var changeRTSPWay = function (way) {
  let username = $("#cameraUsername").val();
  let password = $("#cameraPassword").val();
  setStreamInfo(username, password, function (res) {
    if (res.ret === 0) {
      setRTSPWay(way, function (result) {
        if (result) {
          layer.msg(langMessage.common.editSuc, {icon: 1});
        } else {
          layer.msg(langMessage.common.editFail, {icon: 2});
        }
        getRTSPWay();
      })
    } else {
      layer.msg(langMessage.common.editFail, {icon: 2});
    }
  })
};

