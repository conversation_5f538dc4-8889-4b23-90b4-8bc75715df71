<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>contentAuthorization</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui/css/layui.css">
    <link rel="stylesheet" href="../css/global.css">

    <script src="../js/dep/polyfill.min.js"></script>
    <script src="../js/dep/jquery-3.3.1.js"></script>
    <script src="../js/cookie.js"></script>
    <script src="../layui/layui.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../js/webService.js"></script>
    <script src="../js/settingMain.js"></script>
    <script src="../js/saveTXT.js"></script>
    <script src="../js/dep/spark-md5.js"></script>
    <script src="../js/bufferOperation.js"></script>
    <script src="../js/analysisFile.js"></script>
    <script src="../js/ocxGlobal.js"></script>
    <script src="../js/authorization.js"></script>
    <script src="../js/dep/qrcode/qrcode.js"></script>
    <script src="../js/keepAlive.js"></script>
    <script src="../js/c_authorization_new.js"></script>
    <style type="text/css">
        .sub-body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #f2f2f2;
        }

        .setting-table tr > td:last-child {
            width: 300px;
        }

        .setting-table td {
            padding: 10px 0;
        }

        .qrcode-div {
            margin-top: 10px;
            margin: auto; /*水平居中*/
            width: 200px;
            height: 200px;
            background-color: #fff;
        }

        .qrcode-img {
            display: none;
        }

        .auth-img {
            width: 120%;
            height: 120%;
            position: absolute;
            top: -20px;
            left: -20px;
        }

        .auth-img img {
            width: 120%;
            height: 120%;
        }

        .blur {
            filter: blur(10px);
            -webkit-filter: blur(10px); /* chrome, opera */
            -ms-filter: blur(10px);
            -moz-filter: blur(10px);
        }

        #authContainer {
            width: 50%;
            left: 36%;
            position: absolute;
            padding: 20px;
        }

        .auth-content {
            float: left;
            width: 50%;
        }

        .auth-detail {
            color: #333;
            margin-top: 20px;
            position: relative;
            padding: 30px 50px 50px;
            border-radius: 0;
            border-top: 5px solid #ddd;
            background-color: #fff;
            text-align: center;
            transition: all .3s;
            -webkit-transition: all .3s;
        }

        .auth-detail h3 {
            font-weight: bold;
        }

        .detail {
            height: 330px;
            line-height: 20px;
            margin-top: 10px;
        }

        .detail button {
            margin: 5px;
        }

        .bottom {
            height: 100px;
        }
    </style>
    <link rel="stylesheet" href="../css/subTab.css">
</head>
<body class="sub-body custom-style">
<div style="width: 100%;height: 100%;">
    <div id="authContainer">
        <div class="auth-content">
            <div style="padding: 20px;">
                <!--老版授权-上传lic文件夹兼容浏览器逻辑-->
                <section class="auth-detail">
                    <h3 id="authTitle">授权码授权</h3>
                    <div class="detail">
                        <p id="authInfo">通过申请授权令牌，使用授权令牌（.token）换取对应的授权码文件（.lic）对相机进行授权。</p>
                        <div class="import">
                            <div class="import-btn">
                                <!--<label for="authFile">-->
                                <button class="layui-btn layui-btn-default" id="authFile">选择授权码目录</button>
                                <!--</label>-->
                            </div>
                            <div>
                                <input type="file" id="authFileInput" webkitdirectory="webkitdirectory"
                                       multiple="multiple"
                                       style="display: none">
                                <span id="authName"></span>
                            </div>


                            <div>
                                <button class="layui-btn layui-btn-default layui-btn-disabled" id="importAuth"
                                        style="display: none">授权
                                </button>
                            </div>
                        </div>
                        <div class="export">
                            <div class="exportBtn">
                                <button class="layui-btn layui-btn-default" id="exportAuth">申请授权令牌</button>
                            </div>
                            <div><span id="exportPath" class="maintain-tips"></span></div>
                        </div>
                        <div class="exportFile">
                            <div class="exportFileBtn">
                                <button class="layui-btn layui-btn-default" id="exportFile">导出授权文件</button>
                            </div>
                            <div><span id="exportFilePath" class="maintain-tips"></span></div>
                        </div>
                    </div>
                    <div class="bottom">
                        <p id="authExportInfo">（注1：授权令牌默认保存路径为浏览器的下载路径）</p>
                        <p id="authImportInfo">（注2：授权时只需要选择授权码目录即可，程序会在目录中查找对应的授权文件）</p>
                    </div>
                </section>
                <!--<section class="auth-detail">-->
                <!--<h3>授权码授权</h3>-->
                <!--<div class="detail">-->
                <!--<p>通过申请授权令牌，使用授权令牌（.token）换取对应的授权码文件（.lic.tar）对相机进行授权。</p>-->
                <!--<div class="import">-->
                <!--<div class="import-btn">-->
                <!--&lt;!&ndash;<label for="authFile">&ndash;&gt;-->
                <!--<button class="layui-btn layui-btn-default" id="authFile">选择授权文件</button>-->
                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                <!--</div>-->
                <!--<div>-->
                <!--<input type="file" id="authFileInput"-->
                <!--style="display: none">-->
                <!--<span id="authName"></span>-->
                <!--</div>-->
                <!--<div>-->
                <!--<button class="layui-btn layui-btn-default layui-btn-disabled" id="importAuth"-->
                <!--style="display: none">授权-->
                <!--</button>-->
                <!--</div>-->
                <!--</div>-->
                <!--<div class="export">-->
                <!--<div class="exportBtn">-->
                <!--<button class="layui-btn layui-btn-default" id="exportAuth">申请授权令牌</button>-->
                <!--</div>-->
                <!--<div><span id="exportPath" class="maintain-tips"></span></div>-->
                <!--</div>-->
                <!--</div>-->
                <!--<div class="bottom">-->
                <!--<p class="export">（注：授权令牌默认保存路径为浏览器的下载路径）</p>-->
                <!--</div>-->
                <!--</section>-->
            </div>
        </div>
        <div class="auth-content" style="display: none">
            <div style="padding: 20px;">
                <section class="auth-detail">
                    <h3 id="authorizationText2">扫码授权</h3>
                    <div class="detail">
                        <p id="authorizationText3">扫描下方二维码进行授权。进入授权页面，正确填写相关信息后，将会为您发放免费的试用版授权。</p>
                        <div class="qrcode-div" id="authCode">
                            <img class="qrcode-img" id="authImg" src="" alt="">
                        </div>

                    </div>
                    <div class="bottom">
                        <p id="note3">（注：由于试用版授权通过邮箱发送，请务必确认邮箱填写正确！）</p>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
</body>

</html>
