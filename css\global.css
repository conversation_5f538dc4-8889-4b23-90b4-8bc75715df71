
.index-html {
    background-color: #F2F2F2;
    height: 100%;
}

.index-html body {
    overflow: hidden;
    /*background-color: #fff;*/
    height: 100%;
}

html {

}

body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
}

.site-tree .layui-tree li h2 {
    line-height: 36px;
    border-left: 5px solid #0193de;
    margin: 15px 0 5px;
    padding: 0 10px;
    background-color: #f2f2f2;
}

.site-tree .layui-tree li h3 {
    line-height: 36px;
    /*margin: 15px 0 5px;*/
    padding: 0 10px;
    font-weight: bold;
    background-color: rgba(1, 147, 222, 0.36);
    text-align: center;
}

.site-tree {
    border-right: 1px solid #eee;
}

.site-tree {
    width: 220px;
    min-height: 900px;
    padding: 5px 0 20px;
}

.site-tree, .site-content {
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
}

.layui-header {
    margin-bottom: 15px;
}

.index-video {
    background-color: #777777; /**/
    width: 100%;
    height: calc(50vh - 75px);
    color: #fff;
    text-align: center;
    line-height: calc(50vh - 75px);
    border: 1px solid #F2F2F2
}

.event-video {
    background-color: #777777;
    width: 100%;
    height: calc(50vh - 200px);
    color: #fff;
    text-align: center;
    line-height: calc(50vh - 200px);
    border: 1px solid #F2F2F2
}

#videoContent {
    margin-top: 1px;
    height: 100%;
    width: 100%;
    background-color: #777777
}

.content-min {
    min-width: 1360px;
    min-height: 100vh;
}

.content-min .layui-card-body {
    padding: 0px;
    width: 100%;
}

.footer {
    position: fixed;
    left: 0px;
    right: 0;
    bottom: -20px;
    height: 44px;
    background-color: #eee;
    margin: 10px 0 0;
    padding: 20px 0 20px;
    line-height: 30px;
    text-align: center;
    color: #737573;
    border-top: 1px solid #e2e2e2;
}

.content-body {
    left: 0;
    overflow: hidden
}

.content-body div {
    padding: 15px;
    background-color: #f2f2f2;
}

.layer-pic {
    width: 100%;
}

.pic-height {
    height: 250px;
}

.time-show {
    color: #666;
    padding-left: 10px;
    border-bottom: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;
    height: 25px;
    line-height: 25px;
}

.time-show.show {
    font-weight: bold;
    color: red;
}

.pic-show {
    border: 1px solid #f6f6f6;
    background-color: #777777;
}

.pic-show.show {
    border: 1px solid red;
}

.event-height #currentPic {
    height: 300px;
}

.event-height #picList div {
    height: 100px;
}

.layer-pic img {
    width: 100%;
    height: 100%;
}

.layui-input-center {
    float: left;
    margin: 0 20px;
    width: calc(100% - 40px);
    text-align: center;
}

.layui-tab-brief > .layui-tab-title .layui-this:after {
    border: none;
    border-radius: 0;
    border-right: 2px solid #0193de;
}

.layui-tab {
    /*background-color: #fff;*/
}

/*.layui-logo {*/
/*height: 60px;*/
/*background: url(../img/index_logo.png);*/
/*background-repeat: no-repeat;*/
/*}*/
/*小logo*/
/*.logo-container{*/
/*margin: 0 auto;*/
/*width: 100px;*/
/*height: 60px;*/
/*line-height: 60px;*/
/*background: url(../img/index_logo.png);*/
/*background-repeat: no-repeat;*/
/*background-size:80% 70%;*/
/*-moz-background-size:100% 100%;*/
/*background-position: center;*/
/*}*/
/*.layui-logo {*/
/*line-height: 60px;*/
/*}*/
.my-help {
    position: absolute;
    right: 100px;
    top: 20px;
}

.common-table {
    width: 100%;
    color: #666;
}

.common-table th {
    border: 1px solid #e6e6e6;
    background-color: #f2f2f2;
    line-height: 40px;
}

.common-block {
    display: block;
}

.common-none {
    /*!important*/
    display: none;
}

.dis {
    color: #e6e6e6 !important
}

.sub-body {
    padding: 10px 10px 20px 10px;
    background-color: #fff;
}

.sub-body > div.layui-form {

    width: 65%;
}

.sub-body div.layui-form > fieldset {
    max-width: 600px;
}

.display-table tr {
    display: block;
    margin-bottom: 10px;
}

.layer-pic {
    width: 100%;
}

.layer-pic .pic-height div {
    border: 1px solid #f6f6f6;
    background-color: #777777;

}

.layer-pic img {
    width: 100%;
    height: 100%;
}

.event-picture {
    /*min-height: 27.5%;*/
    padding: 0px;
    /*width: calc(100% - 500px);*/
    /*min-width: 800px;*/
}

.event-picture-4 div {
    border: 1px solid #f6f6f6;
    height: 30%;
    background-color: #777777;
}

.event-picture-4 img {
    width: 100%;
    height: 100%;
}

.event-receive {
    height: 30px;
    padding: 0;
}

.event-receive-toolbar {
    width: 100%;
    height: 100%;
    background-color: #777777
}

.event-left {
    padding-top: 0px;
    padding-bottom: 0px;
    /*width: 500px;*/
    position: relative;
}

.video-style {
    width: 100%;
    text-align: center;
    background-color: #777;
    height: 300px;
}

.event-plate {
    height: 200px;
    text-align: center
}

.event-plate div {
    width: 100%;
    height: 100%;
}

#plate {
    font-size: 50px;
    text-align: center;
    line-height: 200px;
}

.event-info {
    padding: 0px;
    margin-top: 15px;
    /*height: 31vh;*/
    overflow-y: scroll;
}

.event-table td:nth-child(1), .event-table th:nth-child(1) {
    width: 20%
}

.event-table td:nth-child(2), .event-table th:nth-child(2) {
    width: 10%
}

.event-table td:nth-child(3), .event-table th:nth-child(3) {
    width: 5%
}

.event-table td:nth-child(4), .event-table th:nth-child(4) {
    width: 10%
}

.event-table td:nth-child(5), .event-table th:nth-child(5) {
    width: 15%
}

.event-table td:nth-child(6), .event-table th:nth-child(6) {
    width: 10%
}

.event-table td:nth-child(7), .event-table th:nth-child(7) {
    width: 10%
}

.event-table td:nth-child(8), .event-table th:nth-child(8) {
    width: 20%
}

.search-table td {
    text-align: center;
}

.search-table td:nth-child(1), .search-table th:nth-child(1) {
    width: 20%
}

.search-table td:nth-child(2), .search-table th:nth-child(2) {
    width: 5%
}

.search-table td:nth-child(3), .search-table th:nth-child(3) {
    width: 10%
}

.search-table td:nth-child(4), .search-table th:nth-child(4) {
    width: 5%
}

.search-table td:nth-child(5), .search-table th:nth-child(5) {
    width: 10%
}

.search-table td:nth-child(6), .search-table th:nth-child(6) {
    width: 10%
}

.search-table td:nth-child(7), .search-table th:nth-child(7) {
    width: 10%
}

.search-table td:nth-child(8), .search-table th:nth-child(8) {
    width: 10%
}

.search-table td:nth-child(9), .search-table th:nth-child(9) {
    width: 20%
}

.stroll-table {
    width: 100%;
    height: 100vh;
    overflow-y: scroll;
}

.picture-search .layui-card {
    height: 100vh
}

#ocx_down {
    margin-top: 8px;
    margin-left: 8px;
    color: red;
    cursor: pointer;
    text-decoration: underline;
}

#drawContent {
    position: relative;
    float: left;
    min-width: 600px;
    /*max-width: 800px;*/
    max-width: 1000px;
}

.stroll-event-table {
    /*width: 100%;*/

}

.video-event {
    position: absolute;
    bottom: 2px;
    right: 100px;
}

.video-event span {
    color: #fff !important;
}

.video-control {
    line-height: 30px;
    height: 30px;
    width: 100%;
    background-color: #000;
    color: #fff;
    text-align: left;
    bottom: 0;
    position: absolute;
}

.video-control a, .video-control i {
    color: #d2d2d2;
}

.video-control-item {
    display: inline-block;
    vertical-align: top;
}

.video-stop {
    width: 10px;
    height: 10px;
    background-color: #333333;
    float: left;
    margin-left: 10px;
}

.video-play {
    margin: 0 15px;
    float: right;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent transparent #333333;
}

.video-button {
    cursor: pointer;
    left: 180px;
    padding-top: 10px;
}

.video-type {
    bottom: 2px;
    left: 20px;
    color: #fff;
}

.video-type span {
    margin: 0 10px;
}

.video-type a:hover {
    color: #fff
}

.video-type a.video-selected {
    color: #ffffff
}

.video-type a:disabled {
    cursor: auto;
    color: #575757 !important;
}

.video-selected {
    color: #c2c2c2;
}

.video-type iframe {

}

.video-type ul {
    background-color: #fff;
    position: absolute;
    bottom: 30px;
}

.video-type ul li > a {
    color: #000;
}

.video-type ul li > a:hover {

}

.video-type ul li {
    margin: 0;
    line-height: 20px;
    height: 20px;
    padding: 5px;
}

.video-type li.selected > a {
    color: #f58319;
}
