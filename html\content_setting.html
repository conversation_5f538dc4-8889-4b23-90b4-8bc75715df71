<!DOCTYPE html>
<html lang="zh" class="index-html">
<head>
    <meta charset="UTF-8">
    <title>contentSetting</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui/css/layui.css">
    <link rel="stylesheet" href="../css/global.css">
    <link rel="stylesheet" href="../css/setting.css">
    <script src="../js/dep/polyfill.min.js"></script>
    <script src="../js/dep/jquery-3.3.1.js"></script>
    <script src="../js/cookie.js"></script>
    <script src="../layui/layui.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../js/webService.js"></script>
    <script src="../js/settingMain.js"></script>
    <script src="../js/setting.js"></script>
    <script src="../js/global.js"></script>
    <script src="../js/ocxGlobal.js"></script>

    <style>
        dd > a {
            font-size: 13px !important;
            padding-left: 30px !important;
        }

        .my-scroll {
            position: fixed;
            width: 200px;
            overflow-x: hidden;
            overflow-y: scroll;
        }
    </style>
</head>
<body class="custom-style">
<div>
    <div class="my-scroll">
        <ul class="layui-nav layui-nav-tree layui-nav-side " lay-shrink="all" id="sideBar">
            <li class="layui-nav-item">
                <a id="system" href="javascript:;">系统<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                    <dd><a id="Device_information" href="javascript:;"
                           data-href="system_frame/system_info.html">设备信息</a></dd>
                    <dd><a id="Device_status" href="javascript:;" data-href="system_frame/system_status.html">设备状态</a>
                    </dd>
                    <dd><a id="Module_status" href="javascript:;" data-href="system_frame/system_module_status.html">模块状态</a>
                    </dd>
                    <dd><a id="Version_information" href="javascript:;" data-href="system_frame/system_version.html">版本信息</a>
                    </dd>
                    <dd><a id="TPM" href="javascript:;" data-href="system_frame/system_maintain.html">设备维护</a></dd>
                    <dd><a id="Local_configuration" href="javascript:;" data-href="system_frame/system_config.html">本地配置</a></dd>
<!--                    <dd><a id="User" href="javascript:;" data-href="system_frame/system_user.html">用户</a></dd>-->
                </dl>
            </li>
            <li class="layui-nav-item">
                <a id="Internet" href="javascript:;">网络<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                    <dd><a id="Advanced_applications" href="javascript:;"
                           data-href="internet_frame/internet_senior.html">高级应用</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item">
                <a id="Video" href="javascript:;">视频<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                    <dd><a id="Video_parameters" href="javascript:;" data-href="video_frame/video_signal.html">视频参数</a>
                    </dd>
                </dl>
            <li class="layui-nav-item"><a id="Image" href="javascript:;">图像<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                    <dd><a id="Event_OSD_crossword" href="javascript:;" data-href="picture_frame/picture_OSD.html">事件OSD叠字</a>
                    </dd>
                </dl>
            </li>
            <li class="layui-nav-item"><a id="Disk" href="javascript:;">存储<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
<!--                    <dd><a id="disc" href="javascript:;" data-href="storage_frame/storage_disk.html">磁盘</a></dd>-->
                    <dd><a id="sd" href="javascript:;" data-href="storage_frame/storage_sd.html">SD卡</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item"><a id="Algorithm" href="javascript:;">算法<span class="layui-nav-more"></span></a>
                <dl class="layui-nav-child">
                    <dd><a id="Event_detection" href="javascript:;"
                           data-href="setting_frame/setting_detect.html">事件检测</a></dd>
                    <dd><a id="Detection_area1" href="javascript:;" data-href="setting_frame/setting_line_survey.html">绘制检测区域</a>
                    </dd>


                    <!--                    <dd><a id="Detection_area" href="javascript:;" data-href="setting_frame/setting_survey.html">检测区</a>-->
                    <!--                    </dd>-->
                    <!--                    <dd><a id="Lane_lines" href="javascript:;" data-href="setting_frame/setting_line.html">车道线</a></dd>-->
                    <dd><a id="Signal_lamp" href="javascript:;" data-href="setting_frame/setting_signal.html">信号灯</a>
                    </dd>
                    <dd><a id="Traffic_light_status" href="javascript:;" data-href="setting_frame/setting_status.html">红绿灯状态</a>
                    </dd>
                    <dd><a id="System_configuration" href="javascript:;" data-href="setting_frame/setting_system.html">系统配置</a>
                    </dd>
                    <dd><a id="Traffic_expansion_parameters" href="javascript:;"
                           data-href="setting_frame/setting_extend.html">交通扩展参数</a></dd>
                    <dd><a id="Illegal_code" href="javascript:;" data-href="setting_frame/setting_code.html">违法代码</a>
                    </dd>
                    <dd><a id="Configuration_Preview" href="javascript:;"
                           data-href="setting_frame/setting_preview.html">配置预览</a></dd>
                </dl>
            </li>
            <span class="layui-nav-bar" style="height: 0; top: 0; opacity: 0;"></span></ul>
    </div>
    <div style="margin-left: 200px">
        <div>
            <div class="layui-card">
                <div class="layui-card-body" id="settingFrame" style="padding: 0px;min-width: 1100px;">
                    <div class="layui-tab-content" style="padding: 0px;">
                        <div>
                            <iframe id="frame_setting" name="frame_setting_content" src="system_frame/system_info.html"
                                    width="100%" height="100%" scrolling="auto" frameborder="0"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
