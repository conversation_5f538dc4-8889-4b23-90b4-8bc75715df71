var sqlStatus = 0, exportL = null;
var exportConfig = {};
var i18i = getCookie('i18ns');
exportConfig = langMessage.search.exportConfig

$(document).ready(function () {
    window.parent.setNavValue(2);
    getCarType('../config/carType.json', function (carStatus) {
        onloadConfig('../config/event.json', 'eventConfig', function (status) {
            if (!status || !carStatus) {

                layer.msg(langMessage.common.violationError, {icon: 2});

                $("#submit").attr({
                    disabled: true
                })
            }
        });
    });
    $("#data>tr>td").off("click").on("click", function () {
        console.log("click" + this);
    });
    var showData = [];
    var laytpl;
    var beginChange = 0, endChange = 0;
    $("#begin").change(function (e) {
        beginChange = 1;
    });
    $("#end").change(function (e) {
        endChange = 1;
    });

    layui.use(['form', 'laydate', 'laytpl', 'table', 'layer'], function () {
        var laydate = layui.laydate, form = layui.form, table = layui.table, layer = layui.layer;
        laytpl = layui.laytpl;
        let nowParam = {
            id: 0,
            limit: 0
        };
        let result = initialize_ocx('pictureOCX');
        if (!result.result) {
            layer.open({
                title: langMessage.common.error
                , shade: 0.8
                , btn: false
                // ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
                // ,moveType: 1 //拖拽模式，0或者1
                , content: result.msg
                , anim: 6
                // ,success: function(layero,index){
                //     layer.close(index)
                // }
            });
        }
        $("#reset").off("click").on("click", function () {
            $("#begin").val("");
            $("#end").val("");
            $("#plate_type").val("");
            $("plate_string").val("");
            $("#event_type").val("");
            form.render();
        });
        $("#exportBtn").off("click").on("click", function () {
            // let data = [];
            // let dataLength = showData.length;
            // for(let i = 0;i<dataLength;i++){
            //     let item = showData[i];
            //     // if(!data[item['EVENT_TYPE']]){
            //     //     data[item['EVENT_TYPE']] = [];
            //     // }
            //     // let dataI = data[item['EVENT_TYPE']];
            //     if (item['IMAGE_PLATE_PATH'] !== '') {
            //         data.push(item['IMAGE_PLATE_PATH'])
            //     }
            //     if (item['IMAGE_PRE_PATH'] !== '') {
            //         data.push(item['IMAGE_PRE_PATH'])
            //     }
            //     if (item['IMAGE_EVENT_PATH'] !== '') {
            //         data.push(item['IMAGE_EVENT_PATH'])
            //     }
            //     if (item['IMAGE_LAST_PATH'] !== '') {
            //         data.push(item['IMAGE_LAST_PATH'])
            //     }
            // }
            // let status = exportPicture(0,data.join());
            // if(status==='TRUE'){
            //     layer.msg('导出成功!', {icon: 1});
            // }else if(status!=='FALSE'){
            //     layer.msg('导出失败!'+status, {icon: 2});
            // }


            // let dataL = data.length;
            // let exportTimeout = function () {
            //     let s = exportPicture(0,status,data.join());
            //     if(s===-1){
            //         if(exportL) {
            //             closeExportLoading(exportL);
            //             exportL = null;
            //         }
            //         return
            //     }
            //     if(s<-1){
            //         layer.msg(langMessage.common.exportFail+exportConfig[s], {icon: 2});
            //         if(exportL) {
            //             closeExportLoading(exportL);
            //             exportL = null;
            //         }
            //         return
            //     }
            //     if(s===dataL){
            //         layer.msg(langMessage.common.exportSuc, {icon: 1});
            //         if(exportL) {
            //             closeExportLoading(exportL);
            //             exportL = null;
            //         }
            //         return
            //     }
            //     if(!exportL){
            //         exportL = exportLoading(langMessage.common.exporting);
            //     }
            //     setTimeout(exportTimeout,1000);
            // };
            let status = ocxPopOut();
            if (status) {
                if (status === 'error') {
                    layer.msg(langMessage.ocxGlobal.ocxError.SELECT_FAIL, {icon: 2});
                    if (exportL) {
                        closeExportLoading(exportL);
                        exportL = null;
                    }
                    return
                }
                if (exportL) {
                    closeExportLoading(exportL);
                    exportL = null;
                }
                if (!exportL) {
                    exportL = exportLoading(langMessage.common.exporting);
                }
                setTimeout(function () {
                    if (exportData(showData, status)) {
                        layer.msg(langMessage.common.exportSuc, {icon: 1});
                    } else {
                        layer.msg(langMessage.common.exportFail, {icon: 2});
                    }
                    if (exportL) {
                        closeExportLoading(exportL);
                        exportL = null;
                    }
                }, 1000)
            }

        });
        let begin = laydate.render({
            elem: '#begin'
            , type: 'datetime'
            , done: function (value, date, endDate) {
                //console.log(value); //得到日期生成的值，如：2017-08-18
                if (beginChange) {
                    let v = $("#begin").val();
                    if (v !== "") {
                        let dateTime = checkDate(v);
                        if (dateTime) {
                            begin.config.dateTime = dateTime;
                            laydate.render();
                        }
                    }
                    beginChange = 0;
                }
            }
        });
        let end = laydate.render({
            elem: '#end'
            , type: 'datetime'
            , done: function (value, date, endDate) {
                //console.log(value); //得到日期生成的值，如：2017-08-18
                if (endChange) {
                    let v = $("#end").val();
                    if (v != "") {
                        let dateTime = checkDate(v);
                        if (dateTime) {
                            end.config.dateTime = dateTime;
                            laydate.render();
                        }
                    }
                    endChange = 0;
                }
            }
        });
        let globalInfo = checkGlobal();
        let outW = '800px', outH = '500px';
        table.render({
            elem: '#eventTable'
            , cols: [[ //标题栏
                {field: 'time', title: langMessage.event.eventTable.time, minWidth: 200, align: 'center'}
                , {field: 'ID', title: langMessage.event.eventTable.ID, align: 'center'}
                , {field: 'EVENT_TYPE', title: langMessage.event.eventTable.EVENT_TYPE, align: 'center'}
                , {field: 'LANE_INDEX', title: langMessage.event.eventTable.LANE_INDEX, align: 'center'}
                , {field: 'PLATE_TYPE', title: langMessage.event.eventTable.PLATE_TYPE, align: 'center'}
                , {field: 'PLATE_STRING', title: langMessage.event.eventTable.PLATE_STRING, align: 'center'}
                , {field: 'CAR_SPEED', title: langMessage.event.eventTable.CAR_SPEED, align: 'center'}
                , {field: 'CAR_COLOR', title: langMessage.event.eventTable.CAR_COLOR, align: 'center'}
                , {field: 'CAR_TYPE', title: langMessage.event.eventTable.CAR_TYPE, align: 'center'}
                , {field: 'IMAGE_EVENT_PATH', title: langMessage.event.eventTable.IMAGE_EVENT_PATH, hide: true}
                , {field: 'IMAGE_EVENT_X', title: langMessage.event.eventTable.IMAGE_EVENT_X, hide: true}
                , {field: 'IMAGE_EVENT_Y', title: langMessage.event.eventTable.IMAGE_EVENT_Y, hide: true}
                , {field: 'IMAGE_EVENT_W', title: langMessage.event.eventTable.IMAGE_EVENT_W, hide: true}
                , {field: 'IMAGE_EVENT_H', title: langMessage.event.eventTable.IMAGE_EVENT_H, hide: true}
                , {field: 'CAR_EVENT_X', title: langMessage.event.eventTable.IMAGE_EVENT_X, hide: true}
                , {field: 'CAR_EVENT_Y', title: langMessage.event.eventTable.IMAGE_EVENT_Y, hide: true}
                , {field: 'CAR_EVENT_W', title: langMessage.event.eventTable.IMAGE_EVENT_W, hide: true}
                , {field: 'CAR_EVENT_H', title: langMessage.event.eventTable.IMAGE_EVENT_H, hide: true}
                , {field: 'IMAGE_PRE_PATH', title: langMessage.event.eventTable.IMAGE_PRE_PATH, hide: true}
                , {field: 'IMAGE_PRE_X', title: langMessage.event.eventTable.IMAGE_PRE_X, hide: true}
                , {field: 'IMAGE_PRE_Y', title: langMessage.event.eventTable.IMAGE_PRE_Y, hide: true}
                , {field: 'IMAGE_PRE_W', title: langMessage.event.eventTable.IMAGE_PRE_W, hide: true}
                , {field: 'IMAGE_PRE_H', title: langMessage.event.eventTable.IMAGE_PRE_H, hide: true}
                , {field: 'CAR_PRE_X', title: langMessage.event.eventTable.IMAGE_PRE_X, hide: true}
                , {field: 'CAR_PRE_Y', title: langMessage.event.eventTable.IMAGE_PRE_Y, hide: true}
                , {field: 'CAR_PRE_W', title: langMessage.event.eventTable.IMAGE_PRE_W, hide: true}
                , {field: 'CAR_PRE_H', title: langMessage.event.eventTable.IMAGE_PRE_H, hide: true}
                , {field: 'IMAGE_LAST_PATH', title: langMessage.event.eventTable.IMAGE_LAST_PATH, hide: true}
                , {field: 'IMAGE_LAST_X', title: langMessage.event.eventTable.IMAGE_LAST_X, hide: true}
                , {field: 'IMAGE_LAST_Y', title: langMessage.event.eventTable.IMAGE_LAST_Y, hide: true}
                , {field: 'IMAGE_LAST_W', title: langMessage.event.eventTable.IMAGE_LAST_W, hide: true}
                , {field: 'IMAGE_LAST_H', title: langMessage.event.eventTable.IMAGE_LAST_H, hide: true}
                , {field: 'CAR_LAST_X', title: langMessage.event.eventTable.IMAGE_LAST_X, hide: true}
                , {field: 'CAR_LAST_Y', title: langMessage.event.eventTable.IMAGE_LAST_Y, hide: true}
                , {field: 'CAR_LAST_W', title: langMessage.event.eventTable.IMAGE_LAST_W, hide: true}
                , {field: 'CAR_LAST_H', title: langMessage.event.eventTable.IMAGE_LAST_H, hide: true}
                , {field: 'IMAGE_PLATE_PATH', title: langMessage.event.eventTable.IMAGE_PLATE_PATH, hide: true}
                , {field: 'IMAGE_FEATURE_PATH', title: langMessage.event.eventTable.IMAGE_FEATURE_PATH, hide: true}
                , {field: 'CHE_XING', title: langMessage.event.eventTable.CHE_XING, hide: true}
                , {field: 'preTime', title: langMessage.event.eventTable.preTime, hide: true}
                , {field: 'lastTime', title: langMessage.event.eventTable.lastTime, hide: true}
                , {field: 'featureTime', title: langMessage.event.eventTable.featureTime, hide: true}
                , {field: 'desc', title: langMessage.event.eventTable.desc, align: 'center'}
            ]]
            , data: showData
            //,skin: 'line' //表格风格
            , even: true
            , limit: globalInfo.w > 600 ? 15 : 10
            // , limits:false
            , page: true //是否显示分页
            , limits: globalInfo.w > 600 ? [10, 15, 20] : [5, 7, 10]
            , done: function (res, curr, count) {
                //如果是异步请求数据方式，res即为你接口返回的信息。
                //如果是直接赋值的方式，res即为：{data: [], count: 99} data为当前页数据、count为数据总长度
                //console.log(res);
                if (res.length > 0) {
                    nowParam.id = res.data[0]['ID'];
                }
                nowParam.limit = this.limit;
                let filterTotal = [];
                for (let i = 0; i < showData.length; i++) {
                    let item = showData[i];
                    if (langMessage.search.unknownPlate.indexOf(item['PLATE_STRING']) > -1 || filterTotal.indexOf(item['PLATE_STRING']) < 0) {
                        filterTotal.push(item['PLATE_STRING']);
                    }
                }
                $(".layui-laypage-count").after("<span class='filter-count'>" + langMessage.search.removeDuplication(filterTotal.length) + " </span>")
            }
        });

        form.on('submit(formDemo)', function (data) {
            let getNowPage = false, curr = 1;
            let searchData = data.field;
            let searchParam = {};
            // selectDB(searchData)
            if (searchData.begin_time) {
                searchParam.BEGIN_TIME = time2stamp(searchData.begin_time.replace(/-/g, "/"));
            }
            if (searchData.end_time) {
                searchParam.END_TIME = time2stamp(searchData.end_time.replace(/-/g, "/"));
            }
            if (searchData.event_type) {
                searchParam.EVENT_TYPE = parseInt(searchData.event_type);
            }
            if (searchData.plate_string) {
                searchParam.PLATE_STRING = searchData.plate_string;
            }
            if (searchData.plate_type) {
                searchParam.PLATE_TYPE = parseInt(searchData.plate_type);
            }
            if (showData.length > 0) {
                getNowPage = true;
            }
            searchParam.ORDER = "Asc";
            showData = [];
            showData = ocxSQL(searchParam);
            // console.log('###########search###########')
            // console.log(JSON.stringify(showData))
            if (getNowPage) {
                for (let i = 0; i < showData.length; i++) {
                    if (nowParam.id === showData[i].ID) {
                        curr = parseInt(i / nowParam.limit) + 1;
                        break
                    }
                }
            }
            setTimeout(function () {
                table.reload('eventTable', {
                    data: showData
                    , page: {
                        curr: curr //重新从第 1 页开始
                    }
                });
                setTimeout(function () {
                    parent.setIframeHeight();
                }, 500);
                table.on('rowDouble(eventTable)', function (obj) {
                    let clickData = obj.data;
                    let index = getDataset(this).index;
                    showEventDetail(clickData, index, outW, outH)

                });
            }, 500)
        });
        let eventConfig = getSe('eventConfig');
        let eventType = eventConfig.eventType;
        let eventOptions = [];
        eventOptions.push($("<option value=''></option>"));
        for (let key in eventType) {
            let option = $("<option value=" + key + ">" + eventType[key] + "</option>");
            eventOptions.push(option)
        }
        let plateType = eventConfig.plateType;
        let plateOptions = [];
        plateOptions.push($("<option value=''></option>"));
        for (let key in plateType) {
            let option = $("<option value=" + key + ">" + plateType[key] + "</option>");
            plateOptions.push(option)
        }
        $("#event_type").html(eventOptions);
        $("#plate_type").html(plateOptions);
        table.render({ //其它参数省略
            id: 'data'
        });
        form.render()
    })

});

var time2stamp = function (time) {
    let data = new Date(time);
    return parseInt(Date.parse(data) / 1000);
};

var daysInLeap = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var daysInNoLeap = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var isLeapYear = function (year) {
    let cond1 = year % 4 == 0;  //条件1：年份必须要能被4整除
    let cond2 = year % 100 != 0;  //条件2：年份不能是整百数
    let cond3 = year % 400 == 0;  //条件3：年份是400的倍数
    //当条件1和条件2同时成立时，就肯定是闰年，所以条件1和条件2之间为“与”的关系。
    //如果条件1和条件2不能同时成立，但如果条件3能成立，则仍然是闰年。所以条件3与前2项为“或”的关系。
    //所以得出判断闰年的表达式：
    let cond = cond1 && cond2 || cond3;
    if (cond) {
        return true;
    } else {
        return false;
    }
};
var checkDate = function (date) {
    let dateTime = {};
    //IE下时间以斜杠分割
    date = new Date(date.replace(/-/g, "/"));
    if (date !== 'Invalid Date') {
        dateTime.year = date.getFullYear();
        dateTime.month = date.getMonth();
        dateTime.date = date.getDate();
        dateTime.hours = date.getHours();
        dateTime.minutes = date.getMinutes();
        dateTime.seconds = date.getSeconds();
    } else {
        return false
    }

    return dateTime;
};
var exportLoading = function (msg) {
    let topW = getTopWindow();
    let loading = topW.indexLoading(null, msg + langMessage.common.wait);
    return loading;
};
var closeExportLoading = function (loading) {
    let topW = getTopWindow();
    topW.closeLoading(loading);
};
var loadingWait = function (s) {
    let source = window.self.top;

    let app_name = getAppName()

    let loading = source.indexLoading(s, langMessage.common.restartAlgorithm);
    setTimeout(function () {
        source.closeLoading(loading);
        source.location.href = "http://" + location.host + "/SDCWEB/" + app_name + "/index.html";
    }, s * 1000);
};
