/**
 * 页面加载回调
 * @param callback
 */
document.ready = function (callback) {
    if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', function () {
            document.removeEventListener('DOMContentLoaded', null, false);
            callback()
        }, false)
        //兼容IE
    } else if (document.attachEvent) {
        if (document.readyState == 'complete') {
            document.detachEvent("onreadystatechange", null);
            callback()
        }
    } else if (document.lastChild === document.body) {
        callback()
    }
};

var getTopWindow = function () {
    let source = window.self.top;
    // parent是父窗口（通常是指当前框架页面的上级框架）
    // self是当前窗口
    // top是顶级窗口（通常是指框架页面中最外层的那个框架）
    // https://www.cnblogs.com/Jimc/p/9993214.html
    let frame = sessionStorage.getItem("frameElement");
    if (frame) {
        source = source.document.getElementById(frame).contentWindow;
    }
    return source
};

var getProjectConfig = function (callback) {
    $.ajax({
        type: "GET",
        url: "./config/config.json",
        dataType: "json",
        success: function (allData) {
            index_param['projectConfig'] = allData;
            if (callback) {
                callback();
            }
        }
    });
};
//防止globalInfo未取到
var checkGlobal = function () {
    let globalInfo = getSe("globalInfo");
    if (!globalInfo) {
        setInfo();
        globalInfo = getSe('globalInfo');
    }
    return globalInfo
};
var getContainerParam = function () {
    let globalInfo = checkGlobal();
    let imgInfo = getSe('imgInfo');
    let obj = {
        containerW: 0,
        containerH: 0,
        pictureW: 0,
        pictureH: 0
    };
    if (imgInfo) {
        obj.containerW = Math.floor(imgInfo.containerWidth * 1000) / 1000;
        obj.containerH = Math.floor(imgInfo.containerHeight * 1000) / 1000;
        obj.pictureW = Math.floor(imgInfo.width * 1000) / 1000;
        obj.pictureH = Math.floor(imgInfo.height * 1000) / 1000;
    } else {
        obj.containerW = Math.floor(globalInfo.w * 1000) / 1000;
        obj.containerH = Math.floor(globalInfo.h * 1000) / 1000;
        obj.pictureW = Math.floor(globalInfo.w * 1000) / 1000;
        obj.pictureH = Math.floor(globalInfo.h * 1000) / 1000;
    }
    return obj
};
/**
 * 设置配置存在外部页面变量中
 * @param paramName
 * @param paramData
 * @param index
 */
var setSe = function (paramName, paramData, index) {
    // let source = window.parent.parent.parent.parent;
    let source = getTopWindow();
    if (index) {
        source = index;
    }
    let value = paramData;
    if (typeof paramData !== 'object') {
        if (paramName !== 'deviceName' && paramName !== 'osdPositionHeight') {
            value = checkType(paramData);
        }
    }
    source.index_param[paramName] = value;
};

/**
 * 从外部页面中获取配置
 * @param paramName
 * @param index
 * @returns {*}
 */
var getSe = function (paramName, index) {
    // let source = window.parent.parent.parent.parent;
    let source = getTopWindow();
    if (index) {
        source = index;
    }
    let params = source.index_param;
    let result = $.extend(true, {}, params);

    return result[paramName];
};

/**
 * 设置所有的配置
 * @param params
 * @param index
 */
var setConfig = function (params, index) {
    let source = getTopWindow();
    if (index) {
        source = index;
    }
    source.index_param = {};
    for (let key in params) {
        source.index_param[key] = params[key];
    }

};
/**
 * 获取所有的配置
 * @param index
 * @returns {string}
 */
var getConfig = function (index) {
    let source = getTopWindow();
    if (index) {
        source = index;
    }
    let params = source.index_param;
    let result = $.extend(true, {}, params);
    return JSON.stringify(result);
};
/**
 * 判断值是否为数字，若为数字则做parseInt处理
 * @param value
 * @returns {*}
 */
var checkType = function (value) {
    let re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/
    let returnValue = value;
    if (re.test(value)) {
        returnValue = parseInt(value);
    }
    return returnValue
};

/**
 * 清除已有的配置，仅保留传入的配置
 * @param type
 */
var clearSession = function (type) {
    // let value = window.parent.parent.parent.index_param;
    let source = getTopWindow();
    let value = source.index_param;
    let result = $.extend(true, {}, value);
    for (let i in result) {
        if (result.hasOwnProperty(i) && type.indexOf(i) < 0) {
            delete result[i];
        }
    }
    setConfig(result)
};

/**
 * 清除所选配置
 * @param type
 */
var deleteSession = function (type) {
    let source = getTopWindow();
    let value = source.index_param;
    let result = $.extend(true, {}, value);
    for (let i in result) {
        if (result.hasOwnProperty(i) && type.indexOf(i) > -1) {
            delete result[i];
        }
    }
    setConfig(result)
};

/**
 * 初始化图片
 * @param type
 */
var initPicture = function (type) {
    let param = {
        type: type
    };
    initWebService(webserviceCMD.CMD_CAPTURE_JPG, param);
    if (type !== "drawPreview") {
        $("#refreshPic").click(function () {
            let showMsg = getSe("noMsg");
            if (!showMsg) {
                layer.open({
                    title: langMessage.common.hint
                    , shade: 0.8
                    , btn: langMessage.common.confirm
                    , yes: function (index, layero) {
                        initWebService(webserviceCMD.CMD_CAPTURE_JPG, param);
                        layer.close(index);
                        let noMsg = ($("#noMsg")[0].checked === true ? 1 : 0);
                        setSe("noMsg", noMsg)
                    }
                    , icon: 3
                    , content: langMessage.setting.freshSceneMsg
                    , anim: 6
                });
            } else {
                initWebService(webserviceCMD.CMD_CAPTURE_JPG, param);
            }
        })
    } else {
        $("#refreshPic").click(function () {
            initWebService(webserviceCMD.CMD_CAPTURE_JPG, param);
        })
    }

};
/**
 * 对坐标进行排序
 * @param position
 * @param type 传入排序的坐标属性如x,y
 * @returns {*}
 */
var sortPosition = function (position, type) {
    // let p = JSON.parse(position.toString())
    let pos = position.sort(compare(type));
    return pos
};
/**
 * 升序
 * @param a
 * @param b
 * @returns {number}
 */
var sortNumber = function (a, b) {
    return a - b
};

/**
 * 比较传入属性
 * @param property
 * @returns {function(*, *): number}
 */
var compare = function (property) {
    return function (firstobj, secondobj) {
        let firstValue = firstobj[property];
        let secondValue = secondobj[property];
        return firstValue - secondValue; //升序
    };
};

/**
 * 根据当前屏幕大小判断当前需要显示的图片大小
 */
var setInfo = function () {
    // let screenW = window.screen.width;
    // let screenH = window.screen.height;
    let screenW = window.innerWidth;
    let screenH = window.innerHeight;
    // console.log(window.innerWidth, window.innerHeight)
    console.log(screenW, screenH)
    let globalInfo = {};
    // if (screenW < 1800 || screenH < 1000) {
    if (screenW < 1800) {
        globalInfo.w = 600;
        globalInfo.h = 450;
    } else {
        // globalInfo.w = 800;
        // globalInfo.h = 600;
        globalInfo.w = 1000;
        globalInfo.h = 770;
    }
    setSe("globalInfo", globalInfo);
};

/**
 * getDataset兼容性写法
 * @param element
 * @returns {DOMStringMap}
 */
var getDataset = function (element) {
    if (element.dataset) {
        return element.dataset;
    } else {
        // console.log(element.attributes);
        let dataset = {}, name, matchStr;
        for (let i = 0; i < element.attributes.length; i++) {
            let key = element.attributes[i].nodeName;
            matchStr = key.match(/^data-(.+)/);
            if (matchStr) { //data-auto-play 转成驼峰写法 autoPlay
                name = matchStr[1].replace(/-([\da-z])/gi, function (all, letter) {
                    return letter.toUpperCase();
                });
                let v = element.attributes[i].nodeValue;
                dataset[name] = v;
            }
        }
        return dataset;
    }
};

/**
 * setDataset兼容性写法
 * @param element
 * @param key
 * @param value
 */
var setDataset = function (element, key, value) {
    if (element.dataset) {
        element.dataset[key] = value;
    } else {
        //驼峰转成-
        key = key.replace(/([A-Z])/g, "-$1").toLowerCase();
        $(element).attr("data-" + key, value);
    }
};

/**
 * padStart兼容性写法
 * @param str 原字符串
 * @param num 返回几位字符串
 * @param com 需要补全的占位符默认为空格
 * @returns {*}
 */
var myPadStart = function (str, num, com) {
    let l = str.length;
    if (!com) {
        com = " "
    }
    if (l < num) {
        for (let i = 0; i < num - l; i++) {
            str = com + str;
        }
    }
    return str
};

/**
 * padEnd兼容性写法
 * @param str 原字符串
 * @param num 返回几位字符串
 * @param com 需要补全的占位符默认为空格
 * @returns {*}
 */
var myPadEnd = function (str, num, com) {
    let l = str.length;
    if (!com) {
        com = " "
    }
    if (l < num) {
        for (let i = 0; i < num - l; i++) {
            str = str + com;
        }
    }
    return str
};

/**
 * 判断本机字节序是大端还是小端
 * 存储时使用小端存储，如果取出来的值与存进去的值相同，则本机字节序为大端
 * true:小端 false:大端
 * @returns {boolean}
 */
var littleEndian = function () {
    let buffer = new ArrayBuffer(2);
    new DataView(buffer).setInt16(0, 256, true /* 设置值时，使用小端字节序 */);
    // Int16Array 使用系统字节序（由此可以判断系统字节序是否为小端字节序）
    return new Int16Array(buffer)[0] === 256;
};

/**
 * 判断是否是IE浏览器
 * @returns {boolean}
 */
var isIE = function ()//判断浏览器类型
{
    if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
    else
        return false;
};
/**
 * 获取iframe高度设置页面高度
 * @param type
 * @param resize
 */
var setIframeHeight = function (type, resize) {
    let iframe = document.getElementById('frame_c');
    let subHeight = isIE() ? 130 : 150;
    subHeight += checkGlobal().w > 600 ? 0 : 20;
    if (iframe) {
        let clientH = document.body.clientHeight - subHeight;
        iframe.height = clientH;

        if (resize) {
            let settingFrame = iframe.contentDocument.getElementById('frame_setting');
            if (settingFrame) {
                setSettingIframeHeight();
            }
        }
        iframe.onload = function () {
            let realH = iframe.contentDocument.body.clientHeight;
            realH > clientH ? iframe.height = realH : "";
            let settingFrame = iframe.contentDocument.getElementById('frame_setting');
            if (settingFrame) {
                setSettingIframeHeight();
            }
        };
    }

};
/**
 * 设置iframe的高度
 */
var setSettingIframeHeight = function () {
    let iframe = document.getElementById('frame_c');
    let cIframe = iframe.contentDocument.getElementById("frame_setting");
    let subHeight = isIE() ? 130 : 150;
    subHeight += checkGlobal().w > 600 ? 0 : 20;
    if (iframe && cIframe) {
        let clientH = document.body.clientHeight - subHeight;
        cIframe.height = clientH;
        cIframe.onload = function () {
            let realH = cIframe.contentDocument.body.scrollHeight;
            realH > clientH ? iframe.height = realH : "";
        };
        let navScroll = iframe.contentDocument.getElementsByClassName("my-scroll");
        $(navScroll).css({
            height: clientH - 50
        })
    }
    ;
};
// //获取iframe高度设置页面高度
// var setIframeHeight = function () {
//     let iframe = document.getElementById('frame_c');
//     let subHeight = 125;
//     if (iframe) {
//         iframe.height = document.body.clientHeight - subHeight;
//         let iframeWin = iframe.contentWindow || iframe.contentDocument.parentWindow;
//         if (iframeWin.document.body) {
//             let h = iframeWin.document.documentElement.offsetHeight || iframeWin.document.body.offsetHeight;
//             iframe.height = iframe.height < h ? h : iframe.height
//         }
//     }
//     let settingFrame = iframe.contentDocument.getElementById('frame_setting');
//     if (settingFrame) {
//         setSettingIframeHeight();
//     }
// };
// /**
//  * 设置iframe的高度
//  */
// var setSettingIframeHeight = function () {
//     let iframe = document.getElementById('frame_c');
//     let cIframe = iframe.contentDocument.getElementById("frame_setting");
//     if (iframe && cIframe) {
//         iframe.height = window.parent.document.body.clientHeight < window.parent.document.documentElement.clientHeight ? window.parent.document.documentElement.clientHeight : window.parent.document.body.clientHeight;
//         cIframe.height = iframe.height - 125;
//         let iframeWin = cIframe.contentWindow || cIframe.contentDocument.parentWindow;
//         if (iframeWin.document.body) {
//             let h = iframeWin.document.documentElement.offsetHeight || iframeWin.document.body.offsetHeight;
//             // let w = iframeWin.document.documentElement.offsetWidth || iframeWin.document.body.offsetWidth;
//             // let globalInfo = getSe('globalInfo'));
//             // let f = $("#frame_setting").contents();
//             // setSize(globalInfo.w,globalInfo.h,f);
//             // globalInfo.w = w / 12 * 7;
//             // globalInfo.h = globalInfo.w / 4 * 3;
//             // sessionStorage.setItem('globalInfo',JSON.stringify(globalInfo))
//             iframe.height = iframe.height < h ? (h - 15) : iframe.height;
//         }
//         let navScroll = iframe.contentDocument.getElementsByClassName("my-scroll");
//         $(navScroll).css({
//             height: cIframe.height
//         })
//     }
// };

/**
 * 获取不同语言环境下的config文件
 * rpm.json的文件不做处理
 * @param url
 * @param type
 * @param callback
 */
var onloadConfig = function (url, type, callback) {
    if (type !== "rpmConfig") {
        let i18n = getCookie('i18ns');
        if (i18n === "en") {
            let tempUrl = url.slice(0, url.lastIndexOf("."));
            url = tempUrl + "_en.json";
        }
    }
    $.ajax({
        //请求方式
        type: "GET",
        //文件位置
        url: url,
        //返回数据格式为json,也可以是其他格式如
        dataType: "json",
        cache: false,//避免ie从缓存中获取数据文件,相当于在请求时添加随机数作为请求条件
        //请求成功后要执行的函数，拼接html
        success: function (allData) {
            // console.log(allData);
            setSe(type, allData);
            if (callback) {
                callback(true);
            }
        },
        error: function () {
            if (callback) {
                callback(false);
            }
        }
    });
};
/**
 * 获取车型文件
 * @param url
 * @param callback
 */
var getCarType = function (url, callback) {
    $.ajax({
        //请求方式
        type: "GET",
        //文件位置
        url: url,
        //返回数据格式为json,也可以是其他格式如
        dataType: "json",
        //请求成功后要执行的函数，拼接html
        success: function (allData) {
            let carBrand = new Map();
            let carType = new Map();
            let brand = allData.carBrand;
            for (let i = 0; i < brand.length; i++) {
                let j = parseInt(brand[i].code);
                carBrand.set(j, brand[i].desc);
            }
            let type = allData.carType;
            for (let i = 0; i < type.length; i++) {
                let j = type[i].code;
                let brand = carBrand.get(parseInt(j / 10000));
                carType.set(j, brand + " " + type[i].desc);
            }
            setSe("carDetailType", carType);
            callback(true)
        },
        error: function () {
            if (callback) {
                callback(false);
            }
        }
    });
};
/**
 * 初始化logo大小和底部信息
 */
var initCopy = function () {
    let copyR = getSe('projectConfig').copyright;
    $("#copyright").html(copyR);
    let logoSize = getSe('projectConfig').logo_size;
    let logo = $(".layui-logo");
    if (logo.length) {
        if (logoSize === 's') {
            logo.html('<div style="margin: 0 auto;height: 60px;line-height: 60px;background: url(../img/index_logo.png);background-repeat: no-repeat;background-size:80% 70%;-moz-background-size:100% 100%;background-position: center;"></div>');
            logo.css({
                lineHeight: '60px'
            })
        } else {
            logo.css({
                height: '60px',
                background: 'url(../img/index_logo.png)',
                backgroundRepeat: 'no-repeat'
            })
        }
    }
};
/**
 * 延时函数
 * @param delay
 */
var sleep = function (delay) {
    for (let t = Date.now(); Date.now() - t <= (delay * 1000);) {
    }
};

/**
 * 格式化时间
 * @param fmt
 * @returns {*}
 * @constructor
 */
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "D+": this.getDate(), //日
        "H+": this.getHours(), //小时
        "h+": this.getHours() > 12 ? this.getHours() - 12 : this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds(), //毫秒
        "t+": this.getHours() >= 12 ? 'pm' : 'am'
    };
    if (/(Y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    if (/(w+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (getWeek(this.getDay()) + ""));
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
    return fmt;
};

/**
 * @description arg1*arg2，保留两者的小数点
 * @param arg1
 * @param arg2
 * @returns {number}
 */
var accMul = function (arg1, arg2) {
    let m = 0, s1 = arg1.toString(), s2 = arg2.toString();
    try {
        m += s1.split(".")[1].length;
    } catch (e) {
    }
    try {
        m += s2.split(".")[1].length;
    } catch (e) {
    }
    return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
};
/**
 * 比较版本号，strict表示是否严格比较
 * @param v1
 * @param v2
 * @param len
 * @param strict
 * @returns {number}
 */
var compareVersion = function (v1, v2, len, strict) {
    if (v1 === v2) {
        return 0;
    }
    let version1Array = v1.split(".");
    let version2Array = v2.split(".");
    if (version1Array[0] !== version2Array[0]) {
        return -1
    }
    let index = 0;
    let mLen = Math.min(version1Array.length, version2Array.length);
    let minLen = len || mLen;
    minLen = (minLen > mLen ? mLen : minLen);
    let diff = 0;

    while (index < minLen
    && (diff = parseInt(version1Array[index])
        - parseInt(version2Array[index])) === 0) {
        index++;
    }
    if (diff === 0) {
        if (len && index === len) {
            return diff;
        }
        for (let i = index; i < version1Array.length; i++) {
            if (parseInt(version1Array[i]) >= 0) {
                return 1;
            }
        }

        for (let i = index; i < version2Array.length; i++) {
            if (parseInt(version2Array[i]) >= 0) {
                return -1;
            }
        }
        return 0;
    } else {
        if (strict) {
            return -1
        }
        return diff > 0 ? 1 : -1;
    }
};
var getCGI = function (data) {
    $.ajax({
        url: "https://" + location.hostname + "/cgi-bin/main.cgi",
        contentType: "application/x-www-form-urlencoded",
        data: data,
        method: "POST",
        success: function (res) {
            console.log(res)
        },
        error: function (e) {
            console.log(e)
        }
    })
};
/**
 * 获取文件名
 * @param file
 * @returns {string}
 */
var getFileName = function (file) {
    let pos = file.lastIndexOf("\\"); //查找最后一个\的位置
    return file.substring(pos + 1); //截取最后一个\位置到字符长度，也就是截取文件名
};

/**
 * 防抖函数
 * @param fn
 * @param s
 * @returns {Function}
 */
var debounce = function (fn, s) {
    let timeout = null;
    return function () {
        let that = this;
        let args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(function () {
            fn.apply(that, args);
        }, s)
    }
};
/**
 * 节流函数
 * @param fn
 * @param s
 * @returns {Function}
 */
var throttle = function (fn, s) {
    let timeout = null;
    return function () {
        let that = this;
        let args = arguments;
        if (!timeout) {
            timeout = setTimeout(function () {
                fn.apply(that, args);
                clearTimeout(timeout);
                timeout = null;
            }, s)
        }
    }
};
// 数组去重
var unique = function (arr) {
    for (let i = 0; i < arr.length; i++) {
        for (let j = i + 1; j < arr.length; j++) {
            if (arr[i] === arr[j]) { // 第一个等同于第二个，splice方法删除第二个
                arr.splice(j, 1)
                j--
            }
        }
    }
    return arr
};
// 排序 升序
var ascSort = function (arr) {
    var compare = function (x, y) { // 比较函数
        if (x < y) {
            return -1
        } else if (x > y) {
            return 1
        } else {
            return 0
        }
    }
    return arr.sort(compare)
};
// 排序 降序
var descSort = function (arr) {
    var compare = function (x, y) {
        if (x < y) {
            return 1
        } else if (x > y) {
            return -1
        } else {
            return 0
        }
    }
    return arr.sort(compare)
};
/**
 * @description    根据某个字段实现对json数组的排序
 * @param   json  要排序的json数组对象
 * @param   key  根据key值排序，key为string类型
 * @param   reverse 是否倒序（默认为false）
 * @return  json  返回排序后的json数组
 */
var sortTableData = function (json, key, reverse) {
    var ascSort = function (x, y) {
        return (x[key] > y[key]) ? 1 : -1
    }
    var descSort = function (x, y) {
        return (x[key] < y[key]) ? 1 : -1
    }
    if (reverse) {
        return json.sort(descSort)
    } else {
        return json.sort(ascSort)
    }
}
var getAppName = function () {
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        return getSe("rpmConfig").app_name;
    } else {
        // const path = '/SDCWEB/TOPSKY_T_HOLO_M/html/setting_frame/setting_line_survey.html'
        const path = location.pathname
        const appReg = /\/SDCWEB\/(.*)\//
        const exec = appReg.exec(path)
        let appName = false
        if (exec) {
            let path1 = appReg.exec(path)[1]
            appName = path1.substring(0, path1.indexOf('/'))
        }
        return appName
    }
}
var getRouterUrl = function () {
    let appName = getAppName()
    return "/SDCAPI/V1.0/" + appName + "/";

    // return getSe("rpmConfig").router_url;
}
