<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>功能说明</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_description_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="Function_Description_txt1" class="title">二、功能说明</h2>
    <div class="content">
        <p id="Function_Description_txt2">
            在浏览器界面输入相机IP，并加上参数。http://*************，用户名admin，密码12345。
        </p>
        <p id="Function_Description_txt3" style="color:red">
            语言可选中文或英文（此处为中文版）.
        </p>

        <img id="F_D_img1" src="./image001.png" alt="登录界面"/>
        <p id="Function_Description_txt4">登录之后首先进入的是预览界面，如果是第一次进入相机，会要求安装插件，只有安装了插件之后才能看到相机抓拍的画面。</p>
        <img id="F_D_img2" src="./image003.png" alt="预览界面"/>
    </div>
</div>
</body>
</html>