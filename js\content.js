$(document).ready(function () {
    let user = sessionStorage.getItem("loginUser");
    if (!user) {
        let source = getTopWindow();
        source.location.href = "http://" + location.host + "/index.html";
    }
    onloadConfig('../config/config.json', 'projectConfig', initCopy);
    setTimeout(setIframeHeight, 500);
    let childWindow = $("#frame_c")[0].contentWindow;//获得子页面 (#frame_c是个iframe)
    let clickIndex = 0;

    // 新版逻辑-华为版本不显示授权

    let appName = getAppName()
    let allowed = getSe("projectConfig").allowedAuth;
    if (allowed.indexOf(appName) < 0) {
        $('#authorize').parent('li').remove();
    }


    $(".layui-header .layui-nav-item").off("click").on("click", function (e) {//菜单被点击才触发
        let playFlag = false, playChannel = 'ch1';
        let eventFlag = false;
        let ocxExist = childWindow.document.getElementById("ocx");//ocx 在ocxGlobal.js中创建
        let fileInit = getSe("fileInit") || {result: false};//在ocxGlobal.js中，initialize_ocx()方法,setSe("fileInit", fileInit)
        ocxExist = $(ocxExist);
        if (clickIndex === 0) {
            if (childWindow.$("#receiveEvent").length && childWindow.$("#receiveEvent")[0].checked === true) {//#receiveEvent 是content_event.html中的dom
                eventFlag = true;
            }
            // childWindow.close();
        }
        if (ocxExist.length && playFlag && fileInit.result) {
            childWindow.removeVideo(playChannel);
        }
        if (ocxExist.length && eventFlag) {
            //childWindow.stopThread();
        }
        if (ocxExist.length) {
            console.log("ocxLength:" + ocxExist.length);
            ocxExist.addClass("common-none");
            ocxExist.remove();
            console.log("after remove ocxLength:" + childWindow.$("#ocx").length)
        }
        let node = getDataset(e.currentTarget.children[0]).href;
        $("#frame_c").attr("src", node);
        clickIndex = $(this).index();

        // 立即设置导航高亮状态
        let type = null;
        if (node.indexOf('help') > -1) {
            type = 'help';
            setNavValue(-1);//根据index设置导航选中状态
        } else {
            // 设置正确的导航高亮状态
            setNavValue(clickIndex);
        }
        setIframeHeight(type)
    });
    window.onresize = function () {
        let type = "";
        let index = $(".layui-nav-item").index($(".layui-this"));
        if (index < 0) {
            type = 'help'
        }
        setIframeHeight(type, true);
    };
});
//监听页面刷新根据不同页面进行操作
window.onbeforeunload = function () {
    console.log("content");
    let childWindow = $("#frame_c")[0].contentWindow;
    childWindow.$(".layui-nav-item").removeClass("layui-this");
    if (childWindow.$("#eventTable").length) {//#eventTable content_event_new.html
        childWindow.$($(".layui-nav-item")[0]).addClass("layui-this");
    }
    if (childWindow.$("#container").length) {//#container content_picture.html
        childWindow.$($(".layui-nav-item")[1]).addClass("layui-this");
    }
    if (childWindow.$("#settingFrame").length) {//#settingFrame content_setting.html
        childWindow.$($(".layui-nav-item")[2]).addClass("layui-this");
    }
    if (childWindow.$("#authContainer").length) {//#authContainer content_authorization.html
        childWindow.$($(".layui-nav-item")[3]).addClass("layui-this");
    }
    let ocxExist = childWindow.$("#ocx");
    let eventTime = getSe("eventTime");
    let fileInit = getSe("fileInit") || {result: false};
    if (ocxExist.length) {
        let receiveEvent = childWindow.$("#receiveEvent")[0];
        if (ocxExist != null && fileInit.result) {
            let ch = getDataset(childWindow.$("#videoT1Value")[0]).channel;
            childWindow.removeVideo(ch);
        }
        // if (ocxExist != null && childWindow.$("#eventVideo").length && fileInit.result) {
        //     let ch = getDataset(childWindow.$("#videoT1Value")[0]).channel;
        //     childWindow.removeVideo(ch);
        // }
        // if (ocxExist != null && childWindow.$("#indexVideo").length && fileInit.result) {
        //     let channel = '1';
        //     childWindow.removeVideo(channel);
        // }

        // if (receiveEvent && receiveEvent.checked === true) {
        //     console.log("ocx.STOPThread");
        //     let ret = ocxExist[0].STOPThread();
        //     console.log("STOPThread:" + ret);
        // }
        console.log("ocxLength:" + ocxExist.length);
        ocxDestroy(childWindow);
        ocxExist.addClass("common-none");
        ocxExist.remove();
        console.log("after remove ocxLength:" + childWindow.$("#ocx").length);
    } else {
        appendOcx();
        ocxDestroy();
    }
    // window.location.href = 'login.html'
};

/**
 * 根据index设置导航选中状态
 * @param index
 */
var setNavValue = function (index) {
    $(".layui-nav-item").removeClass("layui-this");
    $($(".layui-nav-item")[index]).addClass("layui-this");
};
