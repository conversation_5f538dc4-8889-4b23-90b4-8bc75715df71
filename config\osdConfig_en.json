﻿{
  "osdInfo": [
    {
      "value": 0,
      "title": "License plate number：",
      "default": "苏A123S6",
      "type": "License plate number",
      "checked": 0,
      "filter": "cphm",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 1,
      "title": "Illegal act：",
      "default": "Violation of the red light",
      "type": "Illegal act",
      "checked": 0,
      "filter": "wfxw",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 50,
      "y": 50
    },
    {
      "value": 2,
      "title": "time：",
      "default": "2000/01/01 00:00:00:000",
      "type": "time",
      "checked": 0,
      "filter": "sj",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 3,
      "title": "License plate color：",
      "default": "Blue card",
      "type": "License plate color",
      "checked": 0,
      "filter": "cpys",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 4,
      "title": "Vehicle Type：",
      "default": "car",
      "type": "Vehicle Type",
      "checked": 0,
      "filter": "cllx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 5,
      "title": "location：",
      "default": "XXintersection",
      "type": "location",
      "checked": 0,
      "filter": "dd",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 6,
      "title": "Lane number：",
      "default": "0",
      "type": "Lane number",
      "checked": 0,
      "filter": "cdh",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 7,
      "title": "Lane direction：",
      "default": "straight",
      "type": "Lane direction",
      "checked": 0,
      "filter": "cdfx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 8,
      "title": "Direction of travel：",
      "default": "Turn right",
      "type": "Direction of travel",
      "checked": 0,
      "filter": "xsfx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 9,
      "title": "Red light on time：",
      "default": "60 seconds",
      "type": "Red light on time",
      "checked": 0,
      "filter": "hdlsj",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 10,
      "title": "Road name：",
      "default": "XXXXXX Road",
      "type": "Road name",
      "checked": 0,
      "filter": "dlmc",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 11,
      "title": "speed：",
      "default": "120",
      "type": "speed",
      "checked": 0,
      "filter": "cs",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 12,
      "title": "High and low speed limit：",
      "default": "60-120",
      "type": "High and low speed limit",
      "checked": 0,
      "filter": "gdxs",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 13,
      "title": "Equipment number：",
      "default": "ABC123456789",
      "type": "Equipment number",
      "checked": 0,
      "filter": "sbbh",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 14,
      "title": "Vehicle colour：",
      "default": "black",
      "type": "Vehicle colour",
      "checked": 0,
      "filter": "csys",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 15,
      "title": "Vehicle brand：",
      "default": "XXX brand",
      "type": "Vehicle brand",
      "checked": 0,
      "filter": "clpp",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 16,
      "title": "Illegal code：",
      "default": "010",
      "type": "Illegal code",
      "checked": 0,
      "filter": "wfdm",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 17,
      "title": "Custom 1：",
      "default": "",
      "type": "Custom 1",
      "checked": 0,
      "filter": "zdy1",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 18,
      "title": "Custom 2：",
      "default": "",
      "type": "Custom 2",
      "checked": 0,
      "filter": "zdy2",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 19,
      "title": "Custom 3：",
      "default": "",
      "type": "Custom 3",
      "checked": 0,
      "filter": "zdy3",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    }
  ],
  "dateFormat": {
    "0": "YYYY/MM/DD",
    "1": "DD/MM/YYYY",
    "2": "YYYY-MM-DD",
    "3": "DD-MM-YYYY"
  },
  "timeFormat": {
    "0": "24 hour system",
    "1": "12 hour system"
  },
  "fontSize":{
    "0":16,
    "1":24,
    "2":32,
    "3":48,
    "4":64,
    "5":72,
    "6":88,
    "7":96,
    "8":128
  },
  "osdPosition":{
    "0":"On the picture",
    "1":"In the picture",
    "2":"Below the picture"
  },
  "pixel":8
}