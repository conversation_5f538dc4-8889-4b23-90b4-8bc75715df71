<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>settingSignal</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/DomOperation.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/settingDraw.js"></script>
    <script src="../../js/drawCanvas.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/settingSignal.js"></script>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-show">
    <div class="layui-row">
        <div class="" id="drawContent">
            <img id="configImg" src="../../img/404.png" class="draw-img"/>
            <canvas id="signalCan" width="600" height="450" class="draw-canvas">
            </canvas>
            <div id="drawSignal" class="draw-container">
            </div>
        </div>
        <div class="layui-col-md4 config-setting" style="padding-left: 10px">
            <button class="layui-btn layui-btn-default" id="refreshPic"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">刷新场景
            </button>
            <button id="saveConfig" class="layui-btn layui-btn-default layui-btn-block"
                    onclick="saveConfig('drawSignal')" style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                保存配置
            </button>
            <button id="clearNowConfig" class="layui-btn layui-btn-default" onclick="clearNowConfig()"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">清除当前绘制区域
            </button>
            <button id="showConfig" class="layui-btn layui-btn-default layui-btn-block"
                    onclick="showConfig('drawSignal')" style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                重置配置
            </button>

            <div class="layui-collapse layui-form" lay-accordion
                 style="margin-top: 10px;max-width: 550px;min-width: 450px">
                <div class="layui-colla-item">
                    <h2 id="lightInfo" class="layui-colla-title">信号灯信息</h2>
                    <div class="layui-colla-content layui-show">
                        <div class="config-control">
                            <div>
                                <input type="checkbox" class="check-signal" title="使能红绿灯判断" id="signalJudge"
                                       lay-skin="primary" value="1">
                            </div>
                            <div style="width: 100%;margin-top: 10px;margin-bottom: 10px">
                                <label id="extendSignalLabel" class="layui-form-label"
                                       style="width: 115px;">外接红绿灯信号：</label>
                                <div class="layui-input-inline">
                                    <select id="extendSignal" lay-filter="extendSignal">
                                        <option value="0">不接入</option>
                                        <option value="1">前端奕天</option>
                                        <option value="2">衍之辰</option>
                                        <option value="3">华为平台</option>
                                        <option value="4">竞天</option>
                                    </select>
                                </div>
                            </div>
                            <div style="width: 100%;margin-top: 10px;margin-bottom: 10px">
                                <label id="RS485Label" class="layui-form-label"
                                       style="width: 115px;">RS485：</label>
                                <div class="layui-input-inline">
                                    <select id="RS485" lay-filter="RS485">
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                    </select>
                                </div>
                            </div>
                            <div style="width: 100%;margin-top: 10px;margin-bottom: 10px">
                                <label id="signalIPLabel" class="layui-form-label" style="width: 115px;">IP地址：</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="title" class="layui-input" id="signalIP">
                                </div>
                            </div>
                            <table class="extend-signal">
                                <tr>
                                    <td id="left">左</td>
                                    <td id="straight">直</td>
                                    <td id="right">右</td>
                                    <td id="pedestrian">人行</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline">
                                                <select id="signalLeftNumber" lay-filter="signalLeftNumber"
                                                        disabled="disabled">
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                    <option value="16">16</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline">
                                                <select id="signalStraightNumber" lay-filter="signalStraightNumber"
                                                        disabled="disabled">
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                    <option value="16">16</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline">
                                                <select id="signalRightNumber" lay-filter="signalRightNumber"
                                                        disabled="disabled">
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                    <option value="16">16</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline">
                                                <select id="signalPeopleNumber" lay-filter="signalPeopleNumber"
                                                        disabled="disabled">
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                    <option value="16">16</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <br/>
                            <div style="width: 100%;margin-top: 10px;margin-bottom: 10px">
                                <label id="delay" class="layui-form-label" style="width: 115px;">闯红灯延时判断：</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="title" class="layui-input" placeholder="秒" id="signalTime">
                                </div>
                            </div>
                            <br/>
                            <table style="width: 60%;">
                                <tr>
                                    <td><input type="checkbox" lay-filter="signalStronger" title="红灯加强"
                                               id="signalStronger"
                                               lay-skin="primary" value="1"></td>
                                    <td class="signal-stronger dis">
                                        <div class="layui-form-item">
                                            <div class="layui-input-inline">
                                                <select id="strongerNum" lay-filter="signalPeopleNumber"
                                                        disabled="disabled">
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                </select>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="signal-stronger dis">
                                    <td>
                                        <label class="layui-form-label begin" style="width: 40px">从：</label>
                                        <div class="layui-inline" style="width: 100px"> <!-- 注意：这一层元素并不是必须的 -->
                                            <input type="text" class="layui-input" id="StrongStartTime" disabled>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="layui-form-label to" style="width: 40px">到：</label>
                                        <div class="layui-inline" style="width: 100px"> <!-- 注意：这一层元素并不是必须的 -->
                                            <input type="text" class="layui-input" id="StrongEndTime" disabled>
                                        </div>
                                    </td>
                                </tr>
                                <!--<tr class="signalStronger">-->
                                <!--<td></td>-->
                                <!--<td>-->
                                <!---->
                                <!--</td>-->
                                <!--</tr>-->
                            </table>
                            <table style="width: 60%;">
                                <tr>
                                    <td colspan="3"><input type="checkbox" lay-filter="signalYellow" title="夜晚黄灯时间设置"
                                                           id="signalYellow"
                                                           lay-skin="primary" value="1"></td>
                                </tr>
                                <tr class="signal-yellow dis">
                                    <td>
                                        <label class="layui-form-label begin" style="width: 30px">从：</label>
                                        <div class="layui-inline" style="width: 100px"> <!-- 注意：这一层元素并不是必须的 -->
                                            <input type="text" class="layui-input" id="YellowStartTime" disabled>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="layui-form-label to" style="width: 30px">到：</label>
                                        <div class="layui-inline" style="width: 100px"> <!-- 注意：这一层元素并不是必须的 -->
                                            <input type="text" class="layui-input" id="YellowEndTime" disabled>
                                        </div>
                                    </td>
                                </tr>
                                <!--<tr class="signalYellow">-->
                                <!--<td><label class="layui-form-label">到：</label></td>-->
                                <!--<td>-->
                                <!--<div class="layui-inline"> &lt;!&ndash; 注意：这一层元素并不是必须的 &ndash;&gt;-->
                                <!--<input type="text" class="layui-input" id="YellowEndTime" disabled>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--</tr>-->
                            </table>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="draw" class="layui-colla-title">绘制信号灯</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-block" id="biggerBtn"
                                    onclick="FixedRect('bigger','dashed','signal')">局部放大
                            </button>
                            <!--<button class="layui-btn layui-btn-default layui-btn-block"-->
                            <!--onclick="Amplify('signal')">放大-->
                            <!--</button>-->
                            <button class="layui-btn layui-btn-default layui-btn-block" id="signalBtn"
                                    onclick="FixedRectGroup('signalBig',true)">绘制信号灯
                            </button>
                            <div class="layui-form-item">
                                <label id="num" for="signalNumber">信号灯数：</label>
                                <div class="layui-input-inline">
                                    <select id="signalNumber" lay-filter="signalNumber">
                                        <option value="0">0</option>
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                        <option value="5">5</option>
                                        <option value="6">6</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item common-none">
                                <label for="signalLineType" id="style">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="signalLineType">
                                        <option value="solid" id="solid">实线</option>
                                        <option value="dashed" id="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="signalColor">
                                </div>
                                <div class="layui-inline" style="left: -11px;">
                                    <div id="signalColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="signal-group signal-group1">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="signal-group signal-group2">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="signal-group signal-group3">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="signal-group signal-group4">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="signal-group signal-group5">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="signal-group signal-group6">
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X1："></div>
                                    <div class="input-range" data-label="Y1："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y1：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="signal-point index-point">
                                    <div class="input-range" data-label="X2："></div>
                                    <div class="input-range" data-label="Y2："></div>
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">X2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                    <!--<table class="input-table">-->
                                    <!--<tr>-->
                                    <!--<td rowspan="2">Y2：：</td>-->
                                    <!--<td rowspan="2"><input type="text" value="0"-->
                                    <!--class="input-number"-->
                                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                                    <!--</td>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-up"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-up"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--<tr>-->
                                    <!--<td>-->
                                    <!--<button type="button" class="input-down"-->
                                    <!--disabled="disabled"><i-->
                                    <!--class="button-edge button-down"></i>-->
                                    <!--</button>-->
                                    <!--</td>-->
                                    <!--</tr>-->
                                    <!--</table>-->
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="define" class="layui-colla-title">信号灯定义</h2>
                    <div class="layui-colla-content ">
                        <div id="tableInfo" style="margin-top: 10px">
                            <table id="signalTable" class="table">
                                <tbody id="signalInfo">
                                <!--<tr id="type_1">-->
                                <!--<td id="g16" class="signal-num dis">灯1</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_1 signal-type dis"-->
                                <!--style="width: 50px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g17" value="0">单头灯</option>-->
                                <!--<option id="g18" value="2">三头灯</option>-->
                                <!--<option id="g19" value="3">四头灯</option>-->
                                <!--<option id="g20" value="1">强制长红</option>-->
                                <!--<option id="g21" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_1 signal-type dis"-->
                                <!--style="width: 50px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g22" value="0x0001">左行</option>-->
                                <!--<option id="g23" value="0x0002">直行</option>-->
                                <!--<option id="g24" value="0x0004">右行</option>-->
                                <!--<option id="g25" value="0x0008">掉头</option>-->
                                <!--<option id="g26" value="0x0003">左直</option>-->
                                <!--<option id="g27" value="0x0006">右直</option>-->
                                <!--<option id="g28" value="0x0009">左掉头</option>-->
                                <!--<option id="g29" value="0x0005">左右</option>-->
                                <!--<option id="g30" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g31" value="0x000b">左直掉头</option>-->
                                <!--<option id="g32" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_1 signal-type dis"-->
                                <!--style="width: 50px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g33" value="0">圆形灯</option>-->
                                <!--<option id="g34" value="1">数字灯</option>-->
                                <!--<option id="g35" value="2">箭头灯</option>-->
                                <!--<option id="g36" value="3">左转掉头灯</option>-->
                                <!--<option id="g37" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_1 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g38" value="0">机动车信号灯</option>-->
                                <!--<option id="g39" value="1">车道信号灯</option>-->
                                <!--<option id="g40" value="2">方向指示信号灯</option>-->
                                <!--<option id="g41" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g42" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g43" value="5">非机动车信号灯</option>-->
                                <!--<option id="g44" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g45" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                                <!--<tr id="type_2">-->
                                <!--<td id="11" class="signal-num dis">灯2</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_2 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g46" value="0">单头灯</option>-->
                                <!--<option id="g47" value="2">三头灯</option>-->
                                <!--<option id="g48" value="3">四头灯</option>-->
                                <!--<option id="g49" value="1">强制长红</option>-->
                                <!--<option id="g50" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_2 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g51" value="0x0001">左行</option>-->
                                <!--<option id="g52" value="0x0002">直行</option>-->
                                <!--<option id="g53" value="0x0004">右行</option>-->
                                <!--<option id="g54" value="0x0008">掉头</option>-->
                                <!--<option id="g55" value="0x0003">左直</option>-->
                                <!--<option id="g56" value="0x0006">右直</option>-->
                                <!--<option id="g57" value="0x0009">左掉头</option>-->
                                <!--<option id="g58" value="0x0005">左右</option>-->
                                <!--<option id="g59" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g60" value="0x000b">左直掉头</option>-->
                                <!--<option id="g61" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_2 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g62" value="0">圆形灯</option>-->
                                <!--<option id="g63" value="1">数字灯</option>-->
                                <!--<option id="g64" value="2">箭头灯</option>-->
                                <!--<option id="g65" value="3">左转掉头灯</option>-->
                                <!--<option id="g66" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_2 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g67" value="0">机动车信号灯</option>-->
                                <!--<option id="g68" value="1">车道信号灯</option>-->
                                <!--<option id="g69" value="2">方向指示信号灯</option>-->
                                <!--<option id="g70" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g71" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g72" value="5">非机动车信号灯</option>-->
                                <!--<option id="g73" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g74" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                                <!--<tr id="type_3">-->
                                <!--<td id="12" class="signal-num dis">灯3</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_1 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g75" value="0">单头灯</option>-->
                                <!--<option id="g76" value="2">三头灯</option>-->
                                <!--<option id="g77" value="3">四头灯</option>-->
                                <!--<option id="g78" value="1">强制长红</option>-->
                                <!--<option id="g79" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_3 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g80" value="0x0001">左行</option>-->
                                <!--<option id="g81" value="0x0002">直行</option>-->
                                <!--<option id="g82" value="0x0004">右行</option>-->
                                <!--<option id="g83" value="0x0008">掉头</option>-->
                                <!--<option id="g84" value="0x0003">左直</option>-->
                                <!--<option id="g85" value="0x0006">右直</option>-->
                                <!--<option id="g86" value="0x0009">左掉头</option>-->
                                <!--<option id="g87" value="0x0005">左右</option>-->
                                <!--<option id="g88" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g89" value="0x000b">左直掉头</option>-->
                                <!--<option id="g90" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_3 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g91" value="0">圆形灯</option>-->
                                <!--<option id="g92" value="1">数字灯</option>-->
                                <!--<option id="g93" value="2">箭头灯</option>-->
                                <!--<option id="g94" value="3">左转掉头灯</option>-->
                                <!--<option id="g95" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_3 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g96" value="0">机动车信号灯</option>-->
                                <!--<option id="g97" value="1">车道信号灯</option>-->
                                <!--<option id="g98" value="2">方向指示信号灯</option>-->
                                <!--<option id="g99" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g100" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g101" value="5">非机动车信号灯</option>-->
                                <!--<option id="g102" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g103" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                                <!--<tr id="type_4">-->
                                <!--<td id="13" class="signal-num dis">灯4</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_4 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g104" value="0">单头灯</option>-->
                                <!--<option id="g105" value="2">三头灯</option>-->
                                <!--<option id="g106" value="3">四头灯</option>-->
                                <!--<option id="g107" value="1">强制长红</option>-->
                                <!--<option id="g108" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_4 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g109" value="0x0001">左行</option>-->
                                <!--<option id="g110" value="0x0002">直行</option>-->
                                <!--<option id="g111" value="0x0004">右行</option>-->
                                <!--<option id="g112" value="0x0008">掉头</option>-->
                                <!--<option id="g113" value="0x0003">左直</option>-->
                                <!--<option id="g114" value="0x0006">右直</option>-->
                                <!--<option id="g115" value="0x0009">左掉头</option>-->
                                <!--<option id="g116" value="0x0005">左右</option>-->
                                <!--<option id="g117" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g118" value="0x000b">左直掉头</option>-->
                                <!--<option id="g119" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_4 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g120" value="0">圆形灯</option>-->
                                <!--<option id="g121" value="1">数字灯</option>-->
                                <!--<option id="g122" value="2">箭头灯</option>-->
                                <!--<option id="g123" value="3">左转掉头灯</option>-->
                                <!--<option id="g124" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_4 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g125" value="0">机动车信号灯</option>-->
                                <!--<option id="g126" value="1">车道信号灯</option>-->
                                <!--<option id="g127" value="2">方向指示信号灯</option>-->
                                <!--<option id="g128" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g129" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g130" value="5">非机动车信号灯</option>-->
                                <!--<option id="g131" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g132" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                                <!--<tr id="type_5">-->
                                <!--<td id="14" class="signal-num dis">灯5</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_5 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g133" value="0">单头灯</option>-->
                                <!--<option id="g134" value="2">三头灯</option>-->
                                <!--<option id="g135" value="3">四头灯</option>-->
                                <!--<option id="g136" value="1">强制长红</option>-->
                                <!--<option id="g137" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_5 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g138" value="0x0001">左行</option>-->
                                <!--<option id="g139" value="0x0002">直行</option>-->
                                <!--<option id="g140" value="0x0004">右行</option>-->
                                <!--<option id="g141" value="0x0008">掉头</option>-->
                                <!--<option id="g142" value="0x0003">左直</option>-->
                                <!--<option id="g143" value="0x0006">右直</option>-->
                                <!--<option id="g144" value="0x0009">左掉头</option>-->
                                <!--<option id="g145" value="0x0005">左右</option>-->
                                <!--<option id="g146" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g147" value="0x000b">左直掉头</option>-->
                                <!--<option id="g148" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_5 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g149" value="0">圆形灯</option>-->
                                <!--<option id="g150" value="1">数字灯</option>-->
                                <!--<option id="g151" value="2">箭头灯</option>-->
                                <!--<option id="g152" value="3">左转掉头灯</option>-->
                                <!--<option id="g153" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_5 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g154" value="0">机动车信号灯</option>-->
                                <!--<option id="g155" value="1">车道信号灯</option>-->
                                <!--<option id="g156" value="2">方向指示信号灯</option>-->
                                <!--<option id="g157" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g158" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g159" value="5">非机动车信号灯</option>-->
                                <!--<option id="g160" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g161" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                                <!--<tr id="type_6">-->
                                <!--<td id="15" class="signal-num dis">灯6</td>-->
                                <!--<td class="checkInfo">-->
                                <!--<div>-->
                                <!--<select class="type_6 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g162" value="0">单头灯</option>-->
                                <!--<option id="g163" value="2">三头灯</option>-->
                                <!--<option id="g164" value="3">四头灯</option>-->
                                <!--<option id="g165" value="1">强制长红</option>-->
                                <!--<option id="g166" value="4">仅加强</option>-->
                                <!--</select>-->
                                <!--</div>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_6 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g167" value="0x0001">左行</option>-->
                                <!--<option id="g168" value="0x0002">直行</option>-->
                                <!--<option id="g169" value="0x0004">右行</option>-->
                                <!--<option id="g170" value="0x0008">掉头</option>-->
                                <!--<option id="g171" value="0x0003">左直</option>-->
                                <!--<option id="g172" value="0x0006">右直</option>-->
                                <!--<option id="g173" value="0x0009">左掉头</option>-->
                                <!--<option id="g174" value="0x0005">左右</option>-->
                                <!--<option id="g175" value="0x000f">左直右掉头</option>-->
                                <!--<option id="g176" value="0x000b">左直掉头</option>-->
                                <!--<option id="g177" value="0x000d">左右掉头</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_6 signal-type dis"-->
                                <!--style="width: 80px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g178" value="0">圆形灯</option>-->
                                <!--<option id="g179" value="1">数字灯</option>-->
                                <!--<option id="g180" value="2">箭头灯</option>-->
                                <!--<option id="g181" value="3">左转掉头灯</option>-->
                                <!--<option id="g182" value="4">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--<td>-->
                                <!--<select class="type_6 signal-type dis"-->
                                <!--style="width: 120px" disabled="disabled"-->
                                <!--lay-filter="check1">-->
                                <!--<option id="g183" value="0">机动车信号灯</option>-->
                                <!--<option id="g184" value="1">车道信号灯</option>-->
                                <!--<option id="g185" value="2">方向指示信号灯</option>-->
                                <!--<option id="g186" value="3">闪光警号信号灯</option>-->
                                <!--<option id="g187" value="4">倒计时数码显示器</option>-->
                                <!--<option id="g188" value="5">非机动车信号灯</option>-->
                                <!--<option id="g189" value="6">人行横刀信号灯</option>-->
                                <!--<option id="g190" value="7">其他</option>-->
                                <!--</select>-->
                                <!--</td>-->
                                <!--</tr>-->
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
