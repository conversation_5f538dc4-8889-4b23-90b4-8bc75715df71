/**
 * langMessage.internetSenior.cmd.fileList
 *
 *
 * ftp_upload_kk 卡口上传文件
 * 1全景图，2特写图，4车牌图
 * 参数示例：上传全景和特写 ftp_upload_kk:3   （1+2）
 *
 * ftp_upload_wf 违法上传文件
 * 1图片，2视频
 * 参数示例：上传视频 ftp_upload_wf:2
 *
 * langMessage.internetSenior.cmd.filePath
 * ftp_file_path 文件路径
 * 匹配规则以及自定义中不允许带有#、_、/
 设备IP         xjip
 设备编号       sbbh
 违法类型       wflx
 国标违法代码   bzdm
 时间（年月）   ym
 时间（年月日） ymd
 时间（小时）   hour
 车道行驶方向   cdfx
 车道号         cdno
 自定义

 * 参数示例，匹配规则两边用#号框起来，自定义的不用，不同规则之间用/连接   如ftp_file_path:"/ftp/#localIP#/#yearMon#/123/"
 *
 * langMessage.internetSenior.cmd.fileName
 * ftp_file_name 文件命名规则
 * 匹配规则以及自定义中不允许带有#、_
 设备编号      sbbh
 国标违法代码  bzdm
 时间          time
 时间（带毫秒）timeMs
 车牌号码      cphm
 车牌颜色      cpys
 车牌坐标      cpzb
 车型          cx
 车身颜色      csys
 车标          cb
 车辆速度      clsd
 车道行驶方向  cdno
 车道号        landNo
 自定义


 * 参数示例，匹配规则两边用#号框起来，自定义的不用，不同规则之间用_连接   如ftp_file_name:"#timeSec#_123_#plateNum#"
 * */

var form, layer;
$(document).ready(function () {
    layui.use(['form', 'layer'], function () {
        form = layui.form, layer = layui.layer;
        for (let i = 0; i < 31; i++) {
            $("#ftp_video_days").append('<option value=' + i + '>' + i + '</option>');
            $("#ftp_kk_days").append('<option value=' + i + '>' + i + '</option>');
            $("#ftp_wz_days").append('<option value=' + i + '>' + i + '</option>')
        }
        form.on('submit(saveStorage)', function (data) {
            saveInfo()
            // saveInfo_ftpNew()
        });

        form.verify({
            idCheck: function (value) { //自定义lay-verify的昵称，value：表单的值
                const reg = new RegExp('^([a-z]|[A-Z]|[0-9]){20}$')
                if (value && !reg.test(value)) {
                    return langMessage.internetSenior.tips.id_1400
                }
            },
            // FTP新增功能
            // ftp_path_list_verify: function (value) {
            //     let fileList = langMessage.internetSenior.cmd.fileList
            //     if (value && parseInt(value) > 0) {
            //         for (let i = 0; i < value; i++) {
            //             let input = $("#ftp_path_" + (i + 1) + "_input").val().replace(/\s+/g, '')
            //             if ($("#ftp_path_" + (i + 1)).val() === '') {
            //                 return langMessage.internetSenior.tips.pleaseSelect + fileList[i + 1].desc2
            //             } else if ($("#ftp_path_" + (i + 1)).val() === '0' && input === '') {
            //                 return langMessage.internetSenior.tips.pleaseInput + fileList[i + 1].desc2
            //             }
            //         }
            //         if ($("#ftp_name_0").val() === '') {
            //             return langMessage.internetSenior.tips.pleaseSelect + langMessage.internetSenior.tips.nameList + (0)
            //         } else if ($("#ftp_name_0").val() === '0' && $("#ftp_name_0_input").val() === '') {
            //             return langMessage.internetSenior.tips.pleaseInput + langMessage.internetSenior.tips.nameList + (0)
            //         }
            //     }
            // }
        });

        $("#passwordClick").off("click").on("click", function () {
            let c = this.className;
            if (c.indexOf("icon-yanjing_kai") > -1) {
                this.className = "iconfont icon-yanjing_yincang";
                $("#ftp_password").attr({type: "password"});
            } else {
                this.className = "iconfont icon-yanjing_kai";
                $("#ftp_password").attr({type: "text"});
            }
        })

        $("#1400_passwordClick").off("click").on("click", function () {
            let c = this.className;
            if (c.indexOf("icon-yanjing_kai") > -1) {
                this.className = "iconfont icon-yanjing_yincang";
                $("#1400_password").attr({type: "password"});
            } else {
                this.className = "iconfont icon-yanjing_kai";
                $("#1400_password").attr({type: "text"});
            }
        })

        // FTP新增的
        // renderFTPNew()

        getStorage();
        form.render();

    })
});
var getStorage = function () {
    let eventStorage = getSe('eventStorage');
    assignmentInputValue(eventStorage);

    // ftp新增
    // showCheckboxValue('kk-file-type', eventStorage['ftp_upload_kk']);
    // showCheckboxValue('wf-file-type', eventStorage['ftp_upload_wf']);
    //
    // let path = getPath(eventStorage['ftp_file_path'], '/')
    // $("#ftp_path_list").val(path.length)
    // for (let j = 0; j < path.length; j++) {
    //     $("#ftp_path_" + (j + 1)).val(path[j].select)
    //     $("#ftp_path_" + (j + 1)).attr("disabled", false);
    //     $("#ftp_path_" + (j + 1) + "_input").val(path[j].input)
    //     if (path[j].select === '0') {
    //         $("#ftp_path_" + (j + 1) + "_input").css("display", "inline");
    //     }
    // }
    // let name = getName(eventStorage['ftp_file_name'], '_')
    // for (let j = 0; j < name.length; j++) {
    //     $("#ftp_name_" + j).val(name[j].select)
    //     $("#ftp_name_" + j + "_input").val(name[j].input)
    //     if (name[j].select === '0') {
    //         $("#ftp_name_" + j + "_input").css("display", "inline");
    //     }
    // }

};
var saveInfo = function () {
    let dataArray = getAllInputHaveID();//获取页面上有ID的input text元素并返回一个数组
    let eventStorage = {};
    dataArray.push('ftp_password', 'ftp_data', 'ftp_video_days', 'ftp_kk_days', 'ftp_wz_days', '1400_password');//将input password、select类型的放入数组中(FTP存储路径、存储文件名称另行计算)
    for (let key in dataArray) {
        if (dataArray[key] === 'transmit_addr'
            || dataArray[key] === 'ftp_ip' || dataArray[key] === 'ftp_user' || dataArray[key] === 'ftp_password'
            || dataArray[key] === '1400_ip' || dataArray[key] === '1400_username' || dataArray[key] === '1400_password' || dataArray[key] === '1400_device_id'
            || dataArray[key] === 'forward_plateform_ip') {
            eventStorage[dataArray[key]] = $("#" + dataArray[key]).val()
        } else {
            eventStorage[dataArray[key]] = parseInt($("#" + dataArray[key]).val())
        }
    }

    setSe('eventStorage', eventStorage);

    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
}
var saveInfo_ftpNew = function () {
    let fileList = langMessage.internetSenior.cmd.fileList
    let fileName = langMessage.internetSenior.cmd.fileName
    let dataArray = getAllInputHaveID();//获取页面上有ID的input text元素并返回一个数组
    let eventStorage = {};
    dataArray.push('ftp_password', 'ftp_data', 'ftp_video_days', 'ftp_kk_days', 'ftp_wz_days', '1400_password');//将input password、select类型的放入数组中(FTP存储路径、存储文件名称另行计算)
    let noArray = []// FTP存储路径、存储文件名称的input
    for (let i = 0; i < fileName.length; i++) {
        noArray.push('ftp_name_' + i + '_input')
    }
    for (let i = 0; i < fileList.length; i++) {
        noArray.push('ftp_path_' + i + '_input')
    }
    for (let key in dataArray) {
        if (dataArray[key] === 'transmit_addr'
            || dataArray[key] === 'ftp_ip' || dataArray[key] === 'ftp_user' || dataArray[key] === 'ftp_password'
            || dataArray[key] === '1400_ip' || dataArray[key] === '1400_username' || dataArray[key] === '1400_password' || dataArray[key] === '1400_device_id'
            || dataArray[key] === 'forward_plateform_ip') {
            eventStorage[dataArray[key]] = $("#" + dataArray[key]).val()
        } else {
            let flag = true
            for (let i in noArray) {
                if (dataArray[key] === noArray[i]) {
                    flag = false
                }
            }
            if (flag) {
                eventStorage[dataArray[key]] = parseInt($("#" + dataArray[key]).val())
            }
        }
    }
    // FTP存储类型(input checkbox)
    eventStorage['ftp_upload_kk'] = getCheckboxValue('kk-file-type')
    eventStorage['ftp_upload_wf'] = getCheckboxValue('wf-file-type')

    // FTP存储路径
    let ftp_file_path = '/'
    for (let i = 0; i < parseInt($("#ftp_path_list").val()); i++) {
        let input = $("#ftp_path_" + (i + 1) + "_input").val().replace(/\s+/g, '')
        if ($("#ftp_path_" + (i + 1)).val() === '0') {
            ftp_file_path = ftp_file_path + input + '/'
        } else {
            ftp_file_path = ftp_file_path + '#' + $("#ftp_path_" + (i + 1)).val() + '#/'
        }
    }
    eventStorage['ftp_file_path'] = ftp_file_path

    // FTP命名项
    let ftp_file_name = ''
    for (let i = 0; i < fileName.length; i++) {
        let input = $("#ftp_name_" + i + "_input").val().replace(/\s+/g, '')
        if (i < fileName.length - 1) {
            if ($("#ftp_name_" + i).val() === '' && $("#ftp_name_" + (i + 1)).val() !== "") {
                layer.msg(langMessage.internetSenior.tips.pleaseSelect + langMessage.internetSenior.tips.nameList + (i + 1), {icon: 2});
                return
            }
        }
        if ($("#ftp_name_" + i).val() === '0' && input === '') {
            layer.msg(langMessage.internetSenior.tips.pleaseInput + langMessage.internetSenior.tips.nameList + (i + 1), {icon: 2});
            return
        }
        if ($("#ftp_name_" + i).val() !== "") {
            ftp_file_name = ftp_file_name === '' ? ftp_file_name : (ftp_file_name + '_')
            if ($("#ftp_name_" + i).val() === "0") {
                ftp_file_name = ftp_file_name + input
            } else {
                ftp_file_name = ftp_file_name + '#' + $("#ftp_name_" + i).val() + '#'
            }
        }
    }
    eventStorage['ftp_file_name'] = ftp_file_name


    setSe('eventStorage', eventStorage);

    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
}
var renderFTPNew = function () {
    let fileList = langMessage.internetSenior.cmd.fileList
    let filePath = langMessage.internetSenior.cmd.filePath
    $("#ftp_path_list").append('<option value="">' + langMessage.internetSenior.tips.pleaseSelect + '</option>');
    for (let i = 0; i < fileList.length; i++) {
        $("#ftp_path_list").append('<option value=' + fileList[i].code + '>' + fileList[i].desc1 + '</option>');
        let option = '<option value="">' + langMessage.internetSenior.tips.pleaseSelect + '</option>'
        for (let j = 0; j < filePath.length; j++) {
            option = option + '<option value=' + filePath[j].code + '>' + filePath[j].desc + '</option>'
        }
        if (fileList[i].code !== 0) {
            $("#path_div").append('<div class="layui-form-item">' +
                '                            <label class="layui-form-label" style="width: 80px">' + fileList[i].desc2 + '：</label>' +
                '                            <div class="layui-input-inline">' +
                '                                <select id="ftp_path_' + i + '" disabled lay-filter="ftp_path_' + i + '_filter" name="ftp_path_' + i + '_name">' + option +
                '                                </select>' +
                '                            </div>' +
                '                            <div class="layui-input-inline">' +
                '                                <input id="ftp_path_' + i + '_input" class="layui-input" style="display: none"/>' +
                '                            </div>' +
                '                        </div>')
            $("#ftp_path_" + i).attr("disabled", true);
            form.on('select(ftp_path_' + i + '_filter)', function (data) {
                if (data.value === '0') {
                    $("#ftp_path_" + i + "_input").css("display", "inline");
                } else {
                    $("#ftp_path_" + i + "_input").css("display", "none");
                }
                form.render();
            });
        }

    }
    let fileName = langMessage.internetSenior.cmd.fileName
    for (let i = 0; i < fileName.length; i++) {
        let option = '<option value="">' + langMessage.internetSenior.tips.pleaseSelect + '</option>'
        for (let j = 0; j < fileName.length; j++) {
            option = option + '<option value=' + fileName[j].code + '>' + fileName[j].desc + '</option>'
        }
        $("#name_div").append('<div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 80px">' + langMessage.internetSenior.tips.nameList + (i + 1) + '：</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <select id="ftp_name_' + i + '" lay-filter="ftp_name_' + i + '_filter" name="ftp_name_' + i + '_name">' + option +
            '                                </select>' +
            '                            </div>' +
            '                            <div class="layui-input-inline">' +
            '                                <input id="ftp_name_' + i + '_input" class="layui-input" style="display: none"/>' +
            '                            </div>' +
            '                        </div>')

        form.on('select(ftp_name_' + i + '_filter)', function (data) {
            if (data.value === '0') {
                $("#ftp_name_" + i + "_input").css("display", "inline");
            } else {
                $("#ftp_name_" + i + "_input").css("display", "none");
            }
            form.render();
        });
    }
    //目录结构
    form.on('select(ftp_path_list_filter)', function (data) {
        for (let i = 1; i < fileList.length; i++) {
            $("#ftp_path_" + i).attr("disabled", true);
        }
        for (let i = 0; i < parseInt(data.value); i++) {
            $("#ftp_path_" + (i + 1)).removeAttr("disabled")
        }
        form.render();
    });
}
var getPath = function (value, str_interval) {
    const ret = []
    const arr = value.split(str_interval)
    for (let i = 0; i < arr.length; i++) {
        const a = arr[i].replace(/\s+/g, '')
        if (a !== '') {
            if (a.indexOf('#') > -1) {
                ret.push({
                    type: 'select',
                    select: a.replaceAll('#', ''),
                    input: ''
                })
            } else {
                ret.push({
                    type: 'input',
                    select: '0',
                    input: a
                })
            }
        }
    }
    return ret
}
var getName = function (value, str_interval) {
    const ret = []
    const arr = value.split('#')
    for (let i = 0; i < arr.length; i++) {
        const a = arr[i].replace(/\s+/g, '')
        if (a !== '' && a !== '_') {
            if (checkDictionaries(langMessage.internetSenior.cmd.fileName, a)) {
                ret.push({
                    type: 'select',
                    select: a,
                    input: ''
                })
            } else {
                const a1 = a.split(str_interval)
                for (let j = 0; j < a1.length; j++) {
                    if (a1[j] !== '' && a1[j] !== str_interval) {
                        ret.push({
                            type: 'input',
                            select: '0',
                            input: a1[j]
                        })
                    }
                }
            }
        }
    }
    return ret
}

function checkDictionaries(config, value, key) {
    let flag = false
    let k = 'code'
    if (key) {
        k = key
    }
    for (let i = 0; i < config.length; i++) {
        if (config[i][k] === value) {
            flag = true
        }
    }
    return flag
}
