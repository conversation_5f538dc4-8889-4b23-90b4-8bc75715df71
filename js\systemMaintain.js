var form, laydate, element, layer, upload, table;
var exts = '.token';
var exportYUV_loading;
var info = [], brokenLineRadio = 'day'
$(document).ready(function () {
    layui.use(['form', 'laydate', 'element', 'layer', 'upload', 'table'], function () {
        form = layui.form, laydate = layui.laydate, element = layui.element, layer = layui.layer, upload = layui.upload, table = layui.table;
        laydate.render({
            elem: '#setTimeValue'
            , type: 'time'
        });
        form.on('radio(brokenLineRadio)', function (data) {
            brokenLineRadio = data.value
            showEchartsLine()
        })
        form.render();
        //旧升级逻辑需要判断文件类型
        // form.on('radio(updateType)', function (data) {
        //     let che = data.value;
        //     if (che) {
        //         $("#updateFile").removeAttr("disabled")
        //         $("#updateFile").removeClass("layui-disabled")
        //     }
        // });
        element.on('tab(tabChange)', function (data) {
            if (data.index !== 0) {
                $("#realTimeLogInfo").html("")
            }
            if (data.index !== 1) {
                $("#logInfo").html("")
            }
            // if (data.index === 2) {
            //     let result = initialize_ocx('upgradeContainer');
            //     if (!result.result) {
            //         layer.open({
            //             title: '错误'
            //             , shade: 0.8
            //             , btn: false
            //             , content: result.msg
            //             , anim: 6
            //         });
            //     }
            // }
            // if (data.index === 3) {
            //     if(isIE()){
            //         let result = initialize_ocx('authContainer');
            //         if (!result.result) {
            //             layer.open({
            //                 title: '错误',
            //                 shade: 0.8,
            //                 btn: false,
            //                 content: result.msg,
            //                 anim: 6
            //             });
            //         }
            //     }
            // }
        });
        //旧升级逻辑
        var uploadInst = upload.render({
            elem: '#updateFile' //绑定元素
            , accept: 'file' //允许上传的文件类型
            , auto: false //选择文件后不自动上传
            , bindAction: 'update'
            , before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                // layer.load(); //上传loading
                let type = $("input[type=radio][name=updateType]").val();
                console.log(type)
            }
            , done: function (res) {
                //上传完毕回调
            }
            , error: function () {
                //请求异常回调
            }
            , choose: function (obj) {
                //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                obj.preview(function (index, file, result) {
                    //console.log(file); //得到文件对象
                    if (file.size > 0) {
                        element.progress("analysisProgress", "0%");
                    } else {
                        $("#upBtn").css({
                            display: 'none'
                        })
                    }
                    let path = $(".layui-upload-file[name=file]").val();
                    $("#fileName").html(getFileName(path));
                    get_filemd5sum(file, 'analysisProgress');

                    // let md5 = ocx.getFileMd5(path);
                    // $("#md5Name").html(md5);
                    //obj.resetFile(index, file, '123.jpg'); //重命名文件名，layui 2.3.0 开始新增

                    //这里还可以做一些 append 文件列表 DOM 的操作

                    //obj.upload(index, file); //对上传失败的单个文件重新上传，一般在某个事件中使用
                    //delete files[index]; //删除列表中对应的文件，一般在某个事件中使用
                });

            }
        });
        // var uploadConfig = upload.render({
        //     elem: '#importConfig' //绑定元素
        //     , accept: 'file' //允许上传的文件类型
        //     , auto: false //选择文件后不自动上传
        //     , before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
        //         // layer.load(); //上传loading
        //     }
        //     , done: function (res) {
        //         //上传完毕回调
        //     }
        //     , error: function () {
        //         //请求异常回调
        //     }
        //     , choose: function (obj) {
        //         //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
        //         obj.preview(function (index, file, result) {
        //             console.log(file); //得到文件对象
        //             getFile(file);
        //         });
        //     }
        // });
        $("#importConfigBtn").off("click").on("click", function () {
            $("#importConfig").val("");
            $("#importConfig").click();
        });
        $("#importConfig").change(function (e) {
            let file = this.files[0];
            getFile(file);
        });
        showReboot();


        //旧升级逻辑获取升级进度
        // $("#update").click(function () {
        //     let type = parseInt($("input[name=updateType]:checked").val());
        //     let ip = '*************';
        //     let path = $("#fileName").html();
        //     let updateStatus = ocx.UPDATE(type, ip, path);
        //     let p,updateProcess;
        //     if(updateStatus>=0){
        //         updateProcess = setInterval(function () {
        //             p = ocx.UPDATE(type, ip, path);
        //         },1000)
        //     }
        //     let size = this.dataset.size;
        //     let process = parseInt(p/size*100);
        //     console.log("升级进度："+process)
        //     if(process===100){
        //         clearInterval(updateProcess)
        //     }
        //     // console.log(r)
        // })
        disableFun(['autoRestart']);

        $("#saveLogInfo").off("click").on("click", function () {
            doSave('logInfo', 'log')
        });
        $("#saveRealTimeLogInfo").off("click").on("click", function () {
            doSave('realTimeLogInfo', 'log')
        });
        $("#restartDevice").off("click").on("click", function () {
            let data = {
                ipcamrestartcmd: ''
            };
            layer.confirm(langMessage.setting.confirmRestart, {
                btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
            }, function (index, layero) {
                // changeCameraParam(data);
                initWebService(webserviceCMD.CMD_REBOOT, null, loadingWait, getSe("projectConfig").index_reboot_time);
                let host = location.host;
                layer.close(index);
                //loadingWait(60);
            }, function (index) {
                layer.close(index);
            });
        });
        $("#resume").off("click").on("click", function () {
            let data = {
                paradefaultcmd: 1
            };
            layer.confirm(langMessage.setting.confirmRestore, {
                btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
            }, function (index, layero) {
                changeCameraParam(data);
                layer.close(index);
            }, function (index) {
                layer.close(index);
            });
        });
        $("#resumeWithoutInt").off("click").on("click", function () {
            let data = {
                paradefaultcmd: 2
            };
            layer.confirm(langMessage.setting.confirmRestoreWithoutInternet, {
                btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
            }, function (index, layero) {
                changeCameraParam(data);
                layer.close(index);
            }, function (index) {
                layer.close(index);
            });
        });
        $("#simplyResume").off("click").on("click", function () {
            layer.confirm(langMessage.setting.confirmRecover, {
                btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
            }, function (index, layero) {
                initWebService(webserviceCMD.CMD_RESET_PARA, null, loadingWait, getSe("projectConfig").index_reboot_time);
                layer.close(index);
            }, function (index) {
                layer.close(index);
            });
        });
        $("#getRealTimeLog").off("click").on("click", function () {
            initWebService(webserviceCMD.CMD_GET_DBG_LOG, null, getLogCallback, "dbg_log");
        });
        $("#getLog").off("click").on("click", function () {
            initWebService(webserviceCMD.CMD_GET_LOG, null, getLogCallback, "log");
        });
        $("#getLogS").off("click").on("click", function () {
            let index = layer.load()
            initWebService(webserviceCMD.CMD_ANALYSIS_LOG, {log_type: 0}, getLogSCallback, index);
        });
        $("#exportConfig").off("click").on("click", function () {
            initWebService(webserviceCMD.CMD_CAPTURE_JPG, {
                type: function () {
                    let config = $("#getConfig");
                    if (!config.length) {
                        config = $("<textarea id='getConfig' style='display: none'></textarea>");
                        $("#exportConfig").after(config)
                    }
                    config.val(getConfig());

                    doSave('getConfig', 'config')
                }
            });
        });
        $("#saveReboot").off("click").on("click", function () {
            saveReboot()
        })
        // $("#exportAuth").click(function () {
        //     initWebService(11, null, getAuth);
        // });
        // $('#authFile').click(function (e) {
        //     let importBtn = $("#importAuth");
        //     importBtn.addClass("layui-btn-disabled");
        //     importBtn.css({
        //         display: 'none'
        //     });
        //     setDataset(importBtn[0], "authFileBase64", "");
        //     $("#authFileInput").val("");
        //     let deviceInfo = getSe("deviceInfo");
        //     if (!deviceInfo) {
        //         reloadConfig(reloadAuthCallback);
        //     } else {
        //         reloadAuthCallback();
        //     }
        // $('#authName').text($('#authFile').val());
        // if (e.currentTarget.files.length > 0) {
        //     var name = e.currentTarget.files[0].name;
        //     if (name !== 'lic.dat') {
        //         layer.msg("错误的授权文件", {icon: 2})
        //         $('#authFile').val("");
        //     } else {
        //         $("#importAuth").removeClass("layui-btn-disabled");
        //         $("#importAuth").css({
        //             display: 'inline-block'
        //         })
        //         form.render()
        //     }
        // }
        // });
        // $("#importAuth").click(function () {
        //     let param = {
        //         authFileBase64: getDataset($("#importAuth")[0]).authFileBase64
        //     };
        //     initWebService(12, param, getAuthSuc, null);
        // })
        initWebService(webserviceCMD.CMD_GET_DBG_LOG, null, getLogCallback, "dbg_log");
    });
});
var changeTag = function (type) {
    if (type === 1) {
        initWebService(webserviceCMD.CMD_GET_DBG_LOG, null, getLogCallback, "dbg_log");
    } else if (type === 2) {
        initWebService(webserviceCMD.CMD_GET_LOG, null, getLogCallback, "log");
    } else {
        let index = layer.load()
        initWebService(webserviceCMD.CMD_ANALYSIS_LOG, {log_type: 0}, getLogSCallback, index);
    }
}
var showReboot = function () {
    let isReboot = getSe("isReboot");
    let rebootTime = getSe("rebootTime");
    if (isReboot) {
        $("#autoRestart")[0].checked = true;
        enableFun("auto-restart");
    }
    $("#setTimeValue").val(value2Date(rebootTime, 1));
    form.render();
};
var saveReboot = function () {
    let rebootTime = date2Value($("#setTimeValue").val(), 1);
    let isReboot = $("#autoRestart")[0].checked === true ? 1 : 0;
    setSe("isReboot", isReboot);
    setSe("rebootTime", rebootTime);
    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
};
// var reloadAuthCallback = function (file) {
//     let deviceInfo = getSe('deviceInfo');
//     let auth_status = deviceInfo.auth_status;
//     let unique_id = deviceInfo.unique_id;
//     if (auth_status) {
//         layer.confirm('此程序已授权，是否重新授权？', {
//             btn: ['确定', '取消',] //可以无限个按钮
//         }, function (index, layero) {
//             layer.close(index);
//             if (isIE()) {
//                 //getFileIE(unique_id)
//                 ocxAuth(unique_id)
//             } else {
//                 getFileList(unique_id)
//             }
//         }, function (index) {
//             layer.close(index);
//         });
//     } else {
//         if (isIE()) {
//             ocxAuth(unique_id)
//         } else {
//             getFileList(unique_id)
//         }
//     }
// };
// var getFileList = function (uid) {
//     $("#authFileInput").click();
//     $("#authFileInput").off("change").on("change",function (e) {
//         let files = this.files;
//         readFileList(files, uid);
//     });
// };
// var getAuth = function (data) {
//     let code = data.authcode;
//     let decode = "";
//     try {
//         decode = window.atob(code);
//         var byteArray = new Uint8Array(decode);
//         for (var i = 0; i < byteArray.byteLength; i++) {
//             console.log(byteArray[i])
//         }
//     } catch (e) {
//         console.log(e)
//     }
//     let auth = $("#getAuth");
//     console.log(decode);
//     auth.val(decode);
//     doSave('getAuth', 'dat')
// }

// var getAuth = function (data) {
//     let code = data.authcode;
//     let blob = dataURLtoBlob(code);
//     analysisFile(blob, function (info) {
//         console.log(info);
//         if (info.crc === info.now_crc) {
//             doSaveBlob('exportAuth', location.hostname + '-' + info.crc.toString(16).toUpperCase() + exts, blob);
//         } else {
//             layer.msg("文件损坏请重试！", {icon: 2, time: 5000});
//         }
//     }, function (e) {
//         layer.msg("未知错误！", {icon: 2, time: 5000});
//         console.log(e)
//     });
//     // getPath(code, exportAuthCallback);
// };
// var getAuthSuc = function (data) {
//     layer.confirm('授权成功！（授权重启后生效）是否确认重启？', {
//         btn: ['确定', '取消',] //可以无限个按钮
//     }, function (index, layero) {
//         // changeCameraParam(data);
//         layer.close(index);
//         initWebService(10, null, loadingWait, getSe("projectConfig").index_auth_time);
//         //loadingWait(60);
//     }, function (index) {
//         layer.close(index);
//     });
// };
// var exportAuthCallback = function (Folder, auth, name) {
//     let filePath = Folder + name;
//     let result = saveAuthcode(filePath, auth);
//     if (result != 'FALSE') {
//         $("#exportPath").html("<i class=\"layui-icon layui-icon-tips\"></i>文件导出为：" + result);
//         layer.msg("导出成功！", {icon: 1, time: 5000})
//     } else {
//         $("#exportPath").html("");
//         layer.msg("导出失败！", {icon: 2, time: 5000})
//     }
// };
// var importAuthCallback = function (result) {
//     if (result.result) {
//         $("#importAuth").removeClass("layui-btn-disabled");
//         $("#importAuth").css({
//             display: 'inline-block'
//         });
//         setDataset($("#importAuth")[0], "authFileBase64", result.base);
//     } else {
//         layer.msg(result.msg, {icon: 2, time: 5000});
//         $("#importAuth").css({
//             display: 'none'
//         })
//     }
// };
// var getFileIE = function (uid) {
//     if (isIE()) {//IE浏览器保存文本框内容
//         // name = "token-" + dateValue + timeValue+'-'+host;
//         let fso, tf, fileList = [];
//         try {
//             fso = new ActiveXObject("Scripting.FileSystemObject");
//             // var Message = "请选择文件夹！\n文件将保存为" + name; //选择框提示信息
//             let Message = "请选择文件夹！"; //选择框提示信息
//             let Shell = new ActiveXObject("Shell.Application");
//             // let Folder = Shell.BrowseForFolder(0, Message, 0x0040, 0x11); //起始目录为：我的电脑
//             let Folder = Shell.BrowseForFolder(0, Message, 0); //起始目录为：桌面
//             if (Folder != null) {
//                 // Folder = Folder.items(); // 返回 FolderItems 对象
//                 // Folder = Folder.item(); // 返回 Folderitem 对象
//                 // Folder = Folder.Path; // 返回路径
//                 console.log(Folder);
//                 Folder = Folder.self.path;
//                 if (Folder.charAt(Folder.length - 1) != "\\") {
//                     Folder = Folder + "\\";
//                 }
//                 tf = fso.GetFolder(Folder);
//                 IEGetFiles(fso, tf, fileList);
//                 readFileList(fileList, uid);
//             }
//         } catch (e) {
//             console.log(e.message);
//             layer.msg('当前浏览器不支持此方法，请修改浏览器设置！', {icon: 2});
//         }
//     }
//     else {
//         layer.msg('当前浏览器不支持此方法', {icon: 2});
//     }
// };
/**
 * 使用JScript循环读取文件夹中的文件
 * Unicode读取出来的为两个字节一个字符的字符串数组，需要进行转义
 * @param fso 传入的控件
 * @param folder 文件夹路径
 * @param fileList 读取的文件数组
 */
// var IEGetFiles = function (fso, folder, fileList) {
//     //取文件夹
//     let underFolders = new Enumerator(folder.SubFolders);
//     //取文件
//     let underFiles = new Enumerator(folder.files);
//     for (; !underFiles.atEnd(); underFiles.moveNext()) {
//         let fn = "" + underFiles.item();
//         // File file = ifile.getLocation().toFile;
//         if (underFiles.item().size > 0) {
//             let ForReading = 1, ForWriting = 2;
//             let ForCreate = true, NoCreate = false;
//             let TristateTrue = -1, //以 Unicode 方式打开文件
//                 TristateFalse = 0, //以 ASCII 方式打开文件
//                 TristateUseDefault = -2; //使用系统默认值打开文件
//             let ts0 = fso.OpenTextFile(fn, ForReading, NoCreate, TristateTrue);
//             let s0 = ts0.ReadAll();
//             let getS = str2ab(s0);
//             let getBlob = new Blob([getS]);
//             let file = {
//                 name: "" + underFiles.item().name,
//                 size: underFiles.item().size,
//                 file: getBlob,
//                 type: 'IE'
//             };
//             ts0.Close();
//             fileList.push(file)
//         }
//     }
//     for (; !underFolders.atEnd(); underFolders.moveNext()) {
//         IEGetFiles(fso, underFolders.item(), fileList);
//     }
// };

// var readFileList = function (files, uid) {
//     let fileList = [];
//     let checkExtFiles = checkFiles(files);
//     for (let i = 0; i < checkExtFiles.length; i++) {
//         analysisFile(checkExtFiles[i], function (info) {
//             if (info.now_crc && (info.crc === info.now_crc)) {
//                 fileList.push(info);
//             }
//             if (i === (checkExtFiles.length - 1)) {
//                 compareAuth(fileList, uid)
//             }
//         }, function (e) {
//             console.log(e)
//         });
//     }
// };
var loadingWait = function (s) {
    let topW = getTopWindow();
    let app_name = getAppName()
    let loading = topW.indexLoading(s, langMessage.common.restartAlgorithm);
    setTimeout(function () {
        topW.closeLoading(loading);
        topW.location.href = "https://" + location.host + "/SDCWEB/" + app_name + "/index.html";
    }, s * 1000);
};
var getLogCallback = function (getData, type) {
    let setName = "logInfo";
    if (type === "dbg_log") {
        setName = "realTimeLogInfo"
    }
    let logInfo = getData[type];
    if (logInfo) {
        // let info = JSON.stringify(logInfo);
        // logInfo = logInfo.replace(/&#xA;/g, ",&#10;");
        // logInfo = logInfo.replace(/\\n/g, "\n");
        logInfo = decodeLog(logInfo);
        $("#" + setName).html(logInfo);
        let log = document.getElementById(setName);
        log.scrollTop = log.scrollHeight
    }

};
var getLogSCallback = function (getData, loadIndex) {
    layer.close(loadIndex)
    document.getElementById('right-echarts-line').style.display = 'none'
    // all - 其它异常 = 手动重启
    let cmd = [
        {
            code: '1',
            desc: '正常重启'
        },
        {
            code: '2',
            desc: '异常重启'
        },
        {
            code: '3',
            desc: 'SIGTERM'
        },
        {
            code: '4',
            desc: 'SIGINT'
        },
        {
            code: '5',
            desc: '服务初始化失败'
        }
    ]
    // getData = {
    //     "ret": 0, "desc": "success", "data": [{
    //         "type": "1",
    //         time: [{
    //             "time1": "Mon Feb 20 11:26:42 2023",
    //             "info1": "123",
    //             "time2": "Mon Feb 20 11:26:42 2023",
    //             "info2": "start up its app!"
    //         },
    //             {
    //                 "time1": "EMPTY",
    //                 "info1": "EMPTY",
    //                 "time2": "Mon Feb 20 11:26:42 2023",
    //                 "info2": "start up its app!"
    //             }]
    //     }, {
    //         "type": "2",
    //         time: [{
    //             "time1": "EMPTY",
    //             "info1": "EMPTY",
    //             "time2": "Mon Feb 20 11:26:42 2023",
    //             "info2": "start up its app!"
    //         },
    //             {
    //                 "time1": "EMPTY",
    //                 "info1": "EMPTY",
    //                 "time2": "Mon Feb 20 11:26:42 2023",
    //                 "info2": "start up its app!"
    //             }]
    //     }, {
    //         "type": "3",
    //         time: [{
    //             "time1": "EMPTY",
    //             "info1": "EMPTY",
    //             "time2": "Mon Feb 20 11:26:42 2023",
    //             "info2": "start up its app!"
    //         },
    //             {
    //                 "time1": "EMPTY",
    //                 "info1": "EMPTY",
    //                 "time2": "Mon Feb 20 11:26:42 2023",
    //                 "info2": "start up its app!"
    //             }]
    //     }]
    // }
    let data = []
    let xData = []
    let timeArr = []
    for (let i = 0; i < getData.data.length; i++) {
        let name = ''
        for (let j = 0; j < cmd.length; j++) {
            if (cmd[j].code === getData.data[i].type) {
                name = cmd[j].desc
            }
        }
        xData.push(name)
        data.push({
            name: name,
            value: getData.data[i].time.length
        })
        for (let j = 0; j < getData.data[i].time.length; j++) {
            if (getData.data[i].time[j].time1 !== 'EMPTY') {
                timeArr.push(getData.data[i].time[j].time1)
            }
            if (getData.data[i].time[j].time2 !== 'EMPTY') {
                timeArr.push(getData.data[i].time[j].time2)
            }
        }
    }
    timeArr = ascSort(timeArr)
    $("#logS_echarts_time").html('')
    if (timeArr.length) {
        let start = getFormatTime1(timeArr[0])
        let end = getFormatTime1(timeArr[timeArr.length - 1])
        $("#logS_echarts_time").html(start + ' -- ' + end)
    }
    let option = {
        color: 'rgb(84,112,198)',
        tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} 次'
        },
        xAxis: {
            type: 'category',
            data: xData,
            name: '线程名称',
            axisTick: {
                alignWithLabel: true
            }
        },
        yAxis: {
            type: 'value',
            name: '错误次数',
            minInterval: 1
        },
        series: [
            {
                type: 'bar',
                data: data,
                // 显示数值
                itemStyle: {
                    normal: {
                        label: {
                            show: true,
                            formatter: '{b} : {c} 次'
                        },
                        labelLine: {show: true}
                    }
                }
            }
        ]
    }
    document.getElementById('logS_echarts').style.height = '500px'
    let myChart = echarts.init(document.getElementById('logS_echarts'));
    myChart.setOption(option);
    window.addEventListener('resize', () => {
        myChart.resize()
    })
    myChart.off('click')
    myChart.on('click', function (params) {
        document.getElementById('right-echarts-line').style.display = 'block'
        for (let i = 0; i < data.length; i++) {
            if (data[i].name === params.name) {
                data[i].itemStyle = {
                    color: '#5FB878'
                }
            } else {
                data[i].itemStyle = {
                    color: 'rgb(84,112,198)'
                }
            }
            myChart.setOption(option);
        }
        let code = ''
        for (let j = 0; j < cmd.length; j++) {
            if (cmd[j].desc === params.name) {
                code = cmd[j].code
            }
        }
        for (let i = 0; i < getData.data.length; i++) {
            if (code === getData.data[i].type) {
                info = getData.data[i].time
                showEchartsLine()
                break
            }
        }
    })
}
var showEchartsLine = function () {
    let key = '1', xData = [], seriseData = []
    for (let j = 0; j < info.length; j++) {
        if (info[j].time1 !== 'EMPTY') {
            key = '1'
        } else if (info[j].time2 !== 'EMPTY') {
            key = '2'
        }
        let time = getFormatTime(info[j]['time' + key])
        let flag = true
        for (let k = 0; k < seriseData.length; k++) {
            if (seriseData[k].name === time) {
                seriseData[k].value = seriseData[k].value + 1
                flag = false
                break
            }
        }
        if (flag) {
            xData.push(time)
            seriseData.push({
                name: time,
                value: 1
            })
        }
    }

    let option = {
        tooltip: {
            trigger: 'item',
            formatter: '{b} : {c} 次'
        },
        xAxis: {
            type: 'category',
            name: '时间段',
            data: xData
        },
        yAxis: {
            type: 'value',
            name: '重启次数',
            minInterval: 1
        },
        series: [
            {
                data: seriseData,
                type: 'line',
                smooth: true
            }
        ]
    };
    document.getElementById('echarts_line').style.height = '500px'
    let myChart1 = echarts.init(document.getElementById('echarts_line'));
    myChart1.setOption(option);
    window.addEventListener('resize', () => {
        myChart1.resize()
    })
    myChart1.off('click')
    myChart1.on('click', function (params) {
        let tableData = []
        for (let i = 0; i < info.length; i++) {
            let time = getFormatTime(info[i]['time' + key])
            if (time === params.name) {
                let obj = JSON.parse(JSON.stringify(info[i]))
                obj.time1 = getFormatTime1(obj.time1)
                obj.time2 = getFormatTime1(obj.time2)
                tableData.push(obj)
            }
        }
        tableData = sortTableData(tableData, key)
        layer.open({
            title: '详情',
            type: 1,
            area: ['90%', '570px'],
            offset: ['50px', '5%'],
            success: function (layero, index) {
                table.render({
                    elem: '#logSTable'
                    , cols: [[
                        {type: 'numbers', title: '序号', align: 'center', width: 80},//序号列
                        {
                            field: 'time1',
                            title: '异常发生时间',
                            align: 'center',
                            width: 180,
                            // templet: function (d1) {
                            //     return getFormatTime1(d1.time1)
                            // }
                        },
                        {
                            field: 'info1', title: '异常描述', align: 'center',
                            // templet: function (d1) {
                            //     if (d1.info1 === 'EMPTY') {
                            //         return ''
                            //     } else {
                            //         return d1.info1
                            //     }
                            // }
                        },
                        {
                            field: 'time2',
                            title: '重启发生时间',
                            align: 'center',
                            width: 180,
                            // templet: function (d1) {
                            //     return getFormatTime1(d1.time2)
                            // }
                        },
                        {
                            field: 'info2', title: '重启描述', align: 'center',
                            width: 180,
                            // templet: function (d1) {
                            //     if (d1.info2 === 'EMPTY') {
                            //         return ''
                            //     } else {
                            //         return d1.info2
                            //     }
                            // }
                        }
                    ]]
                    , data: tableData
                    , page: true
                });
            },
            end: function (layero, index) {
            },
            content: $('#logSTableDiv')
        });
    })
}
var getFormatTime = function (data) {
    if (data === 'EMPTY') {
        return 'EMPTY'
    }
    let date = new Date(data)
    let year = date.getFullYear()
    let month = date.getMonth() + 1
    if (month < 10) {
        month = '0' + month
    }
    let day = date.getDate()
    if (day < 10) {
        day = '0' + day
    }
    let hour = date.getHours()
    if (hour < 10) {
        hour = '0' + hour
    }
    let time = year + '-' + month + '-' + day
    if (brokenLineRadio === 'hour') {
        time = year + '-' + month + '-' + day + ' ' + hour
    }
    return time


    // try {
    //     let isoTime = new Date(new Date(data).getTime() + 8 * 60 * 60 * 1000).toISOString()//2022-03-14T11:01:41.968Z
    //     let time = isoTime.substring(0, 10)
    //     if (brokenLineRadio === 'hour') {
    //         time = isoTime.substring(0, 10) + " " + isoTime.substring(11, 13)
    //     }
    //     return time
    // } catch (e) {
    //     return ''
    // }


}
var getFormatTime1 = function (data) {
    if (data === 'EMPTY') {
        return 'EMPTY'
    }
    let date = new Date(data)
    let year = date.getFullYear()
    let month = date.getMonth() + 1
    if (month < 10) {
        month = '0' + month
    }
    let day = date.getDate()
    if (day < 10) {
        day = '0' + day
    }
    let hour = date.getHours()
    if (hour < 10) {
        hour = '0' + hour
    }
    let minute = date.getMinutes()
    if (minute < 10) {
        minute = '0' + minute
    }
    let seconds = date.getSeconds()
    if (seconds < 10) {
        seconds = '0' + seconds
    }
    let time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + seconds

    return time

    // try {
    //     let isoTime = new Date(new Date(data).getTime() + 8 * 60 * 60 * 1000).toISOString();//2022-03-14T11:01:41.968Z
    //     let formatTime = isoTime.substring(0, 10) + " " + isoTime.substring(11, 19)
    //     return formatTime
    // } catch (e) {
    //     return ''
    // }
}
var decodeLog = function (log) {
    // var reg = new RegExp("%(\\w\\w)","g");
    // let result = "";
    let decodeArray = ['%5C', '%2F', '%62', '%66', '%6E', '%72', '%74', '%75'];
    let decodeArrays = {
        "%5C": "\\",
        "%2F": "/",
        "%62": "\b",
        "%66": "\f",
        "%6E": "\n",
        "%72": "\r",
        "%74": "\t"
    };
    for (let i = 0; i < decodeArray.length; i++) {
        let reg = new RegExp(decodeArray[i], "g");
        log = log.replace(reg, decodeArrays[decodeArray[i]]);
    }
    // while ((result = reg.exec(lo{g)) != null)  {
    //     //console.log(result);
    //     result = result[1].toUpperCase();
    //     if(decodeArray.indexOf(result)>-1){
    //         let real = String.fromCharCode(parseInt(result,16));
    //         log = changeStr(log, reg.lastIndex - 3 , decodeArrays[result]);
    //         reg.lastIndex = 0;
    //     }
    // }
    return log
};
var exportYUV = function () {
    let topW = getTopWindow();
    exportYUV_loading = topW.indexLoading(null, "正在导出YUV......");
    initWebService(webserviceCMD.CMD_GET_YUV, null, downloadYUV)
}
var downloadYUV = function (data) {
    let nowDate = new Date();
    let dataValue =
        nowDate.getFullYear() + '-' +
        (nowDate.getMonth() > 9 ? nowDate.getMonth() + 1 : ('0' + (nowDate.getMonth() + 1))) + '-' +
        (nowDate.getDate() > 9 ? nowDate.getDate() : ('0' + nowDate.getDate()));
    let timeValue =
        (nowDate.getHours() > 9 ? nowDate.getHours() : ('0' + nowDate.getHours())) + '-' +
        (nowDate.getMinutes() > 9 ? nowDate.getMinutes() : ('0' + nowDate.getMinutes())) + '-' +
        (nowDate.getSeconds() > 9 ? nowDate.getSeconds() : ('0' + nowDate.getSeconds()));
    let filename = location.hostname + '_' + dataValue + '_' + timeValue + ".yuv"
    let type = getSe("SDC");
    if (type) {
        doSaveBlob('exportYUV', filename, data);
    } else {
        let code = data.yuv;
        let blob = dataURLtoBlob(code);
        doSaveBlob('exportYUV', filename, blob);
    }
    let topW = getTopWindow();
    topW.closeLoading(exportYUV_loading);
}
