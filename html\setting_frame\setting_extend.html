<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>settingExtend</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/extendParams.js"></script>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-form detect-signal layui-show">
    <fieldset class="layui-elem-field" style="max-width: 800px;">
        <legend id="trafficDetection">交通检测扩展参数设置</legend>
        <div class="layui-field-box">
            <div>
                <fieldset class="layui-elem-field" style="width: 400px;height: 400px;float: left;">
                    <legend id="parameterList">参数列表</legend>
                    <div class="layui-field-box">
                        <ul id="extendParams" class="layui-timeline"></ul>
                    </div>
                </fieldset>
                <fieldset class="layui-elem-field" style="width: 300px; height: 400px;float: left;">
                    <legend id="defaultAllocation">默认配置</legend>
                    <div class="layui-field-box" style="text-align: center">
                        <button id="highSensitivity" class="layui-btn layui-btn-default"
                                style="margin-top: 10px;width: 150px;">高敏感度
                        </button>
                        <br/>
                        <button id="mediumSensitivity" class="layui-btn layui-btn-default"
                                style="margin-top: 10px;width: 150px;">中敏感度(推荐)
                        </button>
                        <br/>
                        <button id="lowSensitivity" class="layui-btn layuibtn-default"
                                style="margin-top: 10px;width: 150px;">低敏感度
                        </button>
                    </div>
                </fieldset>
                <div class="layui-clear"></div>
            </div>
            <div>
                <div class="input-range"
                     data-label="参数值："
                     data-label-id="parameterValue"
                     data-id="paramValue"></div>
<!--                <table class="input-table">-->
<!--                    <tr>-->
<!--                        <td id="l7" rowspan="2">参数值：</td>-->
<!--                        <td rowspan="2"><input type="text" value="0" class="input-number" id="paramValue"-->
<!--                                               oninput="value=value.replace(/[^\d]/g,'')"/>-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            <button type="button" class="input-up" id="valueUp"><i-->
<!--                                    class="button-edge button-up"></i></button>-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                    <tr>-->
<!--                        <td>-->
<!--                            <button type="button" class="input-down" id="valueDown"><i-->
<!--                                    class="button-edge button-down"></i></button>-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                </table>-->
                <button class="layui-btn layui-btn-default" id="recommendBtn">推荐值</button>
                <button class="layui-btn layui-btn-default" id="valueBtn">原始值</button>
                <span id="unitS">单位：</span><span id="unit">pixel</span>
            </div>
            <fieldset class="layui-elem-field">
                <legend id="description">说明</legend>
                <div class="layui-field-box">
                    <textarea class="layui-textarea" id="valueText" readonly="readonly"></textarea>
                    <textarea class="layui-textarea" id="valueRange" readonly="readonly"></textarea>
                </div>
            </fieldset>
            <button class="layui-btn layui-btn-default" id="saveExtend">保存配置</button>
            <button class="layui-btn layui-btn-default layui-btn-block" id="resetExtend">重置
            </button>
        </div>
    </fieldset>
</div>
</body>
</html>
