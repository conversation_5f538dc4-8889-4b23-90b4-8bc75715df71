<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>systemModuleStatus</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/dep/echarts.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/systemModuleStatus.js"></script>
    <style>
        .layui-form-label {
            width: 130px;
        }

        .layui-form-item {
            margin-bottom: 5px;
        }

        .span-class {
            width: 400px;
            position: absolute;
            padding: 9px 15px 9px 0px;
        }

        .status-display {
            display: none;
        }


    </style>
</head>
<body class="sub-body custom-style">

<div class="layui-tab" lay-filter="tabChange">
    <ul class="layui-tab-title">
        <li class="layui-this realTimeStatus" onclick="changeTag(1)">实时状态</li>
        <li class="historyStatus" onclick="changeTag(2)">历史状态</li>
<!--        <li class="setHistoryStatus" onclick="changeTag(3)">频率设置</li>-->
        <li class="statusAnalysis" onclick="changeTag(4)">状态分析</li>
    </ul>
    <div class="layui-tab-content layui-form">
        <div class="layui-tab-item layui-show">
            <fieldset class="layui-elem-field">
                <legend class="realTimeStatus">实时状态</legend>
                <div class="layui-field-box layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="calculation_">计算</label>
                        <div class="layui-input-inline">
                            <span id="calculation" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="trace_">轨迹</label>
                        <div class="layui-input-inline">
                            <span id="trace" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="encoding_">编码</label>
                        <div class="layui-input-inline">
                            <span id="encoding" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="vpss_">取流</label>
                        <div class="layui-input-inline">
                            <span id="vpss" class="span-class"></span>
                        </div>
                    </div>
                    <!--            <div class="layui-form-item" style="display: none">-->
                    <!--                <label class="layui-form-label" id="video_record_">录像</label>-->
                    <!--                <div class="layui-input-inline">-->
                    <!--                    <span id="video_record" class="span-class"></span>-->
                    <!--                </div>-->
                    <!--            </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="forwarding_">前端</label>
                        <div class="layui-input-inline">
                            <span id="forwarding" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="console_">控制</label>
                        <div class="layui-input-inline">
                            <span id="console" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="license_">授权</label>
                        <div class="layui-input-inline">
                            <span id="license" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="osd_">叠字</label>
                        <div class="layui-input-inline">
                            <span id="osd" class="span-class"></span>
                        </div>
                    </div>
                    <!--            <div class="layui-form-item" style="display: none">-->
                    <!--                <label class="layui-form-label" id="web_">页面</label>-->
                    <!--                <div class="layui-input-inline">-->
                    <!--                    <span id="web"></span>-->
                    <!--                </div>-->
                    <!--            </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="land_circle_">线圈</label>
                        <div class="layui-input-inline">
                            <span id="land_circle" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="light_signal_">信号</label>
                        <div class="layui-input-inline">
                            <span id="light_signal" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="NTP_">校时</label>
                        <div class="layui-input-inline">
                            <span id="NTP" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="Debug_">调试</label>
                        <div class="layui-input-inline">
                            <span id="Debug" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="uploader_">转发器</label>
                        <div class="layui-input-inline">
                            <span id="uploader" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="blacklist_">黑名单</label>
                        <div class="layui-input-inline">
                            <span id="blacklist" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="h264_">视频创建</label>
                        <div class="layui-input-inline">
                            <span id="h264" class="span-class"></span>
                        </div>
                    </div>
                    <!--            <div class="layui-form-item" style="display: none">-->
                    <!--                <label class="layui-form-label" id="h264_synthetic_">视频合成</label>-->
                    <!--                <div class="layui-input-inline">-->
                    <!--                    <span id="h264_synthetic" class="span-class"></span>-->
                    <!--                </div>-->
                    <!--            </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="video_maker_">录像合成</label>
                        <div class="layui-input-inline">
                            <span id="video_maker" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="checkuid_">设备检查</label>
                        <div class="layui-input-inline">
                            <span id="checkuid" class="span-class"></span>
                        </div>
                    </div>
                    <!--            <div class="layui-form-item" style="display: none">-->
                    <!--                <label class="layui-form-label" id="remote_access_">远程访问</label>-->
                    <!--                <div class="layui-input-inline">-->
                    <!--                    <span id="remote_access" class="span-class"></span>-->
                    <!--                </div>-->
                    <!--            </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="forward_event_">前端事件</label>
                        <div class="layui-input-inline">
                            <span id="forward_event" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="discovery_">发现设备</label>
                        <div class="layui-input-inline">
                            <span id="discovery" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="web_config_">页面配置</label>
                        <div class="layui-input-inline">
                            <span id="web_config" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="web_eventServer_">页面事件</label>
                        <div class="layui-input-inline">
                            <span id="web_eventServer" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="event_forward_">转发事件</label>
                        <div class="layui-input-inline">
                            <span id="event_forward" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="venc_h264_">视频编码</label>
                        <div class="layui-input-inline">
                            <span id="venc_h264" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="free_frame_">释放YUV帧</label>
                        <div class="layui-input-inline">
                            <span id="free_frame" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="vframe_receiver_">接收视频帧</label>
                        <div class="layui-input-inline">
                            <span id="vframe_receiver" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="merge_">识别爆闪帧</label>
                        <div class="layui-input-inline">
                            <span id="merge" class="span-class"></span>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" id="flash_">处理爆闪帧</label>
                        <div class="layui-input-inline">
                            <span id="flash" class="span-class"></span>
                        </div>
                    </div>
                    <!--            <div class="layui-form-item" style="display: none">-->
                    <!--                <label class="layui-form-label" id="command_processor_">后台命令处理器</label>-->
                    <!--                <div class="layui-input-inline">-->
                    <!--                    <span id="command_processor" class="span-class"></span>-->
                    <!--                </div>-->
                    <!--            </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label">实时刷新</label>
                        <div class="layui-input-block">
                            <input type="checkbox" name="yyy" lay-filter="switchRefresh" lay-skin="switch"
                                   lay-text="ON|OFF"
                                   id="switchRefreshID">
                        </div>
                    </div>
                </div>
            </fieldset>
            <button id="fresh" class="layui-btn layui-btn-default" onclick="getModuleStatus()">刷新状态</button>
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field">
                <legend class="historyStatus">历史状态</legend>
                <div class="layui-field-box">
                    <div class="layui-form">
                        <div class="layui-form-item layui-inline" style="width: 380px">
                            <label class="layui-form-label time" style="width: 40px;padding-left: 0">时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="date_input" placeholder="请选择日期时间段"
                                       class="layui-input" style="width: 310px">
                            </div>
                        </div>
                        <div class="layui-form-item layui-inline">
                            <span class="layui-btn query" lay-submit lay-filter="formDemo"
                                  onclick="submit()">查询</span>
                            <span type="reset" class="layui-btn layui-btn-primary reset"
                                  onclick="reset()">重置</span>
                        </div>
                    </div>
                    <table id="status_table" lay-filter="status_data"></table>
                    <div id="status_table_laypage"></div>
                </div>
            </fieldset>
        </div>
<!--        <div class="layui-tab-item">-->
<!--            <fieldset class="layui-elem-field">-->
<!--                <legend class="historyStatus">频率设置</legend>-->
<!--                <div class="layui-field-box">-->
<!--                    <div class="layui-form">-->
<!--                        <div class="layui-form-item">-->
<!--                            <label class="layui-form-label rate" style="width: 40px;padding-left: 0">频率</label>-->
<!--                            <div>-->
<!--                                <table>-->
<!--                                    <tr>-->
<!--                                        <td><input type="text" class="layui-input" style="width: 200px" id="stateRate">-->
<!--                                        </td>-->
<!--                                        <td style="padding: 10px;">(请输入正整数数值，单位为S)</td>-->
<!--                                    </tr>-->
<!--                                </table>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item">-->
<!--                            <label class="layui-form-label" style="width: 40px;padding-left: 0"></label>-->
<!--                            <div>-->
<!--                                <span class="layui-btn setRateConfig" lay-submit lay-filter="formDemo"-->
<!--                                      onclick="setRateConfig()">下发配置</span>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="layui-form-item">-->
<!--                            <label class="layui-form-label" style="width: 40px;padding-left: 0"></label>-->
<!--                            <div>-->
<!--                                <span class="layui-btn getRateConfig"-->
<!--                                      onclick="getRateConfig()">重置配置</span>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->

<!--            </fieldset>-->
<!--        </div>-->
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field">
                <legend class="statusAnalysis">状态分析</legend>
                <div class="layui-field-box">
                    <div class="layui-form">
                        <div class="layui-form-item layui-inline" style="width: 380px">
                            <label class="layui-form-label time" style="width: 40px;padding-left: 0">时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="date_inputAnalysis" placeholder="请选择日期时间段"
                                       class="layui-input" style="width: 310px">
                            </div>
                        </div>
                        <div class="layui-form-item layui-inline">
                            <span class="layui-btn query" lay-submit lay-filter="formDemo"
                                  onclick="getStatusAnalysis()">查询</span>
                            <span type="reset" class="layui-btn layui-btn-primary reset"
                                  onclick="resetAnalysis()">重置</span>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-md6">
                            <!--点击某一柱，err_code展示每个错误的次数柱状图，双击弹框展示时间表格-->
                            <div id="err" style="height: 500px"></div>
                            <div id="err_time" style="padding-left: 20px"></div>
                        </div>
                        <div class="layui-col-md6">
                            <!--点击某一柱，弹框展示展示每个错误码的时间表格-->
                            <div id="err_code" style="height: 500px"></div>
                            <div id="err_code_time" style="padding-left: 20px"></div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <!--            <button id="freshStatusAnalysis" class="layui-btn layui-btn-default" onclick="getStatusAnalysis()">刷新状态-->
            </button>
        </div>
    </div>
</div>
</body>
<div id="moduleDetail" style="padding: 20px;display: none">
    <table id="moduleDetailTable"></table>
</div>
<div id="brokenLine" style="padding: 20px;display: none">
    <form class="layui-form" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">统计方式</label>
            <div class="layui-input-block">
                <input type="radio" lay-filter="brokenLineRadio" name="brokenLineRadio" value="day" title="按天" checked>
                <input type="radio" lay-filter="brokenLineRadio" name="brokenLineRadio" value="hour" title="按小时">
            </div>
        </div>
    </form>
    <div id="brokenLineEcharts"></div>
</div>
</html>
