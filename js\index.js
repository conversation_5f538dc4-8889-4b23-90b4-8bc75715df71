var layer, loadingInterval, isSkip = false;
var index_ini;
var index_default;
var index_param = {};
$(document).ready(function () {

    // 设置当前frameElement名称，以便后续存储数据使用
    let frameElement = "";
    if (window.frameElement) {
        frameElement = window.frameElement.id;
    }
    sessionStorage.setItem("frameElement", frameElement);
    //将langMessage注册到全局
    setLanguage(langMessage);

    //设置语言
    let lang = "";
    lang = getCookie('i18ns');
    if (!lang) {
        lang = 'zh'
    }
    if (lang !== 'zh') {
        $("#skip").html("skip")
    }
    $("#select_i18n").val(lang);


    let height = window.screen.height;
    $("#index_frame").css({
        top: height / 2
    });
    $("#pngContainer").css({
        top: height / 2 - 50
    });
    onloadConfig('./config/rpm.json', 'rpmConfig', function () {
        // $("#welGif").attr({
        //     src: "./img/welcome.gif"
        // });
        // $("#skip").off('click').on('click', function () {
        //     isSkip = true;
        //     showLogin();
        // });
        setTimeout(function () {
            isSkip = true;
            showLogin();
        }, 500)

        function showLogin() {
            //console.log($("#welcome")[0].width);
            // 获取SDC版本做网络协议兼容
            let version = getSe("rpmConfig").protocol;
            console.log("deviceVersion:", version);
            let newAgreement = false;
            // 1旧网络协议，2新网络协议
            if (parseInt(version) === 2) {
                newAgreement = true;
            }
            setSe("SDC", newAgreement);


            let mul = parseInt($("#welGif")[0].width) / 1920;

            let appName = getAppName()

            if (lang !== "zh") {
                lang = "_" + lang;
            } else {
                lang = "";
            }
            $("#welPng")[0].src = "./img/" + appName + lang + ".png";
            $("#welPng")[0].style.width = parseInt(mul * 555) + "px";
            // $("#welTitle").html(appName);
            $("#gifContainer").css({
                display: 'none'
            });
            $("#pngContainer").css({
                display: 'block'
            });
            //$("#welcome").attr("src","./img/welcome.png");
            $("#skip").css({display: "none"});
            $("#select_i18n").css({display: "block"});
            fadeOut(document.getElementById("pngContainer"), 60, 1, 100, 0.1);
            fadeIn(document.getElementById("index_frame"), 1, 150, 0.1);
        }

        function into() {
            //console.log($("#welcome")[0].width);
            // 获取SDC版本做网络协议兼容
            let version = getSe("rpmConfig").protocol;
            console.log("deviceVersion:", version);
            let newAgreement = false;
            // 1旧网络协议，2新网络协议
            if (parseInt(version) === 2) {
                newAgreement = true;
            }
            setSe("SDC", newAgreement);
            $("#skip").css({display: "none"});
            $("#select_i18n").css({display: "block"});
            setCookie('i18ns', 'zh', 0);

            let data = {
                username: "admin",
                password: "12345"
            }
            sessionStorage.setItem("loginUser", JSON.stringify(data));
            setCookie('username', data.username, 30);
            setCookie('password', data.password, 30);
            setCookie('rememberMe', 1, 30);

            setInfo();//根据当前屏幕大小判断当前需要显示的图片大小
            window.location.href = 'html/content.html'; //后台主页
            parent.$("#select_i18n").hide();
        }

        setTimeout(function () {
            if (!isSkip) {
                showLogin();
            }
        }, 4500);
    });
    layui.use(['layer'], function () {
        layer = layui.layer;
    });
    onloadConfig('./config/config.json', 'projectConfig');

    $("#select_i18n").off("change").on("change", function () {
        lang = $("#select_i18n option:selected").val();
        setCookie('i18ns', lang, 0);
        window.location.href = "./index.html";
    });
});

/**
 * 将元素ele逐渐上移淡入
 * @param ele 元素
 * @param s 速度
 * @param p 距离上面高度
 */
var fadeIn = function (ele, s, p, timer) {
    if (ele.style.opacity == 1) {
        return
    }
    let speed = s || 5;
    let opacity = 1;
    let top = p || 0;
    let first = 0;
    let sec = timer || 20;
    // let t = setInterval(function () {
    //     if (parseInt(ele.style.top) === top) {
    //         clearInterval(t);
    //         ele.contentWindow.initLogin();
    //         return
    //     }
    //     ele.style.opacity = parseFloat(ele.style.opacity) + speed * 2 / 100;
    //     if (parseInt(ele.style.top) !== top) {
    //         ele.style.top = (parseInt(ele.style.top) - speed) + "px";
    //     }
    // }, sec)
    ele.style.top = top + 'px'
    ele.style.opacity = 1
    ele.contentWindow.initLogin();
};
/**
 * 将元素ele逐渐上移淡出
 * @param ele 元素
 * @param s1 尺寸
 * @param s2 速度
 * @param t 距离上面高度
 */
var fadeOut = function (ele, s1, s2, t, timer) {
    //console.log("fadeOut");
    let size = s1 || 50;
    let speed = s2 || 5;
    let top = t || 0;
    let sec = timer || 20;
    let initW = parseInt($("#welPng")[0].style.width);
    $("#welPng")[0].style.width = (80 + size) + "px";
    // let i = setInterval(function () {
    //     if (parseInt(ele.style.top) === top) {
    //         clearInterval(i);
    //         return
    //     }
    //     ele.style.top = (parseInt(ele.style.top) - speed) + "px";
    // }, sec)
    ele.style.top = top + 'px'
};
//##############################################################################################################################
var fadeOut2 = function (ele, s1, p, s2) {
    let speed1 = s1 || 5;
    let speed2 = s2 || 5;
    let initW = parseInt($("#welPng")[0].style.width);
    let defaultW = 100;
    let width = p || 0;
    let top = 10;
    if (ele.style.width == p) {
        return
    }
    let t = setInterval(function () {
        if (parseInt(ele.style.top) === top || defaultW === width) {
            clearInterval(t);
            ele.style.zIndex = 998;
            return
        }
        ele.style.top = ((parseInt(ele.style.top) ? parseInt(ele.style.top) : 50) - speed1) + "%";
        defaultW = defaultW - speed2;
        $("#welPng")[0].style.width = (defaultW / 100 * initW) + "px";
    }, 20)
};
var indexLoading = function (time, msg, interval) {
    let option = {
        content: "<div id='loadingContent'>" + msg + "<span id='loadingProgress'></span></div>", shade: 0.8
    };
    if (time) {
        option.time = time * 1000
    }
    let ins = layer.load(1, option);
    if (interval) {
        changeProgressInterval(interval)
    }
    return ins;
};
let closeLoading = function (index) {
    if (index) {
        layer.close(index)
    }
};

var changeProgressInterval = function (s) {
    if (loadingInterval) {
        clearInterval(loadingInterval)
    }
    loadingInterval = setInterval(function () {
        changeProgress(s--);
        if (s === 0) {
            if (loadingInterval) {
                clearInterval(loadingInterval)
            }
        }
    }, 1000)
};
var changeProgress = function (text) {
    if ($("#loadingProgress").length) {
        $("#loadingProgress").html(text)
    }
};
