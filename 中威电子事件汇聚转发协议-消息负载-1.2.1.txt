








中威电子
事件汇聚转发协议

（消息负载）



    协议版本：v1.2.1
















中威电子




修改记录
负载版本
修改日期
负责人
备注
v1.2.0
2024-05-14
闫忠正
制定初版协议
V1.2.1
2025-06-11
周学敏
修改事件响应内容的格式








目录
1. 概述	3
2. 术语和定义	4
3.消息规范	5
3.1消息公共属性定义	5
3.1.1 字符编码	5
3.1.2请求消息和响应消息	5
3.1.3 响应示例	5
3.2 告警消息	5
3.2.1负载内容格式	5
3.2.2 负载内容示例	7
附录	10
厂商列表	10
设备类型	10
车道类型	10
车道类别	10
车道规则	11
方位方向	11
场景类型	11
物体种类	11
物体颜色	12
信号灯类型	12
车牌类型	12
车牌颜色	13
车型大小	13
车辆类型	14
图像类别	14
事件状态	14
事件类型	14




1. 概述
    本协议描述的是"交通事件智能算法"和"电子警察智能算法"产生的事件对页面推送时的消息负载
    本协议仅描述完整事件转发协议中的负载信息，需要配合通讯协议将此负载发送到接收方。


2. 术语和定义
* 字段必要性：在json文本中该字段是否必须提供。R表示必须，O表示非必须。
* host：设备或通道的IP地址。算法受限无法获取时填写"0.0.0.0"。
* 厂商（vender）：算法厂商、供应商，标记事件是由哪家厂商的算法产生的。
* 设备（device）：带有计算资源的检测设备，如带计算卡的服务器、AI相机等。
* 通道（channel）：检测数据源，如视频流、图片流等。通道信息一般指相关相机的信息。
* 关联事件：与当前事件相关联的事件。例如：违停结束时产生的事件与违停开始时产生的事件是相关联的，"开始事件"是"结束事件"的关联事件。
* 事件类型：告警事件的类型。电子警察算法和交通事件算法产生告警事件中,部分事件为同名事件。但两种算法适用的场景不同，判定的条件也有差异，为了避免出现歧义，交通事件类型和电子警察事件类型不做整合。事件类型值小于1000的是交通事件，大于1000的是电子警察事件。
* 空间坐标（spatial coordinates）： GCJ-02坐标系（国测局坐标，火星坐标系）或CGCS2000坐标系（国家大地坐标系）。目前仅做规划，尚未确定坐标系类型。
* 图片hash：原始图片的MD5摘要，即图片二进制数据的摘要。在解析消息负载时，如果图片类型为URL，应先下载图片再校验图片内容；如果图片类型为base64，应先将图片base64数据解码为二进制图片，再校验图片内容。


3.消息规范
3.1消息公共属性定义
    请求和响应数据均为json文本。
3.1.1 字符编码
本协议默认使用UTF-8字符编码。
3.1.2请求消息和响应消息
		请求参数：{"channel_id":0, "web_cmd":28}//查询事件对应的指令		响应消息：
消息字段
字段类型
R/O
描述
ret
int
R
响应码 0：成功；其他：错误（错误信息见响应内容）
has_data
int
R
0代表没数据了，1代表还有数据
data
object
O
响应附带数据 json数据

3.1.3 响应示例
{
"ret": 0,
"has_data": 0,// 0代表没数据了，1代表还有数据
 "data":{}//json对应的数据，每次发一条数据，内容格式为3.2.1对应的内容
}
3.2 告警消息
交通事件、前卡电警、后卡电警等告警信息上报的消息负载。

3.2.1负载内容格式
字段名称
字段类型
必要性
描述
version
string
R
协议版本
vender
string
R
厂商编码 算法供应商参考附录.厂商列表
device
object
R
设备信息

type
int
R
设备类型 参考附录.设备类型

code
string
R
设备编号 设备的唯一编号

name
string
R
设备名称

host
string
R
设备主机 设备的IP地址

location
string
O
设备位置 设备的地理位置

desc
string
O
设备描述 如：型号、用途等信息
channel
object
R
通道信息

code
string
R
通道编号 通道的唯一编号，透传算法页面的通道编号

name
string
R
通道名称透传算法页面的通道名称

host
string
R
通道主机提供检测流的设备IP，透传算法页面的通道IP

location
string
O
通道位置 产生流的地理位置，透传算法页面的所在道路

lat
double
R
通道经度透传算法页面的通道经度

lon
double
R
通道纬度透传算法页面的通道纬度

sc
string
O
通道空间坐标
object
object
R
目标物体信息

kind
int
R
物体种类 参考附录.物体种类

color
int
R
物体颜色可表示车辆颜色、行人衣着颜色，参考附录.物体颜色

distance
int
O
物体距离 单位：m

sc
string
O
物体空间坐标
event
object
R
事件信息

type
int
R
事件类型 参考附录.事件类型

code
string
R
事件编号 事件唯一标识

assoc_code
string
O
关联事件 与当前事件相关联事件的编号

desc
string
R
事件描述 事件类型的描述

datetime
string
R
事件时间 如："2024-04-10 08:00:00"

utc_sec
int
R
UTC秒数

utc_msec
int
R
UTC毫秒

vio_code
string
O
公安网违法代码

status
int
O
事件状态 参考附录.事件状态
lane
object
R
车道信息

type
int
R
车道类型参考附录.车道类型

code
int
R
车道编号如：1、2、3

kind
int
R
车道类别参考附录.车道类别

rule
int
R
车道规则参考附录.车道规则

direction
int
R
行车方向 参考附录.方位方向

affected
int
O
受影响车道数 大于等于0，未知：0；其他：受影响车道数

speed_limit
int
O
最高限速 违反车速规定事件必填

minimum_speed
int
O
最低限速 违反车速规定事件必填
trigger_type
int
R
触发方式未知：0；视频：1；图片：2；雷达：3
video
object
O
视频场景信息

direction
int
O
镜头方向 参考附录.方位方向

scene
int
O
场景类型 参考附录.场景类型
light
object
R/O
交通信号灯信息 闯红灯事件必填

type
int
R
信号灯类型 参考附录.信号灯类型

red_duration
int
O
红灯亮起时间

red_cycle
int
O
红灯周期
vehicle
object
R/O
车辆信息 目标物体为车辆时必填

type
int
R
车辆类型 参考附录.车辆类型

size
int
R
车型大小 参考附录.车型大小

length
int
O
车辆长度

axles
int
O
车辆轴数

speed
int
O
行驶速度

orientation
int
O
车辆朝向 未知：0；车头：1；车尾：2

brand
string
O
车辆品牌

sub_brand
string
O
车辆子品牌
plate
object
R/O
车牌信息 目标物体为车辆时必填

type
int
R
车牌类型 参考附录.车牌类型

color
int
R
车牌颜色 参考附录.车牌颜色

confidence
int
R
车牌置信度

license
string
R
车牌号码
visibility
object
R/O
能见度信息 能见度事件必填

distance
int
R
能见度距离

level
int
R
能见度等级
recording
string
O
伴随录像 录像URL地址
image_num
int
R
图像数量
images
array
R/O
图像信息object数组，图像数量大于0时必填

serial
int
R
图像序号

type
int
R
图像类别 参考附录.图像类别

datetime
string
R
图像抓拍时间 格式"2024-04-09 08:00:00"

utc_sec
int
R
图像抓拍时UTC秒数

utc_msec
int
R
图像抓拍时的毫秒

lic_flag
int
R
车牌识别标记 1表示在当前图片识别的车牌

lic_rect
string
O
车牌区域 {x,y,w,h}

obj_rect
string
R
目标区域 {x,y,w,h}

hash
string
R
图像哈希值

load_kind
int
R
图像载荷类型 未知：0；URL：1；base64：2

load_data
string
R
图像载荷数据

3.2.2 负载内容示例
{
    "version": "v1.2.1",
    "vender": "joyware",
    "device": {
        "type": 1,
        "code": "ivs1542484",
        "name": "IncidentServerI",
        "host": "***************",
        "location": "dev-location",
        "desc": "IVS1800 Incident Algo"
    },
    "channel": {
        "code": "a4c583d419b65ed34f",
        "name": "Gxx高速A-B",
        "host": "*************",
        "location": "stream-location",
        "lat": 118.79647,
        "lon": 32.05838,
        "sc": "stream-sc"
    },
    "object": {
        "kind": 1,
        "color": 2,
        "distance": 3,
        "sc": "obj-sc"
    },
    "event": {
        "type": 2,
        "code": "af9da92af9f29aca",
        "assoc_code": "",
        "desc": "逆向行驶",
        "datetime": "2024-04-0908:00:00",
        "utc_sec": **********,
        "utc_msec": 185,
        "vio_code": "01234",
        "status": 1
    },
    "lane": {
        "type": 1,
        "code": 2,
        "kind": 3,
        "rule": 4,
        "direction": 5,
        "affected": 6,
        "speed_limit": 120,
        "minimum_speed": 60
    },
    "trigger_type": 1,
    "video": {
        "direction": 2,
        "scene": 3
    },
    "light": {
        "type": 1,
        "red_duration": 12,
        "red_cycle": 45
    },
    "vehicle": {
        "type": 1,
        "size": 2,
        "length": 3,
        "axles": 4,
        "speed": 5,
        "orientation": 6,
        "brand": "brand",
        "sub_brand": "sub-brand"
    },
    "plate": {
        "type": 1,
        "color": 2,
        "confidence": 3,
        "license": "plate-license"
    },
	"visibility": {
        "distance": 1,
        "level": 1
    },
    "recording": "http://192.168.1.1:8080/vio/abc.mp4",
    "image_num": 2,
    "images": [
        {
            "serial": 1,
            "type": 1,
            "utc_sec": **********,
            "utc_msec": 185,
            "datetime": "2024-04-09 08:00:00",
            "lic_flag": 1,
            "lic_rect": "750,912,128,30",
            "obj_rect": "642,664,347,395",
            "hash": "64d2ab5eb77234554b88796cab8e3add",
            "load_kind": 1,
            "load_data": "http://***************:8080/img/1.jpg"
        },
        {
            "serial": 2,
            "type": 1,
            "utc_sec": **********,
            "utc_msec": 185,
            "datetime": "2024-04-09 08:00:00",
            "lic_flag": 1,
            "lic_rect": "750,912,128,30",
            "obj_rect": "642,664,347,395",
            "hash": "431a2af72564af638b75feb9680541db",
            "load_kind": 1,
            "load_data": "http://***************:8080/img/2.jpg"
        }
    ]
}



附录
厂商列表
厂商编码
厂商名称
undefined
未定义
frontopsky
中威电子股份有限公司

设备类型
值
设备类型
0
未知
1
交通事件-服务器
2
交通事件-枪机
3
交通事件-球机
4
电子警察-服务器
5
电子警察-前卡相机
6
电子警察-后卡相机

车道类型
值
车道类型
0
未定义
1
行车道
2
应急车道
4
非机动车道
8
公交车道
16
倒流带
32
单行道
64
禁止左转车道
128
禁止右转车道
256
无效车道

车道类别
值
车道类别
0
未定义
1
直行
2
左转
4
右转
8
掉头

车道规则
值
车道规则
0
未知
1
上行
2
下行

方位方向
值
方位方向
0
未知
1
东
2
南
3
西
4
北
5
东北
6
东南
7
西南
8
西北

场景类型
值
场景类型
0
未知
1
高速场景
2
隧道场景
3
市区路段
4
市区路口
5
测试场景

物体种类
值
物体种类
0
未知
1
行人
2
非机动车
3
摩托车
4
机动车
5
遗撒物/遗留物/障碍物
6
烟火

物体颜色
值
物体颜色
0
未知
1
黑色
2
灰色
3
白色
4
红色
5
黄色
6
绿色
7
蓝色
8
粉色
9
紫色
10
棕色

信号灯类型
值
信号灯类型
0
未定义
1
机动车信号灯
2
车道信号灯
3
方向指示信号灯
4
闪光警告信号灯
5
道口信号灯
6
非机动车信号灯
7
人行横道信号灯
8
掉头信号灯
9
左转非机动车信号灯

车牌类型
值
车牌类型
说明
0
未知
对应国标99其他号牌
1
大型汽车号牌
黄底黑字
2
小型汽车号牌
蓝底白字
3
使馆汽车号牌
黑底白字、红"使"字
4
领馆汽车号牌
黑底白字、红"领"字
5
境外汽车号牌
黑底白/红字
6
外籍汽车号牌
黑底白字
7
两、三轮摩托车号牌
黄底黑字
8
轻便摩托车号牌
蓝底白字
9
使馆摩托车号牌
黑底白字、红"使"字
10
领馆摩托车号牌
黑底白字、红"领"字
11
境外摩托车号牌
黑底白字
12
外籍摩托车号牌
黑底白字
13
农用运输车号牌
黄底黑字黑框线
14
拖拉机号牌
黄底黑字
15
挂车号牌
黄底黑字黑框线
16
教练汽车号牌
黄底黑字黑框线
17
教练摩托车号牌
黄底黑字黑框线
18
试验汽车号牌

19
试验摩托车号牌

20
临时人境汽车号牌
白底红字黑"临时人境"
21
临时人境摩托车号牌
白底红字黑"临时人境"
22
临时行驶车号牌
白底黑字黑框线
23
警用汽车号牌

24
警用摩托号牌


车牌颜色
值
事件状态
0
未知
1
蓝色
2
黄色
3
白色
4
黑色
5
渐变绿（小型新能源车牌颜色）
6
黄绿色（大型新能源车牌颜色）

车型大小
值
车型大小
0
未知
1
小型车
2
中型车
3
大型车
4
特大型车

车辆类型
值
事件状态
0
未知
1
非机动车
2
摩托车
3
轿车
4
SUV
5
MPV
6
面包车
7
皮卡车
8
小型客车
9
轻型客车
10
中型客车
11
大型客车
12
轻型卡车
13
中型卡车
14
重型卡车

图像类别
值
事件状态
0
未知
1
全景图片
2
特写图片
3
合成图片

事件状态
值
事件状态
0
未知
1
开始
2
持续
3
结束

事件类型
值
事件类型
备注
0
未知

1
逆向行驶

2
违法停车

3
行人闯入

4
交通阻塞

6
交通事故

7
遗留物
车道中的遗留物、遗撒物、障碍物
8
卡口数据
车辆正常通行抓拍
9
驶离车道

10
烟火

11
施工/封路

12
压线行驶

13
机动车占用应急车道

14
危险品车

19
非机动车闯入

20
车辆倒车

21
非机动车未戴头盔

23
交通拥挤
多个车辆行驶缓慢
25
占用非机动车道

26
连续变道

27
车辆缓行
单一车辆行驶缓慢
28
三轮车载人

29
人员聚集

30
客运车辆

31
变道行驶

32
工程车辆

33
变速行驶
突然变速 急走急停
34
车道排队

35
工作人员

100
能见度正常

101
能见度低
能见度距离小于500米，达到预警级别
200
视频丢失
视频流中断
202
视频清晰度异常

204
视频噪声

206
视频冻结

208
视频遮挡

210
镜头移动
球机、云台的镜头发生了移动
212
视频亮度过亮

214
视频亮度过暗

301
路面破损

303
山体滑坡

304
路面积水

305
路面积雪

1001
逆向行驶

1002
违章掉头

1003
机动车闯红灯
机动车不按交通信号灯规定通行
1004
压线行驶

1005
违反车速规定

1006
违法变道

1007
借道行驶

1008
违章停车

1009
机动车不在机动车道内行驶

1010
机动车违反规定使用专用车道

1011
路口滞留

1012
在禁止左转路口左转

1013
在禁止右转路口右转

1014
人行道违停
遇前方机动车排队等待、缓慢行驶时，在人行横道内停车等候
1015
网格线违停
遇前方机动车排队等待、缓慢行驶时，在网状区域内停车等候
1016
未礼让行人

1017
未交替通行
未依次交替驶入车道减少、变窄后的路口、路段的
1018
大弯小转
左转弯时,本来应该以较大的转向半径转弯,但却以较小的半径转弯的行为
1019
机动车卡口
机动车正常通行时的抓拍
1020
左转不让直行

1021
越线停车

1022
在斑马线掉头

1023
掉头不让直行

1024
大车右转未停车让行
大型车辆右转时未按规定停车让行
1025
连续变道

1026
变道不使用转向灯

1027
转弯不使用转向灯

1028
违法占用应急车道

1029
机动车占用非机动车道

1030
加塞

1031
危化品车

1032
不开车灯
在夜间或视线不良的情况下驾驶机动车时未开启车灯
1033
违反尾号限行

1034
外省市小型车闯禁令
外省市小型车违反禁令标志指示
1035
沪C号牌闯禁令
沪C号牌违反禁令标志指示
1036
教练车闯禁令
教练车违反禁令标志指示
1037
货车违反禁令上匝道

1038
黑名单违法类型1

1039
黑名单违法类型2

1040
黑名单违法类型3

1041
黑名单违法类型4

1042
黑名单违法类型5

1043
黑名单违法类型6

1044
黑名单违法类型7

1045
黑名单违法类型8

1046
非机动车闯红灯
非机动车不按交通信号灯规定通行
1047
非机动车逆行

1048
非机动车闯禁令
非机动车违反禁令标志指示
1049
非机动车不带头盔

1050
非机动车卡口
车辆正常通行时的抓拍
1051
非机动车载人

1052
摩托车卡口
摩托车正常通行时的抓拍
1053
摩托车闯禁令	
摩托车违反禁令标志指示
1054
外牌摩托车闯禁令
外牌摩托车违反禁令标志
1055
摩托车闯红灯
摩托车不按交通信号灯规定通行
1056
摩托车借道行驶

1057
摩托车压线行驶

1058
客车闯禁令
客车违反禁令标志指示
1059
货车闯禁令
货车违反禁令标志指示
1060
货占客
货车占用客车车道，一般适用于单向多于3车道的路况
1061
不系安全带
驾驶人未按规定使用安全带
1062
主驾打手机
驾驶时拨打接听手持电话

中威电子事件汇聚转发协议-消息负载

2025-06-11消息负载：v1.2.1		6
版权所有(c)中威电子

