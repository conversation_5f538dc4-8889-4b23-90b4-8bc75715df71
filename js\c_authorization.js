// 这里是老的授权文件，可以不看，授权相关都在c_authorization_new.js文件中


/**
 * Create by Chelly
 * 2019/11/18
 */
var form, laydate, element, layer, upload;
var exts = '.token';

$(document).ready(function () {
    window.parent.setNavValue(3);
    layui.use(['form', 'laydate', 'element', 'layer', 'upload'], function () {
        form = layui.form, laydate = layui.laydate, element = layui.element, layer = layui.layer, upload = layui.upload;
        laydate.render({
            elem: '#setTimeValue'
            , type: 'time'
        });

        let appName = getAppName()
        let allowed = getSe("projectConfig").allowedAuth;
        if (allowed.indexOf(appName) < 0) {
            $(".import").remove();
        }
        form.render();
        if (isIE()) {
            let result = initialize_ocx('authContainer');
            if (!result.result) {
                layer.open({
                    title: langMessage.common.error,
                    shade: 0.8,
                    btn: false,
                    content: result.msg,
                    anim: 6
                });
            }
        }
    });
    $("#exportAuth").off("click").on("click", function () {
        initWebService(webserviceCMD.CMD_GET_AUTH_CODE, null, getAuth);
    });
    $('#authFile').off("click").on("click", function (e) {
        let importBtn = $("#importAuth");
        importBtn.addClass("layui-btn-disabled");
        importBtn.css({
            display: 'none'
        });
        setDataset(importBtn[0], "authFileBase64", "");
        $("#authFileInput").val("");
        let deviceInfo = getSe("deviceInfo");
        if (!deviceInfo) {
            reloadConfig(reloadAuthCallback);
        } else {
            reloadAuthCallback();
        }
    });

    //initAuthCode();
});
var reloadAuthCallback = function (file) {
    let deviceInfo = getSe('deviceInfo');
    let auth_status = deviceInfo.auth_status;
    let unique_id = deviceInfo.unique_id;
    let startGetFile = function () {
        if (isIE()) {
            readAuthFile(unique_id, importAuthCallback)
        } else {
            getFileList(unique_id)
        }
    };
    if (auth_status) {
        layer.confirm(langMessage.authorization.authMsg.IMPORT_CONFIRM, {
            btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
        }, function (index, layero) {
            layer.close(index);
            startGetFile()
        }, function (index) {
            layer.close(index);
        });


    } else {
        startGetFile()
    }
};

var getFileList = function (uid) {
    $("#authFileInput").click();
    $("#authFileInput").off("change").on("change", function (e) {
        let files = this.files;
        readFileList(files, uid);
    });
};
/**
 * 旧网络协议
 * @param data
 */
var getAuth_1 = function (data) {
    let code = data.authcode;
    let blob = dataURLtoBlob(code);
    analysisFile(blob, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            doSaveBlob('exportAuth', location.hostname + '-' + info.crc.toString(16).toUpperCase() + exts, blob);
        } else {
            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});
        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    }, 292);
    // getPath(code, exportAuthCallback);
};
var getAuthSuc = function (data) {
    layer.confirm('授权成功！（授权重启后生效）是否确认重启？', {
        btn: ['确定', '取消',] //可以无限个按钮
    }, function (index, layero) {
        // changeCameraParam(data);
        layer.close(index);
        initWebService(webserviceCMD.CMD_REBOOT, null, loadingWait, getSe("projectConfig").index_auth_time);
        //loadingWait(60);
    }, function (index) {
        layer.close(index);
    });
};

var exportAuthCallback = function (Folder, auth, name) {
    let filePath = Folder + name;
    let result = saveAuthcode(filePath, auth);
    if (result != 'FALSE') {
        $("#exportPath").html("<i class=\"layui-icon layui-icon-tips\"></i>文件导出为：" + result);
        layer.msg("导出成功！", {icon: 1, time: 5000})
    } else {
        $("#exportPath").html("");
        layer.msg("导出失败！", {icon: 2, time: 5000})
    }
};
var importAuthCallback_1 = function (result) {
    let importA = $("#importAuth");
    if (result.result) {
        importA.removeClass("layui-btn-disabled");
        importA.css({
            display: 'inline-block'
        });
        setDataset(importA[0], "authFileBase64", result.base);
        importA.off('click').on('click', function () {
            let param = {
                authFileBase64: getDataset($("#importAuth")[0]).authFileBase64
            };
            initWebService(webserviceCMD.CMD_IMPORT_AUTH_FILE, param, getAuthSuc, null);
        });
    } else {
        layer.msg(result.msg, {icon: 2, time: 5000});
        importA.css({
            display: 'none'
        })
    }
};
var getFileIE = function (uid) {
    if (isIE()) {//IE浏览器保存文本框内容
        // name = "token-" + dateValue + timeValue+'-'+host;
        let fso, tf, fileList = [];
        try {
            fso = new ActiveXObject("Scripting.FileSystemObject");
            // var Message = "请选择文件夹！\n文件将保存为" + name; //选择框提示信息
            let Message = "请选择文件夹！"; //选择框提示信息
            let Shell = new ActiveXObject("Shell.Application");
            // let Folder = Shell.BrowseForFolder(0, Message, 0x0040, 0x11); //起始目录为：我的电脑
            let Folder = Shell.BrowseForFolder(0, Message, 0); //起始目录为：桌面
            if (Folder != null) {
                // Folder = Folder.items(); // 返回 FolderItems 对象
                // Folder = Folder.item(); // 返回 Folderitem 对象
                // Folder = Folder.Path; // 返回路径
                console.log(Folder);
                Folder = Folder.self.path;
                if (Folder.charAt(Folder.length - 1) != "\\") {
                    Folder = Folder + "\\";
                }
                tf = fso.GetFolder(Folder);
                IEGetFiles(fso, tf, fileList);
                readFileList(fileList, uid);
            }
        } catch (e) {
            console.log(e.message);
            layer.msg('当前浏览器不支持此方法，请修改浏览器设置！', {icon: 2});
        }
    } else {
        layer.msg('当前浏览器不支持此方法', {icon: 2});
    }
};
/**
 * 使用JScript循环读取文件夹中的文件
 * Unicode读取出来的为两个字节一个字符的字符串数组，需要进行转义
 * @param fso 传入的控件
 * @param folder 文件夹路径
 * @param fileList 读取的文件数组
 */
var IEGetFiles = function (fso, folder, fileList) {
    //取文件夹
    let underFolders = new Enumerator(folder.SubFolders);
    //取文件
    let underFiles = new Enumerator(folder.files);
    for (; !underFiles.atEnd(); underFiles.moveNext()) {
        let fn = "" + underFiles.item();
        // File file = ifile.getLocation().toFile;
        if (underFiles.item().size > 0) {
            let ForReading = 1, ForWriting = 2;
            let ForCreate = true, NoCreate = false;
            let TristateTrue = -1, //以 Unicode 方式打开文件
                TristateFalse = 0, //以 ASCII 方式打开文件
                TristateUseDefault = -2; //使用系统默认值打开文件
            let ts0 = fso.OpenTextFile(fn, ForReading, NoCreate, TristateTrue);
            let s0 = ts0.ReadAll();
            let getS = str2ab(s0);
            let getBlob = new Blob([getS]);
            let file = {
                name: "" + underFiles.item().name,
                size: underFiles.item().size,
                file: getBlob,
                type: 'IE'
            };
            ts0.Close();
            fileList.push(file)
        }
    }
    for (; !underFolders.atEnd(); underFolders.moveNext()) {
        IEGetFiles(fso, underFolders.item(), fileList);
    }
};

var readFileList = function (files, uid) {
    let fileList = [];
    let checkExtFiles = checkFiles(files);
    if (checkExtFiles.length === 0) {

        layer.msg(langMessage.authorization.matchFail, {icon: 2});
        return
    }
    for (let i = 0; i < checkExtFiles.length; i++) {
        analysisFile(checkExtFiles[i], function (info) {
            if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
                fileList.push(info);
            }
            if (i === (checkExtFiles.length - 1)) {
                compareAuth(fileList, uid, importAuthCallback)
            }
        }, function (e) {
            console.log(e)
        });
    }
};
var loadingWait = function (s) {
    let source = getTopWindow();

    let app_name = getAppName()

    let loading = source.indexLoading(s, langMessage.common.restartAlgorithm);

    setTimeout(function () {
        source.closeLoading(loading);
        source.location.href = "https://" + location.host + "/SDCWEB/" + app_name + "/index.html";
    }, s * 1000);
};
var initAuthCode = function () {
    initWebService(webserviceCMD.CMD_GET_AUTH_SHORT, null, getAuthCodeCallback);
};
var getAuthCodeCallback = function (data) {
    let code = data.authcode;
    let blob = dataURLtoBlob(code);
    analysisFile(blob, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            let info = code;
            showQRCode(info)
        } else {

            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});

        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    }, 68);
};


/**
 * 显示二维码，弃用
 * @param info
 */
var initAuthCode = function () {
    initWebService(webserviceCMD.CMD_GET_AUTH_SHORT, null, getAuthCodeCallback);
};
var showQRCode = function (info) {
    let qrcode = new QRCode(document.getElementById("authCode"), {
        text: "http://139.224.58.79/authorizeWeb/formHtml.html?" + info,
        //text: "http://192.168.1.101:8081/TestWeb1/WebRoot/formHtmlTest.html?" + info,
        width: 200,
        height: 200,
        colorDark: "#000000", //二维码颜色（默认黑色）
        colorLight: "#ffffff", //二维码背景颜色（默认白色）
        correctLevel: QRCode.CorrectLevel.H //二维码容错level（默认为高）
    });
    //$("#qrcodeDiv").qrcode(encodeURI("http://中文中文"));
    $("#authCode canvas")[0].getContext('2d').drawImage($("#authImg")[0], 80, 80, 40, 40);
};

var getAuthFail = function () {

    layer.msg(langMessage.authorization.authMsg.IMPORT_FAIL, {icon: 2, time: 5000});

};
//新网络协议
var getAuth_2 = function (data) {
    analysisFile(data, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            doSaveBlob('exportAuth', location.hostname + '-' + info.crc.toString(16).toUpperCase() + exts, data);
        } else {
            layer.msg("文件损坏请重试！", {icon: 2, time: 5000});
        }
    }, function (e) {
        layer.msg("未知错误！", {icon: 2, time: 5000});
        console.log(e)
    }, 292);
    // getPath(code, exportAuthCallback);
};
var importAuthCallback_2 = function (result) {
    let importA = $("#importAuth");
    if (result.result) {
        importA.removeClass("layui-btn-disabled");
        importA.css({
            display: 'inline-block'
        });
        importA.off('click').on('click', function () {
            initWebService(webserviceCMD.CMD_IMPORT_AUTH_FILE, {authFileBase64: result.base}, getAuthSuc, null);
        });
    } else {
        layer.msg(result.msg, {icon: 2, time: 5000});
        importA.css({
            display: 'none'
        })
    }
};

var getAuth = function (data) {
    let type = getSe("SDC");
    if (type) {
        getAuth_2(data)
    } else {
        getAuth_1(data)
    }
};
var importAuthCallback = function (result) {
    let type = getSe("SDC");
    if (type) {
        importAuthCallback_2(result)
    } else {
        importAuthCallback_1(result)
    }
};

