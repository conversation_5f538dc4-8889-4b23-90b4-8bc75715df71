var form, colorpicker, slider, element, layer;
//JavaScript代码区域
$(document).ready(function () {
    layui.use(['element', 'layer', 'form', 'colorpicker', 'slider'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, colorpicker = layui.colorpicker, slider = layui.slider;
        $(".input-number").attr({
            disabled: 'disabled'
        });
        let checkArray = ['turnLeft', 'goStraight', 'turnRight', 'cartTurnRight', 'upCalibration', 'downCalibration'];
        for (let i = 0; i < checkArray.length; i++) {
            let nowId = checkArray[i];
            let type = nowId + 'Check';
            let nowBtn = $('#' + nowId + 'Btn');
            form.on('checkbox(' + type + ')', function (data) {
                if (data.elem.checked === false) {
                    $("#" + nowId).css({
                        display: 'none'
                    });
                    // $("#"+checkArray[i]+" ."+checkArray[i]+"-can").css({
                    //     display: 'none'
                    // });
                    // for (let j = 0; j < 2; j++) {
                    //     $($("."+checkArray[i]+"-point")[j]).find(".input-number")[0].value = 0;
                    //     $($("."+checkArray[i]+"-point")[j]).find(".input-number")[1].value = 0;
                    // }
                    $("." + nowId + '-point input').attr({
                        disabled: 'disabled'
                    });
                    $("#" + nowId + "Zone").css({
                        display: 'none'
                    });
                    nowBtn.addClass('layui-btn-disabled');
                    nowBtn.attr("disabled", 'disabled');
                    $("." + nowId + '-queueLength button').attr("disabled", 'disabled');
                } else {
                    $("." + nowId + '-point input').removeAttr('disabled');
                    nowBtn.removeClass('layui-btn-disabled');
                    nowBtn.removeAttr("disabled");
                    $("." + nowId + '-queueLength button').removeAttr("disabled");
                    // $("#"+checkArray[i]+" ."+checkArray[i]+"-can").css({
                    //     display: 'block'
                    // });
                    $("#" + nowId).css({
                        display: 'block'
                    });
                    $("#" + nowId + "Zone").css({
                        display: 'block'
                    });
                }
                drawPath("surveyCan", 'drawSurvey')
            });
        }
        // colorpicker.render({
        //     elem: '#surveyColorEle'
        //     , color: '#ee0000'
        //     , done: function (color) {
        //         $('#surveyColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#noParkingColorEle'
        //     , color: '#ed0973'
        //     , done: function (color) {
        //         $('#noParkingColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#aroundColorEle'
        //     , color: '#0193de'
        //     , done: function (color) {
        //         $('#aroundColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#faceColorEle'
        //     , color: '#f18800'
        //     , done: function (color) {
        //         $('#faceColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#turnLeftColorEle'
        //     , color: '#f8810a'
        //     , done: function (color) {
        //         $('#turnLeftColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#goStraightColorEle'
        //     , color: '#1794e7'
        //     , done: function (color) {
        //         $('#goStraightColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#turnRightColorEle'
        //     , color: '#0cf42b'
        //     , done: function (color) {
        //         $('#turnRightColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#cartTurnRightColorEle'
        //     , color: '#0443CC'
        //     , done: function (color) {
        //         $('#cartTurnRightColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#upCalibrationColorEle'
        //     , color: '#fc2a0a'
        //     , done: function (color) {
        //         $('#upCalibrationColor').val(color);
        //     }
        // });
        // colorpicker.render({
        //     elem: '#downCalibrationColorEle'
        //     , color: '#fc2a0a'
        //     , done: function (color) {
        //         $('#downCalibrationColor').val(color);
        //     }
        // });
        //渲染
        var ins1 = slider.render({
            elem: '#slideMinWidth'  //绑定元素
            , input: true //输入框
            , max: 200
        });
        var ins2 = slider.render({
            elem: '#slideMaxWidth'  //绑定元素
            , input: true //输入框
            , max: 300
        });
        ins1.setValue(getSe('plateMin'));
        ins2.setValue(getSe('plateMax'));
        element.render();
        disableColor(['surveyColor', 'noParkingColor', 'aroundColor', 'faceColor', 'turnLeftColor', 'goStraightColor', 'turnRightColor', 'cartTurnRightColor', 'upCalibrationColor', 'downCalibrationColor'])
        let globalInfo = checkGlobal();
        setSize(globalInfo.w, globalInfo.h);
        // 如果进入页面需要刷新数据
        // reloadConfig(showData,'drawSurvey')
        initPicture('drawSurvey')
    });
});
var show = function () {
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let dataArray = getSe("projectConfig").settingShow[drawContainer].show;
    showDouble(dataArray, drawContainer);
    let noParkingType = getSe('noParkingType');
    if (noParkingType) {
        $("#noParkingType").val(noParkingType)
    }
    leftStraightRightCheck();
    drawPath(drawCanvas, drawContainer);
    form.render();
};
var inputAdd = function (type) {
    let value = $("." + type + "-queueLength input").val();
    let numberValue = Number(value) + 1;
    $("." + type + "-queueLength input").val(numberValue);
}
var inputReduce = function (type) {
    let value = $("." + type + "-queueLength input").val();
    let numberValue = Number(value);
    if (numberValue > 0) {
        numberValue--
    }
    $("." + type + "-queueLength input").val(numberValue);
}
