﻿{
  "eventType": {
    "2027": "Reverse driving",
    "2058": "Illegal U-turn",
    "2069": "Red light violation",
    "2071": "Violation of traffic markings",
    "2072": "Violation of speed regulation",
    "2038": "Borrow way",
    "1039": "Illegal parking",
    "1019": "Motor vehicles use special lanes in violation of regulations",
    "9800": "Control vehicle",
    "9801": "Illegal vehicle control",
    "9802": "Vehicle violation of traffic restrictions",
    "9803": "The vehicle violates the traffic restriction regulations and violates the regulations",
    "9804": "Control vehicles for violation of traffic restrictions",
    "9805": "The vehicle is charged with violating the traffic restriction regulations and violating the regulations",
    "9999": "Normal running vehicle",
    "1208": "Illegal diversion",
    "1018": "Not following the prescribed Lane",
    "1020": "Traffic jam",
    "1044": "No left turn",
    "1045": "No right turn",
    "1344": "Violation of ban on small cars in other provinces and cities",
    "13442": "Shanghai c card violates the ban",
    "13443": "Coach car against ban",
    "13444": "Breaking the ban (violation type 1)",
    "13445": "Breaking the ban (violation type 2)",
    "13446": "Breaking the ban (violation type 3)",
    "13447": "Breaking the ban (violation type 4)",
    "13448": "Breaking the ban (violation type 5)",
    "13449": "Breaking the ban (violation type 6)",
    "13450": "Passenger car violation of ban",
    "13451": "Freight car violation of ban",
    "13452": "The cart occupies the car lane",
    "13453": "Trucks occupy bus lane",
    "10391": "Illegal stop on sidewalk",
    "10392": "Grid line stopped illegally",
    "10393": "Comity to pedestrians",
    "10394": "Alternate traffic",
    "10395": "It should be a big turn but a small turn",
    "10396": "Turn left and don't go straight",
    "10397": "Cross line parking",
    "10398": "Zebra crossing U-turn",
    "10399": "Turning around affects normal traffic",
    "10400": "The cart turned right without stopping to give way",
    "13454": "No truck on ramp",
    "13455": "Breaking the ban (violation type 7)",
    "13456": "Breaking the ban (violation type 8)",
    "13457": "Don't turn on the lights",
    "13458": "Continuous lane change",
    "13459": "Not wearing seat belt",
    "13460": "Cell phone",
    "13461": "Lane changing without turn signal",
    "13462": "Illegal occupation of emergency Lane",
    "13463": "Non motor vehicle lane occupied by motor vehicles",
    "13464": "Illegal jump",
    "13465": "Turn signal not used",
    "13466": "Non motor vehicle running red light",
    "13467": "Non motor vehicle retrograde",
    "13468": "Non motor vehicle breaking ban",
    "13469": "Non motor vehicles without helmets",
    "13470": "Non motor vehicle bayonets",
    "13471": "Dangerous chemical vehicle",
    "13472": "Overtake by passing",
    "13473": "Motorcycles without helmets",
    "13474": "Lightweight motorcycles carrying passengers",
    "13475": "Overloading of passengers on other motorized vehicles besides buses and trucks",
    "13476": "Non-motorized vehicle carrying people",
    "13477": "Traffic restriction",
    "13478": "Illegal loading of goods",
    "13479": "Motorcycle violation ban",
    "13480": "Motorcycle checkpoint",
    "13481": "Foreign motorcycle violation ban",
    "13482": "Motorcycle running red light",
    "13483": "Motorcycles do not follow the guiding lane when driving",
    "13484": "Motorcycle pressing line"
  },
  "carColor": [
    "Unknown color",
    "black",
    "grey",
    "white",
    "red",
    "yellow",
    "green",
    "blue",
    "Pink",
    "violet",
    "brown"
  ],
  "plateColor": [
    "未知",
    "蓝",
    "黄",
    "白",
    "黑",
    "绿",
    "黄绿"
  ],
  "plateType": {
    "30": "unknown",
    "31": "Blue license plate",
    "32": "Black license plate",
    "33": "Single row yellow license plate",
    "34": "Double row yellow license plate",
    "35": "Police car license plate",
    "36": "Armed police license plate",
    "37": "Personalized license plate",
    "38": "Single platoon military vehicle",
    "39": "Double platoon military vehicle",
    "40": "Embassy licence",
    "41": "Hong Kong licence",
    "42": "Tractor license plate",
    "43": "Macao license",
    "44": "Factory license",
    "45": "Civil Aviation Licence",
    "46": "Consulate license plate",
    "47": "New energy license plate",
    "48": "New energy license plate",
    "99": "other",
    "1": "Large Vehicle",
    "2": "Light-Duty Vehicle",
    "3": "Embassy license plate black",
    "4": "Consulate license plate black",
    "5": "Overseas license plate black",
    "6": "Foreign license plate black",
    "16": "learner-driven vehicle",
    "98": "Motorcycle license plate",
    "23": "Police vehicle"
  },
  "carType": {
    "99": "Other",
    "K11": "Large ordinary bus",
    "K33": "Car",
    "0": "Small car",
    "1": "SUV",
    "2": "MPV",
    "3": "Van",
    "4": "Pick-up truck",
    "5": "Minibus",
    "6": "Light bus",
    "7": "Kopaja",
    "8": "Large bus",
    "9": "Light truck",
    "10": "Medium truck",
    "11": "Heavy truck",
    "12": "Motorcycle"
  },
  "flashConfig": {
    "0": "no",
    "1": "T1",
    "2": "T2",
    "3": "T3",
    "4": "T4",
    "5": "F1",
    "6": "F2",
    "7": "F3",
    "8": "F4"
  }
}
