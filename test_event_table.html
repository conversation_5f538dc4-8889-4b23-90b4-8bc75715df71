<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>事件表格测试</title>
    <link rel="stylesheet" href="layui-v2.11.3/layui/css/layui.css">
    <style>
        body {
            margin: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>事件类型映射功能测试</h1>
    
    <div class="test-section">
        <h2>1. 事件类型映射测试</h2>
        <div id="mapping-test"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 模拟事件数据表格</h2>
        <table class="layui-table">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>ID</th>
                    <th>事件类型代码</th>
                    <th>事件类型名称</th>
                    <th>车牌号码</th>
                </tr>
            </thead>
            <tbody id="event-table-body">
            </tbody>
        </table>
    </div>

    <script src="layui-v2.11.3/layui/layui.js"></script>
    <script>
        // 事件类型映射表
        var eventTypeMap = {
            0: "未知",
            1: "逆向行驶",
            2: "违法停车",
            3: "行人闯入",
            4: "交通阻塞",
            6: "交通事故",
            7: "遗留物",
            8: "卡口数据",
            9: "驶离车道",
            10: "烟火",
            11: "施工/封路",
            12: "压线行驶",
            13: "机动车占用应急车道",
            14: "危险品车",
            19: "非机动车闯入",
            20: "车辆倒车",
            21: "非机动车未戴头盔",
            23: "交通拥挤",
            25: "占用非机动车道",
            26: "连续变道",
            27: "车辆缓行",
            28: "三轮车载人",
            29: "人员聚集",
            30: "客运车辆",
            31: "变道行驶",
            32: "工程车辆",
            33: "变速行驶",
            34: "车道排队",
            35: "工作人员",
            100: "能见度正常",
            101: "能见度低",
            200: "视频丢失",
            202: "视频清晰度异常",
            204: "视频噪声",
            206: "视频冻结",
            208: "视频遮挡",
            210: "镜头移动",
            212: "视频亮度过亮",
            214: "视频亮度过暗",
            301: "路面破损",
            303: "山体滑坡",
            304: "路面积水",
            305: "路面积雪",
            1001: "逆向行驶",
            1002: "违章掉头",
            1003: "机动车闯红灯",
            1004: "压线行驶",
            1005: "违反车速规定",
            1006: "违法变道",
            1007: "借道行驶",
            1008: "违章停车",
            1009: "机动车不在机动车道内行驶",
            1010: "机动车违反规定使用专用车道",
            1011: "路口滞留",
            1012: "在禁止左转路口左转",
            1013: "在禁止右转路口右转",
            1014: "人行道违停",
            1015: "网格线违停",
            1016: "未礼让行人",
            1017: "未交替通行",
            1018: "大弯小转",
            1019: "机动车卡口",
            1020: "左转不让直行",
            1021: "越线停车",
            1022: "在斑马线掉头",
            1023: "掉头不让直行",
            1024: "大车右转未停车让行",
            1025: "连续变道",
            1026: "变道不使用转向灯",
            1027: "转弯不使用转向灯",
            1028: "违法占用应急车道",
            1029: "机动车占用非机动车道",
            1030: "加塞",
            1031: "危化品车",
            1032: "不开车灯",
            1033: "违反尾号限行",
            1034: "外省市小型车闯禁令",
            1035: "沪C号牌闯禁令",
            1036: "教练车闯禁令",
            1037: "货车违反禁令上匝道",
            1038: "黑名单违法类型1",
            1039: "黑名单违法类型2",
            1040: "黑名单违法类型3",
            1041: "黑名单违法类型4",
            1042: "黑名单违法类型5",
            1043: "黑名单违法类型6",
            1044: "黑名单违法类型7",
            1045: "黑名单违法类型8",
            1046: "非机动车闯红灯",
            1047: "非机动车逆行",
            1048: "非机动车闯禁令",
            1049: "非机动车不带头盔",
            1050: "非机动车卡口",
            1051: "非机动车载人",
            1052: "摩托车卡口",
            1053: "摩托车闯禁令",
            1054: "外牌摩托车闯禁令",
            1055: "摩托车闯红灯",
            1056: "摩托车借道行驶",
            1057: "摩托车压线行驶",
            1058: "客车闯禁令",
            1059: "货车闯禁令",
            1060: "货占客",
            1061: "不系安全带",
            1062: "主驾打手机"
        };

        // 根据事件类型代码获取事件类型名称
        function getEventTypeName(eventType) {
            return eventTypeMap[eventType] || "未知事件类型(" + eventType + ")";
        }

        // 测试映射功能
        function testMapping() {
            var testCodes = [0, 1, 1001, 1003, 1008, 1019, 1046, 1055, 1061, 9999];
            var html = '';
            
            testCodes.forEach(function(code) {
                html += '<p><strong>代码 ' + code + ':</strong> ' + getEventTypeName(code) + '</p>';
            });
            
            document.getElementById('mapping-test').innerHTML = html;
        }

        // 模拟事件数据
        function createMockEventData() {
            var mockEvents = [
                { time: '2025-06-25 10:30:15', id: 'E001', type: 1001, plate: '京A12345' },
                { time: '2025-06-25 10:31:22', id: 'E002', type: 1003, plate: '沪B67890' },
                { time: '2025-06-25 10:32:45', id: 'E003', type: 1008, plate: '粤C11111' },
                { time: '2025-06-25 10:33:12', id: 'E004', type: 1019, plate: '浙D22222' },
                { time: '2025-06-25 10:34:33', id: 'E005', type: 1046, plate: '苏E33333' },
                { time: '2025-06-25 10:35:44', id: 'E006', type: 1055, plate: '鲁F44444' },
                { time: '2025-06-25 10:36:55', id: 'E007', type: 1061, plate: '川G55555' },
                { time: '2025-06-25 10:37:11', id: 'E008', type: 9999, plate: '未知车牌' }
            ];
            
            var tbody = document.getElementById('event-table-body');
            var html = '';
            
            mockEvents.forEach(function(event) {
                html += '<tr>';
                html += '<td>' + event.time + '</td>';
                html += '<td>' + event.id + '</td>';
                html += '<td>' + event.type + '</td>';
                html += '<td><strong>' + getEventTypeName(event.type) + '</strong></td>';
                html += '<td>' + event.plate + '</td>';
                html += '</tr>';
            });
            
            tbody.innerHTML = html;
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', function() {
            testMapping();
            createMockEventData();
        });
    </script>
</body>
</html>
