<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>抓拍</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_picture_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="pictures" class="title">抓拍</h2>
    <div class="content">
        <p id="pictures_text1">
            抓拍界面，点击右下角播放按键，相机会抓拍实时的画面，这个界面主要是在调试焦距的时候方便查看镜头焦距。通过鼠标滚轮或者键盘CTRL+上下，可以放大缩小画面，便于调试的时候使用。
        </p>
        <img id="pictures_image1" src="./image011.png" alt="抓拍界面"/>
    </div>
</div>
</body>
</html>