<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>事件检测</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_detect_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="Event_detection_txt1" class="title">事件检测</h2>
    <div class="content">
        <p id="Event_detection_txt2">
            算法---事件检测。单独说明事件检测的标准。根据前卡和电警相机的不同功能以及不同场景，勾选需要检测的违法事件。
        </p>
        <img id="Event_detection_img1" src="./image052.png" alt="事件检测界面1"/>
        <img id="Event_detection_img2" src="./image053.png" alt="事件检测界面2"/>
        <img id="Event_detection_img3" src="./image054.png" alt="事件检测界面3"/>
    </div>
</div>
</body>
</html>