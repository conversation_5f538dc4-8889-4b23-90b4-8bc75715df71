<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>systemStatus</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/systemStatus.js"></script>
    <style>
        .layui-form-label {
            width: 130px;
        }

        .layui-form-item {
            margin-bottom: 5px;
        }

        span {
            width: 400px;
            position: absolute;
            padding: 9px 15px 9px 0px;
        }
    </style>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-form layui-show">
    <fieldset class="layui-elem-field">
        <legend id="deviceStatusInformation">设备状态信息</legend>
        <div class="layui-field-box layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label" id="deviceUID">设备UID：</label>
                <div class="layui-input-inline">
                    <span id="device_uid"></span>
                </div>
            </div>
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">软件版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="software_ver"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">硬件版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="hardware_ver"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">DSP连接状态：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="dsp_status"></span>-->
            <!--</div>-->
            <!--</div>-->
            <div class="layui-form-item">
                <label id="environmentVariableStatus" class="layui-form-label">环境变量状态：</label>
                <div class="layui-input-inline">
                    <span id="hjbl_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="productFeatures" class="layui-form-label">产品功能：</label>
                <div class="layui-input-inline">
                    <span id="product_function"></span>
                </div>
            </div>
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">low level 版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="low_level_version"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">mid level 版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="mid_level_version"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">dsp 框架版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="dsp_framework_version"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">arm 框架版本：</label>-->
            <!--<div class="layui-input-inline">-->
            <!--<span id="arm_framework_version"></span>-->
            <!--</div>-->
            <!--</div>-->
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="hardDiskType" class="layui-form-label">硬盘类型：</label>
                <div class="layui-input-inline">
                    <span id="has_hdd"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="partitionInformation" class="layui-form-label">硬盘分区信息：</label>
                <div class="layui-input-inline">
                    <span id="partition_mask"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="systemInformation" class="layui-form-label">硬盘文件系统信息：</label>
                <div class="layui-input-inline">
                    <span id="fs_mask"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="dataAreaStatusInformation" class="layui-form-label">数据区状态信息：</label>
                <div class="layui-input-inline">
                    <span id="data_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="logStatus" class="layui-form-label">日志区状态信息：</label>
                <div class="layui-input-inline">
                    <span id="log_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="checked" class="layui-form-label">分区是否正在检查：</label>
                <div class="layui-input-inline">
                    <span id="disk_checking"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="format" class="layui-form-label">分区是否正在格式化：</label>
                <div class="layui-input-inline">
                    <span id="disk_formating"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="fileSystemStatus" class="layui-form-label">文件系统状态：</label>
                <div class="layui-input-inline">
                    <span id="fs_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="capacity" class="layui-form-label">硬盘容量：</label>
                <div class="layui-input-inline">
                    <span id="hdd_capacity">0</span><span style="margin-left: 20px">MB</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" id="diskUsage">硬盘使用率：</label>
                <div class="layui-input-inline">
                    <span id="hdd_usage">0</span><span style="margin-left: 20px">%</span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="SDCard" class="layui-form-label">SD卡设备：</label>
                <div class="layui-input-inline">
                    <span id="has_sd"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="SDStatus" class="layui-form-label">SD卡状态：</label>
                <div class="layui-input-inline">
                    <span id="sd_fs_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="SDCapacity" class="layui-form-label">SD卡容量：</label>
                <div class="layui-input-inline">
                    <span id="sd_capacity">0</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="SDUsage" class="layui-form-label">SD卡使用率：</label>
                <div class="layui-input-inline">
                    <span id="sd_usage">0</span>
                    <span style="margin-left: 20px">%</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="SDDataNumber" class="layui-form-label">备份数据量：</label>
                <div class="layui-input-inline">
                    <span id="sd_data_number">0</span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="FTPIP" class="layui-form-label">FTP的IP地址：</label>
                <div class="layui-input-inline">
                    <span id="ftp_ip"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="FTPport" class="layui-form-label">FTP的端口号：</label>
                <div class="layui-input-inline">
                    <span id="ftp_port"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="FTPStatus" class="layui-form-label">FTP连接状态：</label>
                <div class="layui-input-inline">
                    <span id="ftp_connect_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="FTPNum" class="layui-form-label">访问FTP成功的统计次数 ：</label>
                <div class="layui-input-inline">
                    <span id="ftp_correct_count"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="FTPNumFailure" class="layui-form-label">访问FTP失败的统计次数：</label>
                <div class="layui-input-inline">
                    <span id="ftp_error_count"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastLogin" class="layui-form-label">最近一次登陆成功时间：</label>
                <div class="layui-input-inline">
                    <span id="ftp_login_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastVisit" class="layui-form-label">最近一次访问成功时间：</label>
                <div class="layui-input-inline">
                    <span id="ftp_access_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastAccessFailed" class="layui-form-label">最后一次访问失败时间：</label>
                <div class="layui-input-inline">
                    <span id="ftp_error_time"></span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="configurationStatus" class="layui-form-label">配置状态：</label>
                <div class="layui-input-inline">
                    <span id="config_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="analysisStatus" class="layui-form-label">分析状态：</label>
                <div class="layui-input-inline">
                    <span id="update_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="externalTrafficLightStatus" class="layui-form-label">外接红绿灯状态：</label>
                <div class="layui-input-inline">
                    <span id="extern_light_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="radarStatus" class="layui-form-label">雷达状态：</label>
                <div class="layui-input-inline">
                    <span id="radar_status"></span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="currentTime" class="layui-form-label">设备当前时间：</label>
                <div class="layui-input-inline">
                    <span id="device_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="NTPAddress" class="layui-form-label">NTP主机地址：</label>
                <div class="layui-input-inline">
                    <span id="ntp_ip"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="NTPStatus" class="layui-form-label">NTP连接状态：</label>
                <div class="layui-input-inline">
                    <span id="ntp_connect_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastTimeNTP" class="layui-form-label">最近一次成功NTP时间：</label>
                <div class="layui-input-inline">
                    <span id="ntp_success_time"></span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="cameraType" class="layui-form-label">相机类型：</label>
                <div class="layui-input-inline">
                    <span id="camera_kind"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="cameraIP" class="layui-form-label">相机IP地址：</label>
                <div class="layui-input-inline">
                    <span id="camera_ip"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="commandConnectionPort" class="layui-form-label">命令连接端口：</label>
                <div class="layui-input-inline">
                    <span id="cmd_port"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="dataPort" class="layui-form-label">数据连接端口：</label>
                <div class="layui-input-inline">
                    <span id="data_port"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="commandConnectionStatus" class="layui-form-label">命令连接状态：</label>
                <div class="layui-input-inline">
                    <span id="cmd_connect_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="dateStatus" class="layui-form-label">数据连接状态：</label>
                <div class="layui-input-inline">
                    <span id="data_connect_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="correctlyReceivedFrames" class="layui-form-label">连续正确接收帧数：</label>
                <div class="layui-input-inline">
                    <span id="frame_cnt"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="acceptFrameStatus" class="layui-form-label">接受帧状态：</label>
                <div class="layui-input-inline">
                    <span id="frame_err"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastConfigurationTime" class="layui-form-label">最近一次成功配置时间：</label>
                <div class="layui-input-inline">
                    <span id="config_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastSuccessfulTime" class="layui-form-label">最近一次成功对时时间：</label>
                <div class="layui-input-inline">
                    <span id="timing_time"></span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="lastCameraPictureTime" class="layui-form-label">最近一次相机图片时间：</label>
                <div class="layui-input-inline">
                    <span id="last_image_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastVideoTime" class="layui-form-label">最近一次存录像的时间：</label>
                <div class="layui-input-inline">
                    <span id="last_video_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lastBayonetTime" class="layui-form-label">最近一次存卡口的时间：</label>
                <div class="layui-input-inline">
                    <span id="last_kakou_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="recentViolation" class="layui-form-label">最近一次存违章的时间：</label>
                <div class="layui-input-inline">
                    <span id="last_violate_time"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="videoSaveDay" class="layui-form-label">录像存放天数：</label>
                <div class="layui-input-inline">
                    <span id="video_days"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="bayonetSaveDay" class="layui-form-label">卡口事件存放天数：</label>
                <div class="layui-input-inline">
                    <span id="kakou_days"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="violationSaveDay" class="layui-form-label">违章事件存放天数：</label>
                <div class="layui-input-inline">
                    <span id="violate_days"></span>
                </div>
            </div>
            <hr class="layui-bg-gray">
            <div class="layui-form-item">
                <label id="leftLane" class="layui-form-label">左行车道的拥堵状态：</label>
                <div class="layui-input-inline">
                    <span id="left_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="straightLane" class="layui-form-label">直行车道的拥堵状态：</label>
                <div class="layui-input-inline">
                    <span id="straight_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="rightLane" class="layui-form-label">右行车道的拥堵状态：</label>
                <div class="layui-input-inline">
                    <span id="right_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="bayonetEventStatus" class="layui-form-label">卡口事件状态：</label>
                <div class="layui-input-inline">
                    <span id="kakou_status"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="trafficLightStatus" class="layui-form-label">红绿灯状态：</label>
                <div class="layui-input-inline">
                    <span id="light_status"></span>
                </div>
            </div>
        </div>
    </fieldset>
    <button id="fresh" class="layui-btn layui-btn-default" onclick="getStatus()">刷新状态</button>
</div>
</body>
</html>
