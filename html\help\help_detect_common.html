<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>通用功能</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_detect_common_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="G_function_txt1" class="title">通用功能</h2>
    <div class="content">
        <img id="General_function_image1"  src="./image055.png" alt="通用功能"/>
        <p id="G_function_txt2" >
            检测卡口：车辆通过相机，抓拍一张卡口图片
        </p>
        <p id="G_function_txt3" >
            检测机动车违反规定使用专用车道：在车道线界面，车道线属性里面设置，应急车道，非机动车道，公交车道等车道属性设置，所有经过这个车道的车辆都检测。1，2张图片在车道内，1，2，3张图片都要有位移。
        </p>
        <p id="G_function_txt4" >
            检测无牌车：无牌车包括实际“无牌车”和看不清车牌的“污损牌车”，无牌车只出卡口，没有违法事件。无牌车也要有运动，停止的车辆不检测。
        </p>
        <p id="G_function_txt5" >
            检测压线：根据实际情况，在车道线设置里查看车道线编号，勾选对应需要检测的车道线。车牌触碰车道线检测，检测车牌，车轮或者车身小部分压线不抓。电警压线在停止线下方，不超过停止线。
        </p>
    </div>
</div>
</body>
</html>