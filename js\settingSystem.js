var layer, form, element, laydate;
$(document).ready(function () {
    let options = [];
    for (let i = 0; i < 1000; i++) {
        let s = i;
        if (i < 100) {
            if (i < 10) {
                s = '00' + s;
            } else {
                s = '0' + s;
            }
        }
        let o = '<option value="' + s + '">' + s + '</option>';
        options.push(o)
    }
    $("#nationRoadCode").append(options);
    numberRange('locateThresh', 0, 9, false, true);
    // numberRange('ocrThresh', 0, 9, false, true);
    numberRange('coef', 0.5, 2.0, true);
    numberRange('cameraHeight', 0, 800, false, true);
    numberRange('cameraImgDown', 0, 6000, false, true);
    numberRange('cameraImgTop', 0, 6000, false, true);
    numberRange('lane1MinSpeed', 0, 240, false, true);
    numberRange('lane1MaxSpeed', 0, 240, false, true);
    numberRange('lane2MinSpeed', 0, 240, false, true);
    numberRange('lane2MaxSpeed', 0, 240, false, true);
    numberRange('lane3MinSpeed', 0, 240, false, true);
    numberRange('lane3MaxSpeed', 0, 240, false, true);
    numberRange('lane4MinSpeed', 0, 240, false, true);
    numberRange('lane4MaxSpeed', 0, 240, false, true);
    numberRange('lane5MinSpeed', 0, 240, false, true);
    numberRange('lane5MaxSpeed', 0, 240, false, true);

    numberRange('lane1MinLimitSpeed', 0, 240, false, true);
    numberRange('lane1MaxLimitSpeed', 0, 240, false, true);
    numberRange('lane2MinLimitSpeed', 0, 240, false, true);
    numberRange('lane2MaxLimitSpeed', 0, 240, false, true);
    numberRange('lane3MinLimitSpeed', 0, 240, false, true);
    numberRange('lane3MaxLimitSpeed', 0, 240, false, true);
    numberRange('lane4MinLimitSpeed', 0, 240, false, true);
    numberRange('lane4MaxLimitSpeed', 0, 240, false, true);
    numberRange('lane5MinLimitSpeed', 0, 240, false, true);
    numberRange('lane5MaxLimitSpeed', 0, 240, false, true);
    numberRange('jpgQuality', 1, 99, false, true);
    layui.use(['element', 'layer', 'form', 'laydate'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, laydate = layui.laydate;
        laydate.render({
            elem: '#restartTime' //指定元素
        });
        let dataArray = ['speedConfig'];
        disableFun(dataArray);
        form.on('select(detectSpeedType)', function (data) {
            if (data.value === '1' || data.value === '2') {
                $("#radarType").removeAttr("disabled")
                if (data.value === '1') {
                    $("#radarType").val(3)
                }
            } else {
                $("#radarType").attr("disabled", 'disabled')
            }
            form.render()
        });
        form.on('radio(roadNumber)', function (data) {
            if (data.value === 'nationCode') {
                enableFun('nationCode');
                disableFunction('selfCode');
            } else if (data.value === 'selfCode') {
                enableFun('selfCode');
                disableFunction('nationCode');
            }
            form.render();
        });
        $.ajax({
            //请求方式
            type: "GET",
            //文件位置
            url: "../../config/cityCode.json",
            //返回数据格式为json,也可以是其他格式如
            dataType: "json",
            //请求成功后要执行的函数，拼接html
            success: function (allData) {
                let options = [];

                options.push('<option value="-1" data-code="00">' + langMessage.setting.provinceSelect + '</option>');

                for (let i = 0; i < allData.length; i++) {
                    let newOption = $('<option value="' + i + '" data-code="' + allData[i].code + '">' + allData[i].name + '</option>');
                    options.push(newOption)
                }
                $("#selectProvince").html(options);
                form.render('select');
                let cityData;
                form.on('select(selectProvince)', function (data) {
                    let options = [];
                    cityData = allData[data.value].city;

                    options.push('<option value="-1" data-code="00">' + langMessage.setting.citySelect + '</option>');

                    for (let i = 0; i < cityData.length; i++) {
                        let newOption = $('<option value="' + i + '" data-code="' + cityData[i].code + '">' + cityData[i].name + '</option>');
                        options.push(newOption)
                    }
                    $("#selectCity").val(-1);
                    let val =
                        getDataset($("#selectProvince option:selected")[0]).code
                        + getDataset($("#selectCity option:selected")[0]).code
                        + getDataset($("#selectArea option:selected")[0]).code
                        + $("#nationRoadCode option:selected").text();
                    $("#selfRoadCode").val(val);
                    $("#selectCity").html(options);
                    form.render('select')
                });
                form.on('select(selectCity)', function (data) {
                    let options = [];
                    let areaData = cityData[data.value].area;

                    options.push('<option value="-1" data-code="00">' + langMessage.setting.countySelect + '</option>');


                    for (let i = 0; i < areaData.length; i++) {
                        let newOption = $('<option value="' + i + '" data-code="' + areaData[i].code + '">' + areaData[i].name + '</option>');
                        options.push(newOption)
                    }
                    $("#selectArea").html(options).val(-1);
                    let val =
                        getDataset($("#selectProvince option:selected")[0]).code
                        + getDataset($("#selectCity option:selected")[0]).code
                        + getDataset($("#selectArea option:selected")[0]).code
                        + $("#nationRoadCode option:selected").text();
                    $("#selfRoadCode").val(val);
                    form.render('select')
                });
                form.on('select(selectArea)', function (data) {
                    let val =
                        getDataset($("#selectProvince option:selected")[0]).code
                        + getDataset($("#selectCity option:selected")[0]).code
                        + getDataset($("#selectArea option:selected")[0]).code
                        + $("#nationRoadCode option:selected").text();
                    $("#selfRoadCode").val(val);
                    form.render('select')
                });
                form.on('select(selectCode)', function (data) {
                    let val =
                        getDataset($("#selectProvince option:selected")[0]).code
                        + getDataset($("#selectCity option:selected")[0]).code
                        + getDataset($("#selectArea option:selected")[0]).code
                        + $("#nationRoadCode option:selected").text();
                    $("#selfRoadCode").val(val);
                    form.render('select')
                });
            }
        });
        showConfig('system', form);
    });
    $("#saveSystem").click(function () {
        saveSystem()
    });
    $("#resetSystem").click(function () {
        showConfig('system');
    })

});


var saveSystem = function () {
    let systemValue = {};
    let systemID = getAllInputHaveID();
    // [
    //     'locateThresh', 'ocrThresh','headHZ', 'headLetter', 'roadName',
    //     'roadCode', 'selfRoadCode', 'coef', 'cameraHeight', 'cameraImgDown', 'cameraImgTop',
    //     'minSpeed', 'maxSpeed',
    //     'lane1MinSpeed', 'lane1MaxSpeed',
    //     'lane2MinSpeed', 'lane2MaxSpeed',
    //     'lane3MinSpeed', 'lane3MaxSpeed',
    //     'lane4MinSpeed', 'lane4MaxSpeed',
    //     'lane5MinSpeed', 'lane5MaxSpeed',
    // ]
    let checkboxArray = ['detect-license', 'detect-speed'];
    let selectArray = ['speedType', 'radarType', 'AIType'];
    for (let x = 0; x < selectArray.length; x++) {
        systemValue[selectArray[x]] = getSelectValue(selectArray[x]);
    }
    for (let j = 0; j < checkboxArray.length; j++) {
        systemValue[checkboxArray[j]] = getCheckboxValue(checkboxArray[j]);
    }
    for (let i = 0; i < systemID.length; i++) {
        systemValue[systemID[i]] = getIDInput(systemID[i]);
    }
    systemValue.roadDirect = $("#roadDirect option:selected").val()
    if (systemValue.lane1MinSpeed > systemValue.lane1MaxSpeed || systemValue.lane2MinSpeed > systemValue.lane2MaxSpeed || systemValue.lane3MinSpeed > systemValue.lane3MaxSpeed ||
        systemValue.lane4MinSpeed > systemValue.lane4MaxSpeed || systemValue.lane5MinSpeed > systemValue.lane5MaxSpeed) {

        layer.msg(langMessage.setting.errorMsg.SPEED_ERROR, {icon: 2});

        return
    }
    setSe('systemValue', systemValue);

    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});

};
var configInterval = null, reloadTime = 0;
var show = function () {
    let system = getSe('systemValue');
    if (system) {
        let inputArray = [];
        for (let d in system) {
            let reg = /^\w+-\w+/;
            if (reg.test(d)) {
                showCheckboxValue(d, system[d]);
            } else {
                inputArray.push(d);
            }
        }
        for (let d in inputArray) {
            $("#" + inputArray[d]).val(system[inputArray[d]]);
        }
        clearTimeout('configInterval');
    } else {
        reloadConfig();
        reloadTime += 1;
        if (reloadTime <= 5) {
            configInterval = setTimeout(function () {
                showConfig('system')
            }, 500)
        } else {
            reloadTime = 0;
            clearTimeout(configInterval);

            layer.msg(langMessage.common.netError, {icon: 2});

        }
    }
    form.render();
};
