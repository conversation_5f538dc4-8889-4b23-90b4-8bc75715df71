<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>videoSignal</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../css/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <!--<link rel="stylesheet" href="../../css/setting.css">-->

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/settingDraw.js"></script>
    <script src="../../js/drawCanvas.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/ocxGlobal.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/videoSignal.js"></script>
    <!--<style>-->
        <!--.OSD-table .layui-form-item {-->
            <!--width: 300px;-->
        <!--}-->
        <!--.layui-form-item{-->
            <!--margin-bottom: 10px;-->
        <!--}-->
        <!--.layui-form-label{-->
            <!--width:120px-->
        <!--}-->
        <!--.layui-input-inline {-->
            <!--margin-top: 16px;-->
        <!--}-->
        <!--.fix-margin{-->
            <!--margin-top: 16px;-->
        <!--}-->

        <!--.draw-canvas {-->
            <!--position: absolute;-->
            <!--top: 0;-->
            <!--left: 0;-->
        <!--}-->
        <!--.draw-container {-->
            <!--top: 0;-->
            <!--left: 0;-->
            <!--width: 600px;-->
            <!--height: 450px;-->
            <!--position: absolute;-->
            <!--z-index: 999;-->
        <!--}-->
        <!--.draw-container .point {-->
            <!--height: 450px;-->
            <!--visibility: hidden;-->
        <!--}-->

        <!--.draw-container .clickZone {-->
            <!--width: 600px;-->
            <!--height: 450px;-->
            <!--display: none;-->
            <!--position: absolute;-->
            <!--top: 0;-->
            <!--left: 0;-->
        <!--}-->
        <!--.mini-box {-->
            <!--width: 10px;-->
            <!--height: 10px;-->
            <!--background-color: #0000FF;-->
            <!--position: absolute;-->
            <!--z-index: 1000;-->
        <!--}-->
    <!--</style>-->
</head>
<body class="sub-body custom-style">
<div id="videoContainer" class=" layui-form ">
    <fieldset class="layui-elem-field">
        <legend id="playParameters">播放参数</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <label id="videoWay" class="layui-form-label">视频流传输方式：</label>
                <div class="layui-input-block">
                    <select id="RTSPWay">
                        <option value="0">UDP</option>
                        <option value="1">TCP</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="cameraUsernameLabel" for="cameraUsername" class="layui-form-label">相机用户名：</label>
                <div class="layui-input-block">
                    <input id="cameraUsername" type="text" name="username" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label id="cameraPasswordLabel" for="cameraPassword" class="layui-form-label">相机密码：</label>
                <div class="layui-input-block">
                    <input name="password" type="password" class="layui-input" id="cameraPassword" >
                    <a id="passwordClick" class="iconfont icon-yanjing_yincang" style="position: absolute;top: 10px;right: 10px;"></a>
                </div>
            </div>
        </div>
    </fieldset>
</div>

<button class="layui-btn layui-btn-default camera-submit" id="submitVideo">保存配置
</button>
<button class="layui-btn layui-btn-default layui-btn-block" id="commonDefault">恢复默认</button>
</body>
</html>
