/**
 * Create by Chelly
 * 2019/5/24
 */
/**
 *
 * @param data
 * @param sucCallback
 * @param sucCallbackParam
 * @param failCallback
 *
 * CMD_GET_CONFIG = 0,       // 0获取配置
 * CMD_SET_CONFIG,             // 1设置配置
 * CMD_CAPTURE_JPG,           // 2抓拍图片
 * CMD_GET_STATUS,             // 3状态查询
 * CMD_GET_VERSION,          // 4版本查询->保存用户名密码
 * CMD_GET_DBG_LOG,          // 5导出实时日志
 * CMD_GET_LOG,                   // 6导出常驻日志
 * CMD_GET_RED_LIGHT = 7, // 7查询红绿灯状态
 * CMD_UPGRADE_APP,          // 8升级算法固件
 * CMD_RESET_PARA,             // 9恢复默认参数
 * CMD_REBOOT,                    // 10重启相机
 * CMD_GET_AUTHCODE,      // 11获取授权码
 * CMD_IMPOET_AUTH_FILE, // 12导入授权文件
 * // 13获取视频流编码格式
 */
var loading = null;
const webserviceCMD = {
    CMD_GET_CONFIG: 0,// 0获取配置
    CMD_SET_CONFIG: 1,// 1设置配置
    CMD_CAPTURE_JPG: 2,// 2抓拍图片
    CMD_GET_STATUS: 3,// 3状态查询
    CMD_SET_VIDEO: 4,// 4版本查询->保存用户名密码
    CMD_GET_DBG_LOG: 5,// 5导出实时日志
    CMD_GET_LOG: 6,// 6导出常驻日志
    CMD_GET_RED_LIGHT: 7,// 7查询红绿灯状态
    CMD_UPGRADE_APP: 8,// 8升级算法固件
    CMD_RESET_PARA: 9,// 9恢复默认参数
    CMD_REBOOT: 10,// 10重启相机
    CMD_GET_AUTH_CODE: 11, // 11获取授权令牌
    CMD_IMPORT_AUTH_FILE: 12,// 12导入授权文件
    CMD_GET_VIDEO: 13,// 13获取主码流编码格式
    CMD_GET_OSD: 14,// 14获取osd配置
    CMD_SET_OSD: 15,// 15设置osd配置
    CMD_GET_AUTH_SHORT: 16,// 16授权界面获取授权信息
    CMD_IMPORT_BLACK_LIST: 17,// 17下发黑名单
    CMD_EXPORT_BLACK_LIST: 18,// 18获取黑名单
    CMD_GET_THRD_STATE: 19,// 获取程序各个线程的状态
    CMD_GET_THRD_STATE_HISTORY: 20,// 获取程序各个线程的历史状态
    CMD_GET_YUV: 21,//获取yuv
    CMD_GET_STATE_RATE: 22,//获取历史状态的存储频率
    CMD_SET_STATE_RATE: 23,//设置历史状态的存储频率
    CMD_SET_SD_BACKUP: 24,//设置是否将数据备份到SD卡
    CMD_GET_SD_BACKUP: 25,//获取事件是否备份到SD卡
    CMD_ANALYSIS_LOG: 26,//重启日志分析 log_type:0重启
    CMD_GET_AUTH_FILE: 27,//导出授权文件
    CMD_GET_EVENT: 28,//获取事件数据
}
let msgArray = langMessage.setting.msgArray;
/**
 * 旧网络协议
 * @param data
 * @param sucCallback
 * @param sucCallbackParam
 * @param failCallback
 */
var sendWebService_1 = function (data, sucCallback, sucCallbackParam, failCallback) {
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        httpsForward_1(data, sucCallback, sucCallbackParam, failCallback)
        return;
    }

    let wsURL = getRouterUrl();
    data.msg = msgArray[data.params.web_cmd];
    let ip = location.hostname;
    let getData;
    let wsData;
    //SOAP 1.1 请求报文格式，1.2在网上可以找到
    wsData = '<?xml version="1.0" encoding="utf-8"?>';
    wsData = wsData + '<SOAP-ENV:Envelope  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xmlns:xsd="http://www.w3.org/2001/XMLSchema"  xmlns:ns1="http://webservice.service.seisys.cn/">';
    wsData = wsData + '<SOAP-ENV:Body>';
    wsData = wsData + '<ns1:CommonOperate>'; //这里就是发布的方法名和xml文档中的命名空间地址（图中画线部分）
    wsData = wsData + '<arg0>';
    wsData = wsData + escapeHTML(JSON.stringify(data.params));
    wsData = wsData + '</arg0>';
    wsData = wsData + '</ns1:CommonOperate>';
    wsData = wsData + '</SOAP-ENV:Body>';
    wsData = wsData + '</SOAP-ENV:Envelope>';
    $.ajax({
        type: "POST", //访问WebService使用Post方式请求
        contentType: "text/xml; charset=utf-8", //WebService 会返回Json类型
        url: wsURL, //调用WebService
        data: wsData, //Email参数
        dataType: 'xml',
        timeout: 180000,
        success: function (response) { //回调函数，result，返回值
            if (data.params.web_cmd === 10) {
                return
            }
            try {
                let res = response.childNodes[0].childNodes[0].childNodes[0].childNodes[0].childNodes[0].data;
                // 转义特殊符号
                res = unescapeHTML(res);
                if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                    res = res.replace(/\n/g, "\\n");
                }
                getData = JSON.parse(res);
                if (parseInt(getData.ret) === 0) {
                    if (getData.desc === 'success') {
                        if (sucCallback) {
                            sucCallback(getData, sucCallbackParam);
                            res = "";
                            getData = null;
                            return
                        }
                    }
                }
                let msg = "";
                if (getData && getData.desc === 'timeout') {
                    msg = langMessage.common.timeout
                } else if (getData && getData.desc === 'request none') {
                    msg = langMessage.common.requestNone
                } else {
                    msg = data.msg + langMessage.common.fail;
                }
                layer.msg(msg, {icon: 2});
                if (failCallback) {
                    failCallback(data.type)
                }
            } catch (e) {
                layer.msg(langMessage.common.jsonError, {icon: 2});
                console.log(e);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest);
            console.log("Webservice" + data.msg + langMessage.common.fail);
            layer.msg(langMessage.common.netError, {icon: 2});
            if (failCallback) {
                failCallback()
            }
        }
    });
    if (data.params.web_cmd === 10) {
        if (sucCallback) {
            console.log(langMessage.common.restart);
            if (sucCallbackParam) {
                sucCallback(sucCallbackParam);
            } else {
                sucCallback()
            }
        }
    }
};
var initWebService_1 = function (cmd, param, sucCallback, sucCallbackParam, failCallback) {
    let data = {
        params: {
            channel_id: 0,
            web_cmd: cmd,
        }
    };
    let type = null;
    if (param) {
        for (let index in param) {
            if (index === "type") {
                type = param[index];
                continue
            }
            data.params[index] = param[index]
        }
        console.log(param)
    }
    if (cmd === 2) {
        sendWebService_1(data, getPictureSuc_1, type, getPictureFail);
    } else {
        sendWebService_1(data, sucCallback, sucCallbackParam, failCallback);
    }
};
// var getPicture = function (type) {
//     let data= {
//         msg: '获取图片',
//         type:type,
//         params: {
//             "channel_id": 0,
//             "web_cmd": 2,
//         }
//     }
//     sendWebService(data,getPictureSuc,getPictureFail);
// }
var getPictureSuc_1 = function (getData, type) {
    let imgInfo = getSe('imgInfo');
    let globalInfo = checkGlobal();
    let containerHeight, containerWidth;
    let configIs = document.getElementById("configImg");
    let imgSrc = "../../img/404.png";
    if (getData.jpg_data) {
        imgSrc = "data:image/jpeg;base64," + getData.jpg_data;
    }
    // if (imgInfo) {
    //     containerHeight = imgInfo.containerHeight;
    //     containerWidth = imgInfo.containerWidth;
    //
    // } else {
    let imgWidth = getData['width'];
    let imgHeight = getData['height'];
    if (imgWidth && imgHeight) {
        imgInfo = {};
        imgInfo.width = imgWidth;
        imgInfo.height = imgHeight;
        imgInfo.multiple = imgInfo.width / globalInfo.w;
        imgInfo.containerWidth = globalInfo.w;
        imgInfo.containerHeight = imgInfo.height / imgInfo.multiple
    }
    containerHeight = imgInfo.containerHeight;
    containerWidth = imgInfo.containerWidth;
    // }
    imgInfo.src = imgSrc;
    setSe('imgInfo', imgInfo);
    if (typeof (type) === "function") {
        type()
    } else {
        let blob = base64ToBlob(imgSrc);
        configIs.onload = function () {
            window.URL.revokeObjectURL(configIs.src);
        };
        configIs.src = window.URL.createObjectURL(blob);
        if (type) {
            // 如果进入页面需要刷新数据
            // reloadConfig(showConfig,type)
            setSize(containerWidth, containerHeight);
            showConfig(type)

        }
    }
    getData = null;
    imgSrc = "";

};
var getPictureFail = function () {
    let globalInfo = checkGlobal();
    let containerHeight, containerWidth;
    let configIs = document.getElementById("configImg");
    let imgSrc = "../../img/404.png";
    let imgInfo = {};
    imgInfo.width = globalInfo.w;
    imgInfo.height = globalInfo.h;
    imgInfo.multiple = 1;
    imgInfo.containerWidth = globalInfo.w;
    imgInfo.containerHeight = globalInfo.h;
    imgInfo.src = "../../img/404.png";
    containerHeight = globalInfo.h;
    containerWidth = globalInfo.w;
    configIs.src = imgSrc;
    setSe('imgInfo', imgInfo);
    setSize(containerWidth, containerHeight);
    // showConfig(type)
};

var base64ToBlob = function (urlData) {
    let arr = urlData.split(',');
    let mime = arr[0].match(/:(.*?);/)[1] || 'image/png';
    // 去掉url的头，并转化为byte
    let bstr = atob(arr[1]),
        n = bstr.length,
        // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
        u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {
        type: mime
    });
};
/**
 * @function escapeHTML 转义html脚本 < > & " '
 * @param a -
 *            字符串
 */
var escapeHTML = function (a) {
    a = "" + a;
    return a.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
};
/**
 * @function unescapeHTML 还原html脚本 < > & " '
 * @param a -
 *            字符串
 */
var unescapeHTML = function (a) {
    a = "" + a;
    return a.replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&amp;/g, "&").replace(/&quot;/g, '"').replace(/&apos;/g, "'");
};

//新网络协议
var sendWebService_2 = function (data, sucCallback, sucCallbackParam, failCallback) {
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        httpsForward_2(data, sucCallback, sucCallbackParam, failCallback)
        return;
    }
    let wsURL = getRouterUrl();
    data.msg = msgArray[data.params.web_cmd];
    let getData;
    $.ajax({
        type: "POST", //访问WebService使用Post方式请求
        contentType: "text/xml; charset=utf-8", //WebService 会返回Json类型
        // contentType: "text/plain; charset=utf-8",
        url: wsURL, //调用WebService
        data: data.params, //Email参数
        dataType: 'json',
        timeout: 180000,
        success: function (response) { //回调函数，result，返回值
            if (data.params.web_cmd === 10) {
                return
            }
            try {
                let res = response;
                // 转义特殊符号
                //res = unescapeHTML(res);
                // if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                //     res = res.replace(/\n/g, "\\n");
                // }
                getData = res;
                if (parseInt(getData.ret) === 0) {
                    if (getData.desc === 'success') {
                        if (sucCallback) {
                            sucCallback(getData, sucCallbackParam);
                            res = "";
                            getData = null;
                            return
                        }
                    }
                }
                let msg = "";
                if (getData && getData.desc === 'timeout') {
                    msg = langMessage.common.timeout
                } else if (getData && getData.desc === 'request none') {
                    msg = langMessage.common.requestNone
                } else {
                    msg = data.msg + langMessage.common.fail;
                }
                layer.msg(msg, {icon: 2});
                if (failCallback) {
                    failCallback(data.type)
                }
            } catch (e) {
                layer.msg(langMessage.common.jsonError, {icon: 2});
                console.log(e);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (data.params.web_cmd !== 10 && data.params.web_cmd !== 9) {
                console.log(XMLHttpRequest);
                console.log("Webservice" + data.msg + langMessage.common.fail);
                layer.msg(langMessage.common.netError, {icon: 2});
                if (failCallback) {
                    failCallback()
                }
            }

        }
    });
    if (data.params.web_cmd === 10 || data.params.web_cmd === 9) {
        if (sucCallback) {
            console.log(langMessage.common.restart);
            if (sucCallbackParam) {
                sucCallback(sucCallbackParam);
            } else {
                sucCallback()
            }
        }
    }
};

var decodeLog = function (log) {
    var reg = new RegExp("%\w\w", "g");
    let result = "";
    while ((result = reg.exec(log)) != null) {
        console.log(result);
        let real = String.fromCharCode(parseInt(result, 16));
        log.splice(reg.lastIndex, 3, real)
    }
};
var initWebService_2 = function (cmd, param, sucCallback, sucCallbackParam, failCallback) {
    let data = {
        params: {
            channel_id: 0,
            web_cmd: cmd,
        }
    };
    let type = null;
    if (param) {
        for (let index in param) {
            if (index === "type") {
                type = param[index];
                continue
            }
            data.params[index] = param[index]
        }
        console.log(param)
    }
    if (cmd === 2) {// 2抓拍图片
        getBlob(cmd, getPictureSuc_2, type, getPictureFail);

    } else if (cmd === 11 || cmd === 21 || cmd === 27) {// 11获取授权令牌   21获取yuv  27获取授权文件
        getBlob(cmd, sucCallback, sucCallbackParam, failCallback);

        // } else if (cmd === 12) {
        //     sendBlob(param, sucCallback, sucCallbackParam, failCallback);

    } else {
        sendWebService_2(data, sucCallback, sucCallbackParam, failCallback);
    }
};

var sendBlob = function (data, sucCallback, sucCallbackParam, failCallback) {
    var formData = new FormData();
    formData.append("channel_id", "0");
    formData.append("web_cmd", "12");
    formData.append("file", data);
    let wsURL = getRouterUrl();
    var xhr = new XMLHttpRequest();
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            //console.log(xhr.responseText);
            if (sucCallback) {
                sucCallback(this.response, sucCallbackParam);
            }
        } else {
            if (failCallback) {
                failCallback()
            }
        }
    };

    xhr.upload.addEventListener("progress", function (event) {
        if (event.lengthComputable) {
            console.log("文件上传", Math.ceil(event.loaded * 100 / event.total) + "%");
        }
    }, false);

    xhr.open("POST", wsURL, true);
    xhr.send(formData);
};

var getBlob = function (data, sucCallback, sucCallbackParam, failCallback) {
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        httpsForward_2_getBlob(data, sucCallback, sucCallbackParam, failCallback)
        return;
    }
    var xmlhttp;
    xmlhttp = new XMLHttpRequest();
    let wsURL = getRouterUrl();
    xmlhttp.open("POST", wsURL, true);
    xmlhttp.responseType = "blob";
    xmlhttp.onload = function () {
        //console.log(this);
        if (this.status == 200) {
            if (sucCallback) {
                sucCallback(this.response, sucCallbackParam);
            }
        } else {
            if (failCallback) {
                failCallback()
            }
        }
    };
    xmlhttp.send("channel_id=0&web_cmd=" + data);
};

var getPictureSuc_2 = function (getData, type) {
    console.log("webService.js:getPictureSuc_2")
    let imgInfo = getSe('imgInfo');
    let globalInfo = checkGlobal();
    let containerHeight, containerWidth;
    let configIs = document.getElementById("configImg");
    let newI = document.createElement("img");
    let imgSrc = "../../img/404.png";
    if (getData) {
        imgSrc = getData;
    }
    if (typeof (type) === "function") {
        type()
    } else {
        newI.onload = function () {
            window.URL.revokeObjectURL(newI.src);
            if (newI.width && newI.height) {
                imgInfo = {};
                imgInfo.src = "exist";
                imgInfo.width = newI.width;
                imgInfo.height = newI.height;
                imgInfo.multiple = imgInfo.width / globalInfo.w;
                imgInfo.containerWidth = globalInfo.w;
                imgInfo.containerHeight = imgInfo.height / imgInfo.multiple;
                containerHeight = imgInfo.containerHeight;
                containerWidth = imgInfo.containerWidth;

                setSe('imgInfo', imgInfo);
                newI.onload = null;
                newI = null;
                if (type) {
                    // 如果进入页面需要刷新数据
                    // reloadConfig(showConfig,type)
                    console.log("webService.js.getPictureSuc_2:开始setSize")
                    setSize(containerWidth, containerHeight);
                    console.log("webService.js.getPictureSuc_2:开始showConfig")
                    showConfig(type)
                }
            }
        };
        configIs.onload = function () {
            window.URL.revokeObjectURL(configIs.src);
            configIs.onload = null;
        };
        configIs.src = window.URL.createObjectURL(imgSrc);
        newI.src = window.URL.createObjectURL(imgSrc);
    }
    getData = null;
    imgSrc = null;
};
//
var initWebService = function (cmd, param, sucCallback, sucCallbackParam, failCallback) {
    let type = getSe("SDC");
    // 1旧网络协议，2新网络协议
    if (type) {
        initWebService_2(cmd, param, sucCallback, sucCallbackParam, failCallback)
    } else {
        initWebService_1(cmd, param, sucCallback, sucCallbackParam, failCallback)
    }
};

// 本地调试请求代理
var httpsForwardUrl = 'https://192.168.8.123/SDCAPI/V1.0/TOPSKY_T_HOLOS_M/';
// var httpsForwardUrl = 'https://192.168.8.90/SDCAPI/V1.0/TOPSKY_T_HOLOS_M/';
var httpsForward_1 = function (data, sucCallback, sucCallbackParam, failCallback) {
    let wsData;
    //SOAP 1.1 请求报文格式，1.2在网上可以找到
    wsData = '<?xml version="1.0" encoding="utf-8"?>';
    wsData = wsData + '<SOAP-ENV:Envelope  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xmlns:xsd="http://www.w3.org/2001/XMLSchema"  xmlns:ns1="http://webservice.service.seisys.cn/">';
    wsData = wsData + '<SOAP-ENV:Body>';
    wsData = wsData + '<ns1:CommonOperate>'; //这里就是发布的方法名和xml文档中的命名空间地址（图中画线部分）
    wsData = wsData + '<arg0>';
    wsData = wsData + escapeHTML(JSON.stringify(data.params));
    wsData = wsData + '</arg0>';
    wsData = wsData + '</ns1:CommonOperate>';
    wsData = wsData + '</SOAP-ENV:Body>';
    wsData = wsData + '</SOAP-ENV:Envelope>';
    let getData;
    $.ajax({
        type: "POST",
        url: "http://localhost:38080/codeExample/servlet/HttpsForwardServlet",
        timeout: 180000,
        data: {
            url: httpsForwardUrl,
            contentType: 'text/xml; charset=utf-8',
            params: wsData
        },
        xhrFields: {
            withCredentials: true
        },
        success: function (r) {
            let response = r.msg;
            if (data.params.web_cmd === 10) {// 10重启相机
                return
            }
            try {
                let res = response.childNodes[0].childNodes[0].childNodes[0].childNodes[0].childNodes[0].data;
                // 转义特殊符号
                res = unescapeHTML(res);
                if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                    res = res.replace(/\n/g, "\\n");
                }
                getData = JSON.parse(res);
                if (parseInt(getData.ret) === 0) {
                    if (getData.desc === 'success') {
                        if (sucCallback) {
                            sucCallback(getData, sucCallbackParam);
                            res = "";
                            getData = null;
                            return
                        }
                    }
                }
                let msg = "";
                if (getData && getData.desc === 'timeout') {
                    msg = langMessage.common.timeout
                } else if (getData && getData.desc === 'request none') {
                    msg = langMessage.common.requestNone
                } else {
                    msg = data.msg + langMessage.common.fail;
                }
                layer.msg(msg, {icon: 2});
                if (failCallback) {
                    failCallback(data.type)
                }
            } catch (e) {
                layer.msg(langMessage.common.jsonError, {icon: 2});
                console.log(e);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            console.log(XMLHttpRequest);
            console.log("Webservice" + data.msg + langMessage.common.fail);
            layer.msg(langMessage.common.netError, {icon: 2});
            if (failCallback) {
                failCallback()
            }
        }
    });
    if (data.params.web_cmd === 10) {// 10重启相机
        if (sucCallback) {
            console.log(langMessage.common.restart);
            if (sucCallbackParam) {
                sucCallback(sucCallbackParam);
            } else {
                sucCallback()
            }
        }
    }
}
var httpsForward_2 = function (data, sucCallback, sucCallbackParam, failCallback) {
    let params = ""
    for (let key in data.params) {
        if (params === "") {
            params = key + "=" + data.params[key]
        } else {
            params = params + '&' + key + "=" + data.params[key]
        }
    }
    let getData;
    $.ajax({
        type: "POST",
        url: "http://localhost:38080/codeExample/servlet/HttpsForwardServlet",
        timeout: 180000,
        data: {
            url: httpsForwardUrl,
            contentType: 'text/xml; charset=UTF-8',
            params: params
        },
        xhrFields: {
            withCredentials: true
        },
        success: function (response) { //回调函数，result，返回值
            if (data.params.web_cmd === 10) {// 10重启相机
                return
            }
            try {
                let res = JSON.parse(response.msg);
                // 转义特殊符号
                //res = unescapeHTML(res);
                // if (data.params.web_cmd === 5 || data.params.web_cmd === 6) {
                //     res = res.replace(/\n/g, "\\n");
                // }
                getData = res;
                if (parseInt(getData.ret) === 0) {
                    if (getData.desc === 'success') {
                        if (sucCallback) {
                            sucCallback(getData, sucCallbackParam);
                            res = "";
                            getData = null;
                            return
                        }
                    }
                }
                let msg = "";
                if (getData && getData.desc === 'timeout') {
                    msg = langMessage.common.timeout
                } else if (getData && getData.desc === 'request none') {
                    msg = langMessage.common.requestNone
                } else {
                    msg = data.msg + langMessage.common.fail;
                }
                layer.msg(msg, {icon: 2});
                if (failCallback) {
                    failCallback(data.type)
                }
            } catch (e) {
                layer.msg(langMessage.common.jsonError, {icon: 2});
                console.log(e);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (data.params.web_cmd !== 10 && data.params.web_cmd !== 9) {
                console.log(XMLHttpRequest);
                console.log("Webservice" + data.msg + langMessage.common.fail);
                layer.msg(langMessage.common.netError, {icon: 2});
                if (failCallback) {
                    failCallback()
                }
            }
        }
    });
    if (data.params.web_cmd === 10 || data.params.web_cmd === 9) {// 10重启相机 9恢复默认参数
        if (sucCallback) {
            console.log(langMessage.common.restart);
            if (sucCallbackParam) {
                sucCallback(sucCallbackParam);
            } else {
                sucCallback()
            }
        }
    }
}
var httpsForward_2_getBlob = function (data, sucCallback, sucCallbackParam, failCallback) {
    $.ajax({
        type: "POST",
        url: "http://localhost:38080/codeExample/servlet/HttpsForwardGetBlobServlet",
        timeout: 180000,
        data: {
            url: httpsForwardUrl,
            contentType: 'text/plain;charset=UTF-8',
            params: "channel_id=0&web_cmd=" + data
        },
        responseType: 'blob',
        xhrFields: {
            withCredentials: true
        },
        success: function (response) { //回调函数，result，返回值
            if (sucCallback) {
                let base64 = 'data:image/jpeg;base64,' + response.msg
                let arr = base64.split(',');
                //注意base64的最后面中括号和引号是不转译的
                let _arr = arr[1].substring(0, arr[1].length - 2);
                let mime = arr[0].match(/:(.*?);/)[1],
                    bstr = atob(_arr),
                    n = bstr.length,
                    u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bstr.charCodeAt(n);
                }
                let blob = new Blob([u8arr], {
                    type: mime
                });
                sucCallback(blob, sucCallbackParam);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (failCallback) {
                failCallback()
            }
        }
    });
}


