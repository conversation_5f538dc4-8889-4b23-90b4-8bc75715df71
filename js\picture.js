/**
 * Create by Chelly
 * 2019/11/13
 */
var form, layer, element;
var pictureInterval = null;
var pictureCallback = true;
var regPicPath = /(data:image\/jpeg;base64,)(.*)/;
window.onresize = function () {
  initPicture();
};
$(document).ready(function () {
  window.parent.setNavValue(1);
  initPicture();
  layui.use(['element', 'form', 'layer'], function () {
    element = layui.element;
    form = layui.form;
    layer = layui.layer;
  });
  let scrollT = 0, scrollL = 0, moveFlag = false, moveX = 0, moveY = 0;
  $("#strollContainer").click(function () {
    moveFlag = false, moveX = 0, moveY = 0;
    //console.log('click')
  }).off("mousemove").on("mousemove", function (e) {
    if (moveFlag) {
      // console.log("move X:" + moveX + " Y" + moveY);
      let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
      let distanceY = e.pageY - moveY;
      //console.log("distance X:" + distanceX + " Y:" + distanceY);

      $('#imgContainer').scrollLeft(scrollL + (-1) * distanceX);
      $('#imgContainer').scrollTop(scrollT + (-1) * distanceY);
      //console.log('mousemove')
    }
  }).off("mousedown").on("mousedown", function (e) {
    moveFlag = true;
    moveX = parseInt(e.pageX);
    moveY = parseInt(e.pageY);
    scrollT = $('#imgContainer').scrollTop();
    scrollL = $('#imgContainer').scrollLeft();
    $("#strollContent").css({
      cursor: 'move'
    });
    //console.log('mousedown')
  }).off("mouseup").on("mouseup", function (e) {
    moveFlag = false;
    //console.log('mouseup')
    // $("#imgContainer").css({
    //     cursor: 'pointer'
    // })
    // console.log('cx:',cX,'cy',cY)
  }).off("mouseleave").on("mouseleave", function (e) {
    moveFlag = false;
  });
  $(".my-button").click(function (e) {
    let imgInfo = getSe('imgInfo');
    let value = parseInt(getDataset(e.currentTarget).value);
    let mX = 1, mY = 1;
    let width = $("#strollContent")[0].clientWidth;
    let height = $("#strollContent")[0].clientHeight;
    if (value === 0) {
      if ((width / height) > 4 / 3) {
        mY = height / imgInfo.height;
        mX = mY * 4 / 3
      } else {
        mX = width / imgInfo.width;
        mY = mY * 3 / 4
      }
    } else if (value === 1) {
      if ((width / height) > 16 / 9) {
        mY = height / imgInfo.height;
        mX = mY * 16 / 9
      } else {
        mX = width / imgInfo.width;
        mY = mY * 9 / 16
      }
    } else if (value === 2) {
      mY = height / imgInfo.height;
      mX = width / imgInfo.width;
    }
    $("#configImg").css({
      height: imgInfo.height * mY,
      width: imgInfo.width * mX
    });
  });
  window.addEventListener('mousewheel', debounceScroll, {passive: false});
  window.addEventListener('keydown', onKeyDown, {passive: false});
  // $(document).on('mousewheel DOMMouseScroll', onMouseScroll,{passive: false});
  $("#savePic").click(function () {
    let imgInfo = getSe("imgInfo");
    let src = imgInfo.src;
    if (src) {

      let format = 'YYYYMMDD HH:mm:ss';
      let c = document.createElement("canvas");
      c.width = imgInfo.width;
      c.height = imgInfo.height;
      let ctx = c.getContext("2d");
      let img = document.getElementById("configImg");
      ctx.drawImage(img, 0, 0);
      src = c.toDataURL("image/jpeg");
      let s = src.match(regPicPath);
      s = s[2];
      let blob = dataURLtoBlob(s);
      doSaveBlob('savePic', new Date().Format(format) + '.jpg', blob);
      c = null;
      ctx = null;
      blob = null;
    }
  });
  $("#getPic").click(function () {
    let getPic = $("#getPic");
    clearInterval(pictureInterval);
    if (getPic.hasClass("layui-icon-play")) {
      getPic.removeClass("layui-icon-play");
      getPic.addClass("layui-icon-pause");
      let t = $("#second").val();
      pictureInterval = setInterval(function () {
        initWebService(webserviceCMD.CMD_CAPTURE_JPG, {});
      }, t * 1000)
    } else {
      getPic.addClass("layui-icon-play");
      getPic.removeClass("layui-icon-pause");
    }
  });
  $("#second").change(function () {
    let t = $("#second").val();
    if (pictureInterval) {
      clearInterval(pictureInterval);
      pictureInterval = setInterval(function () {
        initWebService(webserviceCMD.CMD_CAPTURE_JPG, {});
      }, t * 1000)
    }
  })

});
var onKeyDown = function (e) {
  e.preventDefault();
  let imgInfo = getSe('imgInfo');
  if (imgInfo) {
    let scrollInfo = getSe('scrollInfo');
    if (e.ctrlKey) {
      if (e.keyCode === 38) {
        scrollInfo.m = scrollInfo.m + 0.1 > 2 ? 2 : scrollInfo.m + 0.1;
        $("#configImg").css({
          height: imgInfo.height * scrollInfo.m,
          width: imgInfo.width * scrollInfo.m
        });
      } else if (e.keyCode === 40) {
        scrollInfo.m = scrollInfo.m - 0.1 < 0.1 ? 0.1 : scrollInfo.m - 0.1;
        $("#configImg").css({
          height: imgInfo.height * scrollInfo.m,
          width: imgInfo.width * scrollInfo.m
        });
      }
    }
    setSe('scrollInfo', scrollInfo);
  }
};

var onMouseScroll = function (e) {
  console.log('scroll', new Date().getTime());
  e.preventDefault();
  let imgInfo = getSe('imgInfo');
  if (imgInfo) {
    let scrollInfo = getSe('scrollInfo');
    let wheel = e.wheelDelta || -e.detail;
    let delta = Math.max(-1, Math.min(1, wheel));
    if (delta < 0) {//向下滚动
      console.log('向下滚动', wheel);
      scrollInfo.m = scrollInfo.m - 0.1 < 0.1 ? 0.1 : scrollInfo.m - 0.1;
      $("#configImg").css({
        height: imgInfo.height * scrollInfo.m,
        width: imgInfo.width * scrollInfo.m
      });
    } else {//向上滚动
      console.log('向上滚动', wheel);
      scrollInfo.m = scrollInfo.m + 0.1 > 2 ? 2 : scrollInfo.m + 0.1;
      $("#configImg").css({
        height: imgInfo.height * scrollInfo.m,
        width: imgInfo.width * scrollInfo.m
      });
    }
    setSe('scrollInfo', scrollInfo);
  }
};
var debounceScroll = debounce(onMouseScroll, 500);
var initPicture = function () {
  // 获取实际宽高来赋值
  let fH = window.parent.$(".content-body")[0].clientHeight - 15;
  let fW = window.parent.$(".content-body")[0].clientWidth - 30;
  let imgInfo = getSe('imgInfo');
  if (!imgInfo) {
    imgInfo = {};
    imgInfo.width = fW;
    imgInfo.height = fH;
  }
  let scrollInfo = getSe('scrollInfo');
  if (!scrollInfo) {
    scrollInfo = {};
    scrollInfo.mX = 1;
    scrollInfo.mY = 1;
    scrollInfo.m = 1;
  }
  setSe('scrollInfo', scrollInfo);
  $("#container").css({
    height: fH - 50,
    width: fW
  });
  $("#imgContainer").css({
    height: fH - 50,
    width: fW
  });
  $("#configImg").css({
    height: imgInfo.height * scrollInfo.m,
    width: imgInfo.width * scrollInfo.m
  });
  let cH = $("#imgContainer")[0].clientHeight;
  let cW = $("#imgContainer")[0].clientWidth;
  $(".container").css({
    height: cH,
    width: cW
  });
};