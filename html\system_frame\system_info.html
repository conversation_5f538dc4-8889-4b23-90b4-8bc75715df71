<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>systemInfo</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/system.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/systemInfo.js"></script>
    <style>
        .layui-form-item span i {
            margin-right: 5px;
        }

        .auth {
            color: #5FB878;
        }

        .warning {
            color: #c52616;
        }

        .doing {
            color: #dba71b;
        }
    </style>

</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-form detect-signal layui-show">
    <fieldset class="layui-elem-field">
        <legend id="basicInformation">基本信息</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <label id="name" class="layui-form-label">名称：</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" id="board_name">
                </div>
            </div>
            <div class="layui-form-item">
                <label id="Device_GUID" class="layui-form-label">设备GUID：</label>
                <div class="layui-input-block">
                    <input type="text" class="layui-input" id="device_uid" readonly="readonly" disabled="disabled">
                </div>
            </div>
            <div class="layui-form-item">
                <label id="Equipment_type" class="layui-form-label">设备类型：</label>
                <span id="device_type"></span>
            </div>
            <div class="layui-form-item">
                <label id="manufacturers" class="layui-form-label">制造商：</label>
                <span id="manufacturer"></span>
            </div>
            <div class="layui-form-item">
                <label id="Hardware_version" class="layui-form-label">硬件版本：</label>
                <span id="hardware_ver"></span>
            </div>
            <div class="layui-form-item">
                <label id="Software_version" class="layui-form-label">软件版本：</label>
                <span id="software_ver"></span>
            </div>
            <div class="layui-form-item">
                <label id="Communication_version" class="layui-form-label">通讯版本：</label>
                <span id="communication_ver"></span>
            </div>
            <div class="layui-form-item">
                <label id="Configuration_version" class="layui-form-label">配置版本：</label>
                <span id="configuration_ver"></span>
            </div>
            <div class="layui-form-item">
                <label id="Authorization_status" class="layui-form-label">授权版本：</label>
                <span id="auth_status"></span>
            </div>
            <div class="layui-form-item">
                <label id="Authorization_time" class="layui-form-label">授权期限：</label>
                <span id="auth_time"></span>
            </div>
            <div class="layui-form-item">
                <label id="Algorithm_engine" class="layui-form-label">算法引擎：</label>
                <span id="arithmet_status"></span>
            </div>
            <div class="layui-form-item">
                <label id="NNIE_engine" class="layui-form-label">NNIE引擎：</label>
                <span id="nnie_status"></span>
            </div>
            <div class="layui-form-item">
                <label id="Plugin_version" class="layui-form-label">插件版本：</label>
                <span id="ocx_version"></span>
            </div>
            <div class="layui-form-item">
                <label id="web_ver" class="layui-form-label">web版本：</label>
                <span id="web_version"></span>
            </div>

            <!--<fieldset class="layui-elem-field">-->
            <!--<legend>设备版本</legend>-->
            <!--<div class="layui-field-box">-->
            <!---->
            <!--</div>-->
            <!--</fieldset>-->
            <!--<fieldset class="layui-elem-field">-->
            <!--<legend>网络设置</legend>-->
            <!--<div class="layui-field-box">-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">IP地址：</label>-->
            <!--<span id="ip_addr"></span>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">通讯端口：</label>-->
            <!--<span id="port"></span>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">子网掩码：</label>-->
            <!--<span id="net_mask"></span>-->
            <!--</div>-->
            <!--<div class="layui-form-item">-->
            <!--<label class="layui-form-label">默认网关：</label>-->
            <!--<span id="gate_way"></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--</fieldset>-->
        </div>
    </fieldset>
    <button class="layui-btn layui-btn-default" id="saveDevice">保存配置</button>
    <button class="layui-btn layui-btn-default layui-btn-block" id="resetDevice">重置
    </button>
</div>
</body>
</html>
