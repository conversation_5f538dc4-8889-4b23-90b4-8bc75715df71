<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>匝道电警</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_detect_ramp_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="vj" class="title">匝道电警</h2>
    <div class="content">
        <img id="Ramp_electric_police_img1" src="./image067.png" alt="匝道电警"/>
        <p id="vj2" >
            检测闯禁令（沪C）：检测沪C牌照。
        </p>
        <p id="vj3" >
            检测闯禁令（教练车）：检测教练车。
        </p>
        <p id="vj4" >
            检测货车禁止上匝道：检测车型货车。
        </p>
        <p id="vj5" >
            检测闯禁令（外省市小型车）：可以按星期和时间设置
        </p>
        <img id="Ramp_electric_police_img2" src="./image068.png" alt="闯禁令外省市小型车配置示例"/>
        <p id="vj6" >
            检测闯禁令（黑名单）：配合工控机或者其他程序使用，可以设置8种类型。
        </p>
        <img id="Ramp_electric_police_img3" src="./image069.png" alt="禁闯令黑名单配置示例">
    </div>
</div>
</body>
</html>