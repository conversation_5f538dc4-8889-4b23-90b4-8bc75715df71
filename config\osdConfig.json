﻿{
  "osdInfo": [
    {
      "value": 0,
      "title": "车牌号码：",
      "default": "苏A123S6",
      "type": "车牌号码",
      "checked": 0,
      "filter": "cphm",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 1,
      "title": "违法行为：",
      "default": "违反红灯指示",
      "type": "违法行为",
      "checked": 0,
      "filter": "wfxw",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 2,
      "title": "时间：",
      "default": "2000/01/01 00:00:00:000",
      "type": "时间",
      "checked": 0,
      "filter": "sj",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 3,
      "title": "车牌颜色：",
      "default": "蓝牌",
      "type": "车牌颜色",
      "checked": 0,
      "filter": "cpys",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 4,
      "title": "车辆类型：",
      "default": "小型轿车",
      "type": "车辆类型",
      "checked": 0,
      "filter": "cllx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 5,
      "title": "地点：",
      "default": "XX路口",
      "type": "地点",
      "checked": 0,
      "filter": "dd",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 6,
      "title": "车道号：",
      "default": "0",
      "type": "车道号",
      "checked": 0,
      "filter": "cdh",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 7,
      "title": "车道方向：",
      "default": "直行",
      "type": "车道方向",
      "checked": 0,
      "filter": "cdfx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 8,
      "title": "行驶方向：",
      "default": "右转",
      "type": "行驶方向",
      "checked": 0,
      "filter": "xsfx",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 9,
      "title": "红灯亮时间：",
      "default": "60秒",
      "type": "红灯亮时间",
      "checked": 0,
      "filter": "hdlsj",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 10,
      "title": "道路名称：",
      "default": "XXXXXX路",
      "type": "道路名称",
      "checked": 0,
      "filter": "dlmc",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 11,
      "title": "车速：",
      "default": "120",
      "type": "车速",
      "checked": 0,
      "filter": "cs",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 12,
      "title": "高低限速：",
      "default": "60-120",
      "type": "高低限速",
      "checked": 0,
      "filter": "gdxs",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 13,
      "title": "设备编号：",
      "default": "ABC123456789",
      "type": "设备编号",
      "checked": 0,
      "filter": "sbbh",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 14,
      "title": "车身颜色：",
      "default": "黑色",
      "type": "车身颜色",
      "checked": 0,
      "filter": "csys",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 15,
      "title": "车辆品牌：",
      "default": "XXX品牌",
      "type": "车辆品牌",
      "checked": 0,
      "filter": "clpp",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 16,
      "title": "违法代码：",
      "default": "010",
      "type": "违法代码",
      "checked": 0,
      "filter": "wfdm",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 17,
      "title": "防伪码：",
      "default": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
      "type": "防伪码",
      "checked": 0,
      "filter": "zdy1",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 18,
      "title": "自定义2：",
      "default": "",
      "type": "自定义2",
      "checked": 0,
      "filter": "zdy2",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    },
    {
      "value": 19,
      "title": "自定义3：",
      "default": "",
      "type": "自定义3",
      "checked": 0,
      "filter": "zdy3",
      "fontColor": "255,0,0,1",
      "backColor": "255,255,255,0",
      "fontSize": 5,
      "x": 0,
      "y": 0
    }
  ],
  "dateFormat": {
    "0": "YYYY/MM/DD",
    "1": "DD/MM/YYYY",
    "2": "YYYY-MM-DD",
    "3": "DD-MM-YYYY"
  },
  "timeFormat": {
    "0": "24时制",
    "1": "12时制"
  },
  "fontSize": {
    "0": 16,
    "1": 24,
    "2": 32,
    "3": 48,
    "4": 64,
    "5": 72,
    "6": 88,
    "7": 96,
    "8": 128
  },
  "osdPosition": {
    "0": "图片上",
    "1": "图片内",
    "2": "图片下"
  },
  "pixel": 8,
  "maxHeight": 0
}
