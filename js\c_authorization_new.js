/**
 * Create by Chelly
 * 2019/11/18
 */
var form, laydate, element, layer, upload;
var exts = '.token';

$(document).ready(function () {
    window.parent.setNavValue(3);
    layui.use(['form', 'laydate', 'element', 'layer', 'upload'], function () {
        form = layui.form, laydate = layui.laydate, element = layui.element, layer = layui.layer, upload = layui.upload;
        laydate.render({
            elem: '#setTimeValue'
            , type: 'time'
        });
        let type = getSe("SDC");
        if (type) {
            $("#authInfo").html(langMessage.authorization.authInfo.new)
        } else {
            $("#authInfo").html(langMessage.authorization.authInfo.old)
        }

        let appName = getAppName()
        let allowed = getSe("projectConfig").allowedAuth;
        if (allowed.indexOf(appName) < 0) {
            $(".import").remove();
            $("#authImportInfo").remove();
        }
        form.render();
        // 老版授权-兼容逻辑
        if (isIE()) {
            let result = initialize_ocx('authContainer');
            if (!result.result) {
                layer.open({
                    title: langMessage.common.error,
                    shade: 0.8,
                    btn: false,
                    content: result.msg,
                    anim: 6
                });
            }
        }
    });
    $("#exportAuth").off("click").on("click", function () {
        initWebService(webserviceCMD.CMD_GET_AUTH_CODE, null, getAuth);
    });
    $("#exportFile").off("click").on("click", function () {
        let deviceInfo = getSe("deviceInfo");
        if (!deviceInfo) {
            reloadConfig(exportAuthLic);
        } else {
            exportAuthLic();
        }
    });

    $('#authFile').off("click").on("click", function (e) {
        let importBtn = $("#importAuth");
        importBtn.addClass("layui-btn-disabled");
        importBtn.css({
            display: 'none'
        });
        setDataset(importBtn[0], "authFileBase64", "");
        $("#authFileInput").val("");
        let deviceInfo = getSe("deviceInfo");
        if (!deviceInfo) {
            reloadConfig(reloadAuthCallback);
        } else {
            reloadAuthCallback();
        }
    });
    //老二维码逻辑
    //initAuthCode();
});
var exportAuthLic = function () {
    let deviceInfo = getSe('deviceInfo');
    let auth_status = deviceInfo.auth_status;
    if(auth_status){
        initWebService(webserviceCMD.CMD_GET_AUTH_FILE, null, exportAuthFile);
    }else{
        layer.msg(langMessage.authorization.authMsg.NO_AUTH, {icon: 2})
    }
}
var reloadAuthCallback = function (file) {
    $('#authName').text("");
    let type = getSe("SDC");
    if (type) {
        //reloadAuthCallback_2(file);//.tar
        reloadAuthCallback_1(file)//.lic
    } else {
        reloadAuthCallback_1(file)
    }
};
//老版授权-选择文件夹读取文件逻辑
var reloadAuthCallback_1 = function (file) {
    let deviceInfo = getSe('deviceInfo');
    let auth_status = deviceInfo.auth_status;
    // let auth_status = 0;
    let unique_id = deviceInfo.unique_id;
    // let unique_id = 'RAAAAAAAAAAAAAAAAAAAAAAAAAAAAd+d';
    let startGetFile = function () {
        if (isIE()) {
            readAuthFile(unique_id, importAuthCallback)
        } else {
            getFileList(unique_id)
        }
    };
    if (auth_status) {
        layer.confirm(langMessage.authorization.authMsg.IMPORT_CONFIRM, {
            btn: [langMessage.common.confirm, langMessage.common.cancel] //可以无限个按钮
        }, function (index, layero) {
            layer.close(index);
            startGetFile()
        }, function (index) {
            layer.close(index);
        });
    } else {
        startGetFile()
    }
};
//新版授权-只运行插件授权tar文件
var reloadAuthCallback_2 = function (file) {
    let deviceInfo = getSe('deviceInfo');
    let auth_status = deviceInfo.auth_status;
    let startGetFile = function () {
        getAuthFile();
    };
    if (auth_status) {
        layer.confirm(langMessage.authorization.authMsg.IMPORT_CONFIRM, {
            btn: [langMessage.common.confirm, langMessage.common.cancel] //可以无限个按钮
        }, function (index, layero) {
            layer.close(index);
            startGetFile()
        }, function (index) {
            layer.close(index);
        });
    } else {
        startGetFile()
    }
};

var getAuthFile = function () {
    let path = getLicencePath();
    if (path) {
        let ext = path.slice(path.lastIndexOf(".") + 1);
        if (ext !== 'tar') {
            layer.msg(langMessage.authorization.authMsg.UNSUPPORTED, {icon: 2, time: 5000});
            return
        }
        let r = uploadLicence(path);
        if (r) {
            layer.msg(langMessage.authorization.authMsg.UPLOAD_SUC, {icon: 1})
        } else {
            layer.msg(langMessage.authorization.authMsg.UPLOAD_FAIL, {icon: 2, time: 5000});
        }
        return
    }
    layer.msg(langMessage.authorization.authMsg.NO_SELECTED, {icon: 2, time: 5000});
    // $("#authFileInput").click();
    // $("#authFileInput").off("change").on("change",function (e) {
    //     let path = this.value;
    //     if(path!==""){
    //         let name = getFileName(path);
    //         let ext = name.slice(name.lastIndexOf("."));
    //         if(ext!=='tar'){
    //             layer.msg("不支持的文件格式", {icon: 2, time: 5000});
    //             return
    //         }
    //         let r = uploadLicence(path);
    //         if(r){
    //             layer.msg("上传成功！",{icon:1})
    //         }else{
    //             layer.msg("上传失败！", {icon: 2, time: 5000});
    //         }
    //     }
    // });
};

//老版授权-选择文件夹逻辑
var getFileList = function (uid) {
    $("#authFileInput").click();
    $("#authFileInput").off("change").on("change", function (e) {
        let files = this.files;
        readFileList(files, uid);
    });
};

var getAuth_1 = function (data) {
    let code = data.authcode;
    let blob = dataURLtoBlob(code);
    analysisFile(blob, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            doSaveBlob('exportAuth', location.hostname + '-' + info.crc.toString(16).toUpperCase() + exts, blob);
        } else {

            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});

        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    }, 292);
    // getPath(code, exportAuthCallback);
};
var getAuthSuc = function (data) {

    layer.confirm(langMessage.authorization.authMsg.IMPORT_SUC, {
        btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
    }, function (index, layero) {
        // changeCameraParam(data);
        layer.close(index);
        initWebService(webserviceCMD.CMD_REBOOT, null, loadingWait, getSe("projectConfig").index_auth_time);
        //loadingWait(60);
    }, function (index) {
        layer.close(index);
    });

};

var exportAuthCallback = function (Folder, auth, name) {
    let filePath = Folder + name;
    let result = saveAuthcode(filePath, auth);
    if (result != 'FALSE') {
        $("#exportPath").html("<i class=\"layui-icon layui-icon-tips\"></i>文件导出为：" + result);

        layer.msg(langMessage.authorization.authMsg.EXPORT_SUC, {icon: 1, time: 5000})

    } else {
        $("#exportPath").html("");
        layer.msg(langMessage.authorization.authMsg.EXPORT_FAIL, {icon: 2, time: 5000})
    }
};
var importAuthCallback_1 = function (result) {
    let importA = $("#importAuth");
    if (result.result) {
        $('#authName').text(result.fileName);
        importA.removeClass("layui-btn-disabled");
        importA.css({
            display: 'inline-block'
        });
        setDataset(importA[0], "authFileBase64", result.base);
        importA.off('click').on('click', function () {
            let param = {
                authFileBase64: getDataset($("#importAuth")[0]).authFileBase64
            };
            initWebService(webserviceCMD.CMD_IMPORT_AUTH_FILE, param, getAuthSuc, null);
        });
    } else {
        layer.msg(result.msg, {icon: 2, time: 5000});
        importA.css({
            display: 'none'
        })
    }
};
/**
 * 使用JScript循环读取文件夹中的文件，这个是测试函数，由于需要设置Internet的安全性故弃用
 * Unicode读取出来的为两个字节一个字符的字符串数组，需要进行转义
 * @param fso 传入的控件
 * @param folder 文件夹路径
 * @param fileList 读取的文件数组
 */
var IEGetFiles = function (fso, folder, fileList) {
    //取文件夹
    let underFolders = new Enumerator(folder.SubFolders);
    //取文件
    let underFiles = new Enumerator(folder.files);
    for (; !underFiles.atEnd(); underFiles.moveNext()) {
        let fn = "" + underFiles.item();
        // File file = ifile.getLocation().toFile;
        if (underFiles.item().size > 0) {
            let ForReading = 1, ForWriting = 2;
            let ForCreate = true, NoCreate = false;
            let TristateTrue = -1, //以 Unicode 方式打开文件
                TristateFalse = 0, //以 ASCII 方式打开文件
                TristateUseDefault = -2; //使用系统默认值打开文件
            let ts0 = fso.OpenTextFile(fn, ForReading, NoCreate, TristateTrue);
            let s0 = ts0.ReadAll();
            let getS = str2ab(s0);
            let getBlob = new Blob([getS]);
            let file = {
                name: "" + underFiles.item().name,
                size: underFiles.item().size,
                file: getBlob,
                type: 'IE'
            };
            ts0.Close();
            fileList.push(file)
        }
    }
    for (; !underFolders.atEnd(); underFolders.moveNext()) {
        IEGetFiles(fso, underFolders.item(), fileList);
    }
};

var readFileList = function (files, uid) {
    let fileList = [];
    let checkExtFiles = checkFiles(files);
    if (checkExtFiles.length === 0) {
        layer.msg(langMessage.authorization.matchFail, {icon: 2});
        return
    }
    for (let i = 0; i < checkExtFiles.length; i++) {
        analysisFile(checkExtFiles[i], function (info) {
            if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
                fileList.push(info);
            }
            if (i === (checkExtFiles.length - 1)) {
                compareAuth(fileList, uid, importAuthCallback)
            }
        }, function (e) {
            console.log(e)
        });
    }
};
var loadingWait = function (s) {
    let source = getTopWindow();

    let app_name = getAppName()

    let loading = source.indexLoading(s, langMessage.common.restartAlgorithm);

    setTimeout(function () {
        source.closeLoading(loading);
        source.location.href = "https://" + location.host + "/SDCWEB/" + app_name + "/index.html";
    }, s * 1000);
};
var initAuthCode = function () {
    initWebService(webserviceCMD.CMD_GET_AUTH_SHORT, null, getAuthCodeCallback);
};
var getAuthCodeCallback = function (data) {
    let code = data.authcode;
    let blob = dataURLtoBlob(code);
    analysisFile(blob, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            let info = code;
            showQRCode(info)
        } else {

            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});

        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    }, 68);
};
var showQRCode = function (info) {
    let qrcode = new QRCode(document.getElementById("authCode"), {
        text: "http://*************/authorizeWeb/formHtml.html?" + info,
        //text: "http://*************:8081/TestWeb1/WebRoot/formHtmlTest.html?" + info,
        width: 200,
        height: 200,
        colorDark: "#000000", //二维码颜色（默认黑色）
        colorLight: "#ffffff", //二维码背景颜色（默认白色）
        correctLevel: QRCode.CorrectLevel.H //二维码容错level（默认为高）
    });
    //$("#qrcodeDiv").qrcode(encodeURI("http://中文中文"));
    $("#authCode canvas")[0].getContext('2d').drawImage($("#authImg")[0], 80, 80, 40, 40);
};

var getAuthFail = function () {

    layer.msg(langMessage.authorization.authMsg.IMPORT_FAIL, {icon: 2, time: 5000});

};
//新网络协议
var getAuth_2 = function (data) {
    analysisFile(data, function (info) {
        console.log(info);
        if (info.crc && info.now_crc && (info.crc === info.now_crc)) {
            doSaveBlob('exportAuth', location.hostname + '-' + info.crc.toString(16).toUpperCase() + exts, data);
        } else {

            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});


        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    }, 292);
    // getPath(code, exportAuthCallback);
};

var importAuthCallback_2 = function (result) {
    let importA = $("#importAuth");
    if (result.result) {
        $('#authName').text(result.fileName);
        importA.removeClass("layui-btn-disabled");
        importA.css({
            display: 'inline-block'
        });
        importA.off('click').on('click', function () {
            initWebService(webserviceCMD.CMD_IMPORT_AUTH_FILE, {authFileBase64: result.base}, getAuthSuc, null);
        });
    } else {
        layer.msg(result.msg, {icon: 2, time: 5000});
        importA.css({
            display: 'none'
        })
    }
};

var getAuth = function (data) {
    let type = getSe("SDC");
    if (type) {
        getAuth_2(data)
    } else {
        getAuth_1(data)
    }
};
var exportAuthFile = function (data) {
    let type = getSe("SDC");
    if (type) {
        doSaveBlob('exportFile', location.hostname + '.lic', data);
    } else {
        let code = data.authcode;
        let blob = dataURLtoBlob(code);
        doSaveBlob('exportFile', location.hostname + '.lic', blob);
    }
};
var importAuthCallback = function (result) {
    let type = getSe("SDC");
    if (type) {
        importAuthCallback_2(result)
    } else {
        importAuthCallback_1(result)
    }
};

