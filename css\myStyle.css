/*导航背景色 #9b1f12*/
.custom-style .layui-layout-admin .layui-header {
    background-color: #0d1524
}

/*重点色 #0083c9*/
.custom-style .layui-progress-bar,
.custom-style .layui-form-select dl dd.layui-this,
.custom-style .layui-nav .layui-this:after,
.custom-style .layui-nav-tree .layui-nav-itemed:after,
.custom-style .layui-nav-child dd.layui-this,
.custom-style .layui-laypage .layui-laypage-curr .layui-laypage-em{
    background-color: #f58319
}
.custom-style .layui-layout-admin .layui-logo,
.custom-style .layui-form-radio > i:hover,
.custom-style .layui-form-radioed > i,
.custom-style .layui-breadcrumb a:hover,
.custom-style .layui-tab-card > .layui-tab-more .layui-this,
.custom-style .layui-laypage a:hover{
    color: #f58319;
}
.custom-style .layui-form-checked[lay-skin=primary] i,
.custom-style .layui-form-onswitch{
    border-color: #f58319;
    background-color: #f58319;
}
.custom-style .layui-table-edit:focus,
.custom-style .layui-form-checked,
.custom-style .layui-form-checkbox[lay-skin=primary]:hover i{
    border-color: #f58319; !important
}

.custom-style .layui-tab-brief > .layui-tab-more li.layui-this:after,
.custom-style .layui-tab-brief > .layui-tab-title .layui-this:after {
    border-bottom: 2px solid #fff;
}

.custom-style .layui-nav-itemed > a,
.custom-style .layui-nav-tree .layui-nav-title a,
.custom-style .layui-nav-tree .layui-nav-title a:hover{
    color: #f58319 !important;
}
.custom-style .layui-nav-tree .layui-nav-child dd.layui-this,
.custom-style .layui-nav-tree .layui-nav-child dd.layui-this a,
.custom-style .layui-nav-tree .layui-this,
.custom-style .layui-nav-tree .layui-this > a,
.custom-style .layui-nav-tree .layui-this > a:hover{
    color: #f58319 !important;
}

.custom-style .layui-header {
    height: 60px;
}
.custom-style .layui-nav .layui-nav-item {
    line-height: 60px;
}
/*顶部导航未选择文字色*/
#topBar.layui-nav .layui-nav-item a{
    color: #999;
}
/*顶部导航选中hover文字色*/
#topBar.layui-nav .layui-nav-item a:hover,
#topBar.layui-nav .layui-this a{
    color: #ffffff;
    font-weight: bold;
}
/*顶部导航选中hover底部色块色*/
.custom-style #topBar.layui-nav .layui-this:after,
.custom-style #topBar .layui-nav-bar
{
    background-color: #f58319;
}

/*侧边条色*/
#sideBar.layui-nav-tree .layui-nav-child dd.layui-this,
#sideBar.layui-nav-tree .layui-nav-child dd.layui-this a,
#sideBar.layui-nav-tree .layui-this, .layui-nav-tree .layui-this > a,
#sideBar.layui-nav-tree .layui-this > a:hover {
    background-color: #fff;
    color: #f58319;
}

#sideBar .layui-nav-itemed > a,
#sideBar.layui-nav-tree .layui-nav-title a,
#sideBar.layui-nav-tree .layui-nav-title a:hover {
    color: #f58319 !important;
}

#sideBar.layui-nav .layui-nav-item a:hover,
#sideBar.layui-nav .layui-this a {
    color: #f58319;
    background-color: #cfcfcf;
}

/*时间选择器颜色*/
.custom-style .layui-laydate .layui-this {
    background-color: #f58319 !important;
    color: #fff !important;
}
.custom-style .layui-laydate-footer span[lay-type=date] {
    color: #f58319 !important;
}
.custom-style .layui-laydate-footer span:hover {
    color: #f58319;
}

/*slider颜色*/
.custom-style .layui-slider-wrap-btn{
    border: 2px solid #f58319!important;
}
.custom-style .layui-slider-bar{
    background-color: #f58319!important;
}

/*按钮色 #*/
.custom-style .layui-btn{
    background-color: #0f418a;
}
/*按钮选中色*/
.custom-style .layui-btn-normal{
    background-color: #1E9FFF;
}
/*表格强制居中*/
.custom-style .layui-table td, .layui-table th {
    width: 11.767%;
    text-align: center;
}

/*消除影响*/
.custom-style .layui-btn-disabled,
.custom-style .layui-btn-disabled:active,
.custom-style .layui-btn-disabled:hover{
    background-color: #fbfbfb;
}
.custom-style .layui-btn-primary{
    background-color: #fff;
}
/*侧边栏滚动条*/
.custom-style .layui-nav-tree{
    width:180px
}
.custom-style .layui-nav-side{
    position: relative;
}
#myHelp{
    color: #f58319;
}