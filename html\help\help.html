<!DOCTYPE html>
<html lang="zh" class="index-html">
<head>
    <meta charset="UTF-8">
    <title>帮助文档</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_ch_en.js"></script>

    <style>
        body,html{
            font: 14px "Microsoft YaHei", Arial, sans-serif;
            background-color: #fff;
            height:100%;
        }

        a {
            text-decoration: none;
            color: #666a75;
        }

        ul {
            padding-inline-start: 0
        }

        li {
            list-style: none;
        }

        dd > a {
            font-size: 13px !important;
            padding-left: 30px !important;
        }

        .my-scroll {
            position: fixed;
            width: 200px;
            /*overflow-x: hidden;*/
            overflow-y: hidden;
        }

        #myScroll .nav-more {
            border-color: #666a75 transparent transparent;
            content: '';
            width: 0;
            height: 0;
            border-style: solid dashed dashed;
            border-width: 5px;
            top: 50%;
            margin-top: -2px;
            left: -5px;
            position: absolute;
        }

        #myScroll .nav-item.show > .nav-item-title > .nav-more {
            border-color: transparent transparent #1E9FFF;
            margin-top: -8px;

        }

        #myScroll {
            padding-top: 30px;
            font-size: 14px;
            position: fixed;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 999;
            width: 300px;
            background-color: rgba(30, 159, 255, 0.05);
        }

        #myScroll .nav-side {
            padding-top: 5px;
            padding-left: 15px;
        }

        #mySide {
            overflow-y: auto;
            position: absolute;
            bottom: 0;
            top: 100px;
            width: calc(100% - 15px);
            z-index: 3;
            padding: 5px 0 0 15px;
        }

        #myScroll .nav-hr {
            height: 1px;
            border-top: 1px solid rgb(235, 235, 235);
            border-right: none;
            border-bottom: none;
            border-left: none;
            border-image: initial;
            width: 100%;
        }

        #myScroll .nav-item {
            padding: 11px 10px 11px 15px;
        }

        #myScroll .nav-item .nav-item-child {
            padding-top: 11px;
            padding-left: 0;
            display: none;
        }

        #myScroll .nav-item.show > .nav-item-child {
            display: block;
        }

        #myScroll .nav-item-title {
            position: relative;
            padding-left: 15px;
        }

        #myScroll .nav-version {
            padding-left: 30px;
            font-size: 14px;
            color: #282828;
            cursor: pointer;
            display: inline-block;
            margin-bottom: 5px;
        }

        #myScroll .nav-title {
            padding-left: 30px;
            padding-right: 15px;
            font-size: 22px;
            color: #000;
            font-weight: 400;
        }

        .scroll {
            overflow-y: scroll;
        }

        .scroll-hidden {
            overflow-y: hidden;
        }

        #myScroll .nav-active > a {
            color: #1E9FFF
        }

        #myScroll a:hover {
            color: #1E9FFF;
        }

        ::-webkit-scrollbar {
            width: 6px;
            height: 12px;
        }

        ::-webkit-scrollbar-track-piece {
            background-color: transparent;
        / / 滚动槽 -webkit-border-radius: 3 px;
        }

        ::-webkit-scrollbar-thumb:vertical {
            height: 6px;
            background-color: #BDBDBD;
            -webkit-border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:horizontal {
            width: 6px;
            background-color: #BDBDBD;
            -webkit-border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:vertical:hover, ::-webkit-scrollbar-thumb:horizontal:hover {
            background-color: #808080;
        }
    </style>
    <script>

        var i18 = getCookie('i18ns');
        var catalogConfig;
        console.log(i18);
        if(i18=="en"){
        catalogConfig = {
            index: [
                {
                    title: 'brief introduction',
                    url: './help_synopsis.html',
                    child: []
                },
                {
                    title: 'Function description',
                    url: './help_description.html',
                    child: [
                        {
                            title: 'preview',
                            url: './help_preview.html',
                            child: []
                        },
                        {
                            title: 'event',
                            url: './help_event.html',
                            child: []
                        },
                        {
                            title: 'search',
                            url: './help_search.html',
                            child: []
                        },
                        {
                            title: 'Snap',
                            url: './help_picture.html',
                            child: []
                        },
                        {
                            title: 'Configuration',
                            url: './help_setting.html',
                            child: [
                                {
                                    title: 'system',
                                    url: './help_setting_system.html',
                                    child: []
                                },
                                {
                                    title: 'internet',
                                    url: './help_setting_internet.html',
                                    child: []
                                },
                                {
                                    title: 'video',
                                    url: './help_setting_video.html',
                                    child: []
                                },
                                {
                                    title: 'image',
                                    url: './help_setting_image.html',
                                    child: []
                                },
                                {
                                    title: 'disk',
                                    url: './help_setting_disk.html',
                                    child: []
                                },
                                {
                                    title: 'algorithm',
                                    url: './help_setting_algorithm.html',
                                    child: []
                                }
                            ]
                        }
                    ]
                },
                {
                    title: 'event detection',
                    url: './help_detect.html',
                    child: [
                        {
                            title: 'general functions',
                            url: './help_detect_common.html',
                            child: []
                        },
                        {
                            title: 'multi function electric alarm',
                            url: './help_detect_multiple.html',
                            child: []
                        },
                        {
                            title: 'bayonet police',
                            url: './help_detect_access.html',
                            child: []
                        },
                        {
                            title: 'ramp alarm',
                            url: './help_detect_ramp.html',
                            child: []
                        }
                    ]
                }
            ],
            version: 'Version number v1.0',
            title: 'Web help documentation'
        };
        }else{
        catalogConfig = {
            index: [
                {
                    title: '简介',
                    url: './help_synopsis.html',
                    child: []
                },
                {
                    title: '功能说明',
                    url: './help_description.html',
                    child: [
                        {
                            title: '预览',
                            url: './help_preview.html',
                            child: []
                        },
                        {
                            title: '事件',
                            url: './help_event.html',
                            child: []
                        },
                        {
                            title: '搜索',
                            url: './help_search.html',
                            child: []
                        },
                        {
                            title: '抓拍',
                            url: './help_picture.html',
                            child: []
                        },
                        {
                            title: '配置',
                            url: './help_setting.html',
                            child: [
                                {
                                    title: '系统',
                                    url: './help_setting_system.html',
                                    child: []
                                },
                                {
                                    title: '网络',
                                    url: './help_setting_internet.html',
                                    child: []
                                },
                                {
                                    title: '视频',
                                    url: './help_setting_video.html',
                                    child: []
                                },
                                {
                                    title: '图像',
                                    url: './help_setting_image.html',
                                    child: []
                                },
                                {
                                    title: '存储',
                                    url: './help_setting_disk.html',
                                    child: []
                                },
                                {
                                    title: '算法',
                                    url: './help_setting_algorithm.html',
                                    child: []
                                }
                            ]
                        }
                    ]
                },
                {
                    title: '事件检测',
                    url: './help_detect.html',
                    child: [
                        {
                            title: '通用功能',
                            url: './help_detect_common.html',
                            child: []
                        },
                        {
                            title: '多功能电警',
                            url: './help_detect_multiple.html',
                            child: []
                        },
                        {
                            title: '卡口电警',
                            url: './help_detect_access.html',
                            child: []
                        },
                        {
                            title: '匝道电警',
                            url: './help_detect_ramp.html',
                            child: []
                        }
                    ]
                }
            ],
            version: '版本号V1.0',
            title: 'Web帮助文档'
        };

        }

        var initScroll = function (catalogConfig, child) {
            let catalog = '';
            let catalogIndex;
            if (!child) {
                catalog += '<div class="nav-version">' + catalogConfig.version + '</div>';
                catalog += '<div class="nav-title">' + catalogConfig.title + '</div>';
                catalog += '<hr class="nav-hr"/>';
                catalog += '<div id="mySide">';
                catalog += '<ul class="nav-side">';
                catalogIndex = catalogConfig.index;
            } else {
                catalogIndex = catalogConfig;
                catalog += '<ul class="nav-item-child">'
            }

            catalogIndex.forEach(function(item) {
                if (item.child.length) {
                    catalog += '<li class="nav-item"  data-href="' + item.url + '"><a href="javascript:;" class="nav-item-title"><span class="nav-more"></span>' + item.title + '</a>';
                    catalog += initScroll(item.child, true);
                    catalog += '</li>'
                } else {
                    catalog += '<li class="nav-item"  data-href="' + item.url + '"><a href="javascript:;" class="nav-item-title">' + item.title + '</a></li>'
                }
            });
            if (!child) {
                catalog += '</div>'
            }
            catalog += '</ul>';
            return catalog
        };
        var getDataset = function (element) {
            if (element.dataset) {
                return element.dataset;
            } else {
                // console.log(element.attributes);
                let dataset = {}, name, matchStr;
                for (let i = 0; i < element.attributes.length; i++) {
                    let key = element.attributes[i].nodeName;
                    matchStr = key.match(/^data-(.+)/);
                    if (matchStr) { //data-auto-play 转成驼峰写法 autoPlay
                        name = matchStr[1].replace(/-([\da-z])/gi, function (all, letter) {
                            return letter.toUpperCase();
                        });
                        let v = element.attributes[i].nodeValue;
                        dataset[name] = v;
                    }
                }
                return dataset;
            }
        };
        $(document).ready(function () {
            $("#myScroll").html(initScroll(catalogConfig));
            $("#mySide").mouseover(function () {
                $("#mySide").addClass('scroll');
                $("#mySide").removeClass('scroll-hidden');
            }).mouseleave(function () {
                $("#mySide").addClass('scroll-hidden');
                $("#mySide").removeClass('scroll')
            });
            let cIframe = document.getElementById("frame_help");
            cIframe.onload = function(){
                cIframe.height = cIframe.contentDocument.body.scrollHeight + 100;
            };
            $("body").off('click').on('click', function (e) {
                let event = e || window.event;
                let target = event.target || event.srcElement;
                let item = $(target).parents(".nav-item");
                let bread = "";
                item = $(item[0]);
                if(item.length) {
                    let itemChild = item.find(".nav-item-child");
                    if (itemChild.length > 0) {
                        if (item.hasClass("show")) {
                            item.removeClass("show")
                        } else {
                            if (!item.parents(".nav-item").length){
                                $(".nav-item").removeClass("show");
                            }
                            item.addClass("show")
                        }
                    } else {
                        let parent;
                        if (item.parents(".nav-item")) {
                            parent = item.parents(".nav-item");
                            for (let i = parent.length; i > 0; i--) {
                                bread += ' > ' + $(parent[i - 1]).find(".nav-item-title")[0].innerText
                            }

                        }
                    }
                    if (itemChild.length && item.parents(".nav-item").length) {
                        bread += ' > ' + item.parents(".nav-item").find(".nav-item-title")[0].innerText;
                    }
                    bread += ' > ' + item.find(".nav-item-title")[0].innerText;
                    $(".nav-item").removeClass("nav-active");
                    item.addClass("nav-active");
                    let node = getDataset(item[0]).href;
                    $("#frame_help").attr("src", node);
                    $("#breadLink").html(bread);
                    let cIframe = document.getElementById("frame_help");
                    cIframe.onload = function(){
                        cIframe.height = cIframe.contentDocument.body.scrollHeight + 100;
                    };
                    console.log(cIframe)
                }


            });

        });
    </script>
</head>
<body class="custom-style">
<div style="height:100%">
    <div class="my-scroll" id="myScroll">
    </div>
    <div style="margin: 30px 0 0 300px">
        <div id="settingFrame" style="padding: 30px;min-width: 600px;">
            <div style="padding: 0px;">
                <div>
                    <div  class="breadcrumb">
                        <span id="texts">文档</span><span id="breadLink"></span>
                    </div>
                    <iframe id="frame_help" name="frame_help_content" src="./help_synopsis.html"
                            width="100%" height="100%" frameborder="0"  scrolling="no"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>