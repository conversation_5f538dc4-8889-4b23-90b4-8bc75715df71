.layui-tab-title {
    position: relative;
    left: 0;
    height: 40px;
    white-space: nowrap;
    font-size: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    transition: all .2s;
    -webkit-transition: all .2s;
}
.layui-tab-title li {
    height: 100%;
    background-color: #f2f2f2 !important;
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    transition: all .2s;
    -webkit-transition: all .2s;
    position: relative;
    line-height: 40px;
    min-width: 65px;
    padding: 0 15px;
    text-align: center;
    cursor: pointer;
}
.layui-tab-title .layui-this {
    border-color: #f2f2f2;
    background-color: #fff !important;
    height: 41px;
    border-bottom-color: #fff;
    border-radius: 2px 2px 0 0;
    pointer-events: none;
}
.layui-tab-content {
    padding: 10px 10px 10px 0;
}
.layui-tab {
    margin: 0;
}