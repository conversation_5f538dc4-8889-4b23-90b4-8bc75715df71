var layer, form;
var positionArray = {
    "survey": {name: 'zone', number: [4]},
    "line": {name: 'lane', number: [5, 2]},
    "redStop": {name: 'light_line', number: [5, 2]},
    "redStop1": {name: 'light_line_single', number: [1, 2]},
    "signal": {name: 'light', number: [6]},
    "turnLeft": {name: 'left_line', number: [1, 2]},
    "goStraight": {name: 'straight_line', number: [1, 2]},
    "turnRight": {name: 'right_line', number: [1, 2]},
    "cartTurnRight": {name: 'cart_right_line', number: [1, 2]},
    "upCalibration": {name: 'up_calibration_line', number: [1, 2]},
    "downCalibration": {name: 'down_calibration_line', number: [1, 2]},
    "noParking": {name: 'wei_ting', number: [4]},
    "people1": {name: 'people', number: [1, 4]},
    "people2": {name: 'people', number: [1, 4]},
    "people3": {name: 'people', number: [1, 4]},
    "people4": {name: 'people', number: [1, 4]},
    "people5": {name: 'people', number: [1, 4]},
    "floor1": {name: 'under_ground', number: [1, 4]},
    "floor2": {name: 'under_ground', number: [1, 4]},
    "floor3": {name: 'under_ground', number: [1, 4]},
    "floor4": {name: 'under_ground', number: [1, 4]},
    "floor5": {name: 'under_ground', number: [1, 4]},
    "face": {name: 'dui_xiang', number: [4]},
    "around": {name: 'return', number: [4]}
};
$(document).ready(function () {
    let globalInfo = checkGlobal();
    setSize(globalInfo.w, globalInfo.h);
    layui.use(['table', 'form', 'layer'], function () {
        form = layui.form, layer = layui.layer;
        initPicture('drawPreview');
    });
    $("#sendConfig").on("click", sendConfig);
});


var sendConfig = function () {
    // layer.confirm(langMessage.setting.warningMsg, {
    //     btn: [langMessage.common.confirm, langMessage.common.cancel,] //可以无限个按钮
    // }, function (index, layero) {
    //     layer.close(index);
    //     JSON2send()
    // }, function (index) {
    //     layer.close(index);
    // });
    JSON2send()
};
let warning_type = langMessage.setting.warning_type;

let error_type = langMessage.setting.error_type;

var JSON2send = function () {
    $("#sendConfig").off("click");
    setTimeout(function () {
        $("#sendConfig").on("click", sendConfig);
    }, 3000);
    let sentData = {};
    let allData = [
        {
            currName: 'surveyValue',
            prevName: 'zone',
            dataType: 'point',
            number: 4,
            use: false
        },
        {
            currName: 'noParkingValue',
            prevName: 'wei_ting',
            dataType: 'point',
            number: 4,
            use: false
        },
        {
            currName: 'aroundValue',
            prevName: 'return',
            dataType: 'point',
            number: 4,
            use: false
        },
        {
            currName: 'faceValue',
            prevName: 'dui_xiang',
            dataType: 'point',
            number: 4,
            use: false
        },
        {
            currName: 'plateMin',
            prevName: 'min_plate_width',
            dataType: 'value',
            use: false
        },
        {
            currName: 'plateMax',
            prevName: 'max_plate_width',
            dataType: 'value',
            use: false
        },
        {
            currName: 'noParkingType',
            prevName: 'wei_ting_kind',
            dataType: 'value',
            use: false
        },
        {
            currName: 'turnLeft',
            prevName: 'left_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'goStraight',
            prevName: 'straight_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'turnRight',
            prevName: 'right_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'cartTurnRight',
            prevName: 'cart_right_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'upCalibration',
            prevName: 'up_calibration_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'downCalibration',
            prevName: 'down_calibration_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'lineType',
            prevName: 'lane',
            dataType: 'point',
            use: false
        },
        {
            currName: 'redStop',
            prevName: 'light_line_single',
            prevName1: 'light_line',
            dataType: 'point',
            number: 2,
            use: true
        },
        {
            currName: 'signalType',
            prevName: 'light',
            dataType: 'point',
            number: 0,
            use: false
        },
        {
            currName: 'detects',
            prevName: '',
            dataType: 'value',
            use: false
        },
        {
            currName: 'systemValue',
            prevName: 'null',
            dataType: 'value',
            use: false
        },
        {
            currName: 'extendParams',
            prevName: 'null',
            dataType: 'value',
            use: false
        },
        {
            currName: 'codeValue',
            prevName: 'null',
            dataType: 'value',
            use: false
        },
        {
            currName: 'deviceName',
            prevName: 'board_name',
            dataType: 'value',
            use: false
        },
        {
            currName: 'eventStorage',
            prevName: 'null',
            dataType: 'value',
            use: false
        },
        {
            currName: 'isReboot',
            prevName: 'is_time_reboot',
            dataType: 'value',
            use: false
        },
        {
            currName: 'rebootTime',
            prevName: 'reboot_time',
            dataType: 'value',
            use: false
        }
    ];
    let warningMsg = [];
    let errorMsg = [];
    let surveyValue = [], lineValue = [], noParkingValue = [], turnLeftValue = [], goStraightValue = [],
        turnRightValue = [], cartTurnRightValue = [], upCalibrationValue = [], downCalibrationValue = [],
        redStopType = {},
        redStopValue = [], signalValue = [], lineType;
    let extendArray = [
        'iMatch_X_Threshold', 'iMatch_Y_Threshold', 'iGray_Threshold', 'iPlate_Blue_Grad_Threshold',
        'iPlate_Yellow_Grad_Threshold', 'iPlate_Blue_Skip_Threshold', 'iPlate_Yellow_Skip_Threshold',
        'iIs_All', 'iNight_Gray_Threshold', 'iDay_To_Night_Threshold', 'iKakou_Detect_Area', 'iDetect_Precision',
        'iDelay_Time', 'iDelay_Dis_img2', 'iSame_Plate_Time'
    ];
    let allDataName = [];
    $(allData).each(function (i) {
        allDataName[i] = this.currName;
    });
    let topW = getTopWindow();
    let getData = topW.index_param;
    try {
        for (let dataIndex in getData) {
            if (dataIndex === 'length' || dataIndex === 'key' || dataIndex === 'getItem' || dataIndex === 'setItem' || dataIndex === 'removeItem' || dataIndex === 'clear') {
                continue
            }
            let index = allDataName.indexOf(dataIndex);
            if (index >= 0) {
                let dataType = allData[index].dataType;
                if (dataType === "point") {
                    let use = allData[index].use;
                    if (use) {
                        if (allData[index].currName !== 'redStop') {
                            let sessionValue = getSe(allData[index].currName + 'Value');
                            if (sessionValue) {
                                let sessionValue1 = [];
                                for (let i = 0; i < sessionValue.length; i++) {
                                    for (let j = 0; j < 2; j++) {
                                        sessionValue1.push(sessionValue[i][j])
                                    }
                                }
                                let seValue = getSe(allData[index].currName);
                                if (allData[index].currName === 'turnLeft') {
                                    turnLeftValue = sessionValue1;
                                }
                                if (allData[index].currName === 'turnRight') {
                                    turnRightValue = sessionValue1;
                                }
                                if (allData[index].currName === 'goStraight') {
                                    goStraightValue = sessionValue1;
                                }
                                if (allData[index].currName === 'cartTurnRight') {
                                    cartTurnRightValue = sessionValue1;
                                }

                                if (allData[index].currName === 'upCalibration') {
                                    upCalibrationValue = sessionValue1;
                                }
                                if (allData[index].currName === 'downCalibration') {
                                    downCalibrationValue = sessionValue1;
                                }

                                sentData[allData[index].prevName + '_use'] = seValue.use;
                                if (seValue.use) {
                                    setPoint(allData[index].prevName, sessionValue1, allData[index].number, sentData)
                                    if (allData[index].currName === 'upCalibration') {
                                        sentData['up_calibration_queue_length'] = seValue.up_calibration_queue_length;
                                    }
                                    if (allData[index].currName === 'downCalibration') {
                                        sentData['down_calibration_queue_length'] = seValue.down_calibration_queue_length;
                                    }
                                }
                            }
                        } else {
                            sentData.light_line_mode = getSe(allData[index].currName + 'Type');
                            let sessionValue = getSe(allData[index].currName + 'Value');
                            let session = getSe(allData[index].currName);
                            redStopType.type = sentData.light_line_mode;
                            redStopType.use = session.use;
                            redStopValue = sessionValue;
                            if (sentData.light_line_mode === 0) {
                                sentData[allData[index].prevName + '_use'] = session.use;
                                setPoint(allData[index].prevName, sessionValue[0], allData[index].number, sentData)
                            } else if (sentData.light_line_mode === 1) {
                                for (let i = 0; i < 5; i++) {
                                    let name = allData[index].prevName1 + i;
                                    sentData[name + '_use'] = session.use[i];
                                    if (sentData[name + '_use']) {
                                        setPoint(name, sessionValue[i], allData[index].number, sentData)
                                    }
                                }
                            }
                        }

                    } else {
                        if (allData[index].currName !== 'lineType' && allData[index].currName !== 'signalType') {
                            let sessionValue = getSe(allData[index].currName);
                            if (allData[index].currName === 'surveyValue') {
                                surveyValue = sessionValue;
                            }
                            if (allData[index].currName === 'noParkingValue') {
                                noParkingValue = sessionValue;
                            }
                            setPoint(allData[index].prevName, sessionValue, allData[index].number, sentData);
                        } else if (allData[index].currName === 'lineType') {
                            lineValue = getSe('lineValue');
                            lineType = getSe('lineType');
                            let lines = lineType.lines;
                            sentData.lane_num = lineType.lineNum - 1;
                            // 去掉
                            // sentData.lane_direction = lineType.lineStart;
                            sentData.lane_direction = 0;
                            sentData.lane_start = lineType.lineStartNum;


                            // let floorValue = getSe('floorValue'), peopleValue = getSe('peopleValue');
                            // let floorValue1 = [];
                            // if (floorValue) {
                            //     for (let i = 0; i < floorValue.length; i++) {
                            //         let f = [];
                            //         for (let j = 0; j < 2; j++) {
                            //             for (let z = 0; z < 2; z++) {
                            //                 f.push(floorValue[i][j][z])
                            //             }
                            //         }
                            //         floorValue1.push(f)
                            //     }
                            // }


                            let floorValue1 = [], peopleValue1 = [];
                            for (let m = 1; m < 6; m++) {
                                let floorValue = getSe('floor' + m + 'Value')
                                if (floorValue && floorValue.length) {
                                    for (let i = 0; i < floorValue.length; i++) {
                                        let f = [];
                                        for (let j = 0; j < 2; j++) {
                                            for (let z = 0; z < 2; z++) {
                                                f.push(floorValue[i][j][z])
                                            }
                                        }
                                        floorValue1.push(f)
                                    }
                                } else {
                                    let f = [{x: 0, y: 0}, {x: 0, y: 0}, {x: 0, y: 0}, {x: 0, y: 0}]
                                    floorValue1.push(f)
                                }


                                let peopleValue = getSe('people' + m + 'Value')
                                if (peopleValue && peopleValue.length) {
                                    for (let i = 0; i < peopleValue.length; i++) {
                                        peopleValue1.push(peopleValue[i])
                                    }
                                } else {
                                    let f = [{x: 0, y: 0}, {x: 0, y: 0}, {x: 0, y: 0}, {x: 0, y: 0}]
                                    peopleValue1.push(f)
                                }
                            }


                            for (let i = 0; i < lineType.lineNum; i++) {
                                let name = 'lane' + i;
                                setPoint(name, lineValue[i], 2, sentData);
                                sentData[name + '_type'] = lines[i].type;
                                if (i === 0) {
                                    continue
                                }
                                sentData[name + '_direction'] = lines[i].direction;
                                sentData[name + '_kind'] = lines[i].define;
                                sentData[name + '_special'] = lines[i].special;
                                let time1start = lines[i].time1start;
                                let time1end = lines[i].time1end;
                                let time2start = lines[i].time2start;
                                let time2end = lines[i].time2end;
                                let time3start = lines[i].time3start;
                                let time3end = lines[i].time3end;
                                if (time1start !== undefined || time1end !== undefined) {
                                    sentData[name + '_gongjiao_start1'] = time1start;
                                    sentData[name + '_gongjiao_end1'] = time1end
                                }
                                if (time2start !== undefined || time2end !== undefined) {
                                    sentData[name + '_gongjiao_start2'] = time2start;
                                    sentData[name + '_gongjiao_end2'] = time2end;
                                }
                                if (time2start !== undefined || time2end !== undefined) {
                                    sentData[name + '_gongjiao_start3'] = time3start;
                                    sentData[name + '_gongjiao_end3'] = time3end;
                                }
                                sentData[name + '_leftwait_zone'] = lines[i].leftWait;


                                let floorName = 'under_ground' + (i);
                                // if (floorValue && floorValue[i - 1]) {
                                //     setPoint(floorName, floorValue1[i - 1], 4, sentData);
                                // }
                                setPoint(floorName, floorValue1[i - 1], 4, sentData);

                                let peopleName = 'people' + (i);
                                // if (peopleValue && peopleValue[i - 1]) {
                                //     setPoint(peopleName, peopleValue[i - 1], 4, sentData);
                                // }
                                setPoint(peopleName, peopleValue1[i - 1], 4, sentData);
                            }
                        } else if (allData[index].currName === 'signalType') {
                            let signalType = getSe('signalType');
                            sentData.light_num = signalType.signalNum;
                            setSendValue(sentData, signalType, 'light_enable', 'signal');
                            setSendValue(sentData, signalType, 'out_light_enable', 'extendSignal');
                            setSendValue(sentData, signalType, 'ocr_thresh', 'RS485');
                            setSendValue(sentData, signalType, 'out_light_people', 'signalPeopleNumber');
                            setSendValue(sentData, signalType, 'out_light_left', 'signalLeftNumber');
                            setSendValue(sentData, signalType, 'out_light_straight', 'signalStraightNumber');
                            setSendValue(sentData, signalType, 'out_light_right', 'signalRightNumber');
                            setSendValue(sentData, signalType, 'red_light_delay', 'signalTime');
                            setSendValue(sentData, signalType, 'yellow_mode', 'signalYellow');
                            setSendValue(sentData, signalType, 'red_light_enhance', 'signalStronger');
                            setSendValue(sentData, signalType, 'signal_ip', 'signalIP');
                            if (signalType.signalYellow) {
                                sentData.yellow_mode_start = signalType.yellowStartTime;
                                sentData.yellow_mode_end = signalType.yellowEndTime;
                            }
                            if (signalType.signalStronger) {
                                sentData.red_light_enhance_begin_time = signalType.signalStrongStartTime;
                                sentData.red_light_enhance_end_time = signalType.signalStrongEndTime;
                            }
                            signalValue = getSe('signalValue');
                            for (let i = 0; i < signalType.signalNum; i++) {
                                let name = 'light' + i;
                                if (signalValue && signalValue[i]) {
                                    setPoint(name, signalValue[i], 0, sentData);
                                }
                                sentData[name + '_direction'] = signalType.signals[i].signalCategory;
                                sentData[name + '_kind'] = signalType.signals[i].signalDirection;
                                sentData[name + '_shape'] = signalType.signals[i].signalShape;
                                sentData[name + '_type'] = signalType.signals[i].signalType;
                            }
                        }

                    }
                } else if (dataType === "value") {
                    if (allData[index].currName === 'plateMin' || allData[index].currName === 'plateMax' || allData[index].currName === 'noParkingType' || allData[index].currName === 'deviceName' || allData[index].currName === 'isReboot' || allData[index].currName === 'rebootTime') {
                        sentData[allData[index].prevName] = getSe(allData[index].currName);
                    } else if (allData[index].currName === 'detects') {
                        let detects = getSe('detects');
                        // detectNoLicense 无牌车灵敏度弃用（wpc_lmd）
                        let detectArray = [
                            'detect-signal', 'extend-signal', 'pressing-line', 'kakou-img-num', 'return-enable', 'detect-big-car', 'big-car-type', 'big-car-select',
                            'dui-xiang-enable', 'illegal-change', 'direction-mask', 'detect-front-flash', 'detect-truck', 'truck-type', 'truck-car-select', 'detect-prohibit',
                            'prohibit-car-type', 'prohibit-plate-blue', 'prohibit-plate-yellow', 'detect-jam', 'black-list', 'overLineParking', 'illegalParking1', 'illegalParking2', 'detectNoLicense',
                            'other-provinces', 'time1start', 'time1end', 'time2start', 'time2end', 'time3start', 'time3end', 'black-enable', 'startFlash', 'endFlash', 'front-flash-select',
                            'kakou-feature', 'weizhang-feature', 'weizhang-video', 'illegal-change-light', 'illegal-jump', 'fjdc-type', 'jdc-feature', 'fjdc-feature', 'wshcjl-province', 'wshcjl-city',
                            'whxx', 'kakou-img-count', 'motuocheCjlType', 'motuocheWpcjlProvince', 'moto-local-city'
                        ];
                        for (let i = 0; i < detectArray.length; i++) {
                            if (detects[detectArray[i]] === undefined) {
                                continue
                            }
                            switch (i) {
                                case 0:
                                    sentData.detect_signal = detects[detectArray[0]];
                                    break;
                                case 1:
                                    sentData.extern_signal = detects[detectArray[1]];
                                    break;
                                case 2:
                                    sentData.y_line_signal = detects[detectArray[2]];
                                    break;
                                case 3:
                                    sentData.kakou_img_num = detects[detectArray[3]];
                                    break;
                                case 4:
                                    sentData.return_enable = detects[detectArray[4]];
                                    break;
                                case 5:
                                    sentData.dczyxcd = detects[detectArray[5]];
                                    break;
                                case 6:

                                    let value = detects[detectArray[6]].toString(2);
                                    let l = value.length;
                                    for (let i = 0; i < 3 - l; i++) {
                                        value = '0' + value
                                    }
                                    value = value.split('');
                                    if (value[0] == 1) {
                                        sentData.dczyxcd_zxhc = 1;
                                    } else {
                                        sentData.dczyxcd_zxhc = 0;
                                    }
                                    if (value[1] == 1) {
                                        sentData.dczyxcd_dxkc = 1;
                                    } else {
                                        sentData.dczyxcd_dxkc = 0;
                                    }
                                    if (value[2] == 1) {
                                        sentData.dczyxcd_hp = 1;
                                    } else {
                                        sentData.dczyxcd_hp = 0;
                                    }
                                    break;
                                case 7:
                                    let v = detects[detectArray[7]];
                                    if (v) {
                                        setTypeArray(v, 'dczyxcd_lane', sentData);
                                    }

                                    break;
                                case 8:
                                    sentData.dui_xiang_enable = detects[detectArray[8]];
                                    break;
                                case 9:
                                    sentData.line_signal = detects[detectArray[9]];
                                    break;
                                case 10:
                                    sentData.direction_mask = detects[detectArray[10]];
                                    break;
                                case 11:
                                    sentData.front_flash = detects[detectArray[11]];
                                    break;
                                case 12:
                                    sentData.hczykcd = detects[detectArray[12]];
                                    break;
                                case 13:
                                    let value2 = detects[detectArray[13]].toString(2);
                                    let l1 = value2.length;
                                    for (let i = 0; i < 3 - l1; i++) {
                                        value2 = '0' + value2
                                    }
                                    value2 = value2.split('');
                                    if (value2[0] == 1) {
                                        sentData.hczykcd_zxhc = 1;
                                    } else {
                                        sentData.hczykcd_zxhc = 0;
                                    }
                                    if (value2[1] == 1) {
                                        sentData.hczykcd_zxhc2 = 1;
                                    } else {
                                        sentData.hczykcd_zxhc2 = 0;
                                    }
                                    if (value2[2] == 1) {
                                        sentData.hczykcd_qxhc = 1;
                                    } else {
                                        sentData.hczykcd_qxhc = 0;
                                    }
                                    break;
                                case 14:
                                    let v1 = detects[detectArray[14]];
                                    if (v1) {
                                        setTypeArray(v1, 'hczykcd_lane', sentData);
                                    }
                                    break;
                                case 15:
                                    sentData.cxcjl = detects[detectArray[15]];
                                    break;
                                case 16:
                                    let value3 = detects[detectArray[16]].toString(2);
                                    let l2 = value3.length;
                                    for (let i = 0; i < 7 - l2; i++) {
                                        value3 = '0' + value3
                                    }
                                    value3 = value3.split('');
                                    if (value3[6] == 1) {
                                        sentData.cxcjl_zxhc = 1
                                    } else {
                                        sentData.cxcjl_zxhc = 0;
                                    }
                                    if (value3[5] == 1) {
                                        sentData.cxcjl_zxhc2 = 1
                                    } else {
                                        sentData.cxcjl_zxhc2 = 0;
                                    }
                                    if (value3[4] == 1) {
                                        sentData.cxcjl_qxhc = 1
                                    } else {
                                        sentData.cxcjl_qxhc = 0;
                                    }
                                    if (value3[3] == 1) {
                                        sentData.cxcjl_dxkc = 1
                                    } else {
                                        sentData.cxcjl_dxkc = 0;
                                    }
                                    if (value3[2] == 1) {
                                        sentData.cxcjl_zxkc = 1
                                    } else {
                                        sentData.cxcjl_zxkc = 0;
                                    }
                                    if (value3[1] == 1) {
                                        sentData.cxcjl_qxkc = 1
                                    } else {
                                        sentData.cxcjl_qxkc = 0;
                                    }
                                    if (value3[0] == 1) {
                                        sentData.cxcjl_xxkc = 1
                                    } else {
                                        sentData.cxcjl_xxkc = 0;
                                    }
                                    break;
                                case 17:
                                    let value4 = detects[detectArray[17]].toString(2);
                                    let l3 = value4.length;
                                    for (let i = 0; i < 7 - l3; i++) {
                                        value4 = '0' + value4
                                    }
                                    value4 = value4.split('');
                                    if (value4[6] == 1) {
                                        sentData.cxcjl_zxhc_blue = 1
                                    } else {
                                        sentData.cxcjl_zxhc_blue = 0;
                                    }
                                    if (value4[5] == 1) {
                                        sentData.cxcjl_zxhc2_blue = 1
                                    } else {
                                        sentData.cxcjl_zxhc2_blue = 0;
                                    }
                                    if (value4[4] == 1) {
                                        sentData.cxcjl_qxhc_blue = 1
                                    } else {
                                        sentData.cxcjl_qxhc_blue = 0;
                                    }
                                    if (value4[3] == 1) {
                                        sentData.cxcjl_dxkc_blue = 1
                                    } else {
                                        sentData.cxcjl_dxkc_blue = 0;
                                    }
                                    if (value4[2] == 1) {
                                        sentData.cxcjl_zxkc_blue = 1
                                    } else {
                                        sentData.cxcjl_zxkc_blue = 0;
                                    }
                                    if (value4[1] == 1) {
                                        sentData.cxcjl_qxkc_blue = 1
                                    } else {
                                        sentData.cxcjl_qxkc_blue = 0;
                                    }
                                    if (value4[0] == 1) {
                                        sentData.cxcjl_xxkc_blue = 1
                                    } else {
                                        sentData.cxcjl_xxkc_blue = 0;
                                    }
                                    break;
                                case 18:
                                    let value5 = detects[detectArray[18]].toString(2);
                                    let l4 = value5.length;
                                    for (let i = 0; i < 7 - l4; i++) {
                                        value5 = '0' + value5
                                    }
                                    value5 = value5.split('');
                                    if (value5[6] == 1) {
                                        sentData.cxcjl_zxhc_yellow = 1
                                    } else {
                                        sentData.cxcjl_zxhc_yellow = 0;
                                    }
                                    if (value5[5] == 1) {
                                        sentData.cxcjl_zxhc2_yellow = 1
                                    } else {
                                        sentData.cxcjl_zxhc2_yellow = 0;
                                    }
                                    if (value5[4] == 1) {
                                        sentData.cxcjl_qxhc_yellow = 1
                                    } else {
                                        sentData.cxcjl_qxhc_yellow = 0;
                                    }
                                    if (value5[3] == 1) {
                                        sentData.cxcjl_dxkc_yellow = 1
                                    } else {
                                        sentData.cxcjl_dxkc_yellow = 0;
                                    }
                                    if (value5[2] == 1) {
                                        sentData.cxcjl_zxkc_yellow = 1
                                    } else {
                                        sentData.cxcjl_zxkc_yellow = 0;
                                    }
                                    if (value5[1] == 1) {
                                        sentData.cxcjl_qxkc_yellow = 1
                                    } else {
                                        sentData.cxcjl_qxkc_yellow = 0;
                                    }
                                    if (value5[0] == 1) {
                                        sentData.cxcjl_xxkc_yellow = 1
                                    } else {
                                        sentData.cxcjl_xxkc_yellow = 0;
                                    }
                                    break;
                                case 19:
                                    sentData.yongdu = detects[detectArray[19]];
                                    break;
                                case 20:
                                    sentData.hmd_wflx = detects[detectArray[20]];
                                    break;
                                case 21:
                                    sentData.wei_ting_time3 = detects[detectArray[21]] * 1000;
                                    break;
                                case 22:
                                    sentData.wei_ting_time1 = detects[detectArray[22]] * 1000;
                                    break;
                                case 23:
                                    sentData.wei_ting_time2 = detects[detectArray[23]] * 1000;
                                    break;
                                case 24:
                                    sentData.wpc_lmd = detects[detectArray[24]];
                                    break;
                                case 25:
                                    let value6 = detects[detectArray[25]].toString(2);
                                    let l5 = value6.length;
                                    for (let i = 0; i < 7 - l5; i++) {
                                        value6 = '0' + value6
                                    }
                                    value6 = value6.split('');
                                    if (value6[6] == 1) {
                                        sentData.wshcjl_week0 = 1
                                    } else {
                                        sentData.wshcjl_week0 = 0;
                                    }
                                    if (value6[5] == 1) {
                                        sentData.wshcjl_week6 = 1
                                    } else {
                                        sentData.wshcjl_week6 = 0;
                                    }
                                    if (value6[4] == 1) {
                                        sentData.wshcjl_week5 = 1
                                    } else {
                                        sentData.wshcjl_week5 = 0;
                                    }
                                    if (value6[3] == 1) {
                                        sentData.wshcjl_week4 = 1
                                    } else {
                                        sentData.wshcjl_week4 = 0;
                                    }
                                    if (value6[2] == 1) {
                                        sentData.wshcjl_week3 = 1
                                    } else {
                                        sentData.wshcjl_week3 = 0;
                                    }
                                    if (value6[1] == 1) {
                                        sentData.wshcjl_week2 = 1
                                    } else {
                                        sentData.wshcjl_week2 = 0;
                                    }
                                    if (value6[0] == 1) {
                                        sentData.wshcjl_week1 = 1
                                    } else {
                                        sentData.wshcjl_week1 = 0;
                                    }
                                    break;
                                case 26:
                                    let time1start = detects[detectArray[26]];
                                    if (time1start !== undefined) {
                                        sentData.wshcjl_start_time1 = time1start
                                    }
                                    break;
                                case 27:
                                    let time1end = detects[detectArray[27]];
                                    if (time1end !== undefined) {
                                        sentData.wshcjl_end_time1 = time1end
                                    }
                                    break;
                                case 28:
                                    let time2start = detects[detectArray[28]];
                                    if (time2start !== undefined) {
                                        sentData.wshcjl_start_time2 = time2start
                                    }
                                    break;
                                case 29:
                                    let time2end = detects[detectArray[29]];
                                    if (time2end !== undefined) {
                                        sentData.wshcjl_end_time2 = time2end
                                    }
                                    break;
                                case 30:
                                    let time3start = detects[detectArray[30]];
                                    if (time3start !== undefined) {
                                        sentData.wshcjl_start_time3 = time3start
                                    }
                                    break;
                                case 31:
                                    let time3end = detects[detectArray[31]];
                                    if (time3end !== undefined) {
                                        sentData.wshcjl_end_time3 = time3end
                                    }
                                    break;
                                case 32:
                                    let black_enable = detects[detectArray[32]];
                                    if (black_enable) {
                                        sentData.hmd_enable = black_enable
                                    }
                                    break;
                                case 33:
                                    let start_flash = detects[detectArray[33]];
                                    sentData.start_flash = start_flash ? start_flash : 0;
                                    break;
                                case 34:
                                    let end_flash = detects[detectArray[34]];
                                    sentData.end_flash = end_flash ? end_flash : 0;
                                    break;
                                case 35:
                                    let v2 = detects[detectArray[35]];
                                    if (v2) {
                                        setTypeArray(v2, 'flash_light_lane', sentData);
                                    }
                                    break;
                                case 36:
                                    sentData.kakou_feature = detects[detectArray[36]];
                                    break;
                                case 37:
                                    sentData.weizhang_feature = detects[detectArray[37]];
                                    break;
                                case 38:
                                    sentData.weizhang_video = detects[detectArray[38]];
                                    break;
                                case 39:
                                    sentData.b_line_signal = detects[detectArray[39]];
                                    break;
                                case 40:
                                    sentData.j_line_signal = detects[detectArray[40]];
                                    break;
                                case 41:
                                    sentData.fjdc_type = detects[detectArray[41]];
                                    break;
                                case 42:
                                    sentData.jdc_feature = detects[detectArray[42]];
                                    break;
                                case 43:
                                    sentData.fjdc_feature = detects[detectArray[43]];
                                    break;
                                case 44:
                                    sentData.wshcjl_province = detects[detectArray[44]];
                                    break;
                                case 45:
                                    sentData.wshcjl_city = detects[detectArray[45]];
                                    break;
                                case 46:
                                    let whxx = detects[detectArray[46]]
                                    sentData.whxx_num = whxx.num
                                    for (let j = 0; j < whxx.num; j++) {
                                        let whxx_date_type = whxx.data[j].whxx_date_type
                                        let whxx_date = 0, local_plate_endnum = 0, //nonlocal_plate_endnum = 0,
                                            local_city = 0
                                        for (let k = 0; k < whxx.data[j].whxx_date.length; k++) {
                                            whxx_date = whxx_date + whxx.data[j].whxx_date[k]
                                        }
                                        for (let k = 0; k < whxx.data[j].local_plate_endnum.length; k++) {
                                            local_plate_endnum = local_plate_endnum + whxx.data[j].local_plate_endnum[k]
                                        }
                                        //for (let k = 0; k < whxx.data[j].nonlocal_plate_endnum.length; k++) {
                                        //    nonlocal_plate_endnum = nonlocal_plate_endnum + whxx.data[j].nonlocal_plate_endnum[k]
                                        //}
                                        for (let k = 0; k < whxx.data[j].local_city.length; k++) {
                                            local_city = local_city + whxx.data[j].local_city[k]
                                        }
                                        sentData['whxx' + j + '_date_type'] = whxx_date_type
                                        sentData['whxx' + j + '_date'] = whxx_date
                                        sentData['whxx' + j + '_start_time'] = whxx.data[j].whxx_start_time
                                        sentData['whxx' + j + '_end_time'] = whxx.data[j].whxx_end_time
                                        sentData['whxx' + j + '_local_province'] = whxx.data[j].local_province
                                        sentData['whxx' + j + '_local_city'] = local_city
                                        sentData['whxx' + j + '_local_plate_endnum'] = local_plate_endnum
                                        //sentData['whxx' + j + '_nonlocal_plate_endnum'] = nonlocal_plate_endnum
                                    }
                                    break;
                                case 47:
                                    sentData.kakou_img_count = detects[detectArray[47]]
                                    break;
                                case 48:
                                    sentData.motuoche_cjl_type = detects[detectArray[48]]
                                    break;
                                case 49:
                                    sentData.motuoche_wpcjl_province = detects[detectArray[49]]
                                    break;
                                case 50:
                                    sentData.moto_local_city = detects[detectArray[50]];
                                    break;
                            }
                        }
                    } else if (allData[index].currName === 'systemValue') {
                        //system
                        let systemValue = getSe('systemValue');
                        sentData.camera_height = systemValue.cameraHeight;
                        sentData.camera_img_down = systemValue.cameraImgDown;
                        sentData.camera_img_top = systemValue.cameraImgTop;
                        sentData.coef = systemValue.coef * 100;
                        sentData.jugment_signal = systemValue['detect-license'];
                        sentData.velo_enable = systemValue['detect-speed'];
                        sentData.head_hz = systemValue.headHZ;
                        sentData.head_letter = systemValue.headLetter;
                        sentData.locate_thresh = systemValue.locateThresh;
                        sentData.max_speed = systemValue.maxSpeed;
                        sentData.min_speed = systemValue.minSpeed;
                        // sentData.ocr_thresh = systemValue.ocrThresh;
                        sentData.wzdd = systemValue.roadCode;
                        sentData.road_direct = systemValue.roadDirect;
                        sentData.road_name = systemValue.roadName;
                        sentData.road_code = systemValue.selfRoadCode;
                        sentData.speed_mode = systemValue.speedType;
                        sentData.radar_type = systemValue.radarType;
                        sentData.ai_mode = systemValue.AIType;
                        sentData.jpg_quality = systemValue.jpgQuality;
                        for (let j = 1; j < 6; j++) {
                            let name1 = "lane" + j + "MaxSpeed";
                            let name2 = "lane" + j + "_max_speed";
                            let name3 = "lane" + j + "MinSpeed";
                            let name4 = "lane" + j + "_min_speed";
                            if (systemValue[name1] !== undefined) {
                                sentData[name2] = systemValue[name1];
                            }
                            if (systemValue[name3] !== undefined) {
                                sentData[name4] = systemValue[name3];
                            }
                            let name11 = "lane" + j + "MaxLimitSpeed";
                            let name21 = "lane" + j + "_max_limit_speed";
                            let name31 = "lane" + j + "MinLimitSpeed";
                            let name41 = "lane" + j + "_min_limit_speed";
                            if (systemValue[name11] !== undefined) {
                                sentData[name21] = systemValue[name11];
                            }
                            if (systemValue[name31] !== undefined) {
                                sentData[name41] = systemValue[name31];
                            }

                        }
                    } else if (allData[index].currName === 'extendParams') {
                        let extendParams = getSe('extendParams');
                        for (let i = 0; i < extendArray.length; i++) {
                            sentData[extendArray[i]] = extendParams[extendArray[i]]
                        }
                    } else if (allData[index].currName === 'codeValue') {
                        let codeValue = getSe('codeValue');
                        for (let i in codeValue) {
                            sentData[i] = codeValue[i]
                        }
                    } else if (allData[index].currName === 'eventStorage') {
                        let eventStorage = getSe('eventStorage');
                        for (let i in eventStorage) {
                            sentData[i] = eventStorage[i]
                        }
                    }
                    // else if(allData[index].currName==='deviceName'){
                    //     let deviceName = getSe('deviceName');
                    //     sentData['board_name'] = deviceName;
                    // }
                }
            }
        }
    } catch (e) {
        console.log(e)
    }
    sentData.SafeBelt_score = Number(getSe('SafeBelt_score'))
    sentData.CallPhone_score = Number(getSe('CallPhone_score'))
    //开始校验
    if (!/^(\+)?\d+(\d+)?$/.test(sentData.SafeBelt_score) || sentData.SafeBelt_score < 1 || sentData.SafeBelt_score > 100) {
        errorMsg.push(error_type[27])
    }
    if (!/^(\+)?\d+(\d+)?$/.test(sentData.CallPhone_score) || sentData.CallPhone_score < 1 || sentData.CallPhone_score > 100) {
        errorMsg.push(error_type[28])
    }
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    //相机名称为空
    // if (sentData['board_name'] === "") {
    //     sentData['board_name'] = "Unknown"
    // }
    if (!(lineValue && lineValue.length)) {
        errorMsg.push(error_type[5])
    }
    //验证车牌大小
    if (sentData['min_plate_width'] < 54 || sentData['max_plate_width'] > 300 || sentData['max_plate_width'] < sentData['min_plate_width']) {
        errorMsg.push(error_type[3])
    }
    //验证相机距检测区上沿距离要大于下沿
    if (sentData['camera_img_top'] < sentData['camera_img_down']) {
        errorMsg.push(error_type[20])
    }
    //验证最小速度小于最大速度
    if (sentData['max_speed'] < sentData['min_speed']) {
        errorMsg.push(error_type[24])
    }
    //验证左直右分界线
    if (sentData['straight_line_use']) {
        if (checkStraight(sentData['left_line_use'], sentData['right_line_use'], goStraightValue, turnLeftValue, turnRightValue)) {
            errorMsg.push(error_type[26])
        }
    }
    if (sentData['left_line_use'] && sentData['right_line_use']) {
        checkLeftRight(warningMsg, errorMsg, turnLeftValue, turnRightValue)
    }
    if (!(lineType && (lineType.lineNum && lineType.lineNum > 0))) {
        errorMsg.push(error_type[4])
    }
    if (noParkingValue && noParkingValue.length) {
        let exist = checkExist(noParkingValue);
        if (exist) {
            let ret = check_rect(noParkingValue);
            if (ret) {
                errorMsg.push(error_type[13])
            }
        }
    }

    console.log('红灯停止线', redStopValue, redStopType, lineValue)
    if (surveyValue && surveyValue.length) {
        checkSurvey(warningMsg, errorMsg, surveyValue, pictureW, pictureH);
        if (lineValue && lineValue.length) {
            checkLine(warningMsg, errorMsg, sortPosition(surveyValue, 'x'), sentData['lane_num'], pictureW, lineValue);
            if (redStopValue && redStopValue.length) {
                check_line_redStop(warningMsg, errorMsg, sortPosition(surveyValue, 'y'), redStopValue, redStopType, lineValue)
            }
        } else {
            errorMsg.push(error_type[5])
        }
    } else {
        errorMsg.push(error_type[0])
    }
    if (sentData['light_enable']) {
        if (sentData['light_num'] > 0) {
            checkSignal(warningMsg, errorMsg, signalValue, pictureW, pictureH);
        } else {
            errorMsg.push(error_type[8]);
        }
    }
    // 补全清空

    let clearArray = getSe("clearArray");
    if (!clearArray) {
        clearArray = {}
    }
    if (clearArray) {
        let keys = Object.keys(clearArray);
        for (let i = 0; i < keys.length; i++) {
            let key = keys[i];
            let tempKey = key;
            if (key === 'redStop') {
                let redFlag = sentData.light_line_mode;
                if (redFlag === 0) {
                    tempKey = 'redStop1'
                }
            }
            let a = getSe(key + 'Value')
            setPositionDefault(sentData, positionArray[tempKey], (getSe(key + 'Value') ? getSe(key + 'Value').length : 0))
        }
    }
    console.log('配置内容', sentData)
    if (errorMsg.length === 0 && warningMsg.length === 0) {
        initWebService(webserviceCMD.CMD_SET_CONFIG, sentData, updateCallback);
    } else {
        let msg = '';
        for (let i in errorMsg) {
            msg += errorMsg[i] + '<br>';
        }
        for (let i in warningMsg) {
            msg += warningMsg[i] + '<br>';
        }
        layer.open({
            title: errorMsg.length === 0 ? langMessage.common.warning : langMessage.common.error
            ,
            shade: 0.8
            ,
            btn: langMessage.common.confirm
            ,
            yes: function (index, layero) {
                errorMsg.length === 0 ? initWebService(webserviceCMD.CMD_SET_CONFIG, sentData, updateCallback) : layer.close(index);
                // errorMsg.length === 0 ? console.log('下发') : layer.close(index);
                layer.close(index);
            }
            ,
            icon: errorMsg.length === 0 ? 3 : 2
            ,
            content: msg + (errorMsg.length === 0 ? langMessage.setting.warningMsg : langMessage.setting.errorMsg.ERROR)
        });
    }
};
var updateCallback = function () {
    layer.msg(langMessage.setting.subMsg, {icon: 1});
    initWebService(webserviceCMD.CMD_GET_CONFIG, null, parseValue2JSON, show);
};
var checkSurvey = function (wMsg, eMsg, data, pWidth, pHeight) {
    let MAX_AREA = (5 * 1024 * 1024 / 2);
    let Xs = [], Ys = [];
    for (let i in data) {
        Xs.push(data[i].x);
        Ys.push(data[i].y);
    }
    Xs = Xs.sort(sortNumber);
    Ys = Ys.sort(sortNumber);
    let height = parseInt(parseFloat(Math.abs(Ys[0] - Ys[Ys.length - 1]) / 100) * pHeight);
    let width = parseInt(parseFloat(Math.abs(Xs[0] - Xs[Xs.length - 1]) / 100) * pWidth);
    let area = height * width;
    if (area > MAX_AREA) {
        wMsg.push(warning_type[2])
    }
    let checkR = check_rect(data);
    if (checkR) {
        eMsg.push(error_type[0])
    }
};

let checkLine = function (wMsg, eMsg, data, num, pWidth, lineValue) {
    let width = parseInt(parseFloat(Math.abs(data[0].x - data[data.length - 1].x) / 100) * pWidth / (lineValue.length - 1));
    if (width > 1000) {
        wMsg.push(warning_type[0])
    }
    //第一根线上下方向
    let direction = lineValue[0][0].y - lineValue[0][1].y > 0 ? 0 : 1;
    //第一根线左右方向
    let order;
    // 通过首尾Y坐标计算后一条车道线的首尾点X坐标是否同时大于或小于当前车道线的首尾X坐标
    if (calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][0].y) < lineValue[1][0].x && calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][1].y) < lineValue[1][1].x) {
        order = 0;
    }
    //车道线只能从左往右画故不判断从右往左的情况
    // else if (calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][0].y) > lineValue[1][0].x && calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][1].y) > lineValue[1][1].x) {
    //     order = 1
    // }
    else {
        eMsg.push(error_type[7])
    }
    for (let i in lineValue) {
        //车道线未配置
        if (lineValue[i][0].x === 0 && lineValue[i][0].y === 0 && lineValue[i][1].x === 0 && lineValue[i][1].y === 0) {
            eMsg.push(error_type[5])
        }
        //车道线上下方向
        let d = lineValue[i][0].y - lineValue[i][1].y > 0 ? 0 : 1;
        if (d !== direction) {
            eMsg.push(error_type[6]);
            break;
        }
    }
    for (let i in lineValue) {
        //车道线有相交或次序不一致
        i = parseInt(i);
        if (i !== lineValue.length - 1) {
            let o;
            if (calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][0].y) < lineValue[i + 1][0].x && calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][1].y) < lineValue[i + 1][1].x) {
                o = 0;
            }
            //车道线只能从左往右画故不判断从右往左的情况
            // else if (calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][0].y) > lineValue[i + 1][0].x && calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][1].y) > lineValue[i + 1][1].x) {
            //     o = 1;
            // }
            else {
                o = -1;
            }
            if (order !== o) {
                eMsg.push(error_type[7]);
                break;
            }
        }
    }
};

let calXByY = function (x0, y0, x1, y1, y) {
    let x;
    let temp;
    if (x0 === x1) {
        return x0;
    } else {
        temp = (y0 - y1) / (x0 - x1);
        x = (y - (y0 - ((temp) * x0))) / temp;
        return x;
    }
};


//检测point的四点依次连接是否能构成凸四边形
let check_rect = function (point) {
    let ret = 0, a, b, c, f1, f2, p;
    p = point;
    if (p[0].x === p[2].x && p[0].y === p[2].y)    //两点重合
    {
        ret = -1;
    }
    if (p[0].x === p[2].x) {
        a = -1;
        b = 0;
        c = p[0].x;
    } else if (p[0].y === p[2].y) {
        a = 0;
        b = -1;
        c = p[0].x;
    } else {
        a = p[0].y - p[2].y;
        b = p[2].x - p[0].x;
        c = p[0].x * p[2].y - p[0].y * p[2].x;
    }
    f1 = a * p[1].x + b * p[1].y + c;
    f2 = a * p[3].x + b * p[3].y + c;
    if (f1 === 0 || f2 === 0)  //  三点共线或对角线与y轴重合
    {
        ret = -2;
    } else if ((f1 < 0 && f2 < 0) || (f1 > 0 && f2 > 0))   //两点在对角线同侧，不能构成凸四边形
    {
        ret = -3;
    }
    if (p[1].x === p[3].x && p[1].y === p[3].y)    //两点重合
    {
        ret = -1;
    }
    if (p[1].x === p[3].x) {
        a = -1;
        b = 0;
        c = p[1].x;
    } else if (p[1].y === p[3].y) {
        a = 0;
        b = -1;
        c = p[1].y;
    } else {
        a = p[1].y - p[3].y;
        b = p[3].x - p[1].x;
        c = p[1].x * p[3].y - p[1].y * p[3].x;
    }

    f1 = a * p[0].x + b * p[0].y + c;
    f2 = a * p[2].x + b * p[2].y + c;
    if (f1 === 0 || f2 === 0)  //  三点共线或对角线与y轴重合
    {
        ret = -2;
    } else if ((f1 < 0 && f2 < 0) || (f1 > 0 && f2 > 0))   //两点在对角线同侧，不能构成凸四边形
    {
        ret = -3;
    }

    return ret;
};
var check_line_redStop = function (wMsg, eMsg, sortSurveyValue, redStopValue, redStopType, lineValue) {
    let MAX_DISTANCE = 5;
    let leftLast = -1, rightLast = -1, leftCurr, rightCurr;
    let useRedStop = [];
    if (redStopType.type === 1) {
        for (let i = 0; i < redStopType.use.length; i++) {
            if (redStopType.use[i] === 1) {
                useRedStop.push(redStopValue[i])
            }
        }
    } else {
        useRedStop.push(redStopValue[0])
    }
    let redStop = [];
    array2obj(useRedStop, redStop);
    let sortRedStopValue = null;
    let surveyTop = sortSurveyValue[0].y;
    let redStopTop = -1;


    if (redStopType.type === 0) {
        if (redStopType.use === 1) {
            //直线红灯停止线是否配置
            if (redStopValue[0][0].x === 0 && redStopValue[0][0].y === 0 && redStopValue[0][1].x === 0 && redStopValue[0][1].y === 0) {
                wMsg.push(warning_type[1])
            }
            sortRedStopValue = sortPosition(redStop, 'y');
            redStopTop = sortRedStopValue[0].y;
        } else {
            wMsg.push(warning_type[1])
        }
    } else if (redStopType.type === 1) {
        let tempRedStop = [];
        for (let i = 1; i < lineValue.length - 1; i++) {
            if (redStopType.use[i] === 1) {
                //Z型红灯停止线是否配置
                if (redStopValue[i][0].x === 0 && redStopValue[i][0].y === 0 && redStopValue[i][1].x === 0 && redStopValue[i][1].y === 0) {
                    wMsg.push(warning_type[1]);
                    break
                } else {
                    //多停止线是否有连续或重叠
                    leftCurr = Math.min(redStopValue[i][0].x, redStopValue[i][1].x);
                    rightCurr = Math.max(redStopValue[i][0].x, redStopValue[i][1].x);
                    if ((leftCurr > rightLast) && (rightLast >= 0)) {
                        eMsg.push(error_type[11]);
                        break;
                    } else if ((rightLast - leftCurr > MAX_DISTANCE) && (rightLast >= 0)) {
                        eMsg.push(error_type[11]);
                        break;
                    }
                    tempRedStop.push(redStopValue[i]);
                    leftLast = leftCurr;
                    rightLast = rightCurr;
                }
            } else {
                wMsg.push(warning_type[1]);
                break
            }
            sortRedStopValue = sortPosition(tempRedStop, 'y');
            redStopTop = sortRedStopValue[0].y;
        }
    }
    //停止线距离上沿过近或高于检测区上沿
    if (redStopTop >= 0) {
        let distance = redStopTop - surveyTop;
        if (distance < MAX_DISTANCE) {
            if (distance < 0) {
                eMsg.push(error_type[10])
            } else {
                wMsg.push(warning_type[3])
            }
        }
    }

};
var checkSignal = function (wMsg, eMsg, signalValue, pictureW, pictureH) {
    for (let i = 0; i < signalValue.length; i++) {
        let left = Math.min(signalValue[i][0].x, signalValue[i][1].x),
            right = Math.max(signalValue[i][0].x, signalValue[i][1].x),
            top = Math.min(signalValue[i][0].y, signalValue[i][1].y),
            bottom = Math.max(signalValue[i][0].y, signalValue[i][1].y);
        if (left < 0 || right > pictureW || top < 0 || bottom > pictureH) {
            eMsg.push(error_type[9])
        }
        for (let j = i + 1; j < signalValue.length; j++) {
            if ((signalValue[j][0].x < right && signalValue[j][0].x > left && signalValue[j][0].y < bottom && signalValue[j][0].y > top) ||
                (signalValue[j][1].x < right && signalValue[j][1].x > left && signalValue[j][1].y < bottom && signalValue[j][1].y > top)) {
                eMsg.push(error_type[9])
            }
        }
    }
};
let setSendValue = function (set, get, setKey, getKey) {
    if (get[getKey] !== undefined) {
        set[setKey] = get[getKey]
    }
};
let checkStraight = function (leftUse, rightUse, goStraightValue, turnLeftValue, turnRightValue) {
    let ret = 0;
    if (leftUse) {
        if (Math.max(goStraightValue[0].y, goStraightValue[1].y) > Math.max(turnLeftValue[0].y, turnLeftValue[1].y)) {
            ret = 1;
        }
    }
    if (rightUse) {
        if (Math.max(goStraightValue[0].y, goStraightValue[1].y) > Math.max(turnRightValue[0].y, turnRightValue[1].y)) {
            ret = 1;
        }
    }
    return ret;
};
var checkLeftRight = function (wMsg, eMsg, leftValue, rightValue) {
    if ((leftValue[0].y === leftValue[1].y) || (rightValue[0].y === rightValue[1].y)) {
        eMsg.push(error_type[25])
    } else if ((Math.min(leftValue[0].x, leftValue[1].x) >= Math.min(rightValue[0].x, rightValue[1].x)) || (Math.max(leftValue[0].x, leftValue[1].x) >= Math.max(rightValue[0].x, rightValue[1].x))) {
        eMsg.push(error_type[25])
    }
};
var show = function () {
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let dataArray = getSe("projectConfig").settingShow[drawContainer].show;
    let detects = getSe('detects');
    if (detects && detects['detect-signal']) {
        if (detects['detect-signal'] & '0x8000') {
            dataArray.push('people1')
            dataArray.push('people2')
            dataArray.push('people3')
            dataArray.push('people4')
            dataArray.push('people5')
        }
        if (detects['detect-signal'] & '0x40000') {
            dataArray.push('floor1')
            dataArray.push('floor2')
            dataArray.push('floor3')
            dataArray.push('floor4')
            dataArray.push('floor5')
        }
    }
    let turnLeft = getSe('turnLeft');
    let goStraight = getSe('goStraight');
    let turnRight = getSe('turnRight');
    let cartTurnRight = getSe('cartTurnRight');
    let upCalibration = getSe('upCalibration')
    let downCalibration = getSe('downCalibration')
    let redStopType = getSe('redStopType');
    let redStop = getSe('redStop');
    if (turnLeft && turnLeft.use) {
        dataArray.push('turnLeft')
    }
    if (goStraight && goStraight.use) {
        dataArray.push('goStraight')
    }
    if (turnRight && turnRight.use) {
        dataArray.push('turnRight')
    }
    if (cartTurnRight && cartTurnRight.use) {
        dataArray.push('cartTurnRight')
    }
    if (upCalibration && upCalibration.use) {
        dataArray.push('upCalibration')
    }
    if (downCalibration && downCalibration.use) {
        dataArray.push('downCalibration')
    }
    if (redStopType === 0) {
        if (redStop && redStop.use) {
            dataArray.push('redStop')
        }
    } else if (redStopType === 1) {
        if (redStop && redStop.use) {
            dataArray.push('redStop')
        }
    }
    showDouble(dataArray, drawContainer);
    if (redStop) {
        $(redStop.use).each(function (i) {
            if (redStop.use[i] === 0) {
                $("#redStop .redStop-can" + (i + 1)).css({
                    display: 'none'
                })
            }
        })
    }
    drawPath(drawCanvas, drawContainer);
    form.render();
};
