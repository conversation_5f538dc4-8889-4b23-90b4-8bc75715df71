<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>setSystem</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/DomOperation.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/settingSystem.js"></script>
</head>
<body class="sub-body layui-form custom-style">
<div class="layui-tab-item  system-setting layui-show">
    <fieldset class="layui-elem-field">
        <legend id="parameter">识别参数配置</legend>
        <div class="layui-field-box">
            <div class="input-range locate-thresh"
                 data-label="检测门限："
                 data-label-id="threshold"
                 data-disabled="false"
                 data-id="locateThresh"
            ></div>
            <!--            <div class="input-range ocr-thresh"-->
            <!--                 data-label="识别门限："-->
            <!--                 data-label-id="recognitionThreshold"-->
            <!--                 data-disabled="false"-->
            <!--                 data-id="ocrThresh"></div>-->
            <input id="personalizedPlate" type="checkbox" class="detect-license" title="个性车牌开启"
                   lay-skin="primary" value="0x0010">
            <input id="embassyPlate" type="checkbox" class="detect-license" title="使馆车牌开启"
                   lay-skin="primary" value="0x0040">
            <input id="consulatePlate" type="checkbox" class="detect-license" title="领事馆车牌开启"
                   lay-skin="primary" value="0x0080">
            <div class="layui-form-item">
                <label id="chinaCode" class="layui-form-label" style="width: 100px">地区汉字代码：</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" class="layui-input" id="headHZ">
                </div>
                <label id="code" class="layui-form-label" style="width: 100px">地区字母代码：</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" class="layui-input" id="headLetter">
                </div>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="crossing">路口参数配置</legend>
        <div class="layui-field-box">
            <div class="layui-form-item">
                <label id="crossingName" class="layui-form-label">路口名称：</label>
                <div class="layui-input-inline">
                    <input type="text" id="roadName" class="layui-input">
                </div>
                <label id="direction" class="layui-form-label">路口方向：</label>
                <div class="layui-input-inline">
                    <select id="roadDirect">
                        <option id="direction2" value="东向西">东向西</option>
                        <option id="direction3" value="西向东">西向东</option>
                        <option id="direction4" value="南向北">南向北</option>
                        <option id="direction5" value="北向南">北向南</option>
                        <option id="direction6" value="东北向西南">东北向西南</option>
                        <option id="direction7" value="西南向东北">西南向东北</option>
                        <option id="direction8" value="西北向东南">西北向东南</option>
                        <option id="direction9" value="东南向西北">东南向西北</option>
                    </select>
                </div>
                <label id="roadNum" class="layui-form-label">道路编号：</label>
                <div class="layui-input-inline">
                    <input type="text" id="roadCode" class="layui-input">
                </div>
            </div>
            <fieldset class="layui-elem-field">
                <legend id="num">路口编号</legend>
                <div class="layui-field-box">
                    <div class="layui-input-block">
                        <input type="radio" lay-filter="roadNumber" name="roadNumber" checked value="selfCode"><span
                            id="customNumber">自定义编号</span>
                        <div class="layui-input-inline selfCode"><input type="text" name="title"
                                                                        id="selfRoadCode"
                                                                        class="layui-input"></div>
                    </div>
                    <div class="layui-input-block">
                        <input type="radio" lay-filter="roadNumber" name="roadNumber" value="nationCode"><span
                            id="NationalStandardNumber">国家标准编号</span>
                        <div class="layui-input-inline nationCode">
                            <select lay-filter="selectProvince" id="selectProvince">
                                <option id="province" value="-1" data-code="00">请选择省</option>
                            </select>
                        </div>
                        <div class="layui-input-inline nationCode">
                            <select lay-filter="selectCity" id="selectCity">
                                <option id="city" value="-1" data-code="00">请选择市</option>
                            </select>
                        </div>
                        <div class="layui-input-inline nationCode">
                            <select lay-filter="selectArea" id="selectArea">
                                <option id="township" value="-1" data-code="00">请选择县/区</option>
                            </select>
                        </div>
                        <div class="layui-input-inline nationCode">
                            <select name="quiz4" id="nationRoadCode" lay-filter="selectCode">
                            </select>
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="speedConfiguration">速度配置</legend>
        <div class="layui-field-box">
            <input id="speedConfig" type="checkbox" class="detect-speed" title="速度检测使能"
                   lay-skin="primary" value="1" lay-filter="speedConfig">
            <div class="speed-config dis">
                <div class="">
                    <div style="float: left;">
                        <label id="speedMeasurementMode" class="layui-form-label">测速模式：</label>
                        <div class="layui-input-inline ">
                            <select name="speedType" lay-filter="detectSpeedType" disabled id="speedType">
                                <option id="video" value="0">视频</option>
                                <option id="radar" value="1">雷达</option>
                                <!--                                <option id="all" value="2">视频+雷达</option>-->
                            </select>
                        </div>
                    </div>
                    <div style="float:left;">
                        <label id="kindOfRadar" class="layui-form-label">雷达类型：</label>
                        <div class="layui-input-inline">
                            <select name="radarType" disabled id="radarType">
                                <!--                                <option id="no" value="0">无</option>-->
                                <!--                                <option id="andao" value="1">安道雷雷达</option>-->
                                <!--                                <option id="chuansu" value="2">川速微波</option>-->
                                <option id="huichang" value="3">慧昌雷达</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-clear"></div>
                </div>
                <label id="correctionFactor" class="layui-form-label">修正系数：</label>
                <div class="layui-input-inline"><input type="text" id="coef" disabled
                                                       class="layui-input"></div>
                <div class="input-range camera-height"
                     data-label="相机高度(厘米)："
                     data-label-id="height"
                     data-id="cameraHeight"></div>

                <div class="input-range camera-img-down"
                     data-label="相机与检测区下沿的水平距离(厘米)："
                     data-label-id="horizontalDistance"
                     data-id="cameraImgDown"></div>

                <div class="input-range camera-img-top"
                     data-label="相机与检测区上沿的水平距离(厘米)："
                     data-label-id="downDistance"
                     data-id="cameraImgTop"></div>

                <fieldset class="layui-elem-field">
                    <legend id="laneSpeedConfig">
                        车道限速设置（公里/小时）
                    </legend>
                    <div class="layui-field-box ">
                        <table>
                            <tr class="speed-config-line">
                                <td id="speed1">车道1：</td>
                                <td>
                                    <div class="input-range lane1-max-limit-speed"
                                         data-label="限速(高)："
                                         data-label-class="heightLimitSpeed"
                                         data-id="lane1MaxLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane1-max-speed"
                                         data-label="抓拍速度(高)："
                                         data-label-class="heightSpeed"
                                         data-id="lane1MaxSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane1-min-limit-speed"
                                         data-label="限速(低)："
                                         data-label-class="lowLimitSpeed"
                                         data-id="lane1MinLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane1-min-speed"
                                         data-label="抓拍速度(低)："
                                         data-label-class="lowSpeed"
                                         data-id="lane1MinSpeed"></div>

                                </td>
                            </tr>
                            <tr class="speed-config-line">
                                <td id="speed2">车道2：</td>
                                <td>
                                    <div class="input-range lane2-max-limit-speed"
                                         data-label="限速(高)："
                                         data-label-class="heightLimitSpeed"
                                         data-id="lane2MaxLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane2-max-speed"
                                         data-label="抓拍速度(高)："
                                         data-label-class="heightSpeed"
                                         data-id="lane2MaxSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane2-min-limit-speed"
                                         data-label="限速(低)："
                                         data-label-class="lowLimitSpeed"
                                         data-id="lane2MinLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane2-min-speed"
                                         data-label="抓拍速度(低)："
                                         data-label-class="lowSpeed"
                                         data-id="lane2MinSpeed"></div>

                                </td>
                            </tr>
                            <tr class="speed-config-line">
                                <td id="speed3">车道3：</td>
                                <td>
                                    <div class="input-range lane3-max-limit-speed"
                                         data-label="限速(高)："
                                         data-label-class="heightLimitSpeed"
                                         data-id="lane3MaxLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane3-max-speed"
                                         data-label="抓拍速度(高)："
                                         data-label-class="heightSpeed"
                                         data-id="lane3MaxSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane3-min-limit-speed"
                                         data-label="限速(低)："
                                         data-label-class="lowLimitSpeed"
                                         data-id="lane3MinLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane3-min-speed"
                                         data-label="抓拍速度(低)："
                                         data-label-class="lowSpeed"
                                         data-id="lane3MinSpeed"></div>

                                </td>
                            </tr>
                            <tr class="speed-config-line">
                                <td id="speed4">车道4：</td>
                                <td>
                                    <div class="input-range lane4-max-limit-speed"
                                         data-label="限速(高)："
                                         data-label-class="heightLimitSpeed"
                                         data-id="lane4MaxLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane4-max-speed"
                                         data-label="抓拍速度(高)："
                                         data-label-class="heightSpeed"
                                         data-id="lane4MaxSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane4-min-limit-speed"
                                         data-label="限速(低)："
                                         data-label-class="lowLimitSpeed"
                                         data-id="lane4MinLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane4-min-speed"
                                         data-label="抓拍速度(低)："
                                         data-label-class="lowSpeed"
                                         data-id="lane4MinSpeed"></div>

                                </td>
                            </tr>
                            <tr class="speed-config-line">
                                <td id="speed5">车道5：</td>
                                <td>
                                    <div class="input-range lane5-max-limit-speed"
                                         data-label="限速(高)："
                                         data-label-class="heightLimitSpeed"
                                         data-id="lane5MaxLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane5-max-speed"
                                         data-label="抓拍速度(高)："
                                         data-label-class="heightSpeed"
                                         data-id="lane5MaxSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane5-min-limit-speed"
                                         data-label="限速(低)："
                                         data-label-class="lowLimitSpeed"
                                         data-id="lane5MinLimitSpeed"></div>

                                </td>
                                <td>
                                    <div class="input-range lane5-min-speed"
                                         data-label="抓拍速度(低)："
                                         data-label-class="lowSpeed"
                                         data-id="lane5MinSpeed"></div>

                                </td>
                            </tr>
                        </table>
                    </div>
                </fieldset>
            </div>

        </div>
    </fieldset>
    <!--    <fieldset class="layui-elem-field">-->
    <!--        <legend id="AIMode">AI模型</legend>-->
    <!--        <div class="layui-field-box">-->
    <!--            <label id="AIModes" class="layui-form-label">AI模型：</label>-->
    <!--            <div class="layui-input-inline">-->
    <!--                <select name="quiz4" id="AIType">-->
    <!--                    <option id="mode1" value="0">模型1</option>-->
    <!--                    <option id="mode2" value="1">模型2</option>-->
    <!--                </select>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </fieldset>-->
    <fieldset class="layui-elem-field">
        <legend id="AIMode">日志打印等级</legend>
        <div class="layui-field-box">
            <label id="AIModes" class="layui-form-label" style="width:120px">日志打印等级：</label>
            <div class="layui-input-inline">
                <select name="quiz4" id="AIType">
                    <option value="0">0</option>
                    <option value="1">1</option>
                </select>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="eventImageCoding">事件图像编码参数配置</legend>
        <div class="layui-field-box">
            <!--<input type="checkbox" class="check-signal" title="Resize使能"-->
            <!--lay-skin="primary" value="256">-->
            <div class="input-range jpg-quality"
                 data-label="编码品质："
                 data-label-id="codingQuality"
                 data-disabled="false"
                 data-id="jpgQuality"></div>
            <!--<table class="input-table">-->
            <!--<tr>-->
            <!--<td id="c63" rowspan="2">编码品质：</td>-->
            <!--<td rowspan="2"><input type="text" value="0" class="input-number" id="jpgQuality"-->
            <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
            <!--</td>-->
            <!--<td>-->
            <!--<button type="button" class="input-up"><i-->
            <!--class="button-edge button-up"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--<tr>-->
            <!--<td>-->
            <!--<button type="button" class="input-down"><i-->
            <!--class="button-edge button-down"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--</table>-->
            <!--暂时不用-->
            <!--<table class="input-table">-->
            <!--<tr>-->
            <!--<td rowspan="2">Resize宽度：</td>-->
            <!--<td rowspan="2"><input type="text" value="0" class="input-number"-->
            <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
            <!--</td>-->
            <!--<td>-->
            <!--<button type="button" class="input-up" disabled="disabled"><i-->
            <!--class="button-edge button-up"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--<tr>-->
            <!--<td>-->
            <!--<button type="button" class="input-down" disabled="disabled"><i-->
            <!--class="button-edge button-down"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--</table>-->
            <!--<table class="input-table">-->
            <!--<tr>-->
            <!--<td rowspan="2">Resize高度：</td>-->
            <!--<td rowspan="2"><input type="text" value="0" class="input-number"-->
            <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
            <!--</td>-->
            <!--<td>-->
            <!--<button type="button" class="input-up" disabled="disabled"><i-->
            <!--class="button-edge button-up"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--<tr>-->
            <!--<td>-->
            <!--<button type="button" class="input-down" disabled="disabled"><i-->
            <!--class="button-edge button-down"></i></button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--</table>-->
            <!--暂时不用-->
        </div>
    </fieldset>

    <!--暂时不用-->
    <!--<fieldset class="layui-elem-field">-->
    <!--<legend>测光基准</legend>-->
    <!--<div class="layui-field-box">-->
    <!--<table class="input-table">-->
    <!--<tr>-->
    <!--<td rowspan="2">测光基准上限：</td>-->
    <!--<td rowspan="2"><input type="text" value="0" class="input-number"-->
    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
    <!--</td>-->
    <!--<td>-->
    <!--<button type="button" class="input-up" disabled="disabled"><i-->
    <!--class="button-edge button-up"></i></button>-->
    <!--</td>-->
    <!--</tr>-->
    <!--<tr>-->
    <!--<td>-->
    <!--<button type="button" class="input-down" disabled="disabled"><i-->
    <!--class="button-edge button-down"></i></button>-->
    <!--</td>-->
    <!--</tr>-->
    <!--</table>-->

    <!--<table class="input-table">-->
    <!--<tr>-->
    <!--<td rowspan="2">测光基准下线：</td>-->
    <!--<td rowspan="2"><input type="text" value="0" class="input-number"-->
    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
    <!--</td>-->
    <!--<td>-->
    <!--<button type="button" class="input-up" disabled="disabled"><i-->
    <!--class="button-edge button-up"></i></button>-->
    <!--</td>-->
    <!--</tr>-->
    <!--<tr>-->
    <!--<td>-->
    <!--<button type="button" class="input-down" disabled="disabled"><i-->
    <!--class="button-edge button-down"></i></button>-->
    <!--</td>-->
    <!--</tr>-->
    <!--</table>-->
    <!--</div>-->
    <!--</fieldset>-->
    <!--<fieldset class="layui-elem-field">-->
    <!--<legend>车辆特写设置</legend>-->
    <!--<div class="layui-field-box">-->
    <!--<fieldset class="layui-elem-field">-->
    <!--<legend>卡口特写图设置</legend>-->
    <!--<div class="layui-field-box">-->
    <!--<input type="radio" name="sex" value="nan" title="不需要">-->
    <!--<input type="radio" name="sex" value="nv" title="需要" >-->
    <!--</div>-->
    <!--</fieldset>-->
    <!--<fieldset class="layui-elem-field">-->
    <!--<legend>违章特写图设置</legend>-->
    <!--<div class="layui-field-box">-->
    <!--<input type="radio" name="sex" value="nan" title="不需要">-->
    <!--<input type="radio" name="sex" value="nv" title="取第一张图" >-->
    <!--<input type="radio" name="sex" value="nv" title="取第二张图" >-->
    <!--</div>-->
    <!--</fieldset>-->
    <!--<fieldset class="layui-elem-field">-->
    <!--<legend>违章特写图设置</legend>-->
    <!--<div class="layui-field-box">-->
    <!--<input type="radio" name="sex" value="nan" title="不需要">-->
    <!--<input type="radio" name="sex" value="nv" title="2x2模式" >-->
    <!--<input type="radio" name="sex" value="nv" title="1x4模式" >-->
    <!--<input type="radio" name="sex" value="nv" title="4x1模式" >-->
    <!--</div>-->
    <!--</fieldset>-->
    <!--<input type="checkbox" class="check-signal" title="卡口不上报控制台"-->
    <!--lay-skin="primary" value="256">-->


    <!--    <input type="checkbox" class="check-signal" title="定时重启"-->
    <!--           lay-skin="primary" value="256">-->
    <!--    <label class="layui-form-label">重启时间：</label>-->
    <!--    <div class="layui-inline"> &lt;!&ndash; 注意：这一层元素并不是必须的 &ndash;&gt;-->
    <!--        <input type="text" class="layui-input" id="restartTime">-->
    <!--    </div>-->
    <!--</div>-->
    <!--</fieldset>-->
    <!--暂时不用-->
    <button class="layui-btn layui-btn-default" id="saveSystem">保存配置</button>
    <button class="layui-btn layui-btn-default layui-btn-block" id="resetSystem">重置</button>
</div>
</body>
</html>
