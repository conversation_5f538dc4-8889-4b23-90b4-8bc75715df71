<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>setSurvey</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/DomOperation.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/settingDraw.js"></script>
    <script src="../../js/drawCanvas.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/settingSurvey.js"></script>
    <!--    <script src="../../js/i18n/ch_en/setting_frame_ch_en/setting_survey_ch_en.js"></script>-->
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-show">
    <div class="layui-row">
        <div class="" id="drawContent">
            <img id="configImg" src="../../img/404.png" class="draw-img"/>
            <canvas id="surveyCan" width="600" height="450" class="draw-canvas">
            </canvas>
            <div id="drawSurvey" class="draw-container">
            </div>
        </div>
        <div class="layui-col-md4 config-setting" style="padding-left: 10px">
            <button class="layui-btn layui-btn-default" id="refreshPic"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">刷新场景
            </button>
            <button id="saveConfig" class="layui-btn layui-btn-default layui-btn-block"
                    onclick="saveConfig('drawSurvey')" style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                保存配置
            </button>
            <button id="clear" class="layui-btn layui-btn-default" onclick="clearNowConfig()"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">清除当前绘制区域
            </button>
            <button id="show" class="layui-btn layui-btn-default layui-btn-block" onclick="showConfig('drawSurvey')"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                重置配置
            </button>

            <div class="layui-collapse layui-form" lay-accordion
                 style="margin-top: 10px;max-width: 550px;min-width: 450px">
                <div class="layui-colla-item" style="display: none">
                    <h2 id="plateSize" class="layui-colla-title">车牌大小</h2>
                    <div class="layui-colla-content">
                        <button class="layui-btn layui-btn-default layui-btn-block" id="biggerBtn"
                                onclick="FixedRect('bigger','dashed','plate')">局部放大
                        </button>
                        <!--<button class="layui-btn layui-btn-default layui-btn-block"-->
                        <!--onclick="Amplify('plate')">放大-->
                        <!--&lt;!&ndash;</button>&ndash;&gt;-->
                        <!--<button class="layui-btn layui-btn-default layui-btn-block"-->
                        <!--onclick="FixedRect('plate','solid')">绘制车牌-->
                        <!--</button>-->
                        <br/>
                        <div style="vertical-align: middle">
                            <div style="margin: 30px 0 0 0;">
                                <label id="min">车牌最小宽度：</label>
                                <div class="layui-input-inline" style="width: 80%;">
                                    <div id="slideMinWidth" style="width: 60%;"></div>
                                </div>
                            </div>
                            <div style="margin: 30px 0 0 0;">
                                <label id="max">车牌最大宽度：</label>
                                <div class="layui-input-inline" style="width: 80%;">
                                    <div id="slideMaxWidth" style="width: 60%;"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="targetDetectionArea" class="layui-colla-title">目标检测区</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default" id="surveyBtn"
                                    onclick="CommonRect('survey')">
                                绘制检测
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="surveyLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="surveyLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="surveyColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="surveyColorEle"></div>
                                </div>
                            </div>
                            <!--<div class="layui-form-item">-->
                            <!--<div class="layui-slider-input layui-input"><div class="layui-slider-input-txt"><input type="text" class="layui-input"></div><div class="layui-slider-input-btn" style="display: none;"><i class="layui-icon layui-icon-up"></i><i class="layui-icon layui-icon-down"></i></div></div>-->
                            <!--</div>-->
                        </div>
                        <!--<button id="addSu" class="layui-btn layui-btn-default" title="添加锚点"-->
                        <!--onclick="addSu()"><span-->
                        <!--class="layui-icon layui-icon-add-circle"></span></button>-->
                        <!--<button id="reduceSu" class="layui-btn layui-btn-default" title="减少锚点"-->
                        <!--onclick="reduceSu()"><span-->
                        <!--class="layui-icon layui-icon-circle-dot"></span></button>-->
                        <div class="control-point">
                            <div class="survey-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="survey-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="survey-point index-point">
                                <div class="input-range"
                                     data-label="X3："></div>
                                <div class="input-range"
                                     data-label="Y3："></div>
                            </div>
                            <div class="survey-point index-point">
                                <div class="input-range"
                                     data-label="X4："></div>
                                <div class="input-range"
                                     data-label="Y4："></div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="violationDetectionArea" class="layui-colla-title">违停检测区</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default" id="noParkingBtn"
                                    onclick="CommonRect('noParking')">
                                违停检测
                            </button>
                            <div class="layui-form-item">
                                <label class="kind" for="noParkingType">种类：</label>
                                <div class="layui-input-inline">
                                    <select id="noParkingType">
                                        <option class="ordinary" value="0">普通</option>
                                        <option class="sidewalk" value="1">人行道</option>
                                        <option class="Gridlines" value="2">网格线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item common-none">
                                <label class="type" for="noParkingLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="noParkingLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="noParkingColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="noParkingColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="noParking-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="noParking-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="noParking-point index-point">
                                <div class="input-range"
                                     data-label="X3："></div>
                                <div class="input-range"
                                     data-label="Y3："></div>
                            </div>
                            <div class="noParking-point index-point">
                                <div class="input-range"
                                     data-label="X4："></div>
                                <div class="input-range"
                                     data-label="Y4："></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="dividingLine" class="layui-colla-title">左直右分界线</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="turnLeftBtn"
                                    disabled="disabled"
                                    onclick="commonLine('turnLeft',1,false)">
                                左转分界线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="turnLeftLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="turnLeftLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="turnLeftColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="turnLeftColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="turnLeft-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="turnLeft-signal" lay-filter="turnLeftCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="turnLeft-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="turnLeft-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>
                        <br/>
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="goStraightBtn"
                                    disabled="disabled"
                                    onclick="commonLine('goStraight',1,false)">
                                直行分界线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="goStraightLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="goStraightLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="goStraightColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="goStraightColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="goStraight-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="goStraight-signal" lay-filter="goStraightCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="goStraight-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="goStraight-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>
                        <br/>
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="turnRightBtn"
                                    disabled="disabled"
                                    onclick="commonLine('turnRight',1,false)">
                                右转分界线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="turnRightLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="turnRightLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="turnRightColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="turnRightColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="turnRight-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="turnRight-signal" lay-filter="turnRightCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="turnRight-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="turnRight-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>


                        <br/>
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="cartTurnRightBtn"
                                    disabled="disabled"
                                    onclick="commonLine('cartTurnRight',1,false)">
                                大车右转让行线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="cartTurnRightLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="cartTurnRightLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="cartTurnRightColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="cartTurnRightColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="cartTurnRight-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="cartTurnRight-signal" lay-filter="cartTurnRightCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="cartTurnRight-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="cartTurnRight-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>

                    </div>
                </div>

                <div class="layui-colla-item">
                    <h2 id="queueCalibration" class="layui-colla-title">排队长度标定线</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="upCalibrationBtn"
                                    disabled="disabled"
                                    onclick="commonLine('upCalibration',1,false)">
                                上行标定线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="upCalibrationLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="upCalibrationLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="upCalibrationColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="upCalibrationColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="upCalibration-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="upCalibration-signal" lay-filter="upCalibrationCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="upCalibration-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="upCalibration-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="upCalibration-point upCalibration-queueLength">
                                <div style="position: relative;margin: 5px 10px 5px 0px;display: inline-table;">
                                    <label id="queueLengthText">排队长度(米)：</label>
                                    <div class="input-handler-wrap">
                                        <button type="button" class="input-up1" disabled="disabled"
                                                onclick="inputAdd('upCalibration')"><i
                                                class="button-edge button-up1"></i>
                                        </button>
                                        <button type="button" class="input-down1" disabled="disabled"
                                                onclick="inputReduce('upCalibration')"><i
                                                class="button-edge button-down1"></i></button>
                                    </div>
                                    <input type="text" value="0" class="input-number1"
                                           oninput="value=value.replace(/[^\d]/g,'')"/>
                                </div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>
                        <br/>
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default layui-btn-disabled" id="downCalibrationBtn"
                                    disabled="disabled"
                                    onclick="commonLine('downCalibration',1,false)">
                                下行标定线
                            </button>
                            <div class="layui-form-item common-none">
                                <label class="type" for="downCalibrationLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="downCalibrationLineType">
                                        <option class="solid" value="solid">实线</option>
                                        <option class="dashed" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="downCalibrationColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="downCalibrationColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="downCalibration-checkbox" style="padding: 10px 5px">
                                <input type="checkbox" id="downCalibration-signal" lay-filter="downCalibrationCheck"
                                       lay-skin="primary" value="1">
                            </div>
                            <div class="downCalibration-point index-point">
                                <div class="input-range"
                                     data-label="X1："></div>
                                <div class="input-range"
                                     data-label="Y1："></div>
                            </div>
                            <div class="downCalibration-point index-point">
                                <div class="input-range"
                                     data-label="X2："></div>
                                <div class="input-range"
                                     data-label="Y2："></div>
                            </div>
                            <div class="downCalibration-point downCalibration-queueLength">
                                <div style="position: relative;margin: 5px 10px 5px 0px;display: inline-table;">
                                    <label id="queueLengthText1">排队长度(米)：</label>
                                    <div class="input-handler-wrap">
                                        <button type="button" class="input-up1" disabled="disabled"
                                                onclick="inputAdd('downCalibration')"><i
                                                class="button-edge button-up1"></i></button>
                                        <button type="button" class="input-down1" disabled="disabled"
                                                onclick="inputReduce('downCalibration')"><i
                                                class="button-edge button-down1"></i></button>
                                    </div>
                                    <input type="text" value="0" class="input-number1"
                                           oninput="value=value.replace(/[^\d]/g,'')"/>
                                </div>
                            </div>
                            <div class="layui-clear"></div>
                        </div>

                    </div>
                </div>
                <!--                <div class="layui-colla-item">-->
                <!--                    <h2 id="turnDetectionArea" class="layui-colla-title">掉头检测区</h2>-->
                <!--                    <div class="layui-colla-content">-->
                <!--                        <div class="config-control">-->
                <!--                            <button class="layui-btn layui-btn-default" id="aroundBtn"-->
                <!--                                    onclick="CommonRect('around')">-->
                <!--                                掉头检测-->
                <!--                            </button>-->
                <!--                            <div class="layui-form-item common-none">-->
                <!--                                <label class="type" for="aroundLineType">样式：</label>-->
                <!--                                <div class="layui-input-inline">-->
                <!--                                    <select id="aroundLineType">-->
                <!--                                        <option class="solid" value="solid">实线</option>-->
                <!--                                        <option class="dashed" value="dashed">虚线</option>-->
                <!--                                    </select>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                            <div class="layui-form-item">-->
                <!--                                <div class="layui-input-inline">-->
                <!--                                    <input type="text" value="" placeholder="颜色"-->
                <!--                                           class="layui-input" id="aroundColor">-->
                <!--                                </div>-->
                <!--                                <div class="layui-inline">-->
                <!--                                    <div id="aroundColorEle"></div>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->

                <!--                        <div class="control-point">-->
                <!--                            <div class="around-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X1："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y1："></div>-->
                <!--                            </div>-->
                <!--                            <div class="around-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X2："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y2："></div>-->
                <!--                            </div>-->
                <!--                            <div class="around-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X3："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y3："></div>-->
                <!--                            </div>-->
                <!--                            <div class="around-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X4："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y4："></div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--                <div class="layui-colla-item">-->
                <!--                    <h2 id="v33" class="layui-colla-title">对向直行检测区</h2>-->
                <!--                    <div class="layui-colla-content ">-->
                <!--                        <div class="config-control">-->
                <!--                            <button class="layui-btn layui-btn-default" id="faceBtn"-->
                <!--                                    onclick="CommonRect('face')">-->
                <!--                                对向直行-->
                <!--                            </button>-->
                <!--                            <div class="layui-form-item common-none">-->
                <!--                                <label class="type" for="faceLineType">样式：</label>-->
                <!--                                <div class="layui-input-inline">-->
                <!--                                    <select id="faceLineType">-->
                <!--                                        <option class="solid" value="solid">实线</option>-->
                <!--                                        <option class="dashed" value="dashed">虚线</option>-->
                <!--                                    </select>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                            <div class="layui-form-item">-->
                <!--                                <div class="layui-input-inline">-->
                <!--                                    <input type="text" value="" placeholder="颜色"-->
                <!--                                           class="layui-input" id="faceColor">-->
                <!--                                </div>-->
                <!--                                <div class="layui-inline">-->
                <!--                                    <div id="faceColorEle"></div>-->
                <!--                                </div>-->
                <!--                            </div>-->
                <!--                        </div>-->

                <!--                        <div class="control-point">-->
                <!--                            <div class="face-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X1："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y1："></div>-->
                <!--                            </div>-->
                <!--                            <div class="face-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X2："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y2："></div>-->
                <!--                            </div>-->
                <!--                            <div class="face-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X3："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y3："></div>-->
                <!--                            </div>-->
                <!--                            <div class="face-point index-point">-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="X4："></div>-->
                <!--                                <div class="input-range"-->
                <!--                                     data-label="Y4："></div>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
        </div>
    </div>
</div>
</body>
</html>
