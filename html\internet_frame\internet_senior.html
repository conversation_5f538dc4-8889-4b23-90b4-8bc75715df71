<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>internetSenior</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../css/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/system.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/internetSenior.js"></script>
    <style>
        .layui-form-checkbox[lay-skin=primary] {
            min-height: 28px;
        }
    </style>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-form layui-show">
    <div>
        <fieldset class="layui-elem-field">
            <legend id="serverAddress">服务器地址</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label address" style="width: 60px">地址：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="ip" class="layui-input" id="transmit_addr">
                    </div>
                    <label class="layui-form-label port" style="margin-left: 20px">端口：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="port" class="layui-input" id="transmit_port">
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field">
            <legend id="FTPSet">FTP服务器设置</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label address" style="width: 60px">地址：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="ip" class="layui-input" id="ftp_ip">
                    </div>
                    <label class="layui-form-label port" lay-verify="port" style="margin-left: 20px">端口：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="ftp_Port">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label userName" style="width: 60px">用户名：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="ftp_user" lay-verify="length">
                    </div>
                    <label class="layui-form-label password" style="margin-left: 20px">密码：</label>
                    <div class="layui-input-inline">
                        <input type="password" class="layui-input" id="ftp_password" lay-verify="length">
                        <a id="passwordClick" class="iconfont icon-yanjing_yincang"
                           style="position: absolute;top: 10px;right: 10px;"></a>
                    </div>
                </div>
                <!--                新协议不需要数据位、存储时间-->
                <!--                <div class="layui-form-item">-->
                <!--                    <label id="dataBit" class="layui-form-label" style="width: 60px">数据位：</label>-->
                <!--                    <div class="layui-input-inline">-->
                <!--                        <select id="ftp_data">-->
                <!--                            <option value="0">0</option>-->
                <!--                            <option value="1">1</option>-->
                <!--                            <option value="2">2</option>-->
                <!--                            <option value="3">3</option>-->
                <!--                            <option value="4">4</option>-->
                <!--                            <option value="5">5</option>-->
                <!--                            <option value="6">6</option>-->
                <!--                            <option value="7">7</option>-->
                <!--                        </select>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--                <fieldset class="layui-elem-field">-->
                <!--                    <legend id="saveDat">FTP存储时间(单位:天)</legend>-->
                <!--                    <div class="layui-field-box">-->
                <!--                        <div class="layui-form-item">-->
                <!--                            <label id="video" class="layui-form-label">视频：</label>-->
                <!--                            <div class="layui-input-inline">-->
                <!--                                <select id="ftp_video_days">-->
                <!--                                </select>-->
                <!--                            </div>-->
                <!--                            <label id="bayonet" class="layui-form-label" style="margin-left: 20px">卡口：</label>-->
                <!--                            <div class="layui-input-inline">-->
                <!--                                <select id="ftp_kk_days">-->
                <!--                                </select>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="layui-form-item">-->
                <!--                            <label id="violation" class="layui-form-label">违法：</label>-->
                <!--                            <div class="layui-input-inline">-->
                <!--                                <select id="ftp_wz_days">-->
                <!--                                </select>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </fieldset>-->
                <!--                <fieldset class="layui-elem-field">-->
                <!--                    <legend id="saveType">FTP存储类型</legend>-->
                <!--                    <div class="layui-field-box">-->
                <!--                        <div class="layui-form-item">-->
                <!--                            <label id="bayonetType" class="layui-form-label">卡口：</label>-->
                <!--                            <div class="layui-input-inline" style="width:80px;color: #000">-->
                <!--                                <input id="kk_qj" type="checkbox" class="kk-file-type" title="全景图"-->
                <!--                                       lay-skin="primary" value="1">-->
                <!--                            </div>-->
                <!--                            <div class="layui-input-inline" style="width:80px;color: #000">-->
                <!--                                <input id="kk_tx" type="checkbox" class="kk-file-type" title="特写图"-->
                <!--                                       lay-skin="primary" value="2">-->
                <!--                            </div>-->
                <!--                            <div class="layui-input-inline" style="width:80px;color: #000">-->
                <!--                                <input id="kk_cp" type="checkbox" class="kk-file-type" title="车牌图"-->
                <!--                                       lay-skin="primary" value="4">-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                        <div class="layui-form-item">-->
                <!--                            <label id="violationType" class="layui-form-label">违法：</label>-->
                <!--                            <div class="layui-input-inline" style="width:80px;color: #000">-->
                <!--                                <input id="wf_p" type="checkbox" class="wf-file-type" title="图片"-->
                <!--                                       lay-skin="primary" value="1">-->
                <!--                            </div>-->
                <!--                            <div class="layui-input-inline" style="width:80px;color: #000">-->
                <!--                                <input id="wf_v" type="checkbox" class="wf-file-type" title="视频"-->
                <!--                                       lay-skin="primary" value="2">-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </fieldset>-->
                <!--                <fieldset class="layui-elem-field">-->
                <!--                    <legend id="savePath">FTP存储路径</legend>-->
                <!--                    <div class="layui-field-box" id="path_div">-->
                <!--                        <div class="layui-form-item">-->
                <!--                            <label id="pathList" class="layui-form-label" style="width: 80px">目录结构：</label>-->
                <!--                            <div class="layui-input-inline">-->
                <!--                                <select id="ftp_path_list" lay-filter="ftp_path_list_filter"-->
                <!--                                        lay-verify="ftp_path_list_verify" name="ftp_path_list_name">-->
                <!--                                </select>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </fieldset>-->
                <!--                <fieldset class="layui-elem-field">-->
                <!--                    <legend id="saveName">FTP存储文件名称</legend>-->
                <!--                    <div class="layui-field-box" id="name_div">-->
                <!--                    </div>-->
                <!--                </fieldset>-->
            </div>
        </fieldset>
        <fieldset class="layui-elem-field">
            <legend id="forwardPlateform">汇聚转发平台设置</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <label class="layui-form-label address" style="width: 60px">地址：</label>
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="forward_plateform_ip" class="layui-input" id="forward_plateform_ip">
                    </div>
                    <label class="layui-form-label port" lay-verify="forward_plateform_port" style="margin-left: 20px">端口：</label>
                    <div class="layui-input-inline">
                        <input type="text" class="layui-input" id="forward_plateform_port">
                    </div>
                </div>
            </div>
        </fieldset>
        <!--        <fieldset class="layui-elem-field">-->
        <!--            <legend id="sever_1400">1400协议服务器</legend>-->
        <!--            <div class="layui-field-box">-->
        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label address" style="width: 60px">地址：</label>-->
        <!--                    <div class="layui-input-inline">-->
        <!--                        <input type="text" lay-verify="ip" class="layui-input" id="1400_ip">-->
        <!--                    </div>-->
        <!--                    <label class="layui-form-label port" style="margin-left: 20px">端口：</label>-->
        <!--                    <div class="layui-input-inline">-->
        <!--                        <input type="text" lay-verify="port" class="layui-input" id="1400_port">-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label userName" style="width: 60px">用户名：</label>-->
        <!--                    <div class="layui-input-inline">-->
        <!--                        <input type="text" class="layui-input" id="1400_username" lay-verify="length">-->
        <!--                    </div>-->
        <!--                    <label class="layui-form-label password" style="margin-left: 20px">密码：</label>-->
        <!--                    <div class="layui-input-inline">-->
        <!--                        <input type="password" class="layui-input" id="1400_password" lay-verify="length">-->
        <!--                        <a id="1400_passwordClick" class="iconfont icon-yanjing_yincang"-->
        <!--                           style="position: absolute;top: 10px;right: 10px;"></a>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                <div class="layui-form-item">-->
        <!--                    <label class="layui-form-label" style="width: 60px">设备ID：</label>-->
        <!--                    <div class="layui-input-inline">-->
        <!--                        <input type="text" class="layui-input" id="1400_device_id" lay-verify="idCheck">-->
        <!--                    </div>-->
        <!--                </div>-->

        <!--            </div>-->
        <!--        </fieldset>-->

        <button class="layui-btn layui-btn-default" lay-submit id="saveStorage" lay-filter="saveStorage">保存配置</button>
        <button id="reset" class="layui-btn layui-btn-default layui-btn-block"
                onclick="getStorage()">重置
        </button>
    </div>
</div>

</body>
</html>
