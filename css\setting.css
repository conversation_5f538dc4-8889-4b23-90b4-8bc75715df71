.layui-nav {
    /*position: relative;*/
    /*padding: 0 20px;*/
    background-color: #eeeeee;
    color: #333;
    border-radius: 0px;
    font-size: 0;
    box-sizing: border-box;
}

.layui-nav li > a {
    font-weight: bold;
}


.layui-nav .layui-nav-item a {
    display: block;
    padding: 0 20px;
    color: #333;
    color: rgba(51, 51, 51, 0.7);
    transition: all .3s;
    -webkit-transition: all .3s;
}


.layui-nav .layui-nav-more {
    border-color: #333333 transparent transparent;
}

.layui-nav .layui-nav-mored, .layui-nav-itemed > a .layui-nav-more {
    border-color: transparent transparent #333333;
}

.layui-nav-itemed > .layui-nav-child {
    display: block;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.3) !important;
}


.line {
    margin-top: 10px;
    /*float: left;*/
}

.line div {
    display: inline-block;

}

.line-info {
    margin: 10px 20px 0px 2px;
}

#lineSurvey {

}

.draw-img {
    width: 600px;
    height: 450px;
}

.draw-container .point {
    height: 450px;
    visibility: hidden;
}

.draw-container .clickZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#survey {
    height: 450px;
    visibility: hidden;
}

#surveyZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#noParking {
    height: 450px;
    visibility: hidden;
}

#noParkingZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#around {
    height: 450px;
    visibility: hidden;
}

#aroundZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#face {
    height: 450px;
    visibility: hidden;
}

#faceZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#suLeftUp {
    top: 20px;
    left: 20px;
}

.notice-info {
    font-size: 12px;
    color: #737373;
    padding: 5px;
}

#line {
    height: 450px;
    visibility: hidden;
}

#lineZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#plate {
    height: 450px;
    visibility: hidden;
}

#plaZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

#rule {
    height: 450px;
    visibility: hidden;
}

#ruleZone {
    width: 600px;
    height: 450px;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
}

.mini-box {
    width: 10px;
    height: 10px;
    background-color: #0000FF;
    position: absolute;
    z-index: 1000;
}

.draw-container {
    top: 0;
    left: 0;
    width: 600px;
    height: 450px;
    position: absolute;
    z-index: 999;
}

#drawLine {
    top: 0;
    left: 0;
    width: 600px;
    height: 450px;
    position: absolute;
    z-index: 999;
}
#drawLineSurvey {
    top: 0;
    left: 0;
    width: 600px;
    height: 450px;
    position: absolute;
    z-index: 999;
}

.draw-canvas {
    position: absolute;
    top: 0;
    left: 0;
}

#configCan {
    position: absolute;
    top: 0;
    left: 0;
}

#TestOcx_set {
    z-index: 990;
}

.frame-show {
    position: absolute;
    visibility: inherit;
    top: 0px;
    left: 0px;
    width: 600px;
    height: 450px;
    z-index: -1;
    /*filter:alpha(opacity=0);*/
    opacity: 0;
}


.layui-form {
    /*margin-right: 10px;*/
}


.config-control {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.config-control .layui-form-item, .config-control button {
    margin-left: 10px;
    margin-bottom: 5px;
}

.config-control .layui-input, .layui-textarea {
    display: block;
    padding-left: 10px;
}

.config-control .layui-form-item .layui-input-inline {
    float: left;
    width: 70px;
    margin-right: 10px;
}

.config-control label {
    float: left;
    padding: 9px 0px;
    font-weight: 400;
    line-height: 20px;
    text-align: right
}

.line-type,
.control-point .line-point,
.control-point .floor1-point,
.control-point .floor2-point,
.control-point .floor3-point,
.control-point .floor4-point,
.control-point .floor5-point,
.control-point .turnLeft-point,
.control-point .turnLeft-checkbox,
.control-point .goStraight-point,
.control-point .goStraight-checkbox,
.control-point .turnRight-point,
.control-point .turnRight-checkbox,
.control-point .cartTurnRight-point,
.control-point .cartTurnRight-checkbox,
.control-point .upCalibration-point,
.control-point .upCalibration-checkbox,
.control-point .downCalibration-point,
.control-point .downCalibration-checkbox,
.control-point .redStop-point,
.control-point .redStop-checkbox,
.control-point .signal-point {
    float: left;
}

.line-type {
    width: 80px;
    margin: 5px 0;
}

.common-float {
    float: left;
}

.position-visible {
    visibility: visible;
}

.position-hidden {
    visibility: hidden;
}

.config-control .layui-inline {
    margin-bottom: 0px;
}

.input-table {
    vertical-align: middle;
}

.extend-signal {
    margin-bottom: 10px;
}

.extend-signal td {
    width: 50px;
    text-align: center;
}

.extend-signal .layui-form-checkbox[lay-skin=primary] {
    padding-left: 0;
}

.layui-input-block {
    margin-left: 0;
    margin-bottom: 10px;
}

.layui-tab-item {
    background-color: #fff;
}

#signalTable tr td {
    padding: 3px 5px;
}

#signalTable tr td:nth-child(1) {
    width: 30px;
}

#signalTable tr td:nth-child(2) {
    width: 100px;
}

#signalTable tr td:nth-child(3) {
    width: 80px;
}

#signalTable tr td:nth-child(4) {
    width: 100px;
}

#signalTable tr td:nth-child(5) {
    width: 180px;
}

#signalTable .layui-input, .layui-select {
    height: 35px;
}

.light-status {
    width: 100%;
}

.light-status td, .light-status img {
    width: 100px;
}

#extendParams li {
    position: relative;
}

.layui-timeline-axis:hover {
    color: #5FB878;
}

#extendParams a {
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    font-size: 18px;
}

#extendParams a:hover {
    background-color: #5FB878;
    color: #fff;
}

.iColor {
    color: #FF5722 !important;
}

#lineInfo td {
    text-align: center;
}

#lineInfo td:nth-child(1) {
    width: 100px;
    padding: 0 10px;
}

#lineInfo td:nth-child(2) {
    width: 150px;
    padding: 0 10px;
}

#lineInfo td:nth-child(3) {
    width: 120px;
    padding: 0 10px;
}

.line-config {
    margin: 10px 0 0 0;
}

.line-config .layui-tab-title li {
    background-color: #f2f2f2;
}

.line-config.layui-tab-card {
    border-width: 1px;
    border-style: solid;
    border-bottom: none;
    border-radius: 2px;
    box-shadow: none;
}

.config-table tr:first-child > td .config-inline {
    max-width: 60%;
}

.time-table td input {
    margin-top: 4px;
    max-width: 95%;
}

.config-setting table {
    font-size: 14px;
    color: #666
}

/*.config-table tr:first-child>td:nth-child(2) .config-inline{*/
/*max-width: 80%;*/
/*}*/
/*.config-table tr:first-child>td:nth-child(3) .config-inline{*/
/*max-width: 80%;*/
/*}*/
.input-handler-wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 100%;
}

.input-range {
    position: relative;
    margin: 5px 10px 5px 0px;
    display: inline-table;
}

.input-small .input-number {
    width: 40px;
}

.input-table {
    position: relative;
    margin: 0 10px 5px 0px;
    display: inline-table;
}

.input-number, .input-number1 {
    padding-right: 12px;
    border: 1px solid #e6e6e6;
    text-align: center;
    vertical-align: middle;
    height: 36px;
    width: 50px;
}

.input-number:hover, .input-number1:hover {
    border-color: #D2D2D2 !important;
}

.input-up, .input-up1, .input-down, .input-down1 {
    right: 6px;
    color: #c2c2c2;
    position: absolute;
    font-size: 12px;
    border: none;
    background-color: transparent;
    cursor: pointer;
}

.input-up, .input-up1 {
    top: 0px;
    display: inline-block;
    /*border-style: solid;*/
    /*border-color:transparent transparent #c2c2c2 transparent;*/
    /*transform: rotate(90deg); !*顺时针旋转90°*!*/
}

.input-down, .input-down1 {
    bottom: 0px;
    display: inline-block;
    /*border-style: solid;*/
    /*border-color:#c2c2c2 transparent transparent transparent;*/
}

.input-up:hover, .input-up1:hover, .input-down:hover, .input-down1:hover {
    color: #828282;
}

.input-up:disabled, .input-up1:disabled, .input-down:disabled, .input-down1:disabled {
    cursor: not-allowed;
}

.button-edge {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: solid;
    position: absolute;
    right: 0;
}

.button-up, .button-up1 {
    top: 0;
    border-color: transparent transparent #c2c2c2 transparent;
}

.button-down, .button-down1 {
    bottom: 0;
    border-color: #c2c2c2 transparent transparent transparent;
}

.button-up:hover, .button-up1:hover {
    border-color: transparent transparent #828282 transparent;
}

.button-down:hover, .button-down1:hover {
    border-color: #828282 transparent transparent transparent;
}

.input-up, .input-up1 {
    top: 0
}

.input-down, .input-down1 {
    bottom: 0;
}

.input-up, .input-up1, .input-down, .input-down1 {
    display: inline-block;
    width: 12px;
    height: 12px;
    right: 6px;
    color: #c2c2c2;
    position: absolute;
    font-size: 12px;
    border: none;
    background-color: transparent;
}
