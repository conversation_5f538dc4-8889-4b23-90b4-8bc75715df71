var firstTime = 0, secondTime = 0;
var lineWidth = 2
//#ed0973
//清除点击时间
var clearTime = function () {
    firstTime = 0;
    secondTime = 0;
};
//检测是否短时间内双击
var handleDoubleClick = function () {
    localLog('handleDoubleClick')
    let flag = false;
    if (firstTime === 0) {
        firstTime = new Date().getTime();
    } else {
        secondTime = new Date().getTime();
        if (secondTime - firstTime < 500) {
            flag = true;
        }
    }
    return flag
};
/**
 * 检查所传值是否超过图片大小
 * @param cX
 * @param cY
 * @returns {boolean}
 */
var checkValue = function (cX, cY) {
    localLog('checkValue')
    let globalInfo = checkGlobal();
    let result = true;
    if (cX < 0) {
        result = false;
    }
    if (cX > (globalInfo.w - 5)) {
        result = false;
    }
    if (cY < 0) {
        result = false;
    }
    if (cY > (globalInfo.h - 5)) {
        result = false;
    }
    return result
};
/**
 * 判断容器是否存在
 * @param type
 * @param rectType
 * @param globalInfo
 * @param drawContainer
 */
var checkContainerExist = function (type, rectType, globalInfo, drawContainer) {
    localLog('checkContainerExist')
    let exist = document.getElementById(type);
    let dContainer = $("#" + drawContainer);
    if (!exist) {

        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        dContainer.append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        dContainer.append(newClickDive);
    }
    if (type === 'signalBig') {
        let newDiv2 = $("<div></div>");
        newDiv2.attr({
            "class": "point " + rectType,
            "id": 'signal'
        });
        newDiv2.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        dContainer.append(newDiv2);
        let newClickDive2 = $("<div></div>");
        newClickDive2.attr({
            "class": "clickZone",
            "id": "signalZone"
        });
        newClickDive2.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        dContainer.append(newClickDive2);
    }
};

/**
 * 绘制普通图形
 * @param type
 * @param group
 * @constructor
 */
var CommonRect = function (type, group) {
    localLog('CommonRect')
    $("button").removeClass('layui-btn-normal');
    $(event.currentTarget).addClass('layui-btn-normal');
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let rectType = 'common-rect';
    let groupType = "";
    let currentX1, currentY1;
    let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let groupNum;
    let length = document.getElementsByClassName(type + "-can").length;
    if (group) {
        rectType += '-group'
    }
    $("#amplifyImg").remove();

    let exist = document.getElementById(type);
    let globalInfo = checkGlobal();
    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
    }
    $(".control-point button[type=button]").attr("disabled", true);
    hiddenZone(type, drawContainer, globalInfo);
    // drawPath(drawCanvas, drawContainer);
    if (length) {
        for (let i = 0; i < length; i++) {
            for (let j = 0; j < 4; j++) {
                $($("." + type + "-point button[type=button")[4 * i + j]).attr("disabled", false);
            }
        }
    }
    let typePoint = $("." + type + "-point");
    $("#" + type + "Zone").click(function () {
        moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    }).off("mousemove").on("mousemove", function (e) {
        if (moveFlag) {
            //console.log("move X:" + moveX + " Y" + moveY);
            let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
            let distanceY = e.pageY - moveY;
            moveX = e.pageX;
            moveY = e.pageY;
            let doms;
            if (type === 'people1' || type === 'people2' || type === 'people3' || type === 'people4' || type === 'people5') {
                doms = $("." + type + "-can.rect-group" + (parseInt(moveIndex / 4) + 1))
            } else {
                doms = $("." + type + "-can");
            }
            let points = [];
            //console.log("distance X:" + distanceX + " Y:" + distanceY);
            for (let i = 0; i < doms.length; i++) {
                let p = {};
                let cX = doms[i].offsetLeft + distanceX;
                let cY = doms[i].offsetTop + distanceY;
                if (!checkValue(cX, cY)) {
                    console.log("outPic");
                    return
                }
                p.x = cX;
                p.y = cY;
                points.push(p)
            }
            //console.log(points);
            let nX, nY;
            for (let i in points) {
                console.log("move");
                $(doms[i]).css({
                    left: points[i].x,
                    top: points[i].y
                });
                if (type === 'signalBig') {
                    type = 'signal'
                }
                if (type === 'signal') {
                    let currentObj = getSe('signal-bigger');
                    //信号灯根据缩放比绘制
                    nX = Math.round(getRealPosition(points[i].x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                    nY = Math.round(getRealPosition(points[i].y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                    $("#signal" + (i + 1)).css({
                        top: parseInt(points[i].y / pictureH * containerH),
                        left: parseInt(points[i].x / pictureW * containerW)
                    })
                } else {
                    nX = Math.round(points[i].x / containerW * 100);
                    nY = Math.round(points[i].y / containerH * 100)
                }
                $(typePoint[i]).find(".input-number")[0].value = nX;
                $(typePoint[i]).find(".input-number")[1].value = nY;

            }
            drawAll(type, drawCanvas, drawContainer)
        }
    }).off("mousedown").on("mousedown", function (e) {
        let length = document.getElementsByClassName(type + "-can").length;

        if (group) {
            groupNum = parseInt($("#lineNumber option:selected").text());
            groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
            if (groupNum) {
                groupType = ' rect-group';
                groupType += parseInt(parseInt((length / 4)) + 1);
            } else {
                layer.msg(langMessage.drawCanvas.drawMsg.LINE_FIRST, {icon: 2});
                return
            }
        }
        if (length) {
            let p;
            if (type === 'people1' || type === 'people2' || type === 'people3' || type === 'people4' || type === 'people5') {
                p = getGroupCommonPosition(type)
            } else {
                p = getCommonPosition(type);
            }
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            for (let i in p) {
                if (Math.abs(currentX1 - p[i].x) < 8 && Math.abs(currentY1 - p[i].y) < 8) {
                    return
                }
            }
            let K = getK(p);
            distance = computeD(currentX1, currentY1, K);
            //console.log(distance);
            if (distance.status) {
                moveFlag = true;
                moveIndex = distance.index;
                moveX = currentX1 + 10;
                moveY = currentY1 + 10;
                return
            }
            if (length == (groupNum - 1) * 4) {
                layer.msg(langMessage.drawCanvas.drawMsg.ANCHOR_EXCEED, {icon: 2});
                return;
            }
            if (length == 4 && !group) {
                layer.msg(langMessage.drawCanvas.drawMsg.anchorExceedNumber(4), {icon: 2});
                return;
            }
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + (length + 1)
            });
            $("#" + type + length).after(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });

            $(typePoint[length]).find(".input-up").attr("disabled", false);
            $(typePoint[length]).find(".input-down").attr("disabled", false);
            $(typePoint[length]).find(".input-number").removeAttr('disabled');
            $(typePoint[length]).find(".input-number")[0].value = Math.round(currentX1 / containerW * 100);
            $(typePoint[length]).find(".input-number")[1].value = Math.round(currentY1 / containerH * 100);
            drawAll(type, drawCanvas, drawContainer);
        } else {
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + 1
            });
            $("#" + type).html(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            $(typePoint[0]).find(".input-up").attr("disabled", false);
            $(typePoint[0]).find(".input-down").attr("disabled", false);
            $(typePoint[length]).find(".input-number").removeAttr('disabled');
            $(typePoint[0]).find(".input-number")[0].value = Math.round(currentX1 / containerW * 100);
            $(typePoint[0]).find(".input-number")[1].value = Math.round(currentY1 / containerH * 100);
            drawAll(type, drawCanvas, drawContainer);
        }
    });

};

/**
 * 绘制普通线条
 * @param type
 * @param lineNumber
 * @param group
 */
var commonLine = function (type, lineNumber, group) {
    localLog('commonLine')
    $("button").removeClass('layui-btn-normal');
    $(event.currentTarget).addClass('layui-btn-normal');
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;

    let checkB = document.getElementsByClassName("checkbox");
    let lineNum = document.getElementsByClassName("lineNum");
    let currentX1, currentY1;
    let length = document.getElementsByClassName(type + "-can").length;
    let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    let groupNum;
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let lineType = 'common-line';
    let groupType = "";
    let redType = false;
    if (type === 'redStop' && $("#redStopType option:selected").val() === '1') {
        redType = true
    }
    if (group || redType) {
        lineType += '-group'
    }
    let exist = document.getElementById(type);
    let globalInfo = checkGlobal();
    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + lineType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
    }
    $(".control-point button[type=button]").attr("disabled", true);
    let typePoint = $("." + type + "-point");
    hiddenZone(type, drawContainer, globalInfo);
    // drawPath(drawCanvas, drawContainer);
    if (length) {
        for (let i = 0; i < length; i++) {
            for (let j = 0; j < 4; j++) {
                $($("." + type + "-point button[type=button")[4 * i + j]).attr("disabled", false);
            }
        }
    }
    $("#" + type + "Zone").click(function () {
        moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    }).off("mousemove").on("mousemove", function (e) {
        if (moveFlag) {
            //console.log("move X:" + moveX + " Y" + moveY);
            let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
            let distanceY = e.pageY - moveY;
            moveX = e.pageX;
            moveY = e.pageY;
            let doms = $("." + type + "-can" + (parseInt(moveIndex) + 1));
            let points = [];
            //console.log("distance X:" + distanceX + " Y:" + distanceY);
            for (let i = 0; i < doms.length; i++) {
                let p = {};
                let cX = doms[i].offsetLeft + distanceX;
                let cY = doms[i].offsetTop + distanceY;
                if (!checkValue(cX, cY)) {
                    return
                }
                p.x = cX;
                p.y = cY;
                points.push(p)
            }
            //console.log(points);
            let nX, nY;
            for (let i in points) {
                $(doms[i]).css({
                    left: points[i].x,
                    top: points[i].y
                });
                if (type === 'signalBig') {
                    type = 'signal'
                }
                if (type === 'signal') {
                    let currentObj = getSe('signal-bigger');
                    //信号灯根据缩放比绘制
                    nX = parseInt(getRealPosition(points[i].x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                    nY = parseInt(getRealPosition(points[i].y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                    $("#signal" + (i + 1)).css({
                        top: parseInt(points[i].y / pictureH * containerH),
                        left: parseInt(points[i].x / pictureW * containerW)
                    })
                } else {
                    nX = Math.round(points[i].x / containerW * 100);
                    nY = Math.round(points[i].y / containerH * 100)
                }
                $(typePoint[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[0].value = nX;
                $(typePoint[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[1].value = nY;

            }
            drawAll(type, drawCanvas, drawContainer);
            form.render();
        }
    }).off("mousedown").on("mousedown", function (e) {
        let length = document.getElementsByClassName(type + "-can").length;
        if (group || type === 'redStop') {
            groupNum = parseInt($("#lineNumber option:selected").text());
            groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
            if (groupNum) {
                let nowGroup = $("#" + type + "LineNumber").val();
                if (!nowGroup) {
                    nowGroup = 1;
                    $("#" + type + "LineNumber").val(nowGroup);
                    form.render('select');
                }
                groupType = " line-group";
                if (type === 'redStop') {
                    groupType += parseInt(parseInt((length / 2)) + 1);
                } else {
                    groupType += parseInt(parseInt((length / 4)) + 1);
                }
            } else {
                layer.msg(langMessage.drawCanvas.drawMsg.LINE_FIRST, {icon: 2});
                return
            }

        }
        if (length) {
            let can = 0;
            if (length % 2) {
                can = (length + 1) / 2;
                if (type === 'line') {
                    $("#lineNumber").val(can - 1);
                    $($(".my-colla-item")[can - 1]).removeClass('common-none');
                    $($(".tab-item")[can - 1]).removeClass('common-none1');
                    let carLineNumber = can - 1;
                    $($("#lineNumber option")[carLineNumber]).attr("disabled", false);
                    if (carLineNumber > 0) {

                        $(".redStop-checkbox input:checkbox.redStop-signal").each(function (i) {
                            if (i <= (carLineNumber - 1)) {
                                //$(this).attr('checked', 'checked');
                            }
                        });

                        // $($("#redStopLineNumber option")[carLineNumber]).attr("disabled", false);
                        let redType = $("#redStopType option:selected").val();
                        if (redType === '1') {
                            let groupNum = parseInt($("#lineNumber option:selected").text());
                            groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
                            $("#redStopBtn").attr('onclick', "commonLine('redStop'," + parseInt(groupNum - 1) + ",false)");
                            for (let i = 0; i < 5; i++) {
                                if (i < (groupNum - 1)) {
                                    $(".redStop-group" + (i + 1)).removeClass("common-none")
                                } else {
                                    $(".redStop-group" + (i + 1)).addClass("common-none")
                                }
                            }
                        }
                        form.render();
                    }
                    if ((length + 1) >= 4) {
                        let che = document.getElementsByClassName("check" + (can - 1));
                        for (let j = 0; j < 4; j++) {
                            $(che[j]).attr("disabled", false);
                            $(che[j]).prop("checked", false);
                        }
                        $(checkB[(can - 2)]).removeClass("dis");
                        $(lineNum[(can - 1)]).removeClass("dis");
                        let redType = $("#redStopType option:selected");
                        if (redType === '1') {
                            let groupNum = parseInt($("#lineNumber option:selected").text());
                            groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
                            $("#redStopBtn").attr('onclick', "commonLine('redStop'," + parseInt(groupNum - 1) + ",false)");
                        }
                    }
                    form.render();

                }
                if (type === 'redStop') {
                    if ($("#redStopType").val() == 1) {
                        for (let i = 0; i < can; i++) {
                            $(".redStop-signal")[i].checked = true;
                        }
                    } else {
                        $(".redStop-signal")[0].checked = true;
                    }
                    form.render();
                }
            } else {
                can = (length / 2 + 1);
            }
            let p;
            if (type === 'floor1' || type === 'floor2' || type === 'floor3' || type === 'floor4' || type === 'floor5') {
                p = getGroupCommonLinePosition(type)
            } else {
                p = getCommonLinePosition(type);
            }

            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            for (let i in p) {
                if (p[i].length) {
                    for (let j in p[i]) {
                        if (p[i][j].length) {
                            for (let k in p[i][j]) {
                                if (Math.abs(currentX1 - p[i][j][k].x) < 8 && Math.abs(currentY1 - p[i][j][k].y) < 8) {
                                    return
                                }
                            }
                        } else {
                            if (Math.abs(currentX1 - p[i][j].x) < 8 && Math.abs(currentY1 - p[i][j].y) < 8) {
                                return
                            }
                        }
                    }
                } else {
                    if (Math.abs(currentX1 - p[i].x) < 8 && Math.abs(currentY1 - p[i].y) < 8) {
                        return
                    }
                }
            }
            let K = getK(p);
            distance = computeD(currentX1, currentY1, K);
            //console.log(distance);
            if (distance.status) {
                moveFlag = true;
                moveIndex = distance.index;
                moveX = currentX1 + 10;
                moveY = currentY1 + 10;
                return
            }
            if (length === lineNumber * 2) {
                layer.msg(langMessage.drawCanvas.drawMsg.lineExceedNumber(lineNumber), {icon: 2});
                return;
            }
            if (length === (groupNum - 1) * 4) {
                layer.msg(langMessage.drawCanvas.drawMsg.ANCHOR_EXCEED, {icon: 2});
                return;
            }
            let lineDiv = $('<div></div>');
            if (length === 1) {
                can = 1;
            }
            lineDiv.attr({
                "class": "mini-box " + type + "-can " + type + "-can" + can + groupType,
                "id": type + (length + 1)
            });
            $("#" + type + length).after(lineDiv);
            $("#" + lineDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            $(typePoint[length]).find(".input-up").attr("disabled", false);
            $(typePoint[length]).find(".input-down").attr("disabled", false);
            $(typePoint[length]).find(".input-number").removeAttr('disabled');
            $(typePoint[length]).find(".input-number")[0].value = Math.round(currentX1 / containerW * 100);
            $(typePoint[length]).find(".input-number")[1].value = Math.round(currentY1 / containerH * 100);

            drawAll(type, drawCanvas, drawContainer);
        } else {
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            let lineDiv = $('<div></div>');
            lineDiv.attr({
                "class": "mini-box " + type + "-can " + type + "-can1" + groupType,
                "id": type + "1"
            });
            $("#" + type).html(lineDiv);
            $("#" + lineDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            $(typePoint[0]).find(".input-up").attr("disabled", false);
            $(typePoint[0]).find(".input-down").attr("disabled", false);
            $(typePoint[length]).find(".input-number").removeAttr('disabled');
            $(typePoint[0]).find(".input-number")[0].value = Math.round(currentX1 / containerW * 100);
            $(typePoint[0]).find(".input-number")[1].value = Math.round(currentY1 / containerH * 100);
            drawAll(type, drawCanvas, drawContainer)
        }

    })
    if (type === 'upCalibration' || type === 'downCalibration') {
        if ($("#upCalibration-signal").prop("checked")) {
            $(".upCalibration-queueLength button").removeAttr("disabled");
        }
        if ($("#downCalibration-signal").prop("checked")) {
            $(".downCalibration-queueLength button").removeAttr("disabled");
        }
    }
};
/**
 * 绘制四边形
 * @param type
 * @param lineType
 * @param ampli
 * @constructor
 */
var FixedRect = function (type, lineType, ampli) {
    localLog('FixedRect')
    clearTime();
    let drawCanvas, drawContainer, btnStatus;
    $("button").removeClass('layui-btn-normal');
    if (type === 'plate') {//绘制车牌的已弃用
        // drawCanvas = "surveyCan";
        // drawContainer = "drawSurvey";
        drawCanvas = "lineSurveyCan";
        drawContainer = "drawLineSurvey";
        $("#biggerBtn").addClass("layui-btn-normal");
    } else {
        $(event.currentTarget).addClass('layui-btn-normal');
        drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
        drawContainer = $("#drawContent").find(".draw-container")[0].id;
        btnStatus = $(event.currentTarget);
        let s = btnStatus.html().trim();
        if (s === langMessage.drawCanvas.recover && $("#biggerZone").css("display") === 'none') {
            btnStatus.html(langMessage.drawCanvas.amplify);
        }
    }

    if (type !== "plate") {
        $("#amplifyImg").remove();
    }

    let exist = document.getElementById(type);
    let globalInfo = checkGlobal();
    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point fixed-rect " + lineType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
    }
    let clickFlag = false;
    hiddenZone(type, drawContainer, globalInfo);
    // drawPath(drawCanvas, drawContainer);
    // $("#plate").css('visibility', 'visible');
    var currentX1, currentY1;
    $("#" + type + "Zone").click(function (e) {
        clickFlag = false;
        let ampliFlag = false;
        if ($("#biggerZone").css("display") === 'block') {
            ampliFlag = true;
        }
        if (ampliFlag && $(".bigger-can").length) {
            Amplify(ampli);
        }
    }).off("mousedown").on("mousedown", function (e) {
        if (handleDoubleClick()) {
            hiddenZone("test", drawContainer, globalInfo);
            $("button").removeClass('layui-btn-normal');
            $("#biggerBtn").html(langMessage.drawCanvas.amplify);
            $("#" + type).html("");
            clickFlag = false;
            drawPath(drawCanvas, drawContainer);
            return
        }
        clickFlag = true;
        let length = document.getElementsByClassName(type + "-can").length;
        if (length >= 2) {
            $("#" + type).html("");
            clickFlag = false;
            drawPath(drawCanvas, drawContainer);
            return
        }

        currentX1 = e.offsetX;
        currentY1 = e.offsetY;
        let plateDiv = $('<div></div>');
        plateDiv.attr({
            "class": "mini-box " + type + "-can " + type + "-can1",
            "id": type + "1"
        });
        $("#" + type).html(plateDiv);
        $("#" + plateDiv[0].id).css({
            top: currentY1,
            left: currentX1
        });
        let plateDiv2 = $('<div></div>');
        plateDiv2.attr({
            "class": "mini-box " + type + "-can " + type + "-can2",
            "id": type + "2"
        });
        $("#" + type + "1").after(plateDiv2);

    }).off("mousemove").on("mousemove", function (e) {
        // console.log("mousemove")
        if (clickFlag) {
            currentX1 = e.offsetX;
            currentY1 = e.offsetY;
            $("#" + type + "2").css({
                top: currentY1,
                left: currentX1
            });
            drawAll(type, drawCanvas, drawContainer);
        }
    }).mouseleave(function () {
        if (clickFlag) {
            clickFlag = false;
            let ampliFlag = false;
            if ($("#biggerZone").css("display") === 'block') {
                ampliFlag = true;
            }
            if (ampliFlag && $(".bigger-can").length) {
                Amplify(ampli);
            }
        }
        // console.log("..out");
    });
    drawPath(drawCanvas, drawContainer)
};
/**
 * 绘制四边形组
 * @param type
 * @param group
 * @constructor
 */
var FixedRectGroup = function (type, group) {
    localLog('FixedRectGroup')
    let amplifyImg = document.getElementById("amplifyImg");
    if (!amplifyImg) {
        layer.msg(langMessage.drawCanvas.drawMsg.AMPLIFY_FIRST, {icon: 2});
        return;
    }
    $("button").removeClass('layui-btn-normal');
    $(event.currentTarget).addClass('layui-btn-normal');
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let rectType = 'fixed-rect';
    let currentX1, currentY1;
    let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    let groupNum;
    let length = document.getElementsByClassName(type + "-can").length;
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let groupType = "";
    if (group) {
        rectType += '-group'
    }
    let exist = document.getElementById(type);
    let globalInfo = checkGlobal();
    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
        if (type === 'signalBig') {
            let newDiv2 = $("<div></div>");
            newDiv2.attr({
                "class": "point " + rectType,
                "id": 'signal'
            });
            newDiv2.css({
                'width': globalInfo.w,
                'height': globalInfo.h
            });
            $("#" + drawContainer).append(newDiv2);
            let newClickDive2 = $("<div></div>");
            newClickDive2.attr({
                "class": "clickZone",
                "id": "signalZone"
            });
            newClickDive2.css({
                'width': globalInfo.w,
                'height': globalInfo.h
            });
            $("#" + drawContainer).append(newClickDive2);
        }
    }
    hiddenZone(type, drawContainer, globalInfo);
    if (length) {

        for (let i = 0; i < length; i++) {
            for (let j = 0; j < 2; j++) {
                $($("." + type + "-point button[type=button")[2 * i + j]).attr("disabled", false);
            }
            let currentObj = getSe('signal-bigger');
            let x = Math.round($($(".signal-point")[i]).find(".input-number")[0].value);
            let y = Math.round($($(".signal-point")[i]).find(".input-number")[1].value);
            if (currentObj.x > x || currentObj.y > y) {
                $("#" + type + (i + 1)).css({
                    display: 'none'
                })
            } else {
                let left = (x - currentObj.x) * (containerW / currentObj.w)
                let top = (y - currentObj.y) * (containerH / currentObj.h)
                $("#" + type + (i + 1)).css({
                    left: left,
                    top: top,
                    display: 'block'
                });

            }
        }
        drawAll(type, drawCanvas, drawContainer);

    }


    $("#" + type + "Zone").click(function () {
        moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    }).off("mousemove").on("mousemove", function (e) {
        if (moveFlag) {
            //console.log("move X:" + moveX + " Y" + moveY);
            let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
            let distanceY = e.pageY - moveY;
            moveX = e.pageX;
            moveY = e.pageY;
            let doms;
            if (type === 'signalBig') {
                doms = $("." + type + "-can.fixed-rect-group" + (parseInt(moveIndex) + 1))
            } else {
                doms = $("." + type + "-can");
            }
            let points = [];
            //console.log("distance X:" + distanceX + " Y:" + distanceY);
            for (let i = 0; i < doms.length; i++) {
                let p = {};
                let cX = doms[i].offsetLeft + distanceX;
                let cY = doms[i].offsetTop + distanceY;
                if (!checkValue(cX, cY)) {
                    return
                }
                p.x = cX;
                p.y = cY;
                points.push(p)
            }
            //console.log(points);
            let nX, nY;
            for (let i in points) {
                $(doms[i]).css({
                    left: points[i].x,
                    top: points[i].y
                });
                // if (type === 'signalBig') {
                //     type = 'signal'
                // }
                let domsId = doms[i].id;
                let domsIndex = domsId.substring(domsId.length - 1, domsId.length);
                if (type === 'signalBig') {
                    let currentObj = getSe('signal-bigger');
                    //信号灯根据缩放比绘制
                    nX = Math.round(getRealPosition(points[i].x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                    nY = Math.round(getRealPosition(points[i].y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                    $("#signal" + domsIndex).css({
                        top: parseInt(nY / pictureH * containerH),
                        left: parseInt(nX / pictureW * containerW)
                    });
                    let t = 'signal';
                    $($("." + t + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[0].value = nX;
                    $($("." + t + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[1].value = nY;
                } else {
                    nX = parseInt(points[i].x / containerW * 100);
                    nY = parseInt(points[i].y / containerH * 100);
                    $($("." + type + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[0].value = nX;
                    $($("." + type + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[1].value = nY;
                }


            }
            drawAll(type, drawCanvas, drawContainer)
        }
    }).off("mousedown").on("mousedown", function (e) {
        let length = document.getElementsByClassName(type + "-can").length;
        let fakeType;
        if (type === 'signalBig') {
            fakeType = 'signal'
        }
        let fakePoint = $("." + fakeType + "-point");
        if (group) {
            groupNum = $("#signalNumber").val();
            if (groupNum > 0) {
                groupType = ' fixed-rect-group';
                groupType += parseInt(parseInt((length / 2)) + 1);
            } else {
                layer.msg(langMessage.drawCanvas.drawMsg.SIGNAL_FIRST, {icon: 2});
                return
            }
        }
        if (length) {
            let p = getGroupFixedPosition(type);
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            for (let i in p) {
                if (p[i].length) {
                    for (let j in p[i]) {
                        if (Math.abs(currentX1 - p[i][j].x) < 8 && Math.abs(currentY1 - p[i][j].y) < 8) {
                            return
                        }
                    }
                }
            }
            // let K = getK(p);
            distance = computeStraight(currentX1, currentY1, p);
            //console.log(distance);
            if (distance.status) {
                moveFlag = true;
                moveIndex = distance.index;
                moveX = currentX1 + 10;
                moveY = currentY1 + 10;
                return
            }
            if (length == (groupNum) * 2) {
                layer.msg(langMessage.drawCanvas.drawMsg.ANCHOR_EXCEED, {icon: 2});
                return;
            }
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + (length + 1)
            });
            $("#" + type + length).after(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            $(fakePoint[length]).find(".input-up").attr("disabled", false);
            $(fakePoint[length]).find(".input-down").attr("disabled", false);
            if (type === 'signalBig') {
                let currentObj = getSe('signal-bigger');
                currentX1 = Math.round(getRealPosition(currentX1, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                currentY1 = Math.round(getRealPosition(currentY1, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                let newDiv = $("<div></div>");
                newDiv.attr({
                    "class": "mini-box " + "signal-can" + groupType,
                    "id": "signal" + (length + 1)
                });
                $("#signal" + length).after(newDiv);
                $("#" + newDiv[0].id).css({
                    top: parseInt(currentY1 / pictureH * containerH) - 5,
                    left: parseInt(currentX1 / pictureW * containerW) - 5
                });

            }
            $(fakePoint[length]).find(".input-number").removeAttr('disabled');
            $(fakePoint[length]).find(".input-number")[0].value = currentX1;
            $(fakePoint[length]).find(".input-number")[1].value = currentY1;
            drawAll(type, drawCanvas, drawContainer);
            drawAll(fakeType, drawCanvas, drawContainer);
        } else {
            currentX1 = Math.round(e.offsetX);
            currentY1 = Math.round(e.offsetY);
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + 1
            });
            $("#" + type).html(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            if (type === 'signalBig') {
                let currentObj = getSe('signal-bigger');
                currentX1 = Math.round(getRealPosition(currentX1, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                currentY1 = Math.round(getRealPosition(currentY1, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                let newDiv = $("<div></div>");
                newDiv.attr({
                    "class": "mini-box " + "signal-can" + groupType,
                    "id": "signal" + (length + 1)
                });
                $("#signal").html(newDiv);
                $("#" + newDiv[0].id).css({
                    top: parseInt(currentY1 / pictureH * containerH) - 5,
                    left: parseInt(currentX1 / pictureW * containerW) - 5
                });
            }
            $(fakePoint[0]).find(".input-number").removeAttr('disabled');
            $(fakePoint[0]).find(".input-up").attr("disabled", false);
            $(fakePoint[0]).find(".input-down").attr("disabled", false);
            $(fakePoint[0]).find(".input-number")[0].value = currentX1;
            $(fakePoint[0]).find(".input-number")[1].value = currentY1;
            drawAll(fakeType, drawCanvas, drawContainer);
            drawAll(type, drawCanvas, drawContainer);
        }
    });
};
var addSu = function () {
    localLog('addSu')
    $("#lineSurvey").css("color", "red");
    $("#lineLeft").css("color", "");
    $("#linePlate").css("color", "");
    $("#suZone").css("display", "block");
    $("#lineZone").css("display", "none");
    $("#plaZone").css("display", "none");
    $("#survey").css('visibility', 'visible');
    $("#line").css('visibility', 'hidden');
    $("#plate").css('visibility', 'hidden');
    $("#rule").css('visibility', 'hidden');
    $("#ruleZone").css("display", "none");
    $("#lineRule").css("color", "");
    let length = document.getElementsByClassName("survey-can").length;
    if (length === 20) {
        $("#addSu").attr("disabled", true);
        return;
    } else {
        $("#addSu").attr("disabled", false);
        $("#reduceSu").attr("disabled", false);
    }
    let suDiv = $('<div></div>');
    suDiv.attr({
        "class": "mini-box survey-can",
        "id": "su" + (length + 1)
    });
    $("#" + suDiv[0].id).css({
        top: 10,
        left: 10
    });
    $("#su" + length).after(suDiv);
    drawAll('survey', 'lineSurvey', '#FF0000');

};
var reduceSu = function () {
    localLog('reduceSu')
    $("#lineSurvey").css("color", "red");
    $("#lineLeft").css("color", "");
    $("#linePlate").css("color", "");
    $("#suZone").css("display", "block");
    $("#lineZone").css("display", "none");
    $("#plaZone").css("display", "none");
    $("#survey").css('visibility', 'visible');
    $("#line").css('visibility', 'hidden');
    $("#plate").css('visibility', 'hidden');
    $("#rule").css('visibility', 'hidden');
    $("#ruleZone").css("display", "none");
    $("#lineRule").css("color", "");
    let length = document.getElementsByClassName("survey-can").length;
    if (length === 3) {
        $("#reduceSu").attr("disabled", true);
        return;
    } else {
        $("#reduceSu").attr("disabled", false);
        $("#addSu").attr("disabled", false);
    }
    $("#su" + length).remove();
    drawAll('survey', 'lineSurvey', '#FF0000');
};

/**
 * 清除组
 * @param type
 * @param groupType
 */
var clearGroup = function (type, groupType) {
    localLog('clearGroup')
    let groupNum = $("#" + type + "LineNumber").val();
    if (!groupNum) {
        layer.msg(langMessage.drawCanvas.drawMsg.NOT_SELECT, {icon: 2});
        return
    }
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    $("#" + type + " ." + groupType + groupNum).css({
        top: 0,
        left: 0
    });
    for (let i = 0; i < 4; i++) {
        let num = 4 * (groupNum - 1) + i;
        $($("." + type + "-point")[num]).find(".input-number")[0].value = 0;
        $($("." + type + "-point")[num]).find(".input-number")[1].value = 0;
    }
    drawPath(drawCanvas, drawContainer)
};
/**
 * 绘制简单文字，目前未使用
 * @param y
 * @param type
 * @param t
 * @param x
 */
var textRect = function (y, type, t, x) {
    localLog('textRect')
    let drawCanvas = 'textCan';
    let drawContainer = 'drawText';
    let fontSize = 16;
    let num = type.substring((type.length - 1), type.length);
    let setSize = $("#FontSize" + num).val();
    if (setSize === '0') {
        fontSize = 14;
    }
    let globalInfo = checkGlobal();
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let rectType = 'common-rect';
    let exist = document.getElementById(type);

    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let text;
        if (type === 'DateEnable1') {
            text = t;
        } else {
            text = $("#" + type + "Name").val();
        }

        let newTextDive = $("<div>" + text + "</div>");
        newTextDive.attr({
            "class": "move-text",
            "id": type + "Text"
        });
        if (x && y) {
            newTextDive.css({
                top: parseInt(containerH * y),
                left: parseInt(containerW * x),
                fontSize: fontSize
            });
        } else {
            newTextDive.css({
                top: parseInt(containerH * y),
                left: parseInt(containerW * 0.1),
                fontSize: fontSize
            });
        }

        $("#" + type).append(newTextDive);
    } else {
        $("#" + type + 'Text').html(t);
        if (x && y) {
            $("#" + type + 'Text').css({
                top: parseInt(containerH * y),
                left: parseInt(containerW * x),
                fontSize: fontSize
            });
        } else {
            $("#" + type + 'Text').css({
                fontSize: fontSize,
            });
        }
    }
    drawText(type, drawCanvas, drawContainer);
};

/**
 * 绘制OSD
 * @param type
 * @param callback
 */
var osdRect = function (type, callback) {
    console.log('osdRect type', type)
    localLog('osdRect')
    let drawCanvas = 'osdCan';
    let drawContainer = 'drawOsd';
    let globalInfo = checkGlobal();
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    // let container = getContainerParam();
    // let containerW = container.containerW;
    // let containerH = container.containerH;
    // let pictureW = container.pictureW;
    // let pictureH = container.pictureH;
    let rectType = 'osd-rect';
    let containerExist = document.getElementById('osd');
    if (!containerExist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": 'osd'
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": "osdZone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
        // 注册osd（拖动叠字在此处）
        registerOsd(drawCanvas, drawContainer, callback);
        $("#osdZone").css({
            display: 'block'
        });
    }
    if (type !== undefined) {
        let exist = document.getElementById('osdText' + type);
        let text = "";
        let osdInfo = getSe('osdInfo');//此时的osd中check=1的不包括新添的
        let fontSizes = getSe('osdConfig').fontSize;
        let osdData = osdInfo.filter(function (item) {
            return item.value === type
        });

        text += osdData[0].title + osdData[0].default;
        let fontSize = getContainerPosition(fontSizes[osdData[0].fontSize], containerW, pictureW);
        let osdPosition = getSe("osdPosition");
        if (!exist) {
            if (osdPosition === 2 && osdData[0].y >= pictureH) {
                osdData[0].y = (osdData[0].y - pictureH);
            }
            let newTextDive = $("<div>" + text + "</div>");
            newTextDive.attr({
                "class": "osd-text",
                "id": 'osdText' + osdData[0].value,
                "data-value": osdData[0].value
            });
            newTextDive.css({
                visibility: 'hidden',
                top: osdData[0].y * containerH / pictureH,
                left: osdData[0].x * containerW / pictureW,
                fontSize: fontSize + 'px'
            });
            $("#osd").append(newTextDive);
        } else {
            if (osdPosition === 2 && osdData[0].y >= pictureH) {
                osdData[0].y = (osdData[0].y - pictureH);
            }
            $("#osdText" + type).html(text);
            $("#osdText" + type).css({
                fontSize: fontSize + 'px',
                top: osdData[0].y * containerH / pictureH,
                left: osdData[0].x * containerW / pictureW
            });
        }
        if (callback) {
            callback(type);
        }
    }
    drawPath(drawCanvas, drawContainer);
};

/**
 * 注册OSD
 * @param drawCanvas
 * @param drawContainer
 * @param callback
 */
var registerOsd = function (drawCanvas, drawContainer, callback) {
    localLog('registerOsd')
    let moveFlag = false, moveX = 0, moveY = 0, distance = {status: false, index: -1}, currentY1, currentX1;
    let nowPoint, value = -1;
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    $("#osdZone").click(function () {
        moveFlag = false, moveX = 0, moveY = 0, distance = {status: false, index: -1};
    }).off("mousemove").on("mousemove", function (e) {
        // console.log('registerOsd mousemove')
        if (moveFlag) {
            //console.log("move X:" + moveX + " Y" + moveY);
            let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
            let pageY = e.pageY;
            let osdInfo = getSe('osdInfo');
            let osdPosition = getSe("osdPosition");
            let osdPositionHeight = getSe("osdPositionHeight");
            let fontSizes = getSe('osdConfig').fontSize;
            if (osdPosition === 2) {
                pageY = pageY - (containerH - osdPositionHeight)
            }
            let distanceY = pageY - moveY;
            //console.log("distance X:" + distanceX + " Y:" + distanceY);
            moveX = e.pageX;
            moveY = pageY;
            nowPoint = $("#osdText" + distance.index)[0];
            let cX = nowPoint.offsetLeft + distanceX;
            let cY = nowPoint.offsetTop + distanceY;

            let x = Math.round(cX * pictureW / containerW);
            let y = Math.round(cY * pictureH / containerH);

            let t = y + fontSizes[osdInfo[distance.index].fontSize]
            let realH = reduceSurplusHeight1(osdInfo, osdPositionHeight, osdPosition);
            if ((osdPosition === 0 || osdPosition === 2) && t > realH) {
                distance.status = false
            } else if (osdPosition === 1 && t > pictureH) {
                distance.status = false
            }
            if (osdPosition === 2) {
                y = parseInt(y) + pictureH
            }
            if (distance.status) {
                nowPoint = $("#osdText" + distance.index)[0];
                $(nowPoint).css({
                    left: cX,
                    top: cY
                });
                $('#osdContainer [name="startX"]')[0].value = x;
                $('#osdContainer [name="startY"]')[0].value = y;
                drawPath(drawCanvas, drawContainer);
                form.render();
            }
        }
    }).off("mousedown").on("mousedown", function (e) {
        // console.log('registerOsd mousedown')
        currentX1 = parseInt(e.offsetX);
        currentY1 = parseInt(e.offsetY);
        let K = getOSDPosition('osd');
        distance = computePointInRect(currentX1, currentY1, K);//获得鼠标点中的是哪个叠字
        // console.log(distance);
        if (distance.status) {
            moveFlag = true;
            moveX = currentX1 + 10;
            moveY = currentY1 + 10;
            if (callback) {
                callback(distance.index);
            }
        }
    }).off("mouseup").on("mouseup", function (e) {
        // console.log('registerOsd mouseup')
        if (distance.status) {
            let osdPosition = getSe("osdPosition");
            nowPoint = $("#osdText" + distance.index)[0];
            value = distance.index;
            let cX = nowPoint.offsetLeft;
            let cY = nowPoint.offsetTop;
            let cW = nowPoint.clientWidth;
            let cH = nowPoint.clientHeight;
            if ((cX + cW) > containerW) {
                cX = containerW - cW;
            }
            if ((cY + cH) > containerH) {
                cY = containerH - cH;
            }
            cX = restrictPosition(cX, containerW, pictureW);
            cY = restrictPosition(cY, containerH, pictureH);
            $(nowPoint).css({
                left: cX,
                top: cY
            });
            let x = Math.round(cX * pictureW / containerW);
            let y = Math.round(cY * pictureH / containerH);
            if (osdPosition === 2) {
                y = y + pictureH
            }
            $('#osdContainer [name="startX"]')[0].value = x;
            $('#osdContainer [name="startY"]')[0].value = y;
            let osdInfo = getSe('osdInfo');
            osdInfo[value].x = x;
            osdInfo[value].y = y;
            setSe('osdInfo', osdInfo);
            drawPath(drawCanvas, drawContainer);
        }

        // console.log('cx:',cX,'cy',cY)
    }).off("mouseleave").on("mouseleave", function (e) {
        // console.log('registerOsd mouseleave')
        if (distance.status) {
            let osdPosition = getSe("osdPosition");
            nowPoint = $("#osdText" + distance.index)[0];
            value = distance.index;
            let cX = nowPoint.offsetLeft;
            let cY = nowPoint.offsetTop;
            let cW = nowPoint.clientWidth;
            let cH = nowPoint.clientHeight;
            if ((cX + cW) > containerW) {
                cX = containerW - cW;
            }
            if ((cY + cH) > containerH) {
                cY = containerH - cH;
            }
            cX = restrictPosition(cX, containerW, pictureW);
            cY = restrictPosition(cY, containerH, pictureH);
            $(nowPoint).css({
                left: cX,
                top: cY
            });
            let x = Math.round(cX * pictureW / containerW);
            let y = Math.round(cY * pictureH / containerH);
            if (osdPosition === 2) {
                y = y + pictureH
            }
            $('#osdContainer [name="startX"]')[0].value = x;
            $('#osdContainer [name="startY"]')[0].value = y;
            let osdInfo = getSe('osdInfo');
            osdInfo[value].x = x;
            osdInfo[value].y = y;
            setSe('osdInfo', osdInfo);
            drawPath(drawCanvas, drawContainer);
        }
        moveFlag = false, moveX = 0, moveY = 0, distance = false;
        // console.log("..out");
    });
};

/**
 * 计算当前鼠标是否在矩形中
 * @param x
 * @param y
 * @param rects
 * @returns {{index: number, status: boolean}}
 */
var computePointInRect = function (x, y, rects) {
    localLog('computePointInRect')
    let status = {index: -1, status: false};
    for (let i = 0; i < rects.length; i++) {
        let rect = rects[i];
        let osdPosition = getSe("osdPosition");
        if (osdPosition === 2) {
            let osdPositionHeight = getSe("osdPositionHeight");
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            rect.y = rect.y - (containerH - osdPositionHeight)
        }
        if ((x >= rect.x && x <= rect.x + rect.w) && (y >= rect.y && y <= rect.y + rect.h)) {
            status.status = true;
            status.index = rect.value;
        }
    }
    return status
};
/**
 * 格式化坐标，根据containerW, pictureW, pix等限制条件，计算position
 * @param position
 * @param containerW
 * @param pictureW
 * @param pix
 * @returns {*}
 */
var restrictPosition = function (position, containerW, pictureW, pix) {
    localLog('restrictPosition')
    if (position < 0) {
        return 0;
    }
    if (position > containerW) {
        return pictureW
    }
    let pixel = 8;
    try {
        pixel = getSe('osdConfig').pixel;
    } catch (e) {
        // console.log(e)
    }
    if (pix) {
        pixel = pix
    }
    let multiple = pictureW / containerW;
    pixel = pixel / multiple;
    position = accMul(parseInt(position / pixel), pixel);
    return position;
};
/**
 * 放大
 * @param type
 * @constructor
 */
var Amplify = function (type) {
    localLog('Amplify')
    $(".point").css({
        'visibility': 'hidden'
    });
    $(".clickZone").css({
        'display': 'none'
    });
    $("#biggerBtn").html(langMessage.drawCanvas.recover);
    let plate;
    let plateCan = document.getElementsByClassName("bigger-can");
    let length = plateCan.length;
    //放大区域由两个点构成
    if (length != 2) {
        layer.msg(langMessage.drawCanvas.drawMsg.AMPLIFY_ERROR, {icon: 2});
    } else if (length == 2) {
        // let configCan = document.getElementById("surveyCan");//车牌放大已弃用
        let configCan = document.getElementById("lineSurveyCan");
        if (!configCan) {
            configCan = document.getElementById('signalCan');
        }
        let cctx = configCan.getContext("2d");
        let globalInfo = checkGlobal();
        cctx.clearRect(0, 0, globalInfo.w, globalInfo.h);
        if (plateCan.length === 2) {
            plate = [];
            for (let i = 0; i < plateCan.length; i++) {
                let position = {
                    x: 0,
                    y: 0
                };
                position.x = plateCan[i].offsetLeft;
                position.y = plateCan[i].offsetTop;
                plate.push(position);
            }
        }
        let muliX, muliY, containerW, containerH, imgSrc;
        let imgInfo = getSe('imgInfo');
        if (imgInfo && imgInfo.src !== '../../img/404.png') {
            muliX = imgInfo.multiple;
            muliY = imgInfo.multiple;
            containerW = imgInfo.containerWidth;
            containerH = imgInfo.containerHeight;
            imgSrc = $('#configImg').attr('src');
        } else {
            muliX = 1;
            muliY = 1;
            containerW = globalInfo.w;
            containerH = globalInfo.h;
            imgSrc = '../../img/404.png'
        }
        let x = parseInt(Math.min(plate[0].x, plate[1].x) * muliX);
        let y = parseInt(Math.min(plate[0].y, plate[1].y) * muliY);
        //取绝对值获取长宽
        let w = parseInt(Math.abs(plate[1].x - plate[0].x) * muliX);
        let h = parseInt(Math.abs(plate[1].y - plate[0].y) * muliY);
        let biggerImg = document.createElement("img");
        let configImg = document.getElementById("configImg");
        biggerImg.setAttribute("crossOrigin", 'image/png');
        biggerImg.src = '../../img/404.png';
        let drawBig = function () {
            cctx.drawImage(
                configImg, //规定要使用的图像、画布或视频。
                x, y, //开始剪切的 x 坐标位置。
                w, h,  //被剪切图像的高度。
                0, 0,//在画布上放置图像的 x 、y坐标位置。
                containerW, containerH  //要使用的图像的宽度、高度
            );
            let amplifyImg = document.getElementById("amplifyImg");
            let canvasImage = configCan.toDataURL("image/png");
            if (amplifyImg) {
                $(amplifyImg).attr('src', canvasImage)
            } else {
                let newDiv = $("<img src='" + canvasImage + "' style='visibility: hidden' id='amplifyImg' data-type='" + type + "' data-w='" + w + "' data-h='" + h + "'/>");
                // $("#surveyCan").after(newDiv);//车牌放大已弃用
                $("#lineSurveyCan").after(newDiv);
                $("#signalCan").after(newDiv);
            }
            let biggerType = {};
            biggerType.muliY = muliY;
            biggerType.muliX = muliX;
            biggerType.x = x;
            biggerType.y = y;
            biggerType.w = w;
            biggerType.h = h;
            setSe(type + '-bigger', biggerType);

        };
        biggerImg.onload = function () {
            drawBig();
            biggerImg.onload = null;
        };
        biggerImg.onerror = function () {
            drawBig();
            biggerImg.onerror = null;
        };
        if (type === 'plate') {
            FixedRect('plate', 'solid');
        }
    }
};

/**
 * 绘制的操作
 * @param id
 * @param drawCanvas
 * @param drawContainer
 */
var drawAll = function (id, drawCanvas, drawContainer) {
    localLog('drawAll')
    drawPath(drawCanvas, drawContainer);
    let miniBoxs = document.getElementsByClassName(id + "-can");
    let flag = false;
    let currentX, currentY;
    let that;
    $("#" + drawContainer).css("z-index", 999);
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let h = containerH;
    let w = containerW;
    // let h = sessionStorage.height ? sessionStorage.height :600;
    for (let i = 0; i < miniBoxs.length; i++) {
        $("#" + miniBoxs[i].id).click(function () {
            flag = false;

        }).off("mousedown").on("mousedown", function (e) {
            console.log('drawAll function:' + miniBoxs[i].id + 'mousedown')
            flag = true; //移动标记
            that = e;
            currentX = e.pageX - $("#" + e.currentTarget.id).position().left;
            currentY = e.pageY - $("#" + e.currentTarget.id).position().top;
        }).off("mouseenter").on("mouseenter", function (e) {
            document.getElementById(miniBoxs[i].id).style.cursor = "pointer"
        });
        $("#" + drawContainer).mousemove(function (e) {
            if (flag) {
                //if(e.pageX>)
                let x = e.pageX - currentX; //移动时根据鼠标位置计算控件左上角的绝对位置
                let y = e.pageY - currentY;
                //坐标超过画布大小
                if (x > w - 5 || y > h - 5 || y < -5 || x < -5) {
                    return;
                } else {
                    console.log('drawAll function:[1]' + that.target.id)
                    $("#" + that.target.id).css({
                        top: y,
                        left: x
                    }); //控件新位置
                    let idName = that.target.id;
                    let currentNum = idName.replace(/[^0-9]/ig, "");
                    currentNum = currentNum - 1;
                    if (id === 'signalBig') {
                        id = 'signal'
                    }
                    if (id === 'signal') {
                        let currentObj = getSe('signal-bigger');
                        //信号灯根据缩放比绘制
                        x = parseInt(getRealPosition(x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
                        y = parseInt(getRealPosition(y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
                        $("#signal" + (currentNum + 1)).css({
                            top: Math.round(y / pictureH * containerH) - 5,
                            left: Math.round(x / pictureW * containerW) - 5
                        })
                    } else {
                        x = Math.round(x / containerW * 100);
                        y = Math.round(y / containerH * 100);
                    }
                    let tempId = id.substring(0, 11);
                    if (tempId !== 'videoSignal') {
                        $($("." + id + "-point")[currentNum]).find(".input-number")[0].value = x;
                        $($("." + id + "-point")[currentNum]).find(".input-number")[1].value = y;
                    }
                    drawPath(drawCanvas, drawContainer);
                }

            }
        }).mouseleave(function () {
            flag = false;
            // console.log("..out");
        });
        // $("#" + drawContainer).on("mouseleave", function () {
        //     flag = false;
        // });
    }
};

/**
 * 获取线的坐标
 * @param type
 * @returns {Array}
 */
var getCommonLinePosition = function (type) {
    localLog('getCommonLinePosition')
    let lineCan = document.getElementsByClassName(type + "-can");
    let line;
    if (lineCan.length % 2 === 0) {
        line = [];
        for (let i = 0; i < lineCan.length / 2; i++) {
            let linePosition = [];
            for (let j = 0; j < 2; j++) {
                let position = {
                    x: 0,
                    y: 0
                };
                position.x = lineCan[j + i * 2].offsetLeft + 5;
                position.y = lineCan[j + i * 2].offsetTop + 5;
                linePosition.push(position);
            }
            line.push(linePosition);
        }
    } else {
        line = [];
        for (let i = 0; i < (lineCan.length - 1) / 2; i++) {
            let linePosition = [];
            for (let j = 0; j < 2; j++) {
                let position = {
                    x: 0,
                    y: 0
                };
                position.x = lineCan[j + i * 2].offsetLeft + 5;
                position.y = lineCan[j + i * 2].offsetTop + 5;
                linePosition.push(position);
            }
            line.push(linePosition);
        }
    }
    return line;
};

/**
 * 获取线组的坐标
 * @param type
 * @returns {Array}
 */
var getGroupCommonLinePosition = function (type) {
    localLog('getGroupCommonLinePosition')
    let lineGroup = [];
    let line = getCommonLinePosition(type);
    for (let i = 0; i < line.length / 2; i++) {
        let group = [];
        if (line[2 * i]) {
            group.push(line[2 * i]);
        }
        if (line[(2 * i) + 1]) {
            group.push(line[(2 * i) + 1]);
        }

        lineGroup.push(group)
    }
    return lineGroup
};

/**
 * 获取形状坐标
 * @param type
 * @returns {Array}
 */
var getCommonPosition = function (type) {
    localLog('getCommonPosition')
    let suCan = document.getElementsByClassName(type + "-can");
    let suPositions = [];
    if (suCan.length) {
        for (let i = 0; i < suCan.length; i++) {
            let position = {
                x: 0,
                y: 0
            };
            position.x = suCan[i].offsetLeft + 5;
            position.y = suCan[i].offsetTop + 5;
            suPositions.push(position);
        }
    }
    return suPositions
};

/**
 * 获取形状组坐标
 * @param type
 * @returns {Array}
 */
var getGroupCommonPosition = function (type) {
    localLog('getGroupCommonPosition')
    let suCan = document.getElementsByClassName(type + "-can");
    let suPositions = [];
    if (suCan.length) {
        for (let i = 0; i < suCan.length / 4; i++) {
            let linePosition = [];
            for (let j = 0; j < 4; j++) {
                let newCan = suCan[j + i * 4];
                if (newCan) {
                    let position = {
                        x: 0,
                        y: 0
                    };
                    position.x = suCan[j + i * 4].offsetLeft + 5;
                    position.y = suCan[j + i * 4].offsetTop + 5;
                    linePosition.push(position);
                }
            }
            suPositions.push(linePosition);
        }
    }
    return suPositions
};

/**
 * 获取矩形坐标
 * @param type
 * @returns {Array}
 */
var getFixedPosition = function (type) {
    localLog('getFixedPosition')
    let plateCan = document.getElementsByClassName(type + "-can");
    let plate = [];
    if (plateCan.length === 2) {

        for (let i = 0; i < plateCan.length; i++) {
            let position = {
                x: 0,
                y: 0
            };
            position.x = plateCan[i].offsetLeft + 5;
            position.y = plateCan[i].offsetTop + 5;
            if (type === 'bigger') {
                position.x = position.x - 5;
                position.y = position.y - 5;
            }
            plate.push(position);
        }
    }
    return plate
};

/**
 * 获取矩形组坐标
 * @param type
 * @returns {Array}
 */
var getGroupFixedPosition = function (type) {
    localLog('getGroupFixedPosition')
    let suCan = document.getElementsByClassName(type + "-can");
    let suPositions = [];
    if (suCan.length) {
        for (let i = 0; i < suCan.length / 2; i++) {
            let linePosition = [];
            for (let j = 0; j < 2; j++) {
                let newCan = suCan[j + i * 2];
                if (newCan) {
                    let position = {
                        x: 0,
                        y: 0
                    };
                    position.x = suCan[j + i * 2].offsetLeft + 5;
                    position.y = suCan[j + i * 2].offsetTop + 5;
                    linePosition.push(position);
                }
            }
            suPositions.push(linePosition);
        }
    }
    return suPositions
};
/**
 * 获取OSD坐标
 * @param type
 * @returns {Array}
 */
var getOSDPosition = function (type) {
    localLog('getOSDPosition')
    let suCan = document.getElementsByClassName(type + '-text');
    let suPositions = [];
    if (suCan.length) {
        for (let i = 0; i < suCan.length; i++) {
            let position = {
                value: -1,
                x: 0,
                y: 0,
                w: 0,
                h: 0,
                text: ""
            };
            if (suCan[i]) {
                position.x = suCan[i].offsetLeft;
                position.y = suCan[i].offsetTop;
                position.w = suCan[i].clientWidth;
                position.h = suCan[i].clientHeight;
                let osdPosition = getSe("osdPosition");
                if (osdPosition === 2) {
                    let osdPositionHeight = getSe("osdPositionHeight");
                    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
                    position.y = containerH - osdPositionHeight + suCan[i].offsetTop;
                }
                position.value = parseInt(getDataset(suCan[i]).value)
            }
            suPositions.push(position);
        }
    }

    return suPositions
};
/**
 * 绘制矩形
 * @param position
 * @param cctx
 * @param color
 * @param line
 * @param amplify
 * @param num
 */
var drawFixedRect = function (position, cctx, color, line, amplify, num) {
    localLog('drawFixedRect')
    if (position.length) {
        cctx.setLineDash([]);
        if (line === "dashed") {
            cctx.setLineDash([25, 5]);
        }
        cctx.beginPath();
        cctx.lineWidth = lineWidth;
        cctx.strokeStyle = color;
        let amplifyImg = document.getElementById("amplifyImg");
        if (amplifyImg && amplifyImg.dataset.type === 'plate') {
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let muliX = amplifyImg.dataset.w / containerW;
            let muliY = amplifyImg.dataset.h / containerH;
            let w = parseInt(Math.abs(position[1].x - position[0].x) * muliX);
            let h = parseInt(Math.abs(position[1].y - position[0].y) * muliY);
            cctx.font = 'bold 15px SimSun';
            cctx.fillStyle = color;
            cctx.fillText(w + 'x' + h, (position[0].x), (position[0].y));
        }
        if (amplify) {
            let currentObj = getSe('signal-bigger')
        }
        if (num !== undefined) {
            cctx.fillStyle = color;
            cctx.fillText(num, (position[0].x), (position[0].y));
        }
        if (position.length === 2) {
            cctx.moveTo(position[0].x, position[0].y);
            cctx.lineTo(position[0].x, position[1].y);
            cctx.lineTo(position[1].x, position[1].y);
            cctx.lineTo(position[1].x, position[0].y);
            cctx.closePath();
            cctx.stroke();
        }
    }
};
/**
 * 绘制形状
 * @param Positions
 * @param cctx
 * @param color
 * @param line
 */
var drawCommonRect = function (Positions, cctx, color, line) {
    localLog('drawCommonRect')
    if (Positions.length) {
        // Positions = sortPosition(Positions);
        cctx.beginPath();
        cctx.lineWidth = lineWidth;
        cctx.setLineDash([]);
        if (line === "dashed") {
            cctx.setLineDash([25, 5]);
        }
        cctx.strokeStyle = color;
        cctx.moveTo(Positions[0].x, Positions[0].y);
        cctx.font = 'bold 15px SimSun';
        cctx.fillStyle = color;
        cctx.fillText('0', (Positions[0].x), (Positions[0].y));
        // cctx.fillText(type, (Positions[0].x), (Positions[0].y));
        for (let i = 1; i < Positions.length; i++) {
            cctx.lineTo(Positions[i].x, Positions[i].y);
            cctx.font = 'bold 15px SimSun';
            cctx.fillStyle = color;
            cctx.fillText(i.toString(), (Positions[i].x), (Positions[i].y));
        }
        cctx.closePath();
        cctx.stroke();
    }
};
/**
 * 绘制线条
 * @param line
 * @param cctx
 * @param color
 * @param lineType
 */
var drawCommonLine = function (line, cctx, color, lineType) {
    localLog('drawCommonLine')
    if (line.length) {
        cctx.beginPath();
        cctx.lineWidth = lineWidth;
        cctx.setLineDash([]);
        if (lineType === "dashed") {
            cctx.setLineDash([25, 5]);
        }
        cctx.strokeStyle = color;
        for (let i = 0; i < line.length; i++) {
            cctx.beginPath();
            cctx.lineWidth = lineWidth;
            cctx.moveTo(line[i][0].x, line[i][0].y);
            cctx.lineTo(line[i][1].x, line[i][1].y);
            cctx.font = 'bold 15px SimSun';
            cctx.fillStyle = color;
            cctx.fillText(i, line[i][0].x, line[i][0].y);
            cctx.stroke();
        }
    }
};
var specialLineType = {
    0: ['solid'],
    1: ['dash'],
    2: ['dash', 'solid'],
    3: ['solid', 'dash']
}
/**
 * 绘制虚实线、实虚线
 * @param line
 * @param cctx
 * @param color
 * @param lineType
 */
var drawSpecialLine = function (line, cctx, color, lineType) {
    localLog('drawSpecialLine')
    if (line.length) {
        cctx.beginPath();
        cctx.lineWidth = lineWidth;
        cctx.setLineDash([]);
        cctx.strokeStyle = color;

        function draw(index, item, t) {
            if (t === "dash") {
                cctx.setLineDash([25, 5]);
            } else {
                cctx.setLineDash([]);
            }
            if (index > 0) {
                item = item.map(item => {
                    return {x: item.x + 5, y: item.y}
                })
            }
            cctx.beginPath();
            cctx.lineWidth = lineWidth;
            cctx.moveTo(item[0].x, item[0].y);
            cctx.lineTo(item[1].x, item[1].y);
            cctx.stroke();
        }

        for (let i = 0; i < line.length; i++) {
            cctx.beginPath();
            cctx.lineWidth = lineWidth;
            let type = lineType[i] || 0;
            type = specialLineType[type];
            for (let j = 0; j < type.length; j++) {
                draw(j, line[i], type[j])
            }
            cctx.font = 'bold 15px SimSun';
            cctx.fillStyle = color;
            cctx.fillText(i, line[i][0].x, line[i][0].y);
            cctx.stroke();
        }
    }
};
/**
 * 绘制OSD框
 * @param p
 * @param cctx
 * @param color
 * @param bgColor
 * @param fontSize
 * @param text
 */
var drawOSDRect = function (p, cctx, color, bgColor, fontSize, text) {
    localLog('drawOSDRect')
    if (p) {
        cctx.beginPath();
        cctx.lineWidth = lineWidth;
        cctx.fillStyle = 'rgba(' + bgColor + ')';
        cctx.rect(p.x, p.y, p.w, p.h);
        cctx.fill();
        cctx.font = 'normal normal normal ' + fontSize + 'px SimHei';
        cctx.fillStyle = 'rgba(' + color + ')';
        cctx.fillText(text, p.x, p.y + (p.h) - (fontSize / 3));
    }
};
/**
 * 绘制所有线条
 * @param canvasId
 * @param container
 */
var drawPath = function (canvasId, container) {
    localLog('drawPath')
    // let multiple = sessionStorage.multiple;
    let suPositions = [];
    let noParkingPosition = [];
    let aroundPosition = [];
    let facePosition = [];
    let line = [];
    let plate = [];
    let configCan = document.getElementById(canvasId);
    let cctx = configCan.getContext("2d");
    let globalInfo = checkGlobal();
    cctx.clearRect(0, 0, globalInfo.w, globalInfo.h);
    //预设颜色
    let surveyColor = defaultColor('surveyColor');
    let noParkingColor = defaultColor('noParkingColor');
    let aroundColor = defaultColor('aroundColor');
    let faceColor = defaultColor('faceColor');
    let lineColor = defaultColor('lineColor');
    let plateColor = defaultColor('plateColor');
    let people1Color = defaultColor('people1Color');
    let people2Color = defaultColor('people2Color');
    let people3Color = defaultColor('people3Color');
    let people4Color = defaultColor('people4Color');
    let people5Color = defaultColor('people5Color');

    let floor1Color = defaultColor('floor1Color');
    let floor2Color = defaultColor('floor2Color');
    let floor3Color = defaultColor('floor3Color');
    let floor4Color = defaultColor('floor4Color');
    let floor5Color = defaultColor('floor5Color');
    let redLightColor = defaultColor('redLightColor');
    let turnLeftColor = defaultColor('turnLeftColor');
    let goStraightColor = defaultColor('goStraightColor');
    let turnRightColor = defaultColor('turnRightColor');
    let cartTurnRightColor = defaultColor('cartTurnRightColor');
    let upCalibrationColor = defaultColor('upCalibrationColor');
    let downCalibrationColor = defaultColor('downCalibrationColor');
    let redStopColor = defaultColor('redStopColor');
    let signalColor = defaultColor('signalColor');
    let biggerColor = defaultColor('biggerColor');
    let allColor = {
        surveyColor: surveyColor,
        noParkingColor: noParkingColor,
        aroundColor: aroundColor,
        faceColor: faceColor,
        lineColor: lineColor,
        plateColor: plateColor,
        people1Color: people1Color,
        people2Color: people2Color,
        people3Color: people3Color,
        people4Color: people4Color,
        people5Color: people5Color,
        floor1Color: floor1Color,
        floor2Color: floor2Color,
        floor3Color: floor3Color,
        floor4Color: floor4Color,
        floor5Color: floor5Color,
        redLightColor: redLightColor,
        turnLeftColor: turnLeftColor,
        goStraightColor: goStraightColor,
        turnRightColor: turnRightColor,
        cartTurnRightColor: cartTurnRightColor,
        upCalibrationColor: upCalibrationColor,
        downCalibrationColor: downCalibrationColor,
        redStopColor: redStopColor,
        signalColor: signalColor,
        biggerColor: biggerColor
    };

    let amplifyImg = document.getElementById("amplifyImg");
    if (amplifyImg) {
        if (amplifyImg.dataset.type === 'plate') {
            cctx.drawImage(amplifyImg, 0, 0);
            let position = getFixedPosition("plate");
            drawFixedRect(position, cctx, allColor['plateColor'], "solid");
            return
        } else if (amplifyImg.dataset.type === 'signal') {
            cctx.drawImage(amplifyImg, 0, 0);
            let positions = getGroupFixedPosition('signalBig');
            let t = $("#signalLineType").val();
            let lineType = t === "" || t === undefined ? 'solid' : t;
            for (let i = 0; i < positions.length; i++) {
                drawFixedRect(positions[i], cctx, allColor['signalColor'], lineType);
            }
            return;
        }
    }

    let allPositions = [];

    let po = $("#" + container + " .point");
    if (po.length > 0) {
        for (let i = 0; i < po.length; i++) {
            let item = po[i];
            let newItem = {};
            let position = [];
            let id = item.id;
            if (id === "plate" || id === 'signalBig') {
                continue
            }
            newItem.id = id;
            let classes = item.className;
            classes = classes.split(" ");
            let type = classes[1];
            if (classes[2]) {
                newItem.lineType = classes[2]
            }
            if (type === 'common-rect') {
                position = getCommonPosition(item.id)
            } else if (type === 'fixed-rect') {
                position = getFixedPosition(item.id)
            } else if (type === 'common-line') {
                position = getCommonLinePosition(item.id)
            } else if (type === "common-rect-group") {
                position = getGroupCommonPosition(item.id)
            } else if (type === "common-line-group") {
                position = getGroupCommonLinePosition(item.id)
            } else if (type === "fixed-rect-group") {
                position = getGroupFixedPosition(item.id)
            } else if (type === 'osd-rect') {
                position = getOSDPosition(item.id)
            }
            newItem.type = type;
            newItem.position = position;
            allPositions.push(newItem);
        }
    }
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    for (let i = 0; i < allPositions.length; i++) {
        let item = allPositions[i];
        let thisColor = item.id + "Color";
        let t = $("#" + item.id + "LineType").val();
        let lineType = t === "" || t === undefined ? 'solid' : t;
        if (item.type === "common-rect") {
            drawCommonRect(item.position, cctx, allColor[thisColor], lineType);
            drawName(item.type, item.id, item.position, cctx, allColor[thisColor])
        } else if (item.type === 'fixed-rect') {
            let tempId = item.id.substring(0, 11);
            if (tempId === 'videoSignal') {
                let num = item.id.substring(11, item.id.length);
                drawFixedRect(item.position, cctx, allColor[thisColor], item.lineType, null, num);
            } else {
                drawFixedRect(item.position, cctx, allColor[thisColor], item.lineType);
            }

        } else if (item.type === "common-line") {
            if (item.id === 'line') {
                drawArrow(cctx, item.position, null, allColor[thisColor]);
                drawLineNumber(cctx, item.position);
                lineType = getLineType();
                drawSpecialLine(item.position, cctx, allColor[thisColor], lineType);
            } else {
                drawCommonLine(item.position, cctx, allColor[thisColor], lineType);
                drawName(item.type, item.id, item.position, cctx, allColor[thisColor])
            }
        } else if (item.type === "common-rect-group") {
            for (let i = 0; i < item.position.length; i++) {
                drawCommonRect(item.position[i], cctx, allColor[thisColor], lineType);
                drawName(item.type, item.id, item.position[i], cctx, allColor[thisColor])
            }
        } else if (item.type === "common-line-group") {
            for (let i = 0; i < item.position.length; i++) {
                drawCommonLine(item.position[i], cctx, allColor[thisColor], lineType);
                drawName(item.type, item.id, item.position[i], cctx, allColor[thisColor])
            }
        } else if (item.type === "fixed-rect-group") {
            let amplify = false;
            if (item.id === 'signal') {
                amplify = true;
            }
            for (let i = 0; i < item.position.length; i++) {
                drawFixedRect(item.position[i], cctx, allColor[thisColor], lineType, amplify, i);
            }
        } else if (item.type === 'osd-rect') {
            let osdPosition = getSe("osdPosition");
            let osdInfo = getSe('osdInfo');
            let fontSizes = getSe('osdConfig').fontSize;
            let defaultText = "";
            let systemValue = getSe('systemValue');
            let deviceInfo = getSe('deviceInfo');
            if (osdPosition !== 1) {
                let osdPositionHeight = getSe("osdPositionHeight");
                let mul = 1;
                if (osdPosition === 2) {
                    mul = -1;
                }
                drawShearImg(cctx, osdPositionHeight * mul, containerW, containerH)
            }
            for (let i = 0; i < item.position.length; i++) {
                let osdData = osdInfo.filter(function (index) {
                    return index.value === item.position[i].value
                });
                defaultText = osdData[0].default;
                if (item.position[i].value === 2) {
                    defaultText = getOsdDate();
                }
                if (item.position[i].value === 7) {
                    defaultText = systemValue['roadDirect']
                }
                if (item.position[i].value === 10) {
                    defaultText = systemValue['roadName']
                }
                if (item.position[i].value === 13) {
                    defaultText = deviceInfo['device_uid']
                }
                let text = osdData[0].title.replace(/\\"/g, "\"") + defaultText;
                let fontSize = getContainerPosition(fontSizes[osdData[0].fontSize], containerW, pictureW);
                drawOSDRect(item.position[i], cctx, osdData[0].fontColor, osdData[0].backColor, fontSize, text)
            }

        }
    }
};
var getOsdDate = function () {
    localLog('getOsdDate')
    let osdConfig = getSe('osdConfig');
    let dateFormat = osdConfig.dateFormat;
    let timeFormat = osdConfig.timeFormat;
    let dateF = getSe('dateInfo');
    let format = "";
    format += dateFormat[dateF.dateFormat];
    if (dateF.timeFormat === 0) {
        format += ' HH:mm:ss.S'
    } else {
        format += ' hh:mm:ss.S tt'
    }
    return new Date().Format(format);
};
var getDateFormat = function () {

};
var drawText = function (id, drawCanvas, drawContainer) {
    localLog('drawText')
    // drawTextPath(drawCanvas, drawContainer);
    let miniBoxs = document.getElementById(id + 'Text');
    let flag = false;
    let currentX, currentY;
    let that;
    $("#" + drawContainer).css("z-index", 999);
    let {containerW, containerH} = getContainerParam();
    let h = containerH;
    let w = containerW;
    $("#" + miniBoxs.id).click(function () {
        flag = false;

    }).off("mousedown").on("mousedown", function (e) {
        flag = true; //移动标记
        that = e;
        currentX = e.pageX - parseInt($("#" + e.currentTarget.id).css("left"));
        currentY = e.pageY - parseInt($("#" + e.currentTarget.id).css("top"));
    });
    $("#" + drawContainer).mousemove(function (e) {
        if (flag) {
            //if(e.pageX>)
            var x = e.pageX - currentX; //移动时根据鼠标位置计算控件左上角的绝对位置
            var y = e.pageY - currentY;
            if (x > w - 5 || y > h - 5 || y < -5 || x < -5) {
                return;
            } else {
                $("#" + that.target.id).css({
                    top: y,
                    left: x
                }); //控件新位置
            }

        }
    }).mouseout(function () {
        // flag = false;
        // console.log("..out");
    });
    $("#" + drawContainer).on("moveout", function () {
        flag = false;
    });
    // }
};

var getTextPosition = function (type) {
    let suCan = document.getElementsByClassName(type);
    let suPositions = [];
    if (suCan.length) {
        for (let i = 0; i < suCan.length; i++) {
            let position = {
                x: 0,
                y: 0,
                id: ""
            };
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            position.x = parseInt(suCan[i].offsetLeft / containerW * 100);
            position.y = parseInt(suCan[i].offsetTop / containerH * 100);
            position.id = suCan[i].id;
            if (position.id !== 'DateEnable1Text') {
                //获取当前文字的index
                let index = position.id.replace(/[^0-9]+/g, '');
                position.index = parseInt(index);
                position.text = $(suCan[i]).html()
            }
            suPositions.push(position);
        }
    }
    return suPositions
};

var GroupRect = function (type, group) {
    $("button").removeClass('layui-btn-normal');
    $(event.currentTarget).addClass('layui-btn-normal');
    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    let rectType = 'common-rect';
    let groupType = "";
    if (group) {
        rectType += '-group'
    }
    $("#amplifyImg").remove();
    drawPath(drawCanvas, drawContainer);
    let exist = document.getElementById(type);
    let globalInfo = checkGlobal();

    if (!exist) {
        let newDiv = $("<div></div>");
        newDiv.attr({
            "class": "point " + rectType,
            "id": type
        });
        newDiv.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            'width': globalInfo.w,
            'height': globalInfo.h
        });
        $("#" + drawContainer).append(newClickDive);
    }
    $(".control-point button[type=button]").attr("disabled", true);
    hiddenZone(type, drawContainer, globalInfo);
    var currentX1, currentY1;
    let length = document.getElementsByClassName(type + "-can").length;
    if (length) {
        for (let i = 0; i < length; i++) {
            for (let j = 0; j < 4; j++) {
                $($("." + type + "-point button[type=button")[4 * i + j]).attr("disabled", false);
            }
        }
    }
    // if (length) {
    //     $("." + type + "-point button[type=button").attr("disabled", false);
    // }
    let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    $("#" + type + "Zone").click(function () {
        moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
    }).off("mousemove").on("mousemove", function (e) {
        if (moveFlag) {
            //console.log("move X:" + moveX + " Y" + moveY);
            let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
            let distanceY = e.pageY - moveY;
            moveX = e.pageX;
            moveY = e.pageY;
            let doms = $("." + type + "-can.rect-group" + (parseInt(moveIndex / 4) + 1));

            let points = [];
            //console.log("distance X:" + distanceX + " Y:" + distanceY);
            for (let i = 0; i < doms.length; i++) {
                let p = {};
                let cX = doms[i].offsetLeft + distanceX;
                let cY = doms[i].offsetTop + distanceY;
                if (!checkValue(cX, cY)) {
                    return
                }
                p.x = cX;
                p.y = cY;
                points.push(p)
            }
            for (let i in points) {
                $(doms[i]).css({
                    left: points[i].x,
                    top: points[i].y
                })
            }
            //console.log(points);
            drawAll(type, drawCanvas, drawContainer)
        }
    }).off("mousedown").on("mousedown", function (e) {
        let length = document.getElementsByClassName(type + "-can").length;
        let groupNum;
        if (group) {
            groupNum = group;
            if (groupNum) {
                groupType = ' rect-group';
                groupType += parseInt(parseInt((length / 4)) + 1);
            }
        }
        if (length) {
            let p;
            p = getGroupCommonPosition(type);
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            for (let i in p) {
                if (Math.abs(currentX1 - p[i].x) < 8 && Math.abs(currentY1 - p[i].y) < 8) {
                    return
                }
            }
            let K = getK(p);
            distance = computeD(currentX1, currentY1, K);
            //console.log(distance);
            if (distance.status) {
                moveFlag = true;
                moveIndex = distance.index;
                moveX = currentX1 + 10;
                moveY = currentY1 + 10;
                return
            }
            if (length == (groupNum - 1) * 4) {
                layer.msg(langMessage.drawCanvas.drawMsg.ANCHOR_EXCEED, {icon: 2});
                return;
            }
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + (length + 1)
            });
            $("#" + type + length).after(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            drawGroupAll(type, drawCanvas, drawContainer);
        } else {
            currentX1 = parseInt(e.offsetX);
            currentY1 = parseInt(e.offsetY);
            let suDiv = $('<div></div>');
            suDiv.attr({
                "class": "mini-box " + type + "-can" + groupType,
                "id": type + 1
            });
            $("#" + type).html(suDiv);
            $("#" + suDiv[0].id).css({
                top: currentY1 - 5,
                left: currentX1 - 5
            });
            drawGroupAll(type, drawCanvas, drawContainer);
        }
    });

};
var drawGroupAll = function (id, drawCanvas, drawContainer) {
    localLog('drawGroupAll')
    drawPath(drawCanvas, drawContainer);
    var miniBoxs = document.getElementsByClassName(id + "-can");
    var flag = false;
    var currentX, currentY;
    var that;
    $("#" + drawContainer).css("z-index", 999);
    let {containerW, containerH} = getContainerParam();
    let h = containerH;
    let w = containerW;
    // let h = sessionStorage.height ? sessionStorage.height :600;
    for (let i = 0; i < miniBoxs.length; i++) {
        $("#" + miniBoxs[i].id).click(function () {
            flag = false;

        }).off("mousedown").on("mousedown", function (e) {
            console.log('drawGroupAll function:[1]')
            flag = true; //移动标记
            that = e;
            currentX = e.pageX - parseInt($("#" + e.currentTarget.id).css("left"));
            currentY = e.pageY - parseInt($("#" + e.currentTarget.id).css("top"));
        });
        $("#" + drawContainer).mousemove(function (e) {
            if (flag) {
                //if(e.pageX>)
                let x = e.pageX - currentX; //移动时根据鼠标位置计算控件左上角的绝对位置
                let y = e.pageY - currentY;
                if (x > w - 5 || y > h - 5 || y < -5 || x < -5) {
                    return;
                } else {
                    $("#" + that.target.id).css({
                        top: y,
                        left: x
                    }); //控件新位置
                    drawPath(drawCanvas, drawContainer);
                }

            }
        }).mouseout(function () {
            // flag = false;
            // console.log("..out");
        });
        $("#" + drawContainer).on("moveout", function () {
            flag = false;
        });
    }
};


let getK = function (p) {
    localLog('getK')
    let Ks = [];
    for (let i in p) {
        i = parseInt(i);
        let k;
        if (p[i].length) {
            k = {};
            if (p[i].length === 4 && !p[i][0].length) {
                for (let j in p[i]) {
                    j = parseInt(j);
                    if (j === p[i].length - 1) {
                        k = {}
                        let tempK = (p[i][j].y - p[i][0].y) / (p[i][j].x - p[i][0].x);
                        k.a = tempK;
                        k.b = -1;
                        k.c = tempK * (-p[i][j].x) + p[i][j].y;
                        Ks.push(k)
                    } else {
                        k = {};
                        let tempK = (p[i][j + 1].y - p[i][j].y) / (p[i][j + 1].x - p[i][j].x);
                        k.a = tempK;
                        k.b = -1;
                        k.c = tempK * (-p[i][j].x) + p[i][j].y;
                        Ks.push(k)
                    }

                }
            } else if (p[i].length === 2 && !p[i][0].length) {
                let tempK = (p[i][1].y - p[i][0].y) / (p[i][1].x - p[i][0].x);
                k.a = tempK;
                k.b = -1;
                k.c = tempK * (-p[i][0].x) + p[i][0].y;
                Ks.push(k)
            } else {
                for (let j in p[i]) {
                    j = parseInt(j);
                    k = {};
                    if (p[i][j].length) {
                        if (p[i][j].length === 2) {
                            let tempK = (p[i][j][1].y - p[i][j][0].y) / (p[i][j][1].x - p[i][j][0].x);
                            k.a = tempK;
                            k.b = -1;
                            k.c = tempK * (-p[i][j][0].x) + p[i][j][0].y;
                            Ks.push(k)
                        }
                    }
                }
            }

        } else {
            if (p.length > 1) {
                if (i === p.length - 1) {
                    k = {};
                    let tempK = (p[i].y - p[0].y) / (p[i].x - p[0].x);
                    k.a = tempK;
                    k.b = -1;
                    k.c = tempK * (-p[i].x) + p[i].y;
                    Ks.push(k)
                } else {
                    k = {};
                    let tempK = (p[i + 1].y - p[i].y) / (p[i + 1].x - p[i].x);
                    k.a = tempK;
                    k.b = -1;
                    k.c = tempK * (-p[i].x) + p[i].y;
                    Ks.push(k)
                }


            }

        }

    }
    return Ks
};
var computeD = function (x, y, k) {
    localLog('computeD')
    let r = {status: false, index: 0};
    for (let i in k) {
        let d = Math.abs(k[i].a * x + k[i].b * y + k[i].c) / Math.sqrt(Math.pow(k[i].a, 2) + Math.pow(k[i].b, 2));
        if (d < 2) {
            r.status = true;
            r.index = i;
        }
    }
    return r;
};
/**
 * 移动的计算
 * @param x
 * @param y
 * @param p
 * @returns {{status: boolean, index: number}}
 */
var computeStraight = function (x, y, p) {
    localLog('computeStraight')
    let r = {status: false, index: 0};
    if (p.length) {
        for (let i in p) {
            if (p[i].length !== 1) {
                let dX = Math.abs(p[i][0].x - p[i][1].x);
                let dY = Math.abs(p[i][0].y - p[i][1].y);
                let sX = 0, sY = 0;
                if (p[i][0].x > p[i][1].x) {
                    sX = 1
                }
                if (p[i][0].y > p[i][1].y) {
                    sY = 1
                }
                if ((Math.abs(x - p[i][0].x) < 2 || Math.abs(x - p[i][1].x) < 2) && y < p[i][sY].y + dY && y > p[i][sY].y) {
                    r.status = true;
                    r.index = i;
                }
                if ((Math.abs(y - p[i][0].y) < 2 || Math.abs(y - p[i][1].y) < 2) && x < p[i][sX].x + dX && x > p[i][sX].x) {
                    r.status = true;
                    r.index = i;
                }
            }

        }
    }
    return r
};
var hiddenZone = function (type, drawContainer, globalInfo) {
    localLog('hiddenZone')
    let points = $("#" + drawContainer + " .point");
    let alreadyHidden = [];
    for (let i = 0; i < points.length; i++) {
        if ($(points[i]).css("display") === 'none') {
            alreadyHidden.push(points[i].id);
        }
    }
    points.css({
        'visibility': 'hidden',
        width: '0px',
        height: '0px'
    });
    $("#" + drawContainer + " .clickZone").css({
        'display': 'none',
        width: '0px',
        height: '0px'
    });
    $("#" + type + "Zone").css({
        'display': 'block',
        'width': globalInfo.w,
        'height': globalInfo.h
    });
    let tempType = type.substring(0, 11);
    if (type !== 'bigger' && type !== 'plate' && tempType !== 'videoSignal') {
        $("#" + type).css({
            'visibility': 'visible',
            'width': globalInfo.w,
            'height': globalInfo.h
        });
    }
    return alreadyHidden;
};
/**
 * @param ctx
 * @param line
 * @param distance 箭头长度相对于中心线长度(0.5)
 * @param color
 * @param sharp 箭头角度(0.2)
 * */
var drawArrow = function (ctx, line, distance, color, sharp) {
    localLog('drawArrow')
    let option = {};
    if (distance) {
        option.size = distance
    }
    if (color) {
        option.color = color
    }
    if (sharp) {
        option.sharp = sharp
    }
    let laneInfo = getLaneDirection()
    for (let i = 1; i < line.length; i++) {
        if (laneInfo[i] === 0) {//上行
            if (line[i][0].y > line[i][1].y) {
                option.sp = {x: line[i][0].x, y: line[i][0].y};
                option.ep = {x: line[i][1].x, y: line[i][1].y};
            } else {
                option.sp = {x: line[i][1].x, y: line[i][1].y};
                option.ep = {x: line[i][0].x, y: line[i][0].y};
            }
        } else if (laneInfo[i] === 1) {//下行
            if (line[i][0].y > line[i][1].y) {
                option.sp = {x: line[i][1].x, y: line[i][1].y};
                option.ep = {x: line[i][0].x, y: line[i][0].y};
            } else {
                option.sp = {x: line[i][0].x, y: line[i][0].y};
                option.ep = {x: line[i][1].x, y: line[i][1].y};
            }
        }
        paint(option, ctx)
    }

};
var paint = function (a, context) {
    let sp = a.sp;
    let ep = {x: (a.ep.x + a.sp.x) / 2, y: (a.sp.y + a.ep.y) / 2};
    if (context == undefined)
        return;
    context.beginPath();
    context.strokeStyle = a.color;
    context.lineWidth = lineWidth;
    //画箭头头部
    let h = _calcH(a, sp, ep, context);
    context.moveTo(ep.x, ep.y);
    context.lineTo(h.h1.x, h.h1.y);
    context.moveTo(ep.x, ep.y);
    context.lineTo(h.h2.x, h.h2.y);
    context.stroke();
};
//计算头部坐标
var _calcH = function (a, sp, ep, context) {
    let theta = Math.atan((ep.x - sp.x) / (ep.y - sp.y));
    let cep = _scrollXOY(ep, -theta);
    let csp = _scrollXOY(sp, -theta);
    let ch1 = {x: 0, y: 0};
    let ch2 = {x: 0, y: 0};
    let l = cep.y - csp.y;
    ch1.x = cep.x + l * (a.sharp || 0.05);
    ch1.y = cep.y - l * (a.size || 0.1);
    ch2.x = cep.x - l * (a.sharp || 0.05);
    ch2.y = cep.y - l * (a.size || 0.1);
    let h1 = _scrollXOY(ch1, theta);
    let h2 = _scrollXOY(ch2, theta);
    return {
        h1: h1,
        h2: h2
    };
};
//旋转坐标
var _scrollXOY = function (p, theta) {
    return {
        x: p.x * Math.cos(theta) + p.y * Math.sin(theta),
        y: p.y * Math.cos(theta) - p.x * Math.sin(theta)
    };
};
var defaultColor = function (ele) {
    let defaultC = getSe('projectConfig')['default_color'];
    let val = $("#" + ele).val();
    let color = defaultC[ele];
    return (val === "" || val === undefined) ? color : val;
};
var getLineMiddlePosition = function (line) {
    let middlePosition = [];
    for (let i = 0; i < line.length - 1; i++) {
        let x0 = getMiddle(line[i][0].x, line[i][1].x);
        let x1 = getMiddle(line[i + 1][0].x, line[i + 1][1].x);
        let middleX = getMiddle(x0, x1);
        let y0 = getMiddle(line[i][0].y, line[i][1].y);
        let y1 = getMiddle(line[i + 1][0].y, line[i + 1][1].y);
        let middleY = getMiddle(y0, y1);
        middlePosition.push({x: middleX, y: middleY});
    }
    return middlePosition;
};
var getMiddle = function (x1, x2) {
    return (x1 + x2) / 2;
};
var drawLineNumber = function (ctx, position) {
    localLog('drawLineNumber')
    let nowPosition = getLineMiddlePosition(position);
    let lineStartNum = 0, lineStart = 0;
    let length = nowPosition.length;
    let lineType = getSe("lineType");
    let laneInfo = lineType.lines
    // let define = getLaneDefine()
    // let special = getLaneSpecial()
    if ($("#drawLine").length || $("#drawLineSurvey").length) {
        lineStartNum = parseInt($("#lineStartNumber").val()) + 1;
        // lineStart = parseInt($("#lineStart").val());
    } else {
        lineStartNum = parseInt(lineType.lineStartNum) + 1;
        // lineStart = parseInt(lineType.lineStart)
    }
    for (let i = 0; i < length; i++) {
        ctx.font = 'bold 15px SimSun';
        ctx.fillStyle = defaultColor('lineNumberColor');
        let nowNumber = 0;
        if (lineStart) {
            nowNumber = parseInt(length - i - 1 + lineStartNum)
        } else {
            nowNumber = parseInt(i + lineStartNum)
        }
        ctx.fillText(nowNumber + langMessage.drawCanvas.line, (nowPosition[i].x - 10), (nowPosition[i].y));
        let specialKey = '', defineKey = ''
        if (laneInfo[i + 1]) {
            let specialCode = laneInfo[i + 1].special
            if (specialCode === 0) {
                specialKey = 'ordinary'
            } else if (specialCode === 1) {
                specialKey = 'emergency'
            } else if (specialCode === 2) {
                specialKey = 'nonMotorized'
            } else if (specialCode === 4) {
                specialKey = 'bus'
            } else if (specialCode === 8) {
                specialKey = 'diversion'
            } else if (specialCode === 16) {
                specialKey = 'noRightTurn'
            } else if (specialCode === 32) {
                specialKey = 'noLefTurn'
            } else if (specialCode === 64) {
                specialKey = 'single'
            }
            let defineCode = laneInfo[i + 1].define
            if (defineCode === 1) {
                defineKey = 'left1'
            } else if (defineCode === 2) {
                defineKey = 'straight'
            } else if (defineCode === 4) {
                defineKey = 'right1'
            } else if (defineCode === 8) {
                defineKey = 'turnAround'
            } else if (defineCode === 3) {
                defineKey = 'leftStraight'
            } else if (defineCode === 6) {
                defineKey = 'rightStraight'
            } else if (defineCode === 9) {
                defineKey = 'turnLeft'
            } else if (defineCode === 5) {
                defineKey = 'leftRight'
            } else if (defineCode === 15) {
                defineKey = 'turnLeftRightStraight'
            } else if (defineCode === 11) {
                defineKey = 'turnLeftStraight'
            } else if (defineCode === 13) {
                defineKey = 'turnLeftRight'
            }
        } else {
            specialKey = 'ordinary'
            defineKey = 'left1'
        }
        ctx.fillText(langMessage.settingLine.clazz[defineKey], (nowPosition[i].x - 10), (nowPosition[i].y + 20));
        ctx.fillText(langMessage.settingLine.clazz[specialKey], (nowPosition[i].x - 10), (nowPosition[i].y + 40));
    }
};

var getLineType = function () {
    let lines = $(".line-type");
    let type = [];
    if (lines.length) {
        for (let j = 0; j < lines.length; j++) {
            let v = $(lines[j]).find("select option:selected").val();
            type.push(v)
        }
    } else {
        let lineType = getSe("lineType");
        type = lineType.lines.map(function (item) {
            return item.type
        })
    }

    return type;
};

var getLaneDirection = function () {
    let lines = $(".line-direction");
    let type = [0];
    if (lines.length) {
        for (let j = 0; j < lines.length; j++) {
            let v = $(lines[j]).val();
            type.push(Number(v))
        }
    } else {
        let lineType = getSe("lineType");
        type = lineType.lines.map(function (item) {
            return item.direction ? item.direction : 0
        })
    }

    return type;
}
var getLaneDefine = function () {
    let lines = $(".line-define");
    let type = [0];
    if (lines.length) {
        for (let j = 0; j < lines.length; j++) {
            let v = $(lines[j]).val();
            type.push(Number(v))
        }
    } else {
        let lineType = getSe("lineType");
        type = lineType.lines.map(function (item) {
            return item.define ? item.define : 0
        })
    }

    return type;
}
var getLaneSpecial = function () {
    let lines = $(".line-special");
    let type = [0];
    if (lines.length) {
        for (let j = 0; j < lines.length; j++) {
            let v = $(lines[j]).val();
            type.push(Number(v))
        }
    } else {
        let lineType = getSe("lineType");
        type = lineType.lines.map(function (item) {
            return item.special ? item.special : 0
        })
    }

    return type;
}

/**
 * 绘制被裁剪的图片
 * @param ctx
 * @param y
 * @param w
 * @param h
 */
var drawShearImg = function (ctx, y, w, h) {
    localLog('drawShearImg')
    let img = document.getElementById("configImg");
    ctx.drawImage(img, 0, y, w, h)
};
/**
 * 绘制文字
 * */
var drawName = function (type, id, position, ctx, color) {
    ctx.font = 'bold 15px SimSun';
    ctx.fillStyle = color;
    if (type === 'common-line' || type === 'common-line-group') {
        if (position.length) {
            let name = ''
            if (id === 'turnLeft') {
                name = '左转线'
            } else if (id === 'goStraight') {
                name = '直行线'
            } else if (id === 'turnRight') {
                name = '右转线'
            } else if (id === 'cartTurnRight') {
                name = '大车右转'
            } else if (id === 'upCalibration') {
                name = '上行标定'
            } else if (id === 'downCalibration') {
                name = '下行标定'
            } else if (id === 'redStop') {
                name = '红灯停止线'
            } else if (id === 'floor1') {
                name = '虚拟地感1'
            } else if (id === 'floor2') {
                name = '虚拟地感2'
            } else if (id === 'floor3') {
                name = '虚拟地感3'
            } else if (id === 'floor4') {
                name = '虚拟地感4'
            } else if (id === 'floor5') {
                name = '虚拟地感5'
            }
            ctx.fillText(name, position[0][0].x + 10, position[0][0].y);
        }
    } else if (type === "common-rect" || type === 'common-rect-group') {
        let name = ''
        if (id === 'survey') {
            name = '目标检测区'
        } else if (id === 'noParking') {
            name = '违停检测区'
        } else if (id === 'people1') {
            name = '礼让行人1'
        } else if (id === 'people2') {
            name = '礼让行人2'
        } else if (id === 'people3') {
            name = '礼让行人3'
        } else if (id === 'people4') {
            name = '礼让行人4'
        } else if (id === 'people5') {
            name = '礼让行人5'
        }
        ctx.fillText(name, position[0].x + 10, position[0].y);
    }
}
// let checkLine = function (lineValue) {
//     //第一根线左右方向
//     let order;
//     // 通过首尾Y坐标计算后一条车道线的首尾点X坐标是否同时大于或小于当前车道线的首尾X坐标
//     if (calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][0].y) < lineValue[1][0].x && calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][1].y) < lineValue[1][1].x) {
//         order = 0;
//     } else if (calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][0].y) > lineValue[1][0].x && calXByY(lineValue[0][0].x, lineValue[0][0].y, lineValue[0][1].x, lineValue[0][1].y, lineValue[1][1].y) > lineValue[1][1].x) {
//         order = 1
//     } else {
//         return -1;
//     }
//     let o;
//     for (let i in lineValue) {
//         //车道线有相交或次序不一致
//         i = parseInt(i);
//         if (i !== lineValue.length - 1) {
//
//             if (calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][0].y) < lineValue[i + 1][0].x && calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][1].y) < lineValue[i + 1][1].x) {
//                 o = 0;
//             } else if (calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][0].y) > lineValue[i + 1][0].x && calXByY(lineValue[i][0].x, lineValue[i][0].y, lineValue[i][1].x, lineValue[i][1].y, lineValue[i + 1][1].y) > lineValue[i + 1][1].x) {
//                 o = 1;
//             } else {
//                 o = -1;
//             }
//             if (order !== o) {
//                 return -1
//             }
//         }
//     }
//     return o;
// };
//
// let calXByY = function (x0, y0, x1, y1, y) {
//     let x;
//     let temp;
//     if (x0 === x1){
//         return x0;
//     }
//     else {
//         temp = (y0 - y1) / (x0 - x1);
//         x = (y - (y0 - ((temp) * x0))) / temp;
//         return x;
//     }
// };


// 改写Canvas方法，这个改写没写完，有空可以改写优化一下
// var commonDraw = function (settings = {}) {
//     let _s = Object.assign({
//         id: '', // string 绘画id
//         type:'common-rect', // string 'common-rect' 'common-rect-group'
//         // 'common-line' 'common-line-group'
//         // 'fixed-rect' 'fixed-rect-group'
//         group: false, // boolean 是否成组
//         groupNumber: () => {
//             return 0
//         }, // 成组值的获取方式
//         number: 0, //  number 矩形框或线段个数
//         lineType: 'solid', // string 'solid' 'dashed' 画线样式
//         move: false, //boolean 可否移动，可移动的是正常图形，不可移动的是固定图形
//         bfEvent:()=>{
//         },
//         bfMousemove: (e) => {
//             return true
//         },
//         bfMousedown: (e) => {
//             return true
//         },
//         bfClick: (e) => {
//             return true
//         },
//         bfMouseleave: (e) => {
//         },
//         afMousemove: (e) => {
//         },
//         afMousedown: (e) => {
//         },
//         afClick: (e) => {
//         },
//         afMouseleave: (e) => {
//         }
//     }, settings);
//     let type = _s.id;
//     if(!_s.move&&!_s.group){
//         _s.number=2;
//     }
//     let rectType = _s.type;
//     let groupType = "";
//     if (_s.group) {
//         rectType += '-group'
//     }
//     $("button").removeClass('layui-btn-normal');
//     $(event.currentTarget).addClass('layui-btn-normal');
//     _s.bfEvent();
//     let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
//     let drawContainer = $("#drawContent").find(".draw-container")[0].id;
//     let currentX1, currentY1;
//     let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
//     let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//     let length = document.getElementsByClassName(type + "-can").length;
//     let globalInfo = checkGlobal();
//     let groupNum = 0;
//     let clickFlag = false;
//     checkContainerExist(type, rectType, globalInfo, drawContainer);
//     $(".control-point button[type=button]").attr("disabled", true);
//     hiddenZone(type, drawContainer, globalInfo);
//
//     _s.bfEvent(length);
//
//     $("#amplifyImg").remove();
//
//     $("#" + type + "Zone").click(function (e) {
//         let bfClick = _s.bfClick(e);
//         if(!bfClick){return}
//         clickFlag=false,moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
//         _s.afClick(e);
//     }).off("mousemove").on("mousemove", function (e) {
//         if (moveFlag) {
//             let bfMove = _s.bfMousemove(e);
//             if(!bfMove){
//                 return
//             }
//             //
//             let points = [];
//             if(_s.move){
//                 //console.log("move X:" + moveX + " Y" + moveY);
//                 let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
//                 let distanceY = e.pageY - moveY;
//                 moveX = e.pageX;
//                 moveY = e.pageY;
//                 let can = "";
//                 if(_s.group){
//                     can = rectType + (parseInt(moveIndex) + 1)
//                 }
//                 if(_s.type==='common-line'){
//                     can = (parseInt(moveIndex) + 1)
//                 }
//                 let doms = $("." + type + "-can" + can);
//                 //console.log("distance X:" + distanceX + " Y:" + distanceY);
//                 for (let i = 0; i < doms.length; i++) {
//                     let p = {};
//                     let cX = doms[i].offsetLeft + distanceX;
//                     let cY = doms[i].offsetTop + distanceY;
//                     if (!checkValue(cX, cY)) {
//                         moveFlag = false;
//                         return
//                     }
//                     console.log("move");
//                     p.x = cX;
//                     p.y = cY;
//                     points.push(p);
//                 }
//                 for (let i in points) {
//                     $(doms[i]).css({
//                         left: points[i].x,
//                         top: points[i].y
//                     });
//                 }
//             }else{
//                 if (clickFlag) {
//                     currentX1 = e.offsetX;
//                     currentY1 = e.offsetY;
//                     $("#" + type + "2").css({
//                         top: currentY1,
//                         left: currentX1
//                     });
//                 }
//             }
//
//             drawAll(type, drawCanvas, drawContainer);
//             _s.afMousemove(points,e);
//         }
//     }).off("mousedown").on("mousedown", function (e) {
//         let bfDown = _s.bfMousedown(e);
//         if(!bfDown){
//             return
//         }
//         clickFlag = true;
//
//         let length = document.getElementsByClassName(type + "-can").length;
//         currentX1 = parseInt(e.offsetX);
//         currentY1 = parseInt(e.offsetY);
//         if(!_s.move){
//             if (length >= 2) {
//                 $("#" + type).html("");
//                 clickFlag = false;
//                 drawPath(drawCanvas, drawContainer);
//                 return
//             }
//             let plateDiv = $('<div></div>');
//             plateDiv.attr({
//                 "class": "mini-box " + type + "-can " + type + "-can1",
//                 "id": type + "1"
//             });
//             $("#" + type).html(plateDiv);
//             $("#" + plateDiv[0].id).css({
//                 top: currentY1,
//                 left: currentX1
//             });
//             let plateDiv2 = $('<div></div>');
//             plateDiv2.attr({
//                 "class": "mini-box " + type + "-can " + type + "-can2",
//                 "id": type + "2"
//             });
//             $("#" + type + "1").after(plateDiv2);
//         }else{
//             if (_s.group) {
//                 groupNum = _s.groupNumber();
//                 if (groupNum) {
//                     groupType = ' rect-group';
//                     groupType += parseInt(parseInt((length / 4)) + 1);
//                 }
//             }
//             let suDiv = $('<div></div>');
//             if (length) {
//                 let p;
//                 if (type === 'people1'||type === 'people2'||type === 'people3'||type === 'people4'||type === 'people5') {
//                     p = getGroupCommonPosition(type)
//                 } else {
//                     p = getCommonPosition(type);
//                 }
//
//                 for (let i in p) {
//                     if (Math.abs(currentX1 - p[i].x) < 8 && Math.abs(currentY1 - p[i].y) < 8) {
//                         return
//                     }
//                 }
//                 let K = getK(p);
//                 distance = computeD(currentX1, currentY1, K);
//                 //console.log(distance);
//                 if (distance.status) {
//                     moveFlag = true;
//                     moveIndex = distance.index;
//                     moveX = currentX1 + 10;
//                     moveY = currentY1 + 10;
//                     return;
//                 }
//                 if (length === _s.number) {
//                     layer.msg('锚点不能超过'+_s.number+'个', {icon: 2});
//                     return;
//                 }
//                 suDiv.attr({
//                     "class": "mini-box " + type + "-can" + groupType,
//                     "id": type + (length + 1)
//                 });
//                 $("#" + type + length).after(suDiv);
//             } else {
//                 suDiv.attr({
//                     "class": "mini-box " + type + "-can" + groupType,
//                     "id": type + 1
//                 });
//                 $("#" + type).html(suDiv);
//             }
//             $("#" + suDiv[0].id).css({
//                 top: currentY1,
//                 left: currentX1
//             });
//             drawAll(type, drawCanvas, drawContainer);
//         }
//         _s.afMousedown(currentX1,currentY1,length,e);
//     }).mouseleave(function (e) {
//         let bfLeave = _s.bfMouseleave(e);
//         if(!bfLeave){return}
//         if (clickFlag) {
//             clickFlag = false;
//         }
//     });
// };
//
//
//
// /**
//  * 绘制普通图形
//  * @param type
//  * @param group
//  * @constructor
//  */
// var CommonRect = function (type, group) {
//     let typePoint = $("." + type + "-point");
//     commonDraw({
//         id:type,
//         type:'common-rect',
//         group:group,
//         move:true,
//         number:4,
//         groupNumber:function(){
//             return parseInt($("#lineNumber option:selected").text());
//         },
//         bfEvent:function(length){
//             if (length) {
//                 for (let i = 0; i < length; i++) {
//                     for (let j = 0; j < 4; j++) {
//                         $($("." + type + "-point button[type=button")[4 * i + j]).attr("disabled", false);
//                     }
//                 }
//             }
//         },
//         afMousemove:function (points,e) {
//             let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//             let nX, nY;
//             for (let i in points) {
//                 if (type === 'signalBig') {
//                     type = 'signal'
//                 }
//                 if (type === 'signal') {
//                     let currentObj = getSe('signal-bigger');
//                     //信号灯根据缩放比绘制
//                     nX = Math.round(getRealPosition(points[i].x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
//                     nY = Math.round(getRealPosition(points[i].y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
//                     $("#signal" + (i + 1)).css({
//                         top: parseInt(points[i].y / pictureH * containerH),
//                         left: parseInt(points[i].x / pictureW * containerW)
//                     })
//                 } else {
//                     nX = Math.round(points[i].x / containerW * 100);
//                     nY = Math.round(points[i].y / containerH * 100)
//                 }
//                 $(typePoint[i]).find(".input-number")[0].value = nX;
//                 $(typePoint[i]).find(".input-number")[1].value = nY;
//
//             }
//         },
//         bfMousedown:function(){
//             let r = true;
//             if(group){
//                 let groupNum = parseInt($("#lineNumber option:selected").text());
//                 if(!groupNum){
//                     layer.msg('请先设置车道线', {icon: 2});
//                     r = false
//                 }
//             }
//             return r
//         },
//         afMousedown:function (x,y,length,e) {
//             let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//             $(typePoint[length]).find(".input-up").attr("disabled", false);
//             $(typePoint[length]).find(".input-down").attr("disabled", false);
//             $(typePoint[length]).find(".input-number").removeAttr('disabled');
//             $(typePoint[length]).find(".input-number")[0].value = Math.round(x / containerW * 100);
//             $(typePoint[length]).find(".input-number")[1].value = Math.round(y / containerH * 100);
//         }
//     });
// };
//
//
// var commonLine = function (type, lineNumber, group) {
//     let typePoint = $("." + type + "-point");
//     commonDraw({
//         id:type,
//         type:'common-line',
//         group:group,
//         move:true,
//         number:lineNumber,
//         groupNumber:function(){
//             return parseInt($("#lineNumber option:selected").text());
//         },
//         bfEvent:function(length){
//             if (length) {
//                 for (let i = 0; i < length; i++) {
//                     for (let j = 0; j < 4; j++) {
//                         $($("." + type + "-point button[type=button")[4 * i + j]).attr("disabled", false);
//                     }
//                 }
//             }
//         },
//         afMousemove:function (points,e) {
//             let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//             let nX, nY;
//             for (let i in points) {
//                 nX = Math.round(points[i].x / containerW * 100);
//                 nY = Math.round(points[i].y / containerH * 100);
//                 $(typePoint[i]).find(".input-number")[0].value = nX;
//                 $(typePoint[i]).find(".input-number")[1].value = nY;
//
//             }
//         },
//         bfMousedown:function(){
//             let r = true;
//             if(group || type === 'redStop'){
//                 let groupNum = parseInt($("#lineNumber option:selected").text());
//                 if(!groupNum){
//                     layer.msg('请先设置车道线', {icon: 2});
//                     r = false
//                 }
//             }
//             return r
//         },
//         afMousedown:function (x,y,length,e) {
//             let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//             $(typePoint[length]).find(".input-up").attr("disabled", false);
//             $(typePoint[length]).find(".input-down").attr("disabled", false);
//             $(typePoint[length]).find(".input-number").removeAttr('disabled');
//             $(typePoint[length]).find(".input-number")[0].value = Math.round(x / containerW * 100);
//             $(typePoint[length]).find(".input-number")[1].value = Math.round(y / containerH * 100);
//         }
//     });
// };
// /**
//  * 绘制四边形
//  * @param type
//  * @param lineType
//  * @param ampli
//  * @constructor
//  */
// var FixedRect = function (type, lineType, ampli) {
//     clearTime();
//     let drawCanvas, drawContainer, btnStatus;
//     $("button").removeClass('layui-btn-normal');
//     if (type === 'plate') {//绘制车牌的已弃用
/*//         drawCanvas = "surveyCan";
//         drawContainer = "drawSurvey";*/
//         drawCanvas = "lineSurveyCan";
//         drawContainer = "drawLineSurvey";
//         $("#biggerBtn").addClass("layui-btn-normal");
//     } else {
//         $(event.currentTarget).addClass('layui-btn-normal');
//         drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
//         drawContainer = $("#drawContent").find(".draw-container")[0].id;
//         btnStatus = $(event.currentTarget);
//         let s = btnStatus.html().trim();
//         // if(s==='局部放大'&&$("#biggerZone").css("display")==='block'){
//         //     btnStatus.html("恢复原图");
//         // }else
//         if (s === '恢复原图' && $("#biggerZone").css("display") === 'none') {
//             btnStatus.html("局部放大");
//         }
//     }
//
//     if (type !== "plate") {
//         $("#amplifyImg").remove();
//     }
//
//     let exist = document.getElementById(type);
//     let globalInfo = checkGlobal();
//     if (!exist) {
//         let newDiv = $("<div></div>");
//         newDiv.attr({
//             "class": "point fixed-rect " + lineType,
//             "id": type
//         });
//         newDiv.css({
//             'width': globalInfo.w,
//             'height': globalInfo.h
//         });
//         $("#" + drawContainer).append(newDiv);
//         let newClickDive = $("<div></div>");
//         newClickDive.attr({
//             "class": "clickZone",
//             "id": type + "Zone"
//         });
//         newClickDive.css({
//             'width': globalInfo.w,
//             'height': globalInfo.h
//         });
//         $("#" + drawContainer).append(newClickDive);
//     }
//     let clickFlag = false;
//     hiddenZone(type, drawContainer, globalInfo);
//     // drawPath(drawCanvas, drawContainer);
//     // $("#plate").css('visibility', 'visible');
//     var currentX1, currentY1;
//     $("#" + type + "Zone").click(function (e) {
//         clickFlag = false;
//         let ampliFlag = false;
//         if ($("#biggerZone").css("display") === 'block') {
//             ampliFlag = true;
//         }
//         if (ampliFlag && $(".bigger-can").length) {
//             Amplify(ampli);
//         }
//     }).off("mousedown").on("mousedown", function (e) {
//         if (handleDoubleClick()) {
//             hiddenZone("test", drawContainer, globalInfo);
//             $("button").removeClass('layui-btn-normal');
//             $("#biggerBtn").html("局部放大");
//             $("#" + type).html("");
//             clickFlag = false;
//             drawPath(drawCanvas, drawContainer);
//             return
//         }
//         clickFlag = true;
//         let length = document.getElementsByClassName(type + "-can").length;
//         if (length >= 2) {
//             $("#" + type).html("");
//             clickFlag = false;
//             drawPath(drawCanvas, drawContainer);
//             return
//         }
//
//         currentX1 = e.offsetX;
//         currentY1 = e.offsetY;
//         let plateDiv = $('<div></div>');
//         plateDiv.attr({
//             "class": "mini-box " + type + "-can " + type + "-can1",
//             "id": type + "1"
//         });
//         $("#" + type).html(plateDiv);
//         $("#" + plateDiv[0].id).css({
//             top: currentY1,
//             left: currentX1
//         });
//         let plateDiv2 = $('<div></div>');
//         plateDiv2.attr({
//             "class": "mini-box " + type + "-can " + type + "-can2",
//             "id": type + "2"
//         });
//         $("#" + type + "1").after(plateDiv2);
//
//     }).off("mousemove").on("mousemove", function (e) {
//         // console.log("mousemove")
//         if (clickFlag) {
//             currentX1 = e.offsetX;
//             currentY1 = e.offsetY;
//             $("#" + type + "2").css({
//                 top: currentY1,
//                 left: currentX1
//             });
//             drawAll(type, drawCanvas, drawContainer);
//         }
//     }).mouseleave(function () {
//         if(clickFlag){
//             clickFlag = false;
//             let ampliFlag = false;
//             if ($("#biggerZone").css("display") === 'block') {
//                 ampliFlag = true;
//             }
//             if (ampliFlag && $(".bigger-can").length) {
//                 Amplify(ampli);
//             }
//         }
//         // console.log("..out");
//     });
//     drawPath(drawCanvas,drawContainer)
// };
// var FixedRectGroup = function (type, group) {
//     let amplifyImg = document.getElementById("amplifyImg");
//     if (!amplifyImg) {
//         layer.msg('请先放大再绘制', {icon: 2});
//         return;
//     }
//     $("button").removeClass('layui-btn-normal');
//     $(event.currentTarget).addClass('layui-btn-normal');
//     let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
//     let drawContainer = $("#drawContent").find(".draw-container")[0].id;
//     let rectType = 'fixed-rect';
//     let currentX1, currentY1;
//     let moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
//     let groupNum;
//     let length = document.getElementsByClassName(type + "-can").length;
//     let {containerW, containerH, pictureW, pictureH} = getContainerParam();
//     let groupType = "";
//     if (group) {
//         rectType += '-group'
//     }
//     // let clickFlag = false;
//     // $("#amplifyImg").remove();
//     let exist = document.getElementById(type);
//     let globalInfo = checkGlobal();
//     if (!exist) {
//         let newDiv = $("<div></div>");
//         newDiv.attr({
//             "class": "point " + rectType,
//             "id": type
//         });
//         newDiv.css({
//             'width': globalInfo.w,
//             'height': globalInfo.h
//         });
//         $("#" + drawContainer).append(newDiv);
//         let newClickDive = $("<div></div>");
//         newClickDive.attr({
//             "class": "clickZone",
//             "id": type + "Zone"
//         });
//         newClickDive.css({
//             'width': globalInfo.w,
//             'height': globalInfo.h
//         });
//         $("#" + drawContainer).append(newClickDive);
//         if (type === 'signalBig') {
//             let newDiv2 = $("<div></div>");
//             newDiv2.attr({
//                 "class": "point " + rectType,
//                 "id": 'signal'
//             });
//             newDiv2.css({
//                 'width': globalInfo.w,
//                 'height': globalInfo.h
//             });
//             $("#" + drawContainer).append(newDiv2);
//             let newClickDive2 = $("<div></div>");
//             newClickDive2.attr({
//                 "class": "clickZone",
//                 "id": "signalZone"
//             });
//             newClickDive2.css({
//                 'width': globalInfo.w,
//                 'height': globalInfo.h
//             });
//             $("#" + drawContainer).append(newClickDive2);
//         }
//     }
//     hiddenZone(type, drawContainer, globalInfo);
//     if (length) {
//
//         for (let i = 0; i < length; i++) {
//             for (let j = 0; j < 2; j++) {
//                 $($("." + type + "-point button[type=button")[2 * i + j]).attr("disabled", false);
//             }
//             let currentObj = getSe('signal-bigger');
//             // let signalValue = JSON.parse(sessionStorage.getItem('signalValue'));
//             let x = Math.round($($(".signal-point")[i]).find(".input-number")[0].value);
//             let y = Math.round($($(".signal-point")[i]).find(".input-number")[1].value);
//             if (currentObj.x > x || currentObj.y > y) {
//                 $("#" + type + (i + 1)).css({
//                     display: 'none'
//                 })
//             } else {
//                 $("#" + type + (i + 1)).css({
//                     left: Math.round((x - currentObj.x) * (containerW / currentObj.w)),
//                     top: Math.round((y - currentObj.y) * (containerH / currentObj.h)),
//                     display: 'block'
//                 });
//
//             }
//         }
//         drawAll(type, drawCanvas, drawContainer);
//         // drawPath(drawCanvas, drawContainer);
//     }
//
//     // $(".control-point button[type=button]").attr("disabled", true);
//
//
//     $("#" + type + "Zone").click(function () {
//         moveFlag = false, moveIndex = 0, moveX = 0, moveY = 0, distance = {status: false, index: 0};
//     }).off("mousemove").on("mousemove", function (e) {
//         if (moveFlag) {
//             //console.log("move X:" + moveX + " Y" + moveY);
//             let distanceX = e.pageX - moveX; //移动时根据鼠标位置计算控件左上角的绝对位置
//             let distanceY = e.pageY - moveY;
//             moveX = e.pageX;
//             moveY = e.pageY;
//             let doms;
//             if (type === 'signalBig') {
//                 doms = $("." + type + "-can.fixed-rect-group" + (parseInt(moveIndex) + 1))
//             } else {
//                 doms = $("." + type + "-can");
//             }
//             let points = [];
//             //console.log("distance X:" + distanceX + " Y:" + distanceY);
//             for (let i = 0; i < doms.length; i++) {
//                 let p = {};
//                 let cX = doms[i].offsetLeft + distanceX;
//                 let cY = doms[i].offsetTop + distanceY;
//                 if (!checkValue(cX, cY)) {
//                     return
//                 }
//                 p.x = cX;
//                 p.y = cY;
//                 points.push(p)
//             }
//             //console.log(points);
//             let nX, nY;
//             for (let i in points) {
//                 $(doms[i]).css({
//                     left: points[i].x,
//                     top: points[i].y
//                 });
//                 // if (type === 'signalBig') {
//                 //     type = 'signal'
//                 // }
//                 let domsId = doms[i].id;
//                 let domsIndex = domsId.substring(domsId.length - 1, domsId.length);
//                 if (type === 'signalBig') {
//                     let currentObj = getSe('signal-bigger');
//                     //信号灯根据缩放比绘制
//                     nX = Math.round(getRealPosition(points[i].x, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
//                     nY = Math.round(getRealPosition(points[i].y, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
//                     $("#signal" + domsIndex).css({
//                         top: parseInt(nY / pictureH * containerH),
//                         left: parseInt(nX / pictureW * containerW)
//                     });
//                     let t = 'signal';
//                     $($("." + t + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[0].value = nX;
//                     $($("." + t + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[1].value = nY;
//                 } else {
//                     nX = parseInt(points[i].x / containerW * 100);
//                     nY = parseInt(points[i].y / containerH * 100);
//                     $($("." + type + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[0].value = nX;
//                     $($("." + type + "-point")[parseInt(2 * (parseInt(moveIndex)) + parseInt(i))]).find(".input-number")[1].value = nY;
//                 }
//
//
//             }
//             drawAll(type, drawCanvas, drawContainer)
//         }
//     }).off("mousedown").on("mousedown", function (e) {
//         let length = document.getElementsByClassName(type + "-can").length;
//         let fakeType;
//         if (type === 'signalBig') {
//             fakeType = 'signal'
//         }
//         let fakePoint = $("." + fakeType + "-point");
//         if (group) {
//             groupNum = $("#signalNumber").val();
//             if (groupNum > 0) {
//                 groupType = ' fixed-rect-group';
//                 groupType += parseInt(parseInt((length / 2)) + 1);
//             } else {
//                 layer.msg('请先设置信号灯数量', {icon: 2});
//                 return
//             }
//         }
//         if (length) {
//             let p = getGroupFixedPosition(type);
//             currentX1 = parseInt(e.offsetX);
//             currentY1 = parseInt(e.offsetY);
//             for (let i in p) {
//                 if (p[i].length) {
//                     for (let j in p[i]) {
//                         if (Math.abs(currentX1 - p[i][j].x) < 8 && Math.abs(currentY1 - p[i][j].y) < 8) {
//                             return
//                         }
//                     }
//                 }
//             }
//             // let K = getK(p);
//             distance = computeStraight(currentX1, currentY1, p);
//             //console.log(distance);
//             if (distance.status) {
//                 moveFlag = true;
//                 moveIndex = distance.index;
//                 moveX = currentX1 + 10;
//                 moveY = currentY1 + 10;
//                 return
//             }
//             if (length == (groupNum) * 2) {
//                 layer.msg('锚点数量超出', {icon: 2});
//                 return;
//             }
//             let suDiv = $('<div></div>');
//             suDiv.attr({
//                 "class": "mini-box " + type + "-can" + groupType,
//                 "id": type + (length + 1)
//             });
//             $("#" + type + length).after(suDiv);
//             $("#" + suDiv[0].id).css({
//                 top: currentY1,
//                 left: currentX1
//             });
//             $(fakePoint[length]).find(".input-up").attr("disabled", false);
//             $(fakePoint[length]).find(".input-down").attr("disabled", false);
//             if (type === 'signalBig') {
//                 let currentObj = getSe('signal-bigger');
//                 currentX1 = Math.round(getRealPosition(currentX1, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
//                 currentY1 = Math.round(getRealPosition(currentY1, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
//                 let newDiv = $("<div></div>");
//                 newDiv.attr({
//                     "class": "mini-box " + "signal-can" + groupType,
//                     "id": "signal" + (length + 1)
//                 });
//                 $("#signal" + length).after(newDiv);
//                 $("#" + newDiv[0].id).css({
//                     top: parseInt(currentY1 / pictureH * containerH),
//                     left: parseInt(currentX1 / pictureW * containerW)
//                 });
//
//             }
//             $(fakePoint[length]).find(".input-number").removeAttr('disabled');
//             $(fakePoint[length]).find(".input-number")[0].value = currentX1;
//             $(fakePoint[length]).find(".input-number")[1].value = currentY1;
//             drawAll(type, drawCanvas, drawContainer);
//             drawAll(fakeType, drawCanvas, drawContainer);
//         } else {
//             currentX1 = Math.round(e.offsetX);
//             currentY1 = Math.round(e.offsetY);
//             let suDiv = $('<div></div>');
//             suDiv.attr({
//                 "class": "mini-box " + type + "-can" + groupType,
//                 "id": type + 1
//             });
//             $("#" + type).html(suDiv);
//             $("#" + suDiv[0].id).css({
//                 top: currentY1,
//                 left: currentX1
//             });
//             if (type === 'signalBig') {
//                 let currentObj = getSe('signal-bigger');
//                 currentX1 = Math.round(getRealPosition(currentX1, pictureW, currentObj.x / currentObj.muliX, currentObj.w / currentObj.muliX, containerW));
//                 currentY1 = Math.round(getRealPosition(currentY1, pictureH, currentObj.y / currentObj.muliY, currentObj.h / currentObj.muliY, containerH));
//                 let newDiv = $("<div></div>");
//                 newDiv.attr({
//                     "class": "mini-box " + "signal-can" + groupType,
//                     "id": "signal" + (length + 1)
//                 });
//                 $("#signal").html(newDiv);
//                 $("#" + newDiv[0].id).css({
//                     top: parseInt(currentY1 / pictureH * containerH),
//                     left: parseInt(currentX1 / pictureW * containerW)
//                 });
//             }
//             $(fakePoint[0]).find(".input-number").removeAttr('disabled');
//             $(fakePoint[0]).find(".input-up").attr("disabled", false);
//             $(fakePoint[0]).find(".input-down").attr("disabled", false);
//             $(fakePoint[0]).find(".input-number")[0].value = currentX1;
//             $(fakePoint[0]).find(".input-number")[1].value = currentY1;
//             drawAll(fakeType, drawCanvas, drawContainer);
//             drawAll(type, drawCanvas, drawContainer);
//         }
//     });
// };


var localLog = function (str) {
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        console.log(str)
    }
}
