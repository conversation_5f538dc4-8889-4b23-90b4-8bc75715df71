<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>事件</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_event_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="events" class="title">事件</h2>
    <div class="content">
        <p id="event_text1">
            这个界面可以查看相机抓拍的事件图片和车牌号码，勾选右上角“接收事件”，就会接收相机抓拍的事件图片。左上角点击播放按键，可以实时查看相机画面。
        </p>
        <p id="event_text2">
            左边是车牌特写图，和识别的车牌字符。中间是抓拍的车辆卡口图片和违章图片，如果点击右上角“隐藏图片”，则不会显示图片。下方是抓拍车辆信息，根据时间不断滚动。
        </p>
        <p id="event_text3" class="warning">注：此功能需要安装插件</p>
        <img id="event_image1" src="./image007.png" alt="事件界面"/>
    </div>
</div>
</body>
</html>