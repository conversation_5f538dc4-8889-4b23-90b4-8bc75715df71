<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>事件类型映射测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .event-code {
            font-weight: bold;
            color: #333;
        }
        .event-name {
            color: #666;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>事件类型映射测试</h1>
    <div id="test-results"></div>

    <script>
        // 事件类型映射表
        var eventTypeMap = {
            0: "未知",
            1: "逆向行驶",
            2: "违法停车",
            3: "行人闯入",
            4: "交通阻塞",
            6: "交通事故",
            7: "遗留物",
            8: "卡口数据",
            9: "驶离车道",
            10: "烟火",
            11: "施工/封路",
            12: "压线行驶",
            13: "机动车占用应急车道",
            14: "危险品车",
            19: "非机动车闯入",
            20: "车辆倒车",
            21: "非机动车未戴头盔",
            23: "交通拥挤",
            25: "占用非机动车道",
            26: "连续变道",
            27: "车辆缓行",
            28: "三轮车载人",
            29: "人员聚集",
            30: "客运车辆",
            31: "变道行驶",
            32: "工程车辆",
            33: "变速行驶",
            34: "车道排队",
            35: "工作人员",
            100: "能见度正常",
            101: "能见度低",
            200: "视频丢失",
            202: "视频清晰度异常",
            204: "视频噪声",
            206: "视频冻结",
            208: "视频遮挡",
            210: "镜头移动",
            212: "视频亮度过亮",
            214: "视频亮度过暗",
            301: "路面破损",
            303: "山体滑坡",
            304: "路面积水",
            305: "路面积雪",
            1001: "逆向行驶",
            1002: "违章掉头",
            1003: "机动车闯红灯",
            1004: "压线行驶",
            1005: "违反车速规定",
            1006: "违法变道",
            1007: "借道行驶",
            1008: "违章停车",
            1009: "机动车不在机动车道内行驶",
            1010: "机动车违反规定使用专用车道",
            1011: "路口滞留",
            1012: "在禁止左转路口左转",
            1013: "在禁止右转路口右转",
            1014: "人行道违停",
            1015: "网格线违停",
            1016: "未礼让行人",
            1017: "未交替通行",
            1018: "大弯小转",
            1019: "机动车卡口",
            1020: "左转不让直行",
            1021: "越线停车",
            1022: "在斑马线掉头",
            1023: "掉头不让直行",
            1024: "大车右转未停车让行",
            1025: "连续变道",
            1026: "变道不使用转向灯",
            1027: "转弯不使用转向灯",
            1028: "违法占用应急车道",
            1029: "机动车占用非机动车道",
            1030: "加塞",
            1031: "危化品车",
            1032: "不开车灯",
            1033: "违反尾号限行",
            1034: "外省市小型车闯禁令",
            1035: "沪C号牌闯禁令",
            1036: "教练车闯禁令",
            1037: "货车违反禁令上匝道",
            1038: "黑名单违法类型1",
            1039: "黑名单违法类型2",
            1040: "黑名单违法类型3",
            1041: "黑名单违法类型4",
            1042: "黑名单违法类型5",
            1043: "黑名单违法类型6",
            1044: "黑名单违法类型7",
            1045: "黑名单违法类型8",
            1046: "非机动车闯红灯",
            1047: "非机动车逆行",
            1048: "非机动车闯禁令",
            1049: "非机动车不带头盔",
            1050: "非机动车卡口",
            1051: "非机动车载人",
            1052: "摩托车卡口",
            1053: "摩托车闯禁令",
            1054: "外牌摩托车闯禁令",
            1055: "摩托车闯红灯",
            1056: "摩托车借道行驶",
            1057: "摩托车压线行驶",
            1058: "客车闯禁令",
            1059: "货车闯禁令",
            1060: "货占客",
            1061: "不系安全带",
            1062: "主驾打手机"
        };

        // 根据事件类型代码获取事件类型名称
        function getEventTypeName(eventType) {
            return eventTypeMap[eventType] || "未知事件类型(" + eventType + ")";
        }

        // 测试一些常见的事件类型
        var testCodes = [0, 1, 1001, 1003, 1008, 1019, 1046, 1055, 1061, 9999];
        
        var resultsDiv = document.getElementById('test-results');
        
        testCodes.forEach(function(code) {
            var div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = '<span class="event-code">事件代码: ' + code + '</span>' +
                           '<span class="event-name">=> ' + getEventTypeName(code) + '</span>';
            resultsDiv.appendChild(div);
        });
    </script>
</body>
</html>
