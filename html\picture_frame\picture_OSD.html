<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>pictureOSDs</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">
    <link rel="stylesheet" href="../../css/myTab.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/settingDraw.js"></script>
    <script src="../../js/drawCanvas.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/pictureOSD.js"></script>
    <style>
        .my-select-title {
            padding-right: 40px;
            display: table;
            /*height: 32px;*/
            line-height: 1.3;
            line-height: 38px \9;
            border-width: 1px;
            border-style: solid;
            background-color: #fff;
            border-radius: 2px;
            border-color: #e8e8e8;
            cursor: text;
        }

        .my-select-selected li.my-selected-item {
            height: 24px;
            margin: 3px;
            line-height: 22px;
            position: relative;
            float: left;
            max-width: 99%;
            margin-right: 4px;
            padding: 0 20px 0 10px;
            overflow: hidden;
            color: rgba(0, 0, 0, 0.65);
            background-color: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 2px;
            cursor: default;
        }

        .layui-form-select dl {
            top: auto !important;
        }

        .my-selected-search {
            height: 24px;
            margin-top: 5px;
            line-height: 22px;
        }

        .my-selected-search input {
            width: .75em;
            max-width: 100%;
            border: none;
        }

        .my-selected-search--inline {
            position: static;
            float: left;
            width: auto;
            max-width: 100%;
            padding: 0;
        }

        #view .layui-input-inline {
            width: auto !important;
        }

        .layui-tab-card {
            box-shadow: none;
        }

        .layui-tab-title li {
            background-color: #f2f2f2;
        }

        .osd-text {
            position: absolute;
            font-style: normal;
            font-variant: normal;
            font-weight: normal;
            white-space: nowrap;
        }

        .config-control .layui-form-item .layui-input-inline {
            width: 95px;
        }

        .config-control td[colspan="2"] .layui-form-item .layui-input-inline {
            width: calc(100% - 80px);
        }

        .config-control .layui-form-item .layui-input-inline.inline-color {
            width: 57px;
        }

        .my-select-input {
            width: auto !important;
            max-width: 400px;
        }

        .button-edge {
            margin: 3px 5px;
            cursor: pointer;
        }

        #osdContainer {
            max-width: 500px;
            min-width: 400px;
        }

        #osdContainer table td {
            width: 50%;
        }

        #osdContainer table .layui-form-item {
            width: 100%;
        }

        #osdContainer table .layui-form-label {
            width: 70px;
        }

        #myDrag {
            position: absolute;
            width: 100%;
            height: 5px;
            background-color: #000;
        }

        .resize {
            position: absolute;
            overflow: hidden;
            opacity: 0;
            width: 100%;
            height: 5px;
            cursor: n-resize;
            z-index: 1000
        }

        .resizeB {
            bottom: 0;
        }

        .resizeT {
            top: 0;
        }

        #osdPositionTips:hover {
            cursor: pointer;
        }

    </style>

    <style>

    </style>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-show">
    <div class="layui-row">
        <div class="" id="drawContent">
            <img id="configImg" src="../../img/404.png" class="draw-img"/>
            <canvas id="osdCan" width="600" height="450" class="draw-canvas">
            </canvas>
            <div id="drawOsd" class="draw-container">
                <!--叠字的内容在这里显示，大小覆盖img，结构：
                    <div id="osd">
                        这里为具体各项内容
                    </div>
                -->
            </div>
        </div>
        <div class="layui-col-md4 config-setting  layui-form" style="padding-left: 10px">
            <button class="layui-btn layui-btn-default" id="refreshPic"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">刷新场景
            </button>
            <button class="layui-btn layui-btn-default layui-btn-block" id="saveConfig"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">保存配置
            </button>
            <button class="layui-btn layui-btn-default" id="clearConfig"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">清除叠字
            </button>
            <button class="layui-btn layui-btn-default layui-btn-block" id="resetConfig"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                重置配置
            </button>

            <div class="layui-collapse" style="margin-top: 10px;max-width: 550px;min-width: 450px"
                 lay-filter="osdCollapse">
                <div class="layui-colla-item">
                    <h2 id="doubleStyle" class="layui-colla-title">叠加样式</h2>
                    <div class="layui-colla-content layui-show">
                        <div class="config-control">
                            <table>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="checkOSD" title="OSD使能" id="checkOSD"
                                               lay-skin="primary" value="1" lay-filter="checkOSD">

                                    </td>
                                    <td class="test-version">
                                        <div class="layui-form-item">
                                            <label id="mode" class="layui-form-label">叠加方式：</label>
                                            <div class="layui-input-inline">
                                                <select id="osdPosition" name="position" lay-filter="position">
                                                </select>
                                            </div>
                                        </div>
                                        <!--<div class="layui-form-item main-back-color common-none">-->
                                        <!--<label class="layui-form-label">背景：</label>-->
                                        <!--<div class="layui-input-inline inline-color">-->
                                        <!--<input type="text" value="" id="mainBackColorValue"-->
                                        <!--placeholder="颜色"-->
                                        <!--class="layui-input">-->
                                        <!--</div>-->
                                        <!--<div class="layui-inline" style="left: -11px;">-->
                                        <!--<div id="mainBackColor"></div>-->
                                        <!--</div>-->
                                        <!--</div>-->
                                    </td>
                                    <td>
                                        <i id="osdPositionTips" class="layui-icon layui-icon-help"
                                           style="font-size: 18px; color: #FF5722;" onclick="showTips()"></i>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 id="doubleInfo" class="layui-colla-title">叠加信息</h2>
                    <div class="layui-colla-content test-version">
                        <div class="config-control osd">
                            <div class="layui-form-item" style="width: 100%;">
                                <div class="my-select-container" id="selectContainer">

                                    <label id="doubleText" class="layui-form-label">叠加内容：</label>
                                    <div class="my-select-input layui-input-inline ">
                                        <div class="my-select layui-form-select layui-form">
                                            <div class="my-select-title">
                                                <div class="layui-select-title">
                                                    <div class="my-select-selected">
                                                        <ul>
                                                            <li class="my-selected-search my-selected-search--inline">
                                                                <input autocomplete="off" class="" value="">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <i class="layui-edge"></i>
                                                </div>
                                            </div>
                                            <dl class="my-select-content layui-anim layui-anim-upbit">
                                            </dl>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab layui-tab-card common-none" lay-filter="osdChecked" id="osdContainer">
                                <ul class="layui-tab-title">

                                </ul>
                                <div class="layui-tab-content">
                                    <div class="layui-tab-item">
                                        <div data-value="0" class="layui-form-item" style="width: 100%;">
                                            <table>
                                                <tr>
                                                    <td colspan="2">
                                                        <div class="layui-form-item">
                                                            <label id="title" class="layui-form-label">标题：</label>
                                                            <div class="layui-input-inline">
                                                                <input type="text" name="title" class="layui-input">
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="layui-form-item">
                                                            <label id="font" class="layui-form-label">字体：</label>
                                                            <div class="layui-input-inline inline-color">
                                                                <input id="fontColors" type="text" value=""
                                                                       name="fontColorValue"
                                                                       placeholder="颜色"
                                                                       class="layui-input">
                                                            </div>
                                                            <div class="layui-inline" style="left: -11px;">
                                                                <div id="fontColor"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="main-color">
                                                        <div class="layui-form-item">
                                                            <label id="background" class="layui-form-label">背景：</label>
                                                            <div class="layui-input-inline inline-color">
                                                                <input id="fontColors2" type="text" value=""
                                                                       name="backColorValue"
                                                                       placeholder="颜色"
                                                                       class="layui-input" disabled>
                                                            </div>
                                                            <div class="layui-inline" style="left: -11px;">
                                                                <div id="backColor"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="layui-form-item">

                                                            <label id="xCoordinate"
                                                                   class="layui-form-label">X坐标：</label>
                                                            <div class="layui-input-inline">
                                                                <input type="text" name="startX" autocomplete="off"
                                                                       class="layui-input input-position">
                                                                <i class="button-edge button-up"></i>
                                                                <i class="button-edge button-down"></i>
                                                            </div>

                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="layui-form-item">
                                                            <label id="yCoordinate"
                                                                   class="layui-form-label">Y坐标：</label>
                                                            <div class="layui-input-inline">
                                                                <input type="text" name="startY" autocomplete="off"
                                                                       class="layui-input input-position">
                                                                <i class="button-edge button-up"></i>
                                                                <i class="button-edge button-down"></i>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="layui-form-item">
                                                            <label id="fontSize" class="layui-form-label">字体大小：</label>
                                                            <div class="layui-input-inline">
                                                                <select name="fontSize" lay-filter="fontSize">
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td></td>
                                                    <!--<td class="time-config" style="display: none;">-->
                                                    <!--<div class="layui-form-item">-->
                                                    <!--<label class="layui-form-label">分隔符：</label>-->
                                                    <!--<div class="layui-input-inline">-->
                                                    <!--<select name="separator" lay-filter="separator">-->
                                                    <!--</select>-->
                                                    <!--</div>-->
                                                    <!--</div>-->
                                                    <!--</td>-->
                                                </tr>
                                                <tr class="time-config" style="display: none">
                                                    <td>
                                                        <div class="layui-form-item">
                                                            <label id="dateStyle" class="layui-form-label">日期格式：</label>
                                                            <div class="layui-input-inline">
                                                                <select name="dateFormat" lay-filter="dateFormat">

                                                                </select>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="layui-form-item">
                                                            <label id="timeStyle" class="layui-form-label">时间格式：</label>
                                                            <div class="layui-input-inline">
                                                                <select name="timeFormat" lay-filter="timeFormat">
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
