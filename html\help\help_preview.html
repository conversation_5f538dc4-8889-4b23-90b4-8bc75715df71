<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>预览</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_preview_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="h" class="title">预览</h2>
    <div class="content">
        <p id="h2">
            下载插件，安装插件的时候关闭浏览器，安装完毕打开浏览器输入相机IP。进入预览界面，这个界面就是实时的相机画面。通过这个界面，在安装相机的时候，可以调整相机抓拍的画面角度。
        </p>
        <p id="h3" class="warning">注：此功能需要安装插件</p>
        <img id="h4" src="./image005.png" alt="预览界面"/>
    </div>
</div>
</body>
</html>