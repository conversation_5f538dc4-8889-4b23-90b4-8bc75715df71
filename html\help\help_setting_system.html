<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>系统</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_setting_system_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="jj" class="title">系统</h2>
    <div class="content">
        <p id="jj2">
            这个页面显示系统的主要信息，进入的默认页面为系统信息页面。
        </p>
        <img id="system_img1" src="./image013.png" alt="系统界面">
    </div>
    <h3 id="jj3" class="title">设备信息</h3>
    <div class="content">
        <p id="jj4">
            显示相机的软硬件信息，其中包括设备的各种版本信息，授权状态等，还可以修改设备的名称。
        </p>
        <img id="system_img2" src="./image015.png" alt="设备信息界面"/>
    </div>
    <h3 id="jj5" class="title">设备状态</h3>
    <div class="content">
        <p id="jj6">
            显示相机的信息
        </p>
        <img id="system_img3" src="./image017.png" alt="设备状态界面"/>
    </div>
    <h3 id="jj7" class="title">版本信息</h3>
    <div class="content">
        <p id="jj8">
            显示相机的软件版本
        </p>
        <img id="system_img4" src="./image019.png" alt="版本信息界面"/>
    </div>
    <h3 id="jj9" class="title">设备维护</h3>
    <div class="content">
        <p id="jj10">
            查看相机日志，重启相机，导入导出配置文件等功能。
        </p>
        <img id="system_img5" src="./image021.png" alt="保存日志操作示例"/>

    </div>
    <h3 id="jj14" class="title">本地配置</h3>
    <div class="content">
        <p id="jj15">
            设置图片，录像，日志的保存路径。
        </p>
        <p id="jj16" class="warning">注：此功能需要安装插件</p>
        <img id="system_img6" src="./image023.png" alt="本地配置界面"/>
    </div>
    <h3 id="jj17" class="title">用户</h3>
    <div class="content">
        <p id="jj18">
            功能正在开发中......
        </p>
    </div>
</div>
</body>
</html>