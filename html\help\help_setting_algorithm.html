<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>算法</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title {

        }

        p {
            text-indent: 1cm;
        }

        img {
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_setting_algorithm_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="y" class="title">算法</h2>
    <h3 id="y2" class="title">检测区</h3>
    <div class="content">
        <p id="y3">
            检测区里是配置车牌大小，目标检测区，违停检测区，左直右分界线，掉头检测区，对向直行检测区。点击每个检测区的设置，检测区都有4个蓝色标记表示检测区的范围，可以移动蓝色标记来移动检测区，同时放大和缩小。
        </p>
        <img id="images1" src="./image034.png" alt="检测区界面"/>
        <p id="y6">
            目标检测区：该区域为所有违法类型基础的检测区域设置，用以检测车辆轨迹及号牌的识别结果，该区域正常情况下设置范围不得大于图像的 2/3 大小，否则会导致相机无法运行抓拍。确认检测区域是否设置正常，可通过系统---设备状态，查询运行状态或者直接观察是否能够正常捕获卡口图片为设置依据。
        </p>
        <img id="images2" src="./image035.png" alt="目标检测区">
        <p id="y7">
            违停检测区：违停区域设置，目前主要针对（滞留人行横道线/网格线区域进行设置，路口掉头违法抓拍时，也需根据抓拍要求设置借用人行道掉头的区域范围）， 设置时以实际需要抓拍的人行横道线或网格线大小为依据即可。
        </p>
        <img id="images3" src="./image036.png" alt="违停检测区">
        <p id="y8">
            左直右分界线（左转线橙色，直行线玫红色，右转线绿色）：左转和右转分界线，其目的为检测车辆的左转和右转，故应尽量能涵盖住左转或右转车辆的转向路线，同时，需确保直行车辆不会触碰到这两个转向分界线，应尽量贴合大多数车辆的运动轨迹，在车辆即将左、右转时车头所在的位置处来绘制左、右转触发线。</p><br />
        <p id="y81">   直行触发线，其目的为检测车辆直行及是否闯直行红灯，因此，直行触发线需确保满足以下条件：</p>

        <p id="y9">
            •	应尽量涵盖车辆直行通过路口的路线，确保所有直行车辆都能被检测到；
        </p>
        <p id="y10">
            •	触发线不宜过长，防止左右转车辆触碰到直行触发线；
        </p>
        <p id="y11">
            •	车辆在直行触发线的位置，车牌像素值和图像质量需满足识别要求，主要是横向像素值不能低于80pix。
        </p>
        <img id="images4" src="./image037.png" alt="左直右分界线">
        <p id="y12">
            掉头检测区： 该区域目前主要针对抓拍路口掉头违法类型时，对掉头车辆驶入对向车道后的检测区域进行框选。
        </p>
        <img id="images5" src="./image038.png" alt="掉头检测区">
        <p id="y13">
            对向直行检测区： 该区域目前主要针对抓拍左转不让直行违法类型时，对向行驶车辆的行经轨迹进行设置，具体效果需要根据实际抓拍效果进行调整。
        </p>
        <img id="images6" src="./image039.png" alt="对向直行检测区">
    </div>
    <h3 id="y14" class="title">车道线</h3>
    <div class="content">
        <p id="y15">
            大部分情况下，根据当前车道从左至右，从下至上绘制车道线，并根据实际车道情况定义车道 属性（车道类型、车道数和车道号）。
        </p>
        <p id="y16">
            车道线相关属性：按画面上实际车道画线，从画面下沿到停止线。起始车道可以选择任意车道。从左到右画车道线，车道线1没有属性，车道线2定义左边车道属性“左行”，包括行车方向，车道方向属性，车道属性。车道3定义左边车道属性“右直”。车道4定义左边车道属性“非机动”。
        </p>
        <img id="images7" src="./image040.png" alt="车道线1"/>
        <p id="y17">
            虚拟线圈：该区域主要针对视频车检器功能使用时的，虚拟检测线圈进行设置，每条车道根据实际宽度，在当前车道绘制 2 根检测绊线。紫色线段，从下到上，下面是1线，上面是2线。
        </p>
        <img id="images8" src="./image041.png" alt="虚拟线圈">
        <p id="y18">
            礼让行人：礼让行人检测区域设置时，应遵循从1车道开始从左至右依次绘制的原则进行选取，每个车道检测区域，应尽量保证左右宽度多出半条车道的范围进行设置，用以保证检测行人的移动轨迹范围，便于违法判定。
        </p>
        <img id="images9" src="./image042.png" alt="礼让行人">
        <p id="y19">
            红灯停止线：按照实际停止线画z型或者直线停止线。绘制停车线时，针对第二张违法车辆必须捕获在停车线以后的违法取证要求（闯红灯、导向车道违法等），绘制的停止线应尽量高于实际停车线 1 个车牌像素的距离。
        </p>
        <img id="images10" src="./image043.png" alt="红灯停止线">
    </div>
    <h3 id="y20" class="title">信号灯</h3>
    <div class="content">
        <p id="y21">信号灯信息： <p><br />
        <p id="y22">   （1） 根据实际情况配置“使能红绿灯判断-视频检测”或者“外接红绿灯信号-外接红灯信号检测”；</p><br />
        <p id="y23">   （2）当设置外接红灯信号时，根据红灯检测器的端口接入情况，配置“左、直、右、行人”红灯的接入端口（目前大部分红灯信号左转接端口 1、直行接端口 2、右转接端口 3、行人红灯接端口 4）；</p><br />
        <p id="y24">   （3）闯红灯延时判断，默认是3，根据实际情况调整。</p><br />
        <p id="y25">   （4）红灯信号不明显的场景，可以勾选“红灯加强”，总共4个等级，4级最高。</p><br />
        <p id="y26">   （5）夜晚黄灯时间设置，按照实际黄灯时间设置。</p><br />

        <p id="y27" >
            绘制信号灯：
        </p>
        <p id="y28" >
            点击局部放大，放大信号灯的图像，然后再点击绘制信号灯，检测区包含信号灯。</p><br />
        <p id="y281" >
            在图像画面中分别框出红绿灯，填写相应的配置参数后，摄像机就可以通过实况画面来分析判断红绿灯的实时状态。根据现场需要实际和需要设置的红绿灯数量，设置红灯路数，并在下方正确配置红灯属性。
        </p>
        <p id="y29" >
            信号灯定义：
        </p>
        <p id="y30" >
            按实际情况选择信号灯类型，和信号灯指示方向。对于圆头红灯，应配置类别为“机动车信号灯”、对于箭头灯，应配置类别为“方向指示信号灯”。
        </p>
        <img id="images11" src="./image044.png" alt="事件界面"/>
        <img id="images12" src="./image045.png" alt="事件界面"/>
    </div>
    <h3 id="y31"  class="title">红绿灯状态</h3>
    <div class="content">
        <p id="y32" >
            实时查看相机检测的红绿灯状态。
        </p>
        <img id="images13" src="./image046.png" alt="红绿灯状态界面"/>
    </div>
    <h3 id="y33"  class="title">事件检测</h3>
    <div class="content">
        <p id="y34"  >
            根据前卡或者电警相机，选择需要检测抓拍的事件。所有事件类型，检测条件等选项后面单独说明。
        </p>
        <img id="images14" src="./image047.png" alt="红绿灯状态界面"/>
    </div>
    <h3 id="y35" class="title">系统配置</h3>
    <div class="content">
        <p id="y36" >
            地区汉字代码，地区字母代码，根据相机安装地点，选择当地车牌主要的汉字和字母。
        </p>
        <p id="y37" >
            路口名称，路口方向，道路编号，路口编号可以根据实际道路名称输入。
        </p>
        <p id="y38" >
            速度测试，视频或者雷达测速。相机高度和相机距离检测区的水平距离根据实际距离填写。可以设置每条车道单独的限速大小。
        </p>
        <p id="y39" >
            事件图像编码参数配置，编码品质默认80，数值大图片质量就好，图片大小就大，数值小图片质量就差，图片大小就小，可以根据实际需要选择适当的数值。
        </p>
        <img id="images15" src="./image048.png" alt="系统配置界面"/>
    </div>
    <h3 id="y40"  class="title">交通扩展参数</h3>
    <div class="content">
        <p id="y41" >
            调整相机抓拍事件的各个参数，最好不要改变，推荐使用默认值。
        </p>
        <img id="images16" src="./image049.png" alt="交通扩展参数界面"/>
    </div>
    <h3 id="y42"  class="title">违法代码</h3>
    <div class="content">
        <p id="y43" >
            根据各个地方的要求输入交警平台的违法代码，可以设置违法事件的优先级。
        </p>
        <img id="images17" src="./image050.png" alt="交通扩展参数界面"/>
    </div>
    <h3 id="y44"  class="title">配置预览</h3>
    <div class="content">
        <p id="y45" >
            所有参数修改，保存之后，必须到配置预览界面点击“下发配置”，才可以把修改保存的参数下发到相机。
        </p>
        <img id="images18" src="./image051.png" alt="配置预览界面"/>
    </div>
</div>
</body>
</html>