var element, layer, form;
$(document).ready(function () {
    layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate', 'slider', 'table'], function () {
        element = layui.element, layer = layui.layer, form = layui.form;
        showDevice();
    });
    $("#saveDevice").off("click").on("click", function () {
        let deviceName = $("#board_name").val();
        if (!deviceName) {
            deviceName = "Unknown"
        }
        setSe('deviceName', deviceName);
        layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
    });

    $("#resetDevice").off("click").on("click", function () {
        showDevice()
    });
});
var showDevice = function () {

    // 如果进入页面需要刷新数据
    reloadConfig(getDevice);
    // getDevice();
    let projectConfig = getSe("projectConfig");
    $("#web_version").html(projectConfig.web_version);
    try {
        let version = "";
        let ocxVersion = projectConfig.ocx_version;
        // let indexVersion = projectConfig.index_ocx;
        let indexVersion = projectConfig.newestOcxVersion;
        if (ocxVersion) {
            version = ocxVersion.main + "." + ocxVersion.sub + "." + ocxVersion.revise;
            // if (compareVersion(version, indexVersion.main + "." + indexVersion.sub + "." + indexVersion.revise) !== 0) {
            //     version += '<a id="ocx_down" href="../../setup.exe">' + langMessage.ocxGlobal.update + '</a>'
            // }
            let diff = compareVersion(version, indexVersion.main + "." + indexVersion.sub + "." + indexVersion.revise)
            if (diff < 0) {
                version += '<a id="ocx_down" href="../../setup.exe">' + langMessage.ocxGlobal.update + '</a>'
            } else if (diff > 0) {
                version += '<a style="margin-left: 8px;color:#E6A23C">' + langMessage.ocxGlobal.low + '</a>'
            }
            // if ((ocxVersion.main < indexVersion.main) ||
            //     (ocxVersion.main === indexVersion.main && ocxVersion.sub < indexVersion.sub) ||
            //     (ocxVersion.main === indexVersion.main && ocxVersion.sub === indexVersion.sub && ocxVersion.revise < indexVersion.revise)) {
            //     version += '<a id="ocx_down" href="../../setup.exe">' + langMessage.ocxGlobal.update + '</a>'
            // } else if ((ocxVersion.main > indexVersion.main) ||
            //     (ocxVersion.main === indexVersion.main && ocxVersion.sub > indexVersion.sub) ||
            //     (ocxVersion.main === indexVersion.main && ocxVersion.sub === indexVersion.sub && ocxVersion.revise > indexVersion.revise)) {
            //
            // }
        } else {
            version = '<a style="margin-left: 8px;color:#E6A23C">' + langMessage.ocxGlobal.versionNone + '</a>'
        }
        $("#ocx_version").html(version);
    } catch (e) {
        console.log(e);
        $("#ocx_version").html(langMessage.ocxGlobal.ocxError.ERROR);
    }


};


