<!DOCTYPE html>
<html lang="zh" class="index-html">
<head>
    <meta charset="UTF-8">
    <title>contentEvent</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui/css/layui.css">
    <link rel="stylesheet" href="../css/global.css">
    <!--<link rel="stylesheet" href="../css/event.css">-->

    <script src="../js/dep/polyfill.min.js"></script>
    <script src="../js/dep/jquery-3.3.1.js"></script>
    <script src="../js/cookie.js"></script>
    <script src="../layui/layui.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../js/bufferOperation.js"></script>
    <script src="../js/ocxGlobal.js"></script>
    <script src="../js/webService.js"></script>
    <!--    收事件时不退，所以保活操作放到event.js去做-->
    <!--    <script src="../js/keepAlive.js"></script>-->
    <script src="../js/event.js"></script>
    <style>
        /*.layui-col-md4 {*/
        /*min-width: 400px;*/
        /*}*/

        /*.layui-col-md8 {*/
        /*width: calc(100% - 500px)*/
        /*}*/
        .layui-table tbody tr:hover, .layui-table-click, .layui-table-hover {
            background-color: #dcdcdc !important;
        }

        #eventPicture .layui-table tr > td:nth-child(2), #eventPicture .layui-table tr > td:nth-child(4) {
            width: 60px;
        }

        div[lay-id=eventTable] {
            margin-top: 0;
        }

        #platePic img {
            width: 320px;
            height: 100px;
        }

        .laytable-cell-1-0-2 {
            width: auto !important;
        }

        .laytable-cell-1-0-3 {
            width: auto !important;
        }

        .laytable-cell-1-0-4 {
            width: auto !important;
        }

        .laytable-cell-1-0-5 {
            width: auto !important;
        }

        .laytable-cell-1-0-6 {
            width: auto !important;
        }

        .laytable-cell-1-0-7 {
            width: auto !important;
        }

        .laytable-cell-1-0-24 {
            width: auto !important;
        }

        .magCanvas {
            border-radius: 101px;
            position: fixed;
            border: 5px solid #d3d3d3;
            display: none;
            width: 200px;
            height: 200px;
            z-index: 10001
        }

        .pictureMode_td_eventPic {
            width: 155px;
            height: 75px;
            text-align: center;
            vertical-align: middle;
            padding: 2px 10px
        }

        .pictureMode_td_platePic {
            width: 155px;
            height: 30px;
            text-align: center;
            vertical-align: middle;
            padding: 2px 10px
        }

        .pictureMode_td_plateNum {
            width: 155px;
            height: 30px;
            text-align: center;
            vertical-align: middle;
            padding: 2px 10px
        }

        .right-event-info-div {
            background: #777777;
            border: 1px solid #f6f6f6;
        }

        .right-event-info-table tr td {
            padding: 10px 5px;
            font-size: 14px;
            color: #f6f6f6;
            white-space: nowrap; /*不允许换行*/
        }

        /*两端对齐*/
        .text-justify {
            text-align: justify;
            text-align-last: justify;
        }
    </style>

</head>
<body style="width: 99%;" class="custom-style">
<!--<object classid="CLSID:76A64158-CB41-11D1-8B02-00600806D9B6" id="locator" style="display:none;visibility:hidden"></object>-->
<!--<object classid="CLSID:75718C9A-F029-11d1-A1AC-00C04FB6C223" id="foo" style="display:none;visibility:hidden"></object>-->
<div class="layui-row layui-col-space15 content-min">
    <div class="layui-col-md12 event-receive">
        <div class="event-receive-toolbar">
            <div class="video-event layui-form" style="position:absolute;bottom: 2px;right: 0;">
                <input type="checkbox" title="隐藏图片" lay-filter="hidePicture" id="hidePicture"
                       lay-skin="primary" value="">
                <input type="checkbox" title="接收事件" lay-filter="receiveEvent" id="receiveEvent"
                       lay-skin="primary" value="">
            </div>
        </div>
    </div>
    <div class="layui-col-md6 layui-col-xs6 layui-col-sm6 layui-col-lg6 event-left">
        <div class="layui-card">
            <!--<div class="layui-card-header">事件视频</div>-->
            <div class="layui-card-body">
                <div class="layui-form video-style" id="eventVideo">
                    <!--<OBJECT ID="ocx" WIDTH=100% HEIGHT=100% classid="clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA"></OBJECT>-->
                    <!--<a href="javascript:void(0);" onclick="show()">show</a>-->
                    <!--<a href="javascript:void(0);" onclick="stop()">stop</a>-->
                    <div class="video-control">
                        <div class="video-control-item video-button">
                            <i class="video-play" id="playVideo"></i>
                        </div>
                        <div class="video-control-item video-type layui-disabled" id='videoT1C'>
                            <span><a class="layui-disabled" href="javascript:;" id='videoT1Value' data-channel="1"
                                     disabled="disabled">主码流</a></span>
                            <div class='video-select' style='display: none;'>
                                <iframe id="videoT1"
                                        style="bottom:30px;position:absolute;background-color:#fff;width: 60px;height: 60px;">
                                </iframe>
                                <ul id='videoT1Select'>
                                    <li class='selected'><a id="mainStream" data-channel="1" href="javascript:;">主码流</a>
                                    </li>
                                    <li><a id="subStream" data-channel="2" href="javascript:;">子码流</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="video-control-item video-type" id='videoT2C'>
                            <span><a href="javascript:;" id='videoT2Value' data-type="0">UDP</a></span>
                            <div class='video-select' style='display: none;'>
                                <iframe id="videoT2"
                                        style="bottom:30px;position:absolute;background-color:#fff;width: 60px;height: 60px;">
                                </iframe>
                                <ul id='videoT2Select'>
                                    <li class='selected'><a data-type="0" href="javascript:;">UDP</a></li>
                                    <li><a data-type="1" href="javascript:;">TCP</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <iframe id="RTSPError"
                            style="top:0;position:absolute;background-color:#000;width: 100%;height: 30px;border: none;display: none">
                    </iframe>
                    <div id="errorMsg" style="width: 100%;line-height: 30px;color: red;position:absolute;"></div>
                </div>
            </div>
        </div>
        <div class="layui-card" style="display: none">
            <div id="snapPlateNumber" class="layui-card-header">抓拍车牌号</div>
            <div class="layui-card-body">
                <div class="layui-form event-plate">
                    <div id="platePic1">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card" style="display: none">
            <div id="recognitionPlateNumber" class="layui-card-header">识别车牌号</div>
            <div class="layui-card-body">
                <div class="layui-form">
                    <div id="plate">&nbsp;&nbsp;</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md6 layui-col-xs6 layui-col-sm6 layui-col-lg6 event-picture">
        <div class="layui-row">
            <div class="layui-col-md9 layui-col-xs9 layui-col-sm9 layui-col-lg9 event-picture-4">
                <div class="layui-col-md6" id="prePic1"></div>
                <div class="layui-col-md6" id="eventPic1"></div>
                <div class="layui-col-md6" id="lastPic1"></div>
                <div class="layui-col-md6" id="extendPic1"></div>
            </div>
            <div class="layui-col-md3 layui-col-xs3 layui-col-sm3 layui-col-lg3 right-event-info-div">
                <table class="right-event-info-table">
                    <tr>
                        <td id="TIME_" class="text-justify">时 间：</td>
                        <td id="TIME-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="EVENT_TYPE_">事件类型：</td>
                        <td id="EVENT_TYPE-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="LANE_INDEX_" class="text-justify">车 道 号：</td>
                        <td id="LANE_INDEX-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="PLATE_TYPE_">车牌类型：</td>
                        <td id="PLATE_TYPE-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="PLATE_STRING_">车牌号码：</td>
                        <td id="PLATE_STRING-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="CAR_SPEED_" class="text-justify">速 度：</td>
                        <td id="CAR_SPEED-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="CAR_COLOR_">车身颜色：</td>
                        <td id="CAR_COLOR-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="CAR_TYPE_">车辆类型：</td>
                        <td id="CAR_TYPE-" style="white-space: pre-wrap;"></td>
                    </tr>
                    <tr>
                        <td id="CHE_XING_">细分车型：</td>
                        <td id="CHE_XING-" style="white-space: pre-wrap;"></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="layui-col-md12" style="padding-top: 0px;padding-bottom: 0px">
        <div class="layui-card">
            <div class="layui-card-body event-info">
                <!--                    <div class="layui-form">-->
                <!--                        <table id="eventTable" lay-filter="eventTable"></table>-->
                <!--                    </div>-->
                <div class="layui-tab layui-tab-card" style="height: 100%">
                    <ul class="layui-tab-title">
                        <li class="layui-this" id="pictureMode">图片模式</li>
                        <li id="listMode">列表模式</li>
                    </ul>
                    <div class="layui-tab-content" style="height: 150px;">
                        <div class="layui-tab-item layui-show" style="height: 150px;">
                            <div id="eventDataPictureMode">
                                <table>
                                    <tr id="tr_eventPic">
                                        <td id="td_eventPicName" style="width: 80px;">全 &nbsp;景 &nbsp;图</td>
                                        <td id="td_eventPic_0" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_1" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_2" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_3" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_4" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_5" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_6" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_7" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_8" class="pictureMode_td_eventPic"></td>
                                        <td id="td_eventPic_9" class="pictureMode_td_eventPic"></td>
                                    </tr>
                                    <tr id="tr_plateNum">
                                        <td id="td_plateNumName" style="width: 80px">车牌号码</td>
                                        <td id="td_plateNum_0" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_1" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_2" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_3" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_4" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_5" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_6" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_7" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_8" class="pictureMode_td_plateNum"></td>
                                        <td id="td_plateNum_9" class="pictureMode_td_plateNum"></td>
                                    </tr>
                                    <!--                                    <tr id="tr_platePic">-->
                                    <!--                                        <td id="td_platePicName" style="width: 80px">车牌图片</td>-->
                                    <!--                                        <td id="td_platePic_0" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_1" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_2" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_3" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_4" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_5" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_6" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_7" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_8" class="pictureMode_td_platePic"></td>-->
                                    <!--                                        <td id="td_platePic_9" class="pictureMode_td_platePic"></td>-->
                                    <!--                                    </tr>-->
                                    <tr id="tr_eventType">
                                        <td id="td_eventType" style="width: 80px">事件类型</td>
                                        <td id="td_eventType_0" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_1" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_2" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_3" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_4" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_5" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_6" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_7" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_8" class="pictureMode_td_plateNum"></td>
                                        <td id="td_eventType_9" class="pictureMode_td_plateNum"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="layui-form">
                                <table id="eventTable" lay-filter="eventTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
                <!--<table class="event-table common-table">-->
                <!--<thead>-->
                <!--<tr>-->
                <!--<th>触发时间</th>-->
                <!--&lt;!&ndash;<th>事件ID</th>&ndash;&gt;-->
                <!--<th>事件类型</th>-->
                <!--<th>车道</th>-->
                <!--<th>车牌类型</th>-->
                <!--<th>车牌号</th>-->
                <!--<th>车速</th>-->
                <!--<th>车身颜色</th>-->
                <!--<th>概述</th>-->
                <!--</tr>-->
                <!--</thead>-->
                <!--</table>-->
                <!--<script type="text/template" id="dataTpl">-->
                <!--{{#  layui.each(d, function(index, item){ }}-->
                <!--<tr>-->
                <!--<td>{{ item.time }}</td>-->
                <!--<td>{{ item.eventType }}</td>-->
                <!--<td>{{ item.laneID }}</td>-->
                <!--<td>{{ item.plateType }}</td>-->
                <!--<td>{{ item.plate }}</td>-->
                <!--<td>{{ item.speed }}</td>-->
                <!--<td>{{ item.carColor }}</td>-->
                <!--<td>{{  }}</td>-->
                <!--</tr>-->
                <!--{{#  }); }}-->
                <!--{{#  if(d.length === 0){ }}-->
                <!--无数据-->
                <!--{{#  } }}-->
                <!--</script>-->
                <!--<table class="event-table common-table">-->
                <!--<tbody id="data">-->
                <!--</tbody>-->
                <!--</table>-->
                <!--<table class="layui-table" lay-data="{height:315, url:'/demo/table/user/', page:true, id:'test'}" lay-filter="test" style="display: inline">-->
                <!--<thead>-->
                <!--<tr>-->
                <!--<th lay-data="{field:'id', width:100, sort: true}">触发时间</th>-->
                <!--<th lay-data="{field:'username', width:80}">事件ID</th>-->
                <!--<th lay-data="{field:'sex', width:80, sort: true}">事件类型</th>-->
                <!--<th lay-data="{field:'city'}">车道</th>-->
                <!--<th lay-data="{field:'sign'}">车牌类型</th>-->
                <!--<th lay-data="{field:'experience', sort: true}">车牌号</th>-->
                <!--<th lay-data="{field:'score', sort: true}">车速</th>-->
                <!--<th lay-data="{field:'classify'}">车身颜色</th>-->
                <!--<th lay-data="{field:'wealth',width:120, sort: true}">概述</th>-->
                <!--</tr>-->
                <!--</thead>-->
                <!--</table>-->
            </div>
        </div>
    </div>
</div>
</body>
<div id="eventPicture" class="layer-pic">
    <div class="layui-row">
        <div class="layui-col-md8 pic-show" id="currentPic"></div>
        <div class="layui-col-md4" id="picList">
            <div class="layui-row">
                <div class="layui-col-md6 pic-show" id="prePic"></div>
                <div class="layui-col-md6 pic-show" id="eventPic"></div>
                <div class="layui-col-md6 pic-show" id="lastPic"></div>
                <div class="layui-col-md6 pic-show" id="featurePic"></div>
                <div class="layui-col-md12 pic-show" id="platePic"></div>
            </div>
        </div>
        <div class="layui-col-md12">
            <table class="layui-table" style="margin: 0;" id="detailTable">
                <thead>
                <tr>

                </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        <div class="layui-col-md12" id="timeList">
            <div class="layui-row">
                <div class="layui-col-md6 time-show" id="preTime"></div>
                <div class="layui-col-md6 time-show" id="eventTime"></div>
                <div class="layui-col-md6 time-show" id="lastTime"></div>
                <div class="layui-col-md6 time-show" id="featureTime"></div>
            </div>
        </div>
    </div>
</div>
<!--<div id="eventPicture" class="layer-pic">-->
<!--<div class="layui-row">-->
<!--<div class="layui-col-md6" id="prePic"></div>-->
<!--<div class="layui-col-md6" id="eventPic"></div>-->
<!--<div class="layui-col-md6" id="lastPic"></div>-->
<!--<div class="layui-col-md6" id="platePic"></div>-->
<!--</div>-->
<!--</div>-->
</html>
