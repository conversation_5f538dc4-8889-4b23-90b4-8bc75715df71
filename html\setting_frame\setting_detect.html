<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>settingDetect</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/DomOperation.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/settingDetect.js"></script>
    <script src="../../js/saveTXT.js"></script>
    <script src="../../js/dep/spark-md5.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/bufferOperation.js"></script>
    <style>
        .layui-form-checkbox[lay-skin=primary] {
            min-height: 28px;
        }

        .layui-form-radio div {
            color: #666666;
        }
    </style>
</head>
<body class="sub-body layui-form custom-style">
<div class="layui-tab-item   layui-show">
    <fieldset class="layui-elem-field">
        <legend id="general">通用功能</legend>
        <div class="layui-field-box">
            <div class="">
                <div class="checkbox">
                    <div style="height: 50px">
                        <div style="float: left;width:100px;">
                            <input id="bayonet" type="checkbox" class="detect-signal" title="检测卡口"
                                   lay-skin="primary" value="0x0200" lay-filter="detectKakou">
                        </div>
                        <div class="detect-kakou" style="float: left;width:70px">
                            <select name="kakou-img-count" lay-filter="kakou-img-count" id="kakou-img-count"
                                    style="height: 50px"
                                    disabled>
                                <option value="1" selected>1</option>
                                <option value="2">2</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <input id="output" type="checkbox" class="detect-signal" title="输出轨迹"
                               lay-skin="primary" value="0x2000">
                        <input id="unlicensedCars" type="checkbox" class="detect-signal" title="检测无牌车"
                               lay-skin="primary" value="0x0080" lay-filter="detectNoLicense">
                        <!--                    <div class="input-range detect-no-license dis"-->
                        <!--                         data-label="无牌车灵敏度："-->
                        <!--                         data-label-id="sensitivity"-->
                        <!--                         data-id="detectNoLicense"-->
                        <!--                    ></div>-->
                        <!--<table class="input-table detect-no-license dis">-->
                        <!--<tr>-->
                        <!--<td id="u8" rowspan="2">无牌车灵敏度：</td>-->
                        <!--<td rowspan="2"><input-->
                        <!--type="text" value="0" class="input-number" id="detectNoLicense" disabled-->
                        <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                        <!--</td>-->
                        <!--<td>-->
                        <!--<button type="button" class="input-up detect-no-license" disabled="disabled"><i-->
                        <!--class="button-edge button-up"></i></button>-->
                        <!--</td>-->
                        <!--</tr>-->
                        <!--<tr>-->
                        <!--<td>-->
                        <!--<button type="button" class="input-down detect-no-license" disabled="disabled"><i-->
                        <!--class="button-edge button-down"></i></button>-->
                        <!--</td>-->
                        <!--</tr>-->
                        <!--</table>-->
                        <br>
                        <input id="video" type="checkbox" class="weizhang-video" title="违章录像"
                               lay-skin="primary" value="1">
                        <input id="viewBayonet" type="checkbox" class="kakou-feature" title="卡口特写图"
                               lay-skin="primary" value="1">
                        <input id="violationFeature" type="checkbox" class="weizhang-feature" title="违章特写图"
                               lay-skin="primary" value="1">
                    </div>

                    <div class="layui-form-item" style="margin-bottom: 0">
                        <label class="layui-form-label" style="width: 150px;color: #666666">机动车特写图类型</label>
                        <div class="layui-input-block" style="margin-bottom: 0">
                            <input type="radio" lay-filter="jdc-feature" name="jdc-feature" value="0" title="整个目标"
                                   checked>
                            <input type="radio" lay-filter="jdc-feature" name="jdc-feature" value="1" title="主驾">
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom: 0">
                        <label class="layui-form-label" style="width: 150px;color: #666666">行人/非机动特写图类型</label>
                        <div class="layui-input-block" style="margin-bottom: 0">
                            <input type="radio" lay-filter="fjdc-feature" name="fjdc-feature" value="0" title="整个目标"
                                   checked>
                            <input type="radio" lay-filter="fjdc-feature" name="fjdc-feature" value="1" title="人脸">
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="electricPolice">多功能电警</legend>
        <div class="layui-field-box">
            <div class="">
                <div class="checkbox dis">
                    <input id="redLight" type="checkbox" class="detect-signal" title="检测闯红灯"
                           lay-skin="primary" value="0x0400">
                    <input id="alternately" type="checkbox" class="detect-signal" title="检测不按交替通行"
                           lay-skin="primary" value="0x2000000">
                    <input id="retrograde" type="checkbox" class="detect-signal" title="检测逆行"
                           lay-skin="primary" value="0x0002">
                    <input id="coilOutput" type="checkbox" class="detect-signal" title="检测地感线圈输出"
                           lay-skin="primary" value="0x40000">
                    <input id="nonMotor" type="checkbox" class="detect-signal" title="检测大车右转未停车让行"
                           lay-skin="primary" value="0x4000000">
                    <input id="turns" type="checkbox" class="detect-signal" title="检测大弯小转"
                           lay-skin="primary" value="0x20000">
                    <input id="straight" type="checkbox" class="detect-signal" title="检测左转不让直行"
                           lay-skin="primary" value="0x10000">
                    <input id="pedestrianDetection" type="checkbox" class="detect-signal" title="检测礼让行人"
                           lay-skin="primary" value="0x8000">
                    <input id="violation" type="checkbox" class="detect-signal" title="检测违规使用专用车道"
                           lay-skin="primary" value="0x1000">
                    <input id="changes" type="checkbox" class="detect-signal" title="检测连续变道"
                           lay-skin="primary" value="0x100000">
                    <input id="zebra" type="checkbox" class="return-enable" title="检测斑马线掉头"
                           lay-skin="primary" value="1">
                    <input id="loiter" type="checkbox" class="detect-signal" title="检测路口滞留"
                           lay-skin="primary" value="0x4000">
                    <input id="wheel" type="checkbox" class="detect-signal" title="检测转弯不使用转向灯"
                           lay-skin="primary" value="0x80000">
                    <input id="dcv" type="checkbox" class="detect-signal" title="检测危化品车"
                           lay-skin="primary" value="0x1000000">
                    <input id="obp" type="checkbox" class="detect-signal" title="检测借道超车"
                           lay-skin="primary" value="0x200000">
                    <input id="affectsNormalTraffic" type="checkbox" class="dui-xiang-enable" title="检测掉头影响正常通行"
                           lay-skin="primary" value="1">

                    <!--                    <input id="threePictures" type="checkbox" class="kakou-img-num" title="卡口采集3张图片"-->
                    <!--                           lay-skin="primary" value="1">-->
                    <!--                           lay-skin="primary" value="0x2000">-->
                    <input id="detectWeiFaZaiHuo" type="checkbox" class="extend-signal" title="检测违法载货"
                           lay-skin="primary" value="0x00001000">

                </div>
            </div>
            <!--            <fieldset class="layui-elem-field">-->
            <!--                <legend><input id="strictViolation" type="checkbox" class="detect-signal" title="严格违章检测"-->
            <!--                               lay-skin="primary" value="0x0020" lay-filter="strictDetect"></legend>-->
            <!--                <div class="layui-field-box strict-detect dis">-->
            <!--                    <input id="strictRedLight" type="checkbox" class="detect-signal" title="严格闯红灯检测" disabled-->
            <!--                           lay-skin="primary" value="0x80000">-->
            <!--                    <input id="strictlyPass" type="checkbox" class="detect-signal" title="严格借道检测" disabled-->
            <!--                           lay-skin="primary" value="0x200000">-->
            <!--                    <input id="strictPressureLine" type="checkbox" class="detect-signal" title="严格压线检测" disabled-->
            <!--                           lay-skin="primary" value="0x400000">-->
            <!--                    <input id="strictRetrograde" type="checkbox" class="detect-signal" title="严格逆行检测" disabled-->
            <!--                           lay-skin="primary" value="0x800000">-->
            <!--                </div>-->
            <!--            </fieldset>-->
            <fieldset class="layui-elem-field">
                <legend><input id="pressureLine" type="checkbox" class="detect-signal" title="检测压线"
                               lay-filter="detectPressingLine"
                               lay-skin="primary" value="0x0010"></legend>
                <div class="layui-field-box">
                    <div class="">
                        <div id="press" class="detect-pressing-line dis">
                            <!--<input id="u10" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线0" disabled-->
                            <!--lay-skin="primary" value="0x0001">-->
                            <!--<input id="u11" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线1" disabled-->
                            <!--lay-skin="primary" value="0x0002">-->
                            <!--<input id="u12" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线2" disabled-->
                            <!--lay-skin="primary" value="0x0004">-->
                            <!--<input id="u13" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线3" disabled-->
                            <!--lay-skin="primary" value="0x0008">-->
                            <!--<input id="u14" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线4" disabled-->
                            <!--lay-skin="primary" value="0x0010">-->
                            <!--<input id="u15" type="checkbox" class="pressing-line detect-pressing-line-line" title="车道线5" disabled-->
                            <!--lay-skin="primary" value="0x0020">-->
                        </div>
                    </div>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend><input id="changeWay" type="checkbox" class="detect-signal" title="检测变道行驶"
                               lay-skin="primary" value="0x0008" lay-filter="illegalChange">

                </legend>
                <div id="illegalChange" class="layui-field-box illegal-change dis">
                    <!--<input id="u37" type="checkbox" class="illegal-change illegal-change-line" title="车道线0" disabled-->
                    <!--lay-skin="primary" value="0x0001">-->
                    <!--<input id="u38" type="checkbox" class="illegal-change illegal-change-line" title="车道线1" disabled-->
                    <!--lay-skin="primary" value="0x0002">-->
                    <!--<input id="u39" type="checkbox" class="illegal-change illegal-change-line" title="车道线2" disabled-->
                    <!--lay-skin="primary" value="0x0004">-->
                    <!--<input id="u40" type="checkbox" class="illegal-change illegal-change-line" title="车道线3" disabled-->
                    <!--lay-skin="primary" value="0x0008">-->
                    <!--<input id="u41" type="checkbox" class="illegal-change illegal-change-line" title="车道线4" disabled-->
                    <!--lay-skin="primary" value="0x0010">-->
                    <!--<input id="u42" type="checkbox" class="illegal-change illegal-change-line" title="车道线5" disabled-->
                    <!--lay-skin="primary" value="0x0020">-->
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend><input id="withoutTurningLights" type="checkbox" class="detect-signal" title="检测变道不使用转向灯"
                               lay-skin="primary" value="0x0004" lay-filter="illegalChangeLight">

                </legend>
                <div id="illegalLight" class="layui-field-box illegal-change-light dis">
                    <!--<input id="u155" type="checkbox" class="illegal-change-light" title="车道线0" disabled-->
                    <!--lay-skin="primary" value="0x0001">-->
                    <!--<input id="u156" type="checkbox" class="illegal-change-light" title="车道线1" disabled-->
                    <!--lay-skin="primary" value="0x0002">-->
                    <!--<input id="u157" type="checkbox" class="illegal-change-light" title="车道线2" disabled-->
                    <!--lay-skin="primary" value="0x0004">-->
                    <!--<input id="u158" type="checkbox" class="illegal-change-light" title="车道线3" disabled-->
                    <!--lay-skin="primary" value="0x0008">-->
                    <!--<input id="u159" type="checkbox" class="illegal-change-light" title="车道线4" disabled-->
                    <!--lay-skin="primary" value="0x0010">-->
                    <!--<input id="u160" type="checkbox" class="illegal-change-light" title="车道线5" disabled-->
                    <!--lay-skin="primary" value="0x0020">-->
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend><input id="detectJump" type="checkbox" class="extend-signal" title="检测加塞"
                               lay-skin="primary" value="0x00010" lay-filter="illegalJump">

                </legend>
                <div id="illegalJump" class="layui-field-box illegal-jump dis">
                    <!--<input id="u162" type="checkbox" class="illegal-jump" title="车道线0" disabled-->
                    <!--lay-skin="primary" value="0x0001">-->
                    <!--<input id="u163" type="checkbox" class="illegal-jump" title="车道线1" disabled-->
                    <!--lay-skin="primary" value="0x0002">-->
                    <!--<input id="u164" type="checkbox" class="illegal-jump" title="车道线2" disabled-->
                    <!--lay-skin="primary" value="0x0004">-->
                    <!--<input id="u165" type="checkbox" class="illegal-jump" title="车道线3" disabled-->
                    <!--lay-skin="primary" value="0x0008">-->
                    <!--<input id="u166" type="checkbox" class="illegal-jump" title="车道线4" disabled-->
                    <!--lay-skin="primary" value="0x0010">-->
                    <!--<input id="u167" type="checkbox" class="illegal-jump" title="车道线5" disabled-->
                    <!--lay-skin="primary" value="0x0020">-->
                </div>
            </fieldset>


            <fieldset class="layui-elem-field">
                <legend>
                    <input id="motorcycles" type="checkbox" class="detect-signal" title="检测非机动车"
                           lay-skin="primary" value="0x0100" lay-filter="nonMotoDetect">
                </legend>
                <div class="layui-field-box non-moto-detect dis">
                    <input id="NonMotoUp" type="checkbox" class="fjdc-type" title="车头" disabled
                           lay-skin="primary" value="1">
                    <input id="NonMotoDown" type="checkbox" class="fjdc-type" title="车尾" disabled
                           lay-skin="primary" value="2">
                </div>
                <div class="layui-field-box non-moto-detect dis">
                    <input id="NonMotoRed" type="checkbox" class="extend-signal" title="检测非机动车闯红灯" disabled
                           lay-skin="primary" value="0x0020">
                    <input id="NonMotoRet" type="checkbox" class="extend-signal" title="检测非机动车逆行" disabled
                           lay-skin="primary" value="0x0040">
                    <input id="NonMotoVeh" type="checkbox" class="extend-signal" title="检测非机动车闯禁令" disabled
                           lay-skin="primary" value="0x0080">
                    <input id="NonMotoNoHel" type="checkbox" class="extend-signal" title="检测非机动车不戴头盔" disabled
                           lay-skin="primary" value="0x0100">
                    <input id="NonMotoKaKou" type="checkbox" class="extend-signal" title="检测非机动车卡口" disabled
                           lay-skin="primary" value="0x0200">
                    <input id="NonMotoOverload" type="checkbox" class="extend-signal" title="检测非机动车超载" disabled
                           lay-skin="primary" value="0x0400">
                </div>
            </fieldset>

            <fieldset class="layui-elem-field">
                <legend>
                    <input id="moto" type="checkbox" class="detect-signal" title="检测摩托车"
                           lay-skin="primary" value="0x800000" lay-filter="motoDetect">
                </legend>
                <div class="layui-field-box moto-detect dis">
                    <input id="MotoKaKou" type="checkbox" class="extend-signal" title="检测摩托车卡口" disabled
                           lay-skin="primary" value="0x00002000">
                    <input id="motoChd" type="checkbox" class="extend-signal" title="检测摩托车闯红灯" disabled
                           lay-skin="primary" value="0x00008000" lay-filter="motoChd">
                    <input id="motoBdtk" type="checkbox" class="extend-signal" title="检测摩托车不带头盔" disabled
                           lay-skin="primary" value="0x00040000" lay-filter="motoBdtk">
                </div>

                <div class="layui-field-box moto-detect dis">
                    <input id="motoJd" type="checkbox" class="extend-signal" title="检测摩托车借道" disabled
                           lay-skin="primary" value="0x00010000" lay-filter="motoJd">
                </div>
                <div class="layui-field-box moto-jd dis" style="padding-left: 45px">
                    <input id="motoJd_left" type="checkbox" class="direction-mask" title="借道左行" disabled
                           lay-skin="primary" value="0x00000020">
                    <input id="motoJd_goStraight" type="checkbox" class="direction-mask" title="借道直行" disabled
                           lay-skin="primary" value="0x00002000">
                    <input id="motoJd_right" type="checkbox" class="direction-mask" title="借道右行" disabled
                           lay-skin="primary" value="0x00200000">
                </div>


                <div class="layui-field-box moto-detect dis">
                    <input id="detectMotoPressingLine" type="checkbox" class="extend-signal" title="检测摩托车压线" disabled
                           lay-skin="primary" value="0x00020000" lay-filter="detectMotoPressingLine">
                </div>
                <div id="motopress" class="layui-field-box detect-moto-pressing-line dis" style="padding-left: 45px">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线0"
                           disabled
                           lay-skin="primary" value="0x0040">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线1"
                           disabled
                           lay-skin="primary" value="0x0080">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线2"
                           disabled
                           lay-skin="primary" value="0x0100">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线3"
                           disabled
                           lay-skin="primary" value="0x0200">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线4"
                           disabled
                           lay-skin="primary" value="0x0400">
                    <input type="checkbox" class="pressing-line detect-motopressing-line-line" title="车道线5"
                           disabled
                           lay-skin="primary" value="0x0800">
                </div>


                <div class="layui-field-box moto-detect dis">
                    <input id="motoCjl" type="checkbox" class="extend-signal" title="检测摩托车闯禁令" disabled
                           lay-skin="primary" value="0x00004000" lay-filter="motoCjl">
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px">检测范围：</label>
                    <div class="layui-input-inline">
                        <select id="motuocheCjlType" lay-filter="motuocheCjlType" disabled>
                            <option value="0">全部</option>
                            <option value="1">外省</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px">本地车牌过滤：</label>
                    <div class="layui-input-inline">
                        <select id="motuocheWpcjlProvince" disabled>

                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px"></label>
                    <div id="moto_local_city">
                        <input type="checkbox" class="moto-local-city" title="全部"
                               lay-skin="primary" value="all" lay-filter="motoLocalCityAll" disabled>
                        <!--<input type="checkbox" class="moto-local-city" title="A" lay-skin="primary" value="A"-->
                        <!--  lay-filter="localPlateCity">-->
                    </div>
                </div>
            </fieldset>

            <fieldset class="layui-elem-field">
                <legend><input id="illegalWays" type="checkbox" class="detect-signal" title="检测违法借道"
                               lay-skin="primary" value="0x0040" lay-filter="illegalBorrowing"></legend>
                <div class="layui-field-box illegal-borrowing dis">
                    <input id="left" type="checkbox" class="direction-mask" title="借道左行" disabled
                           lay-skin="primary" value="0x00000010">
                    <input id="goStraight" type="checkbox" class="direction-mask" title="借道直行" disabled
                           lay-skin="primary" value="0x00001000">
                    <input id="right" type="checkbox" class="direction-mask" title="借道右行" disabled
                           lay-skin="primary" value="0x00100000">
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend><input id="parking" type="checkbox" class="detect-signal" title="检测越线停车"
                               lay-skin="primary" value="0x8000000" lay-filter="overLineParking"></legend>
                <div class="layui-field-box over-line-parking dis">
                    <span id="parkingTime">越线停车时间：</span>
                    <div class="input-range over-line-parking" data-label="2,3张图片间隔：" data-id="overLineParking"
                         data-label-class="pictureInterval"></div>
                    <!--<table class="input-table ">-->
                    <!--<tr>-->
                    <!--<td id="u50" rowspan="2">2,3张图片间隔：</td>-->
                    <!--<td rowspan="2"><input type="text" value="0" class="input-number" id="overLineParking"-->
                    <!--oninput="value=value.replace(/[^\d]/g,'')" disabled/>-->
                    <!--</td>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-up over-line-parking" disabled="disabled"><i-->
                    <!--class="button-edge button-up"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--<tr>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-down over-line-parking" disabled="disabled"><i-->
                    <!--class="button-edge button-down"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--</table>-->
                    <span class="min">秒</span>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend><input id="illegalParking" type="checkbox" class="detect-signal" title="检测违章停车"
                               lay-skin="primary" value="0x0001" lay-filter="illegalParking"></legend>
                <div class="layui-field-box illegal-parking dis">
                    <span id="u53">违停时间：</span>
                    <div class="input-range illegal-parking1"
                         data-label="1,2张图片间隔："
                         data-label-id="stopTime"
                         data-id="illegalParking1"
                    ></div>
                    <!--<table class="input-table">-->
                    <!--<tr>-->
                    <!--<td id="u54" rowspan="2">1,2张图片间隔：</td>-->
                    <!--<td rowspan="2"><input type="text" value="0" class="input-number" disabled-->
                    <!--id="illegalParking1"-->
                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                    <!--</td>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-up illegal-parking1" disabled="disabled"><i-->
                    <!--class="button-edge button-up"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--<tr>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-down illegal-parking1" disabled="disabled"><i-->
                    <!--class="button-edge button-down"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--</table>-->
                    <span class="min">秒</span>
                    <div class="input-range illegal-parking2"
                         data-label="2,3张图片间隔："
                         data-label-class="pictureInterval"
                         data-id="illegalParking2"
                    ></div>
                    <!--<table class="input-table">-->
                    <!--<tr>-->
                    <!--<td id="u56" rowspan="2">2,3张图片间隔：</td>-->
                    <!--<td rowspan="2"><input type="text" value="0" class="input-number" disabled-->
                    <!--id="illegalParking2"-->
                    <!--oninput="value=value.replace(/[^\d]/g,'')"/>-->
                    <!--</td>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-up illegal-parking2" disabled="disabled"><i-->
                    <!--class="button-edge button-up"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--<tr>-->
                    <!--<td>-->
                    <!--<button type="button" class="input-down illegal-parking2" disabled="disabled"><i-->
                    <!--class="button-edge button-down"></i></button>-->
                    <!--</td>-->
                    <!--</tr>-->
                    <!--</table>-->
                    <span class="min">秒</span>
                    <input id="congestionDetection" type="checkbox" class="detect-jam" title="拥堵检测" disabled
                           lay-skin="primary" value="128">
                </div>
            </fieldset>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="bayonetPolice">卡口电警</legend>
        <div class="layui-field-box">
            <input id="violationSpeed" type="checkbox" class="detect-signal" title="检测违反车速规定"
                   lay-skin="primary" value="0x0800">
            <input id="noDrivingLights" type="checkbox" class="extend-signal" title="检测不开车灯"
                   lay-skin="primary" value="0x0002">

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input id="checkSeatBelts" type="checkbox" class="extend-signal" title="检测安全带"
                           lay-skin="primary" value="0x0004">
                    <span style="color: #666666" class="confidence">置信度</span>
                    <input id="SafeBeltScore" type="text" class="layui-input" placeholder="1 - 100"
                           style="width: 100px;display: inline;height: 30px;;margin-left: 10px"/>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input id="phone" type="checkbox" class="extend-signal" title="检测打手机"
                           lay-skin="primary" value="0x0008">
                    <span style="color: #666666" class="confidence">置信度</span>
                    <input id="CallPhoneScore" type="text" class="layui-input" placeholder="1 - 100"
                           style="width: 100px;display: inline;height: 30px;margin-left: 10px"/>
                </div>
            </div>
            <fieldset class="layui-elem-field">
                <legend><input id="pre_card" type="checkbox" class="detect-front-flash" title="前卡事件曝闪"
                               lay-skin="primary" value="1" lay-filter="frontFlash"></legend>
                <div class="layui-field-box front-flash dis">
                    <div class="layui-inline">
                        <label id="effectiveTime" class="layui-form-label">起效时间段</label>
                        <div class="layui-input-inline">
                            <input type="text" class="layui-input" id="flashTime" placeholder=" - " disabled>
                        </div>
                    </div>
                    <div class="config-control" style="margin-top: 10px" id="light">
                        <!--<div class="layui-form-item front-flash-line">
                            <label>车道1曝闪灯：</label>
                            <div class="layui-input-inline">
                                <select class="front-flash-select" disabled>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item front-flash-line">
                            <label>车道2曝闪灯：</label>
                            <div class="layui-input-inline">
                                <select class="front-flash-select" disabled>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item front-flash-line">
                            <label>车道3曝闪灯：</label>
                            <div class="layui-input-inline">
                                <select class="front-flash-select"  disabled>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item front-flash-line">
                            <label>车道4曝闪灯：</label>
                            <div class="layui-input-inline">
                                <select class="front-flash-select" disabled>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item front-flash-line">
                            <label>车道5曝闪灯：</label>
                            <div class="layui-input-inline">
                                <select class="front-flash-select" disabled>
                                </select>
                            </div>
                        </div>-->
                    </div>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend>
                    <input id="largeVehiclesOccupyingSmallLanes" type="checkbox" class="detect-big-car"
                           title="检测大车占用小车道"
                           lay-filter="bigCarSmallCar"
                           lay-skin="primary" value="1">
                </legend>
                <div class="layui-field-box big-car-small-car dis">
                    <input id="heavyTrucks" type="checkbox" class="big-car-type" title="重型货车" disabled
                           lay-skin="primary" value="4">
                    <input id="largeBuses" type="checkbox" class="big-car-type" title="大型客车" disabled
                           lay-skin="primary" value="2">
                    <input id="yellowLicensePlate" type="checkbox" class="big-car-type" title="黄牌" disabled
                           lay-skin="primary" value="1">
                    <div class="config-control" style="margin-top: 10px" id="attributes">
                        <!-- <div class="layui-form-item big-car-small-car-line">
                             <label>车道1属性：</label>
                             <div class="layui-input-inline">
                                 <select class="big-car-select" disabled>
                                     <option value="0">普通</option>
                                     <option value="1">小车道</option>
                                 </select>
                             </div>
                         </div>
                         <div class="layui-form-item big-car-small-car-line">
                             <label>车道2属性：</label>
                             <div class="layui-input-inline">
                                 <select
                                         class="big-car-select"
                                         disabled>
                                     <option value="0">普通</option>
                                     <option value="1">小车道</option>
                                 </select>
                             </div>
                         </div>
                         <div class="layui-form-item big-car-small-car-line">
                             <label >车道3属性：</label>
                             <div class="layui-input-inline">
                                 <select class="big-car-select"  disabled>
                                     <option value="0">普通</option>
                                     <option value="1">小车道</option>
                                 </select>
                             </div>
                         </div>
                         <div class="layui-form-item big-car-small-car-line">
                             <label id="u84" >车道4属性：</label>
                             <div class="layui-input-inline">
                                 <select class="big-car-select" disabled>
                                     <option id="u85" value="0">普通</option>
                                     <option id="u86" value="1">小车道</option>
                                 </select>
                             </div>
                         </div>
                         <div class="layui-form-item big-car-small-car-line">
                             <label id="u87" >车道5属性：</label>
                             <div class="layui-input-inline">
                                 <select class="big-car-select" disabled>
                                     <option id="u88" value="0">普通</option>
                                     <option id="u89" value="1">小车道</option>
                                 </select>
                             </div>
                         </div>-->
                    </div>
                </div>
            </fieldset>
            <!--<button class="layui-btn layui-btn-disabled" id="bigSmallBtn">-->
            <!--车道属性设置-->
            <!--</button>-->
            <fieldset class="layui-elem-field">
                <legend>
                    <input id="trucksOccupying" type="checkbox" class="detect-truck" title="检测货车占用客车道"
                           lay-filter="passengerCar"
                           lay-skin="primary" value="1">
                </legend>
                <div class="layui-field-box passenger-car dis">
                    <input id="heavyTruck" type="checkbox" class="truck-type" title="重型货车" disabled
                           lay-skin="primary" value="4">
                    <input id="mediumTruck" type="checkbox" class="truck-type" title="中型货车" disabled
                           lay-skin="primary" value="2">
                    <input id="lightTruck" type="checkbox" class="truck-type" title="轻型货车" disabled
                           lay-skin="primary" value="1">
                    <div class="config-control" style="margin-top: 10px" id="attributesTwo">
                        <!--<div class="layui-form-item passenger-car-line">
                            <label>车道1属性：</label>
                            <div class="layui-input-inline">
                                <select class="truck-car-select" disabled>
                                    <option value="0">普通</option>
                                    <option value="1">客车道</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item passenger-car-line">
                            <label>车道2属性：</label>
                            <div class="layui-input-inline">
                                <select class="truck-car-select" disabled>
                                    <option value="0">普通</option>
                                    <option value="1">客车道</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item passenger-car-line">
                            <label>车道3属性：</label>
                            <div class="layui-input-inline">
                                <select class="truck-car-select" disabled>
                                    <option value="0">普通</option>
                                    <option value="1">客车道</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item passenger-car-line">
                            <label>车道4属性：</label>
                            <div class="layui-input-inline">
                                <select class="truck-car-select" disabled>
                                    <option value="0">普通</option>
                                    <option value="1">客车道</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item big-car-small-car-line">
                            <label>车道5属性：</label>
                            <div class="layui-input-inline">
                                <select class="truck-car-select" disabled>
                                    <option value="0">普通</option>
                                    <option value="1">客车道</option>
                                </select>
                            </div>
                        </div>-->
                    </div>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field">
                <legend>
                    <input id="vehicleBan" type="checkbox" class="detect-prohibit" title="检测车型禁闯令"
                           lay-filter="prohibitType"
                           lay-skin="primary" value="1">
                </legend>
                <div class="layui-field-box">
                    <table class="prohibit-type dis" id="plateTable">
                        <!--<tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="重型货车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0001">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0001">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0001">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="中型货车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0002">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0002">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0002">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="轻型货车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0004">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0004">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0004">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="大型客车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0008">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0008">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0008">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="中型客车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0010">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0010">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0010">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="轻型客车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0020">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0020">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0020">
                            </td>
                        </tr>
                        <tr>
                            <td class="prohibit-type">
                                <input type="checkbox" class="prohibit-car-type" title="小型客车"
                                       lay-filter="prohibitCarType" disabled
                                       lay-skin="primary" value="0x0040">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-blue" title="蓝牌" disabled
                                       lay-skin="primary" value="0x0040">
                            </td>
                            <td>
                                <input type="checkbox" class="prohibit-plate-yellow" title="黄牌" disabled
                                       lay-skin="primary" value="0x0040">
                            </td>
                        </tr>-->
                    </table>
                </div>
            </fieldset>

            <!--<button class="layui-btn layui-btn-disabled" id="prohibitBtn">-->
            <!--车型设置-->
            <!--</button>-->
        </div>
    </fieldset>
    <fieldset class="layui-elem-field">
        <legend id="ramp">匝道电警</legend>
        <div class="layui-field-box">
            <input id="shanghaiC" type="checkbox" class="detect-signal" title="检测闯禁令(沪C)"
                   lay-skin="primary" value="0x20000000">
            <input id="coachCar" type="checkbox" class="detect-signal" title="检测闯禁令(教练车)"
                   lay-skin="primary" value="0x40000000">
            <input id="trucks" type="checkbox" class="extend-signal" title="检测闯禁令(货车)"
                   lay-skin="primary" value="0x0001">
            <fieldset class="layui-elem-field">
                <legend>
                    <input id="otherProvinces" type="checkbox" class="detect-signal" title="检测闯禁令(外省市小型车)"
                           lay-skin="primary" value="0x10000000" lay-filter="otherProvinces">
                </legend>
                <div class="layui-field-box other-provinces dis">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 120px">本地车牌过滤：</label>
                        <div class="layui-input-inline">
                            <select id="wshcjl_province" style="width: 180px" disabled>
                                <option value="">请选择</option>
                                <!--<option value="京">京</option>-->
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"></label>
                        <div id="wshcjl_city">
                            <input type="checkbox" class="wshcjl-city" title="全部" disabled
                                   lay-skin="primary" value="all" lay-filter="wshcjlCityAll">
                            <!--<input type="checkbox" class="wshcjl-city" title="A" lay-skin="primary" value="A"-->
                            <!--  lay-filter="wshcjlCity">-->
                        </div>
                    </div>
                </div>
                <div class="layui-field-box other-provinces dis">
                    <div class="layui-form-item">
                        <label class="layui-form-label">日期</label>
                        <div id="weekDay">
                            <!-- <input type="checkbox" class="other-provinces" title="周一" disabled
                            lay-skin="primary" value="64">
                     <input type="checkbox" class="other-provinces" title="周二" disabled
                            lay-skin="primary" value="32">
                     <input type="checkbox" class="other-provinces" title="周三" disabled
                            lay-skin="primary" value="16">
                     <input type="checkbox" class="other-provinces" title="周四" disabled
                            lay-skin="primary" value="8">
                     <input type="checkbox" class="other-provinces" title="周五" disabled
                            lay-skin="primary" value="4">
                     <input type="checkbox" class="other-provinces" title="周六" disabled
                            lay-skin="primary" value="2">
                     <input type="checkbox" class="other-provinces" title="周日" disabled
                            lay-skin="primary" value="1">-->
                        </div>
                    </div>
                    <div>
                        <div class="layui-inline">
                            <label class="layui-form-label" id="time">起效时间段1</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" id="time_1" placeholder=" - " disabled>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" id="time2">起效时间段2</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" id="time_2" placeholder=" - " disabled>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" id="time3">起效时间段3</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" id="time_3" placeholder=" - " disabled>
                            </div>
                        </div>
                    </div>
                </div>
            </fieldset>

            <!--<button class="layui-btn layui-btn-disabled" id="otherProvincesBtn">-->
            <!--起效时间段-->
            <!--</button>-->
            <fieldset class="layui-elem-field">
                <legend><input id="blacklist" type="checkbox" class="black-enable" title="检测闯禁令(黑名单)"
                               lay-skin="primary" value="1" lay-filter="blacklist">
                </legend>
                <div class="layui-field-box blacklist dis" id="violationType">
                    <!--<input type="checkbox" class="black-list" title="违法类型1" disabled
                           lay-skin="primary" value="0x01">
                    <input id="u148" type="checkbox" class="black-list" title="违法类型2" disabled
                           lay-skin="primary" value="0x02">
                    <input id="u149" type="checkbox" class="black-list" title="违法类型3" disabled
                           lay-skin="primary" value="0x04">
                    <input id="u150" type="checkbox" class="black-list" title="违法类型4" disabled
                           lay-skin="primary" value="0x08">
                    <input id="u151" type="checkbox" class="black-list" title="违法类型5" disabled
                           lay-skin="primary" value="0x10">
                    <input id="u152" type="checkbox" class="black-list" title="违法类型6" disabled
                           lay-skin="primary" value="0x20">
                    <input id="u153" type="checkbox" class="black-list" title="违法类型7" disabled
                           lay-skin="primary" value="0x40">
                    <input id="u154" type="checkbox" class="black-list" title="违法类型8" disabled
                           lay-skin="primary" value="0x80">-->
                    <div style="margin-top: 10px;">
                        <button id="exportBlackBtn" class="layui-btn layui-btn-default layui-btn-disabled"
                                disabled="disabled">导出黑名单
                        </button>
                        <label for="importBlack">
                            <button id="importBlackBtn" class="layui-btn layui-btn-default layui-btn-disabled"
                                    disabled="disabled">导入黑名单
                            </button>
                        </label>
                        <span id="blackName"></span>
                        <input type="file" id="importBlack" style="display: none">

                    </div>
                </div>

            </fieldset>


            <fieldset class="layui-elem-field">
                <legend>
                    <input id="xianXing" type="checkbox" class="extend-signal" title="尾号限行"
                           lay-skin="primary" value="0x00000800" lay-filter="xianXing">
                </legend>
                <div class="layui-field-box xian-xing dis">
                    <button class="layui-btn layui-btn-default" id="addXianXing" disabled>增加</button>
                    <table id="xianXingTable" lay-filter="xianXing_Table"></table>
                    <script type="text/html" id="xianXingTableOp">
                        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
                    </script>
                </div>

            </fieldset>
        </div>
    </fieldset>
    <button class="layui-btn layui-btn-default" id="saveDetect">保存配置</button>
    <button class="layui-btn layui-btn-default layui-btn-block" id="resetDetect">重置
    </button>
</div>

<div id="whxxConfig" style="display: none;">
    <div style="padding: 20px">
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">日期：</label>
            <div class="layui-input-inline">
                <select id="xianxing_date_type" style="width: 180px" lay-filter="xianxingDateType">
                    <option value="">请选择</option>
                    <option value="0">星期</option>
                    <option value="1">日期尾号</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px"></label>
            <div id="xianxing_date_0" style="display: none">
                <!--                            星期-->
                <input type="checkbox" class="xianxing-date-0" title="全部"
                       lay-skin="primary" value="all" lay-filter="xianxingDate0All">
            </div>
            <div id="xianxing_date_1" style="display: none">
                <!--                            日期尾号-->
                <input type="checkbox" class="xianxing-date-1" title="全部"
                       lay-skin="primary" value="all" lay-filter="xianxingDate1All">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px" id="xianXingTime">起效时间段</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="xian_xing_time" placeholder=" - ">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">不受限行车牌：</label>
            <div class="layui-input-inline">
                <select id="local_plate_province" lay-filter="local_plate_province" style="width: 180px">
                    <option value="">请选择</option>
                    <option value="无">无</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px"></label>
            <div id="local_plate_city">
                <input type="checkbox" class="local-plate-city" title="全部"
                       lay-skin="primary" value="all" lay-filter="localPlateCityAll">
                <!--<input type="checkbox" class="local-plate-city" title="A" lay-skin="primary" value="A"-->
                <!--  lay-filter="localPlateCity">-->
            </div>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">限行车牌尾号：</label>
            <div id="local_plate_endnum">
                <input type="checkbox" class="local-plate-endnum" title="全部"
                       lay-skin="primary" value="all" lay-filter="localPlateEndnumAll">
            </div>
        </div>
        <!--        <div class="layui-form-item">-->
        <!--            <label class="layui-form-label" style="width: 100px">外地车牌尾号：</label>-->
        <!--            <div id="nonlocal_plate_endnum">-->
        <!--                <input type="checkbox" class="nonlocal-plate-endnum" title="全部"-->
        <!--                       lay-skin="primary" value="all" lay-filter="nonlocalPlateEndnumAll">-->
        <!--            </div>-->
        <!--        </div>-->
    </div>
</div>
</body>
</html>
