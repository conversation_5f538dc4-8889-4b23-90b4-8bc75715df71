var element, layer, form, colorpicker, laydate, timeLoop, deviceLoop;
$(document).ready(function () {
  disableFun(['NTPEnable']);
  $("#submitTime").click(function () {
    let m = $('input[lay-filter="checkSystemTime"]:checked').val();
    let date, time;
    let d = new Date();
    if (d.getFullYear() <= 1970 || d.getFullYear() >= 2037) {
      layer.msg(langMessage.setting.errorMsg.DATE_ERROR, {icon: 2});
      return;
    }
    if ('1' === m) {
      date = $("#dateValue").text();
      time = $("#timeValue").text();


    } else {
      if (!checkDate("setDateValue")) {
        layer.msg(langMessage.setting.errorMsg.DATE_ERROR, {icon: 2});
        addDanger($("#setDateValue"));
        return;
      }
      if (!$("#setTimeValue").val()) {
        layer.msg(langMessage.setting.errorMsg.TIME_ERROR, {icon: 2});
        addDanger($("#setTimeValue"))
      }
      date = $("#setDateValue").val();
      time = $("#setTimeValue").val();
    }
    let vd, vt;

    vd = date.replace(/-/g, "/");
    vt = time.replace(/:/g, "/");
    let data = {
      newdate: vd + '/' + vt
    };
    changeCameraParam(data, initTime);
  });
  $("#submitNTP").click(function () {
    let data = getDataArray(['sntpip', 'sntpport', 'sntpintval']);
    if (data) {
      data.sntpenable = $("#sntpenable")[0].checked ? 1 : 0;
      changeCameraParam(data, initTime);
    }
  });
  numberRange('sntpintval', 1, 1800);
  numberRange('sntpport', 0, 65535);
  layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate'], function () {
    element = layui.element, layer = layui.layer, form = layui.form, colorpicker = layui.colorpicker, laydate = layui.laydate;
    laydate.render({
      elem: '#setTimeValue'
      , type: 'time'
    });
    laydate.render({
      elem: '#setDateValue'
      , type: 'date'
    });
    laydate.render({
      elem: '#setTimeValue'
      , type: 'time'
    });
    form.on('checkbox(checkSystemTime)', function (data) {
      if (data.elem.checked) {
        let nowDate = new Date();
        let y = nowDate.getFullYear(),
          m = nowDate.getMonth() + 1,
          d = nowDate.getDate(),
          h = nowDate.getHours(),
          mm = nowDate.getMinutes(),
          s = nowDate.getSeconds();
        let dataValue = y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d);
        let timeValue = (h < 10 ? "0" + h : h) + ":" + (mm < 10 ? "0" + mm : mm) + ":" + (s < 10 ? "0" + s : s);
        $("#setDateValue").css('display', 'none');
        $("#setTimeValue").css('display', 'none');
        $("#dateValue").html(dataValue);
        $("#timeValue").html(timeValue);
        $("#timeValue").css('display', 'block');
        $("#dateValue").css('display', 'block');
        timeLoop = setInterval(function () {
          let nowDate = new Date();
          let h = nowDate.getHours(),
            mm = nowDate.getMinutes(),
            s = nowDate.getSeconds();
          let timeValue = (h < 10 ? "0" + h : h) + ":" + (mm < 10 ? "0" + mm : mm) + ":" + (s < 10 ? "0" + s : s);
          $("#timeValue").html(timeValue);
        }, 1000)
      } else {
        clearInterval(timeLoop)
        $("#setDateValue").css('display', 'block');
        $("#dateValue").css('display', 'none');
        $("#setTimeValue").css('display', 'block');
        $("#timeValue").css('display', 'none')
      }
    });
    initDefaultBtn();
    getCameraParam(initTime);
  });
});
var initTime = function () {
  clearInterval(deviceLoop);
  // clearInterval(timeLoop);
  setDeviceTime();
  setNTPTime();
};
var setNTPTime = function () {
  let sntpenable = parseInt(get_name("sntpenable"));
  if (sntpenable === 1) {
    $("#sntpenable")[0].checked = true;
    $(".NTPEnable").removeClass('dis');
  } else {
    $("#sntpenable")[0].checked = false;
    $(".NTPEnable").addClass('dis');
  }
  setDataArray(['sntpip', 'sntpport', 'sntpintval'])
  form.render()
};
var setDeviceTime = function () {

  let date = get_name('date');
  let time = get_name('time');
  let tmp = date.split('/');
  date = tmp[0] + "-" + tmp[1] + "-" + tmp[2];
  tmp = time.split('/');
  time = tmp[0] + ":" + tmp[1] + ":" + tmp[2];
  $("#deviceDate").html(date);
  $("#deviceTime").html(time);
  deviceLoop = setInterval(updateDevice, 1000)
};

var updateDevice = function () {
  let time = $("#deviceTime").text();
  let tmp = time.split(':');
  let h = tmp[0];
  let m = tmp[1];
  let s = parseInt(tmp[2], 10) + 1;
  if (60 == s) {
    s = 0;
    m = parseInt(m, 10) + 1;
    if (60 == m) {
      m = 0;
      h = parseInt(h, 10) + 1;
    }
  }
  time = format(h) + ":" + format(m) + ":" + format(s);
  $("#deviceTime").html(time);
};
var IEVersion = function () {
  var rv = -1;
  if (navigator.appName == 'Microsoft Internet Explorer') {
    var ua = navigator.userAgent;
    var re = new RegExp("MSIE ([0-9]{1,}[\.0-9]{0,})");
    if (re.exec(ua) != null) rv = parseFloat(RegExp.$1);
  } else if (navigator.appName == 'Netscape') {
    var ua = navigator.userAgent;
    var re = new RegExp("Trident/.*rv:([0-9]{1,}[\.0-9]{0,})");
    if (re.exec(ua) != null) rv = parseFloat(RegExp.$1);
  }
  return rv;
};

var format = function (n) {
  if (parseInt(n) < 10) {
    var m = IEVersion();
    if (m == 8) {
      if (n == 8) {
        return "08";
      }
      if (n == 9) {
        return "09";
      }
    }
    var time = "0" + parseInt(n);
    return time;
  }
  else {
    return parseInt(n, 10);
  }
};
var commonDefault = function () {
  let data = getDefaultData(['sntpip', 'sntpport', 'sntpintval', 'sntpenable'])
  changeCameraParam(data, initTime);
};