let dataArray = [
  'iMatch_X_Threshold', 'iMatch_Y_Threshold', 'iGray_Threshold', 'iPlate_Blue_Grad_Threshold',
  'iPlate_Yellow_Grad_Threshold', 'iPlate_Blue_Skip_Threshold', 'iPlate_Yellow_Skip_Threshold',
  'iIs_All', 'iNight_Gray_Threshold', 'iDay_To_Night_Threshold', 'iKakou_Detect_Area', 'iDetect_Precision',
  'iDelay_Time', 'iDelay_Dis_img2', 'iSame_Plate_Time'
];
let dataObjects = [
  {
    name: 'iMatch_X_Threshold',
    unit: 'pixel',
    recommend: 400,
    value: 400,
    desc1: langMessage.extendParams.object.iMatch_X_Threshold.desc1,
    desc2: langMessage.extendParams.object.iMatch_X_Threshold.desc2
  },
  {
    name: 'iMatch_Y_Threshold',
    unit: 'pixel',
    recommend: 600,
    value: 600,
    desc1: langMessage.extendParams.object.iMatch_Y_Threshold.desc1,
    desc2: langMessage.extendParams.object.iMatch_Y_Threshold.desc2
  },
  {
    name: 'iGray_Threshold',
    unit: '',
    recommend: 15,
    value: 15,
    desc1: langMessage.extendParams.object.iGray_Threshold.desc1,
    desc2: langMessage.extendParams.object.iGray_Threshold.desc2
  },
  {
    name: 'iPlate_Blue_Grad_Threshold',
    unit: '',
    recommend: 100,
    value: 100,
    desc1: langMessage.extendParams.object.iPlate_Blue_Grad_Threshold.desc1,
    desc2: langMessage.extendParams.object.iPlate_Blue_Grad_Threshold.desc2
  },
  {
    name: 'iPlate_Yellow_Grad_Threshold',
    unit: 'km/h',
    recommend: 100,
    value: 100,
    desc1: langMessage.extendParams.object.iPlate_Yellow_Grad_Threshold.desc1,
    desc2: langMessage.extendParams.object.iPlate_Yellow_Grad_Threshold.desc2
  },
  {
    name: 'iPlate_Blue_Skip_Threshold',
    unit: 'pixel',
    recommend: 1,
    value: 1,
    desc1: langMessage.extendParams.object.iPlate_Blue_Skip_Threshold.desc1,
    desc2: langMessage.extendParams.object.iPlate_Blue_Skip_Threshold.desc2
  },
  {
    name: 'iPlate_Yellow_Skip_Threshold',
    unit: 'pixel',
    recommend: 1,
    value: 1,
    desc1: langMessage.extendParams.object.iPlate_Yellow_Skip_Threshold.desc1,
    desc2: langMessage.extendParams.object.iPlate_Yellow_Skip_Threshold.desc2
  },
  {
    name: 'iIs_All',
    unit: 'pixel',
    recommend: 1,
    value: 1,
    desc1: langMessage.extendParams.object.iIs_All.desc1,
    desc2: langMessage.extendParams.object.iIs_All.desc2
  },
  {
    name: 'iNight_Gray_Threshold',
    unit: 'pixel',
    recommend: 10,
    value: 10,
    desc1: langMessage.extendParams.object.iNight_Gray_Threshold.desc1,
    desc2: langMessage.extendParams.object.iNight_Gray_Threshold.desc2
  },
  {
    name: 'iDay_To_Night_Threshold',
    unit: 'pixel',
    recommend: 30,
    value: 30,
    desc1: langMessage.extendParams.object.iDay_To_Night_Threshold.desc1,
    desc2: langMessage.extendParams.object.iDay_To_Night_Threshold.desc2
  },
  {
    name: 'iKakou_Detect_Area',
    unit: 'pixel',
    recommend: 80,
    value: 80,
    desc1: langMessage.extendParams.object.iKakou_Detect_Area.desc1,
    desc2: langMessage.extendParams.object.iKakou_Detect_Area.desc2
  },
  {
    name: 'iDetect_Precision',
    unit: 'pixel',
    recommend: 8,
    value: 8,
    desc1: langMessage.extendParams.object.iDetect_Precision.desc1,
    desc2: langMessage.extendParams.object.iDetect_Precision.desc2
  },
  {
    name: 'iDelay_Time',
    unit: '毫秒',
    recommend: 800,
    value: 800,
    desc1: langMessage.extendParams.object.iDelay_Time.desc1,
    desc2: langMessage.extendParams.object.iDelay_Time.desc2
  },
  {
    name: 'iDelay_Dis_img2',
    unit: '',
    recommend: 0,
    value: 0,
    desc1: langMessage.extendParams.object.iDelay_Dis_img2.desc1,
    desc2: langMessage.extendParams.object.iDelay_Dis_img2.desc2
  },
  {
    name: 'iSame_Plate_Time',
    unit: '秒',
    recommend: 15,
    value: 15,
    desc1: langMessage.extendParams.object.iSame_Plate_Time.desc1,
    desc2: langMessage.extendParams.object.iSame_Plate_Time.desc2
  },
];
var form, layer;
$(document).ready(function () {
  let getLis = showParams(dataObjects);
  $("#extendParams").append(getLis);
  $("#valueUp").off("click").on("click", function (e) {
    let v = $($(e.currentTarget).parent().prev()[0]).children(".input-number");
    v.val(parseInt(v.val()) + 1 > 12000 ? 12000 : parseInt(v.val()) + 1);
    $($("#extendParams i")[this.dataset.index]).addClass('iColor');
    $($(".extend")[this.dataset.index]).attr('data-value', v.val())
  });
  $("#valueDown").off("click").on("click", function (e) {
    let v = $($(e.currentTarget).parent().parent().prev()[0]).children().children(".input-number");
    v.val(parseInt(v.val()) - 1 < 0 ? 0 : parseInt(v.val()) - 1);
    $($("#extendParams i")[this.dataset.index]).addClass('iColor');
    $($(".extend")[this.dataset.index]).attr('data-value', v.val())
  });
  $("#paramValue").off("change").on("change", function (e) {
    let v = $(this);
    let value = 0;
    let val = v.val();
    //范围0-12000
    if (val > 0) {
      value = v.val() > 12000 ? 12000 : v.val()
    } else if (val < 0) {
      value = v.val() < 0 ? 0 : v.val()
    } else {
      value = v.val() == '' ? 0 : v.val()
    }
    v.val(value);
    $($("#extendParams i")[this.dataset.index]).addClass('iColor');
    $($(".extend")[this.dataset.index]).attr('data-value', v.val())
  });
  $(".extend").click(function (e) {
    let values = this.dataset;
    let valueInput = $("#paramValue");
    valueInput.val(values.value);
    $("#recommendBtn").off("click").on("click", function () {
      valueInput.val(values.recommend);
    });
    $("#valueBtn").off("click").on("click", function () {
      valueInput.val(values.recommend);
    });
    $("#unit").html(values.unit);
    $("#valueText").html(values.desc1);
    $("#valueRange").html(values.desc2);
    $("#valueUp").attr("data-index", values.index);
    $("#valueDown").attr("data-index", values.index);
    valueInput.attr("data-index", values.index);
  });
  layui.use(['layer', 'form'], function () {
    form = layui.form, layer = layui.layer;
    showConfig('extend');
  });
  $("#resetExtend").click(function () {
    showConfig('extend')
  });
  $("#saveExtend").click(function () {
    saveExtendConfig()
  })
});
var showParams = function (data) {
  let lis = [];
  for (let i = 0; i < data.length; i++) {
    let newLi = $('<li></li>');
    newLi.attr({
      'class': '"layui-timeline-item"'
    });
    let newI = $('<i class="layui-icon layui-timeline-axis" id="extendStatus' + i + '">&#xe643;</i>');
    let newContent = $('<div class="layui-timeline-content layui-text">' +
      '<a class="layui-timeline-title extend" ' +
      'data-index="' + i + '" ' +
      'data-desc1="' + data[i].desc1 + '" ' +
      'data-desc2="' + data[i].desc2 + '" ' +
      'data-value="' + data[i].value + '" ' +
      'data-recommend="' + data[i].recommend + '" ' +
      'data-unit="' + data[i].unit + '" ' +
      'href="javascript:;">' + data[i].name + '</a>' +
      '</div>');
    newLi.append(newI, newContent);
    lis.push(newLi)
  }
  return lis
};
var getParams = function (data) {
  let dataArray = $("#extendParams a");
  for (let i in dataArray) {
    if (i === 'length') {
      break
    }
    let value = data[$(dataArray[i]).html()];
    if (value !== '') {
      dataArray[i].dataset.value = value
    }
  }
  $(".layui-icon").removeClass('iColor');
};
var saveExtendConfig = function () {
  let dataArray = $("#extendParams a");
  let extendParams = {};
  for (let i = 0; i < dataArray.length; i++) {
    let name = $(dataArray[i]).html();
    extendParams[name] = parseInt(dataArray[i].dataset.value);
  }
  setSe('extendParams', extendParams);
  layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
};
var reloadTime = 0, configInterval = null;
var show = function () {
  let extendParams = getSe('extendParams');
  if (extendParams) {
    getParams(extendParams);
    clearTimeout('configInterval');
  } else {
    reloadConfig();
    reloadTime += 1;
    if (reloadTime <= 5) {
      configInterval = setTimeout(function () {
        showConfig("extend")
      }, 500)
    } else {
      reloadTime = 0;
      clearTimeout(configInterval);
      layer.msg(langMessage.common.netError, {icon: 2});
    }
  }
  form.render();
};

