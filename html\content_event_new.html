<!DOCTYPE html>
<html lang="zh" class="index-html">
<head>
    <meta charset="UTF-8">
    <title>contentEventNew</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui-v2.11.3/layui/css/layui.css">
    <link rel="stylesheet" href="../css/global.css">
    <link rel="stylesheet" href="../css/event.css">


    <script src="../js/dep/polyfill.min.js"></script>
    <script src="../js/dep/jquery-3.3.1.js"></script>
    <script src="../js/cookie.js"></script>
    <script src="../layui-v2.11.3/layui/layui.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../js/webService.js"></script>
    <script src="../js/event_new.js"></script>

    <style>
        .layui-table, .layui-table-view {
            margin: 0;
        }

        .layui-table tbody tr:hover, .layui-table-click, .layui-table-hover {
            background-color: #dcdcdc !important;
        }

        /* 放大镜样式 */
        .magCanvas {
            border-radius: 101px;
            position: fixed;
            border: 5px solid #d3d3d3;
            display: none;
            width: 200px;
            height: 200px;
            z-index: 10001;
        }

        /* 图片切换按钮样式 - 完全参考layui layer模块原始实现 */
        .layui-layer-iconext {
            background: url(../layui/css/modules/layer/default/icon-ext.png) no-repeat;
        }

        .layui-layer-imgnext, .layui-layer-imgprev {
            position: absolute;
            top: 50%;
            width: 27px;
            height: 44px;
            margin-top: -22px;
            outline: 0;
            cursor: pointer;
        }

        .layui-layer-imgprev {
            left: 10px;
            background-position: -5px -5px;
        }

        .layui-layer-imgprev:hover {
            background-position: -33px -5px;
        }

        .layui-layer-imgnext {
            right: 10px;
            background-position: -5px -50px;
        }

        .layui-layer-imgnext:hover {
            background-position: -33px -50px;
        }
    </style>
</head>
<body style="width: 99%;" class="custom-style">
<div class="layui-row content-min">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form">
                    <div class="stroll-table">
                        <table id="eventTable" lay-filter="eventTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<!-- 事件详情弹窗HTML结构 - 完全参考原始搜索模块 -->
<div id="eventPicture" class="layer-pic">
    <div class="layui-row">
        <div class="layui-col-md8 pic-show" id="currentPic"></div>
        <div class="layui-col-md4" id="picList">
            <div class="layui-row">
                <div class="layui-col-md6 pic-show" id="prePic"></div>
                <div class="layui-col-md6 pic-show" id="eventPic"></div>
                <div class="layui-col-md6 pic-show" id="lastPic"></div>
                <div class="layui-col-md6 pic-show" id="featurePic"></div>
                <div class="layui-col-md12 pic-show" id="platePic"></div>
            </div>
        </div>
        <div class="layui-col-md12">
            <table class="layui-table" style="margin: 0;" id="detailTable">
                <thead>
                <tr>

                </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>

    </div>
</div>
</html>
