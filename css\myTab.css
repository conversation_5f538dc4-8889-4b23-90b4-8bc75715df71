li {
    list-style: none;
}

blockquote, body, button, dd, div, dl, dt, form, h1, h2, h3, h4, h5, h6, input, li, ol, p, pre, td, textarea, th, ul {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.my-colla-tab-title {
    position: relative;
    left: 0;
    height: 40px;
    white-space: nowrap;
    font-size: 0;
    display: none;
    transition: all .2s;
    -webkit-transition: all .2s;
}

.my-colla-tab-title li {
    height: 100%;
    background-color: #f2f2f2 !important;
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    transition: all .2s;
    -webkit-transition: all .2s;
    position: relative;
    line-height: 42px;
    min-width: 65px;
    padding: 0 15px;
    text-align: center;
    cursor: pointer;
}

.my-colla-colla-title {
    /*display: none;*/
}

.my-collapse {
    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    border-color: #e6e6e6;
}

.my-colla-item, .my-this:after, .my-colla-content{
    border-color: #e6e6e6;
}

.my-colla-item:first-child {
    border-top: none;
}

.my-colla-title {
    position: relative;
    height: 42px;
    line-height: 42px;
    padding: 0 15px 0 35px;
    color: #333;
    background-color: #f2f2f2;
    cursor: pointer;
    font-size: 14px;
    overflow: hidden;
}

.my-this .my-colla-content {
    display: block;
}

.my-colla-content {
    display: none;
    padding: 10px 15px;
    line-height: 22px;
    color: #666;
}

.my-colla-content, .my-colla-item {
    border-top-width: 1px;
    border-top-style: solid;
}

.my-colla-colla-title .my-colla-icon {
    left: 15px;
}
.my-colla-tab-title .my-colla-icon {
    left: -20px;
}

.my-colla-icon {
    position: absolute;
    top: 0;
    font-size: 14px;
}

.my-show {
    display: block !important;
}

.my-this {
    background-color: #fff;
}

.my-this .my-colla-colla-title {
    display: none;
}

.my-this .my-colla-tab-title {
    display: block;
}

.my-colla-title .my-this {
    border-color: #f2f2f2;
    background-color: #fff !important;
    height: 41px;
    border-bottom-color: #fff;
    border-radius: 2px 2px 0 0;
    pointer-events: none;
}

.my-colla-tab-title .my-this:after {
    position: absolute;
    left: 0;
    top: 0;
    content: '';
    width: 100%;
    height: 42px;
    border-width: 1px;
    border-style: solid;
    border-bottom-color: #fff;
    border-radius: 2px 2px 0 0;
    box-sizing: border-box;
    pointer-events: none;
}

.my-colla-tab-item {
    display: none;
}
.config-table{
    width: 100%;
}
.config-table .config-inline {
    float: left;
    margin-right: 10px;
}
.config-table label {
    float: left;
    padding: 9px 0px;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
}