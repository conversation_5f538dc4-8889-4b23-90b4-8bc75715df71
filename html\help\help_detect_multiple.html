<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>多功能电警</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_detect_multiple_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="d" class="title">多功能电警</h2>
    <div class="content">
        <img id="M_e_police_img1" src="./image056.png" alt="多功能电警"/>
        <p id="d2">
            检测闯红灯：车辆违反交通指示灯。根据车辆轨迹和指示灯的情况判定，不认车道属性是直行还是左转。三张图都要是红灯。
        </p>
        <p id="d3">
            检测路口滞留：直行车道，第一，第二张图绿灯，第三张图红灯。第一张图在停止线前，第二张图过停止线，不过直行判断线。前方车辆拥堵，滞留车辆影响道路通行。
        </p>
        <p id="d4">
            检测逆行：第一张无要求，第二，第三张在车道内。
        </p>
        <p id="d5">
            检测大弯小转：车辆没有经过路口中心，在斑马线左转。设置违停检测区，种类选择人行道。
        </p>
        <img id="M_e_police_img2" src="./image057.png" alt="检测大湾小转配置示例">
        <p id="d6">
            检测左转不让直行：检测区域画对向直行检测区域，左转判断线画在对向检测区旁边，左转车辆碰到左转判断线的时候，对向检测区内有直行车辆。
        </p>
        <img id="M_e_police_img3" src="./image058.png" alt="检测左转不让直行配置示例">
        <p id="d7">
            检测斑马线掉头：设置违停检测区，种类为人行道，设置掉头检测区。需保证掉头车辆车牌能通过检测区，且车牌清晰
        </p>
        <img id="M_e_police_img4" src="./image059.png" alt="检测斑马线掉头配置示例">
        <p id="d8">
            检测连续变道：车辆从车道1变道车道2，再变道车道3。三张图在三个车道。
        </p>
        <p id="d9">
            检测变道行驶：设置需要抓拍的车道线，车道线左右两个车道检测抓拍。第一张在第一个车道，第二张车辆压线，第三张在旁边的车道内。在实线范围内，以车道线为准，不判定停止线。
        </p>
        <p id="d10">
            检测违法借道：借道左行，借道直行，借道右行，看车道属性，运动方向，不看指示灯。
        </p>
        <p id="d11">
            检测越线停车：三张图都是红灯，车辆没有经过直行分界线，第一张在停止线下方，第二张和第三张在停止线上方。第二，第三张图片车辆没有位移。可以选择第二第三张图片的时间间隔。停止线以上，画面25%高度为检测区域。
        </p>
        <p id="d12">
            检测违章停车：三张图片都在违停检测区。普通，违停。人行道，人行道违停。网格线，网格线违停。可以选择三张图片之间的间隔，勾选拥堵检测，因为拥堵造成的违停不抓拍。
        </p>
        <img id="M_e_police_img5" src="./image060.png" alt="检测违章停车配置示例1">
        <img  id="M_e_police_img6" src="./image061.png" alt="检测违章停车配置示例2">
    </div>
</div>
</body>
</html>
