var copyArr = function (arr) {
    let res = [];
    for (let i = 0; i < arr.length; i++) {
        let newDiv = $("<div></div>");
        let poId = arr[i][0].id;
        let poClass = arr[i][0].className;
        let newId = poId.replace(/signal/g, "signalBig")
        let newClass = poClass.replace(/signal/g, 'signalBig');
        let styleT = arr[i][0].style.top;
        let styleL = arr[i][0].style.left;
        newDiv.attr({
            "class": newClass,
            "id": newId,
        });
        newDiv.css({
            "left": styleL,
            "top": styleT
        });
        res.push(newDiv)
    }
    return res
};
let showDouble = function (dataArray, type) {
    for (let i = 0; i < dataArray.length; i++) {
        let d = getSe(dataArray[i]);
        if (d) {
            if (type === 'drawPreview') {
            } else {
                let v = getSe(dataArray[i] + 'Value');
                if (v) {
                    showValue(v, dataArray[i])
                }
            }
            showPoint(d, dataArray[i], type);
        }
    }
    let canvasId = $($("#" + type)[0].offsetParent).find('canvas')[0].id;
    drawPath(canvasId, type)
};
var showPoint = function (point, type, container) {
    let exist = document.getElementById(type);
    if (!exist) {
        let newDiv = $("<div></div>");
        let {containerW, containerH} = getContainerParam();
        newDiv.attr({
            "class": "point " + point.type,
            "id": type
        });
        newDiv.css({
            "width": containerW,
            "height": containerH
        });
        $("#" + container).append(newDiv);
        let newClickDive = $("<div></div>");
        newClickDive.attr({
            "class": "clickZone",
            "id": type + "Zone"
        });
        newClickDive.css({
            "width": containerW,
            "height": containerH
        });
        $("#" + container).append(newClickDive);
        let positionDiv = [];
        let coordinate = point.position;
        let typeArr = point.type.split("-");
        let realType = typeArr[1];
        if (typeArr.length > 2) {
            realType = typeArr[0] + "-" + typeArr[1]
        }
        for (let i = 0; i < coordinate.length; i++) {
            let g = 0;
            if (point.type === 'common-rect') {
                positionDiv.push(newDIV(type, 0, (i + 1), realType, g, coordinate[i].x, coordinate[i].y))
            } else {
                let forNum = 2;
                g = i + 1;
                if (point.type === 'common-rect-group') {
                    forNum = 4
                }
                if (point.type === 'common-line-group') {
                    forNum = coordinate[i].length
                }
                for (let j = 0; j < forNum; j++) {
                    if (point.type === 'common-line-group' && type !== 'redStop') {
                        for (let s = 0; s < 2; s++) {
                            positionDiv.push(newDIV(type, (2 * i + j + 1), (1 + s + j * 2 + i * forNum * 2), realType, g, coordinate[i][j][s].x, coordinate[i][j][s].y))
                        }
                    } else {
                        if (point.type === 'common-line' || type === 'redStop') {
                            g = 0
                        }
                        positionDiv.push(newDIV(type, (i + 1), (1 + j + i * forNum), realType, g, coordinate[i][j].x, coordinate[i][j].y))
                    }
                }
            }
        }
        if (type === 'signal') {
            let p = copyArr(positionDiv);
            let newDiv = $("<div></div>");
            newDiv.attr({
                "class": "point " + point.type,
                "id": type + 'Big'
            });
            newDiv.css({
                "width": containerW,
                "height": containerH
            });
            $("#" + container).append(newDiv);
            let newClickDive = $("<div></div>");
            newClickDive.attr({
                "class": "clickZone",
                "id": type + "BigZone"
            });
            newClickDive.css({
                "width": containerW,
                "height": containerH
            });
            $("#" + container).append(newClickDive);

            $("#signalBig").html(p);
        }
        $("#" + type).html(positionDiv);
        let canvasId = $($("#" + container)[0].offsetParent).find('canvas')[0].id;
        drawAll(type, canvasId, container);
    } else {
        let coordinate = point.position;
        for (let i = 0; i < coordinate.length; i++) {
            let g = 0;
            if (point.type === 'common-rect') {
                $("#" + type + (i + 1)).css({
                    top: coordinate[i].y - 5,
                    left: coordinate[i].x - 5
                })
            } else {
                let forNum = 2;
                if (point.type === 'common-rect-group') {
                    forNum = 4
                }
                if (point.type === 'common-line-group') {
                    forNum = coordinate[i].length
                }
                for (let j = 0; j < forNum; j++) {
                    if (point.type === 'common-line-group' && type !== 'redStop') {
                        for (let s = 0; s < 2; s++) {
                            $("#" + type + (1 + s + j * 2 + i * forNum * 2)).css({
                                top: coordinate[i][j][s].y - 5,
                                left: coordinate[i][j][s].x - 5
                            })
                        }
                    } else {
                        $("#" + type + (1 + j + i * forNum)).css({
                            top: coordinate[i][j].y - 5,
                            left: coordinate[i][j].x - 5
                        })
                    }
                }
            }
        }

    }
};
var showValue = function (value, type) {
    let data = [];
    array2obj(value, data);
    $("." + type + '-point').each(function (i) {
        if (data[i]) {
            $(this).find(".input-number")[0].value = data[i].x;
            $(this).find(".input-number")[1].value = data[i].y;
            $(this).find(".input-number").attr("disabled", false);
            $(this).find(".input-up").attr("disabled", false);
            $(this).find(".input-down").attr("disabled", false)
        }
    })
};

var disableColor = function (array) {
    for (let i in array) {
        let key = array[i];
        $("#" + key).css("display", "none");
        $("#" + key).parents(".layui-input-inline").css({
            width: 0,
            margin: 0,
            padding: 0
        });
        $("#" + key + "Ele").find('i').css("display", "none");
        $("#" + key + "Ele").css("margin", "0px");
        $("#" + key + "Ele").parents(".layui-inline").css({
            margin: 0,
            padding: 0
        })
    }
};
var hiddenShow = function () {
    $("div.layui-colla-content").removeClass("layui-show")
};









