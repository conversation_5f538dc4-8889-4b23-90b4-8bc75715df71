/**
 * Create by Chelly
 * 2019/8/30
 */

var registerMyTab = function () {
  $(".my-colla-colla-title").append("<i class='my-colla-icon layui-icon layui-icon-right'></i>");
  $(".my-colla-tab-title").append("<i class='my-colla-icon layui-icon layui-icon-down'></i>");
  let myColla = $(".my-collapse");
  myColla.click(function (e) {
    let event = e || window.event;
    let target = event.target || event.srcElement;
    let myLi = $(target).parents(".my-colla-title").find('li');
    if (target.nodeName.toLowerCase() === 'li') {  //判断目标对象是不是li
      myLi.removeClass('my-this');
      $(target).addClass('my-this');
      $(target).parents(".my-colla-item").find(".my-colla-tab-item").removeClass('my-show');
      let index = $(target).index();
      $($(target).parents(".my-colla-item").find(".my-colla-tab-item")[index]).addClass('my-show');
    }
    if ((target.nodeName.toLowerCase() === 'i' && target.className.indexOf('my-colla-icon') > -1) || target.nodeName.toLowerCase() === 'h4' || target.className.indexOf('.my-colla-title') > 0) {
      target = target.className.indexOf('.my-colla-title') > 0 ? $(target) : $(target).parents(".my-colla-item");
      if (target[0].className.indexOf('my-this') > 0) {
        target.removeClass('my-this')
      } else {
        myColla.find('.my-colla-item').removeClass('my-this');
        target.addClass('my-this');
        myLi.removeClass('my-this');
        $(myLi[0]).addClass('my-this');
        target.find(".my-colla-tab-item").removeClass('my-show');
        $(target.find(".my-colla-tab-item")[0]).addClass('my-show');
      }
    }

  })
  // let myColla = $(".my-colla-item");
  // for(let i = 0;i<myColla.length;i++){
  //     setDataset(myColla[i],'index',i);
  //     let myLi = $(myColla[i]).find(".my-colla-tab-title li");
  //     myLi.off('click').on('click',function (e) {
  //         myLi.removeClass('my-this');
  //         let index = $(this).index();
  //         $(myLi[index]).addClass('my-this');
  //         $(myColla[i]).find(".my-colla-tab-item").removeClass('my-show');
  //         $($(myColla[i]).find(".my-colla-tab-item")[index]).addClass('my-show');
  //         e.stopPropagation();
  //     });
  //     $(myColla[i]).find('.my-colla-title').off('click').on('click',function (e) {
  //         if(myColla[i].className.indexOf('my-this')>0){
  //             $(myColla[i]).removeClass('my-this');
  //         }else{
  //             myColla.removeClass('my-this');
  //             $(myColla[i]).addClass('my-this');
  //             myLi.removeClass('my-this');
  //             $(myLi[0]).addClass('my-this');
  //             $(myColla[i]).find(".my-colla-tab-item").removeClass('my-show');
  //             $($(myColla[i]).find(".my-colla-tab-item")[0]).addClass('my-show');
  //         }
  //         e.stopPropagation();
  //     });
  // }
};