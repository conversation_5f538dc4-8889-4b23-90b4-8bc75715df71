<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>卡口电警</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .title{

        }
        p{
            text-indent: 1cm;
        }
        img{
            display: inline-block;
            height: auto;
            max-width: 100%;
            border: 2px solid rgba(30, 159, 255, 0.2);
        }
        .warning{
            color:#c52616
        }
    </style>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/i18n/ch_en/help/help_detect_access_ch_en.js"></script>
</head>
<body style="height: 100%;">
<div>
    <h2 id="Bayonet_Police_txt1" class="title">卡口电警</h2>
    <div class="content">
        <img id="Bayonet_Police_image1" src="./image062.png" alt="卡口电警"/>
        <p id="Bayonet_Police_txt2">
            前卡事件爆闪：可以选择爆闪时间，以及各个车道单独爆闪。
        </p>
        <p id="Bayonet_Police_txt3">
            检测违反车速规定：配合“系统配置”—“速度配置”使用。
        </p>
        <img id="Bayonet_Police_image2"  src="./image063.png" alt="速度配置"/>
        <p id="Bayonet_Police_txt4">
            检测不开车灯：自动检测。
        </p>
        <p id="Bayonet_Police_txt5">
            检测安全带：自动检测。
        </p>
        <p id="Bayonet_Police_txt6">
            检测打手机：自动检测。
        </p>
        <p id="Bayonet_Police_txt7">
            检测大车占用小车道：设置车道属性，可以单独检测某一条车道
        </p>
        <img id="Bayonet_Police_image3"  src="./image064.png" alt="大车占用小车道配置示例">
        <p id="Bayonet_Police_txt8">
            检测货车占用客车道：设置车道属性，可以单独检测某一条车道。
        </p>
        <img id="Bayonet_Police_image4"  src="./image065.png" alt="货车占用客车道配置示例">
        <p id="Bayonet_Police_txt9">
            检测车型闯禁令：所有车道都检测车型。可以单独设置牌照类型。
        </p>
        <img id="Bayonet_Police_image5"  src="./image066.png" alt="车型闯禁令配置示例">
    </div>
</div>
</body>
</html>