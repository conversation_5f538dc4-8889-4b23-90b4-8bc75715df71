{"calculation": {"state": {"0": "App_Init_Error", "1": "Create_JpegHandle_Error", "2": "Jpeg_Encode_Error", "3": "Feed_Dog_TimeOver", "4": "Feed_Dog_Error", "5": "Copy_Event_Error", "6": "<PERSON>ush_<PERSON><PERSON>_Error", "7": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>", "8": "Fetch_Jpeg_Error", "9": "Fetch_YUV_Error", "10": "G<PERSON>liu_Jpeg_Error", "11": "<PERSON><PERSON>_<PERSON><PERSON>_Error_Forward", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "trace": {"state": {"0": "Convert_TraceData_Error", "1": "Epoll_wait error", "2": "Upload_Data_Error", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "encoding": {"state": {"0": "Send_Jpeg_Error", "1": "Send_BiG_Get_Error", "2": "Send_BiG_Slice_Error", "3": "Location_Error", "4": "Lane_Zone_Error", "5": "Plate_Size_Error", "6": "Light_RectWidth_Error", "7": "Light_RectHeight_Error", "8": "Light_Absolute_Position_Error", "9": "<PERSON><PERSON>_<PERSON><PERSON>_Error", "10": "<PERSON>ush_<PERSON><PERSON>_Error", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "vpss": {"state": {"0": "Fetch_Yuv_Error", "1": "Yuv_Width_Height_Error", "2": "Fetch_Yuv_TimeOver", "3": "<PERSON>ush_<PERSON><PERSON>_Error", "4": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_Error", "5": "Decode_Jpeg_Error_G<PERSON>u", "6": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>r_G<PERSON>u", "7": "Get_Time_Error", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "video_record": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "forwarding": {"state": {"0": "Receive_Data_Error", "1": "Send_Data_Error", "2": "Server_Register_Error", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "console": {"state": {"0": "Bind_Cpu_Error", "1": "Host_Service_Error", "2": "Accept_Failed", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "license": {"state": {"0": "Open_LicFile_Error", "1": "Write_Data_Error", "2": "Read_DataLen_Error", "3": "Get_HwLicensePath_Error", "4": "Get_Aistick_Error", "5": "Decode_AuthFile_Error", "6": "Aistick_Reinit_Error", "7": "Load_NewData_Error", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "osd": {"state": {"0": "Get_Event_Jpeg_Error", "1": "Add_Osd_Error", "2": "Create_CarInfoTree_Error", "3": "Load_CLPPDict_Error", "4": "Fetch_Jpeg_Error", "5": "Get_VideoInfo_Error", "6": "Send_Event_WEB_Error", "7": "Send_Event_Console_Error", "8": "Send_Event_<PERSON><PERSON>_Error", "9": "Send_Event_Tlv_Error", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "web": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "land_circle": {"state": {"0": "Serialize_Msg_Error", "1": "Send_Msg_Error", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "light_signal": {"state": {"0": "Service_Register_Error", "1": "Create_UnixSock_Error", "2": "Accept_Fail", "3": "Receive_OverTime", "4": "Light0_Error", "5": "Light2_Error", "6": "Light3_Error", "7": "Light4_Error", "8": "Light5_Error", "9": "Read_Data_Error", "10": "Data_After_FrameHead_Error", "11": "CRCData_Error", "12": "CRC_Error", "13": "BCC_Error", "14": "Not_Find_Data", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "NTP": {"state": {"0": "Receive_NtpData_Error", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "Debug": {"state": {"0": "Server_Register_Error", "1": "Mode_Error", "2": "Check_W<PERSON><PERSON>_Error", "3": "Unpack_Data_Error", "4": "Creat_UnixSock_Error", "5": "Fetch_Jpeg_Error", "6": "Encode_Data_Error", "7": "Deal_ClientReq_Error", "8": "Respond_Error", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "h264": {"state": {"0": "Add_List_Error", "1": "Send_Event_TimeOver", "2": "<PERSON>ush_<PERSON><PERSON>_Error", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "video_maker": {"state": {"0": "Ftp_Login_Error", "1": "Make_Mp4Header_Error", "2": "Output_Mp4_Error", "3": "Make_Dir_Error", "4": "Send_Data_Error", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "uploader": {"state": {"0": "Upload_Start_Error", "1": "Upload_Config_Error", "2": "Process_Command_Error", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "blacklist": {"state": {"0": "Decode_StoreData_Error", "1": "Read_LocalInfo2_Error", "2": "Check_Md5_Error", "3": "Read_LocalInfo1_Error", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "checkuid": {"state": {"0": "Data_Dencrypt_Error", "1": "Send_DataLen_Error", "2": "Crc_Error", "3": "Deal_ClientData_Error", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "command_processor": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "remote_access": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "h264_synthetic": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "vframe_receiver": {"state": {"0": "Register_VFrm_Service_Error", "1": "Epoll_Wait_Error", "2": "Reset_Connect_Error", "3": "Unable_Reset_Connect", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "forward_event": {"state": {"0": "Init_Epoll_Error", "1": "Init_ServerEvent_Error", "2": "Send_Event_Error", "3": "Send_Event_OverTime", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "venc_h264": {"state": {"0": "Mallod_Error", "1": "InitTS_VENC_FRAME_Error", "2": "<PERSON>ush_<PERSON><PERSON>_Error", "3": "Data_Type_Error", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "discovery": {"state": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "free_frame": {"state": {"0": "Free_Frame_NULL", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "web_config": {"state": {"0": "Create_Tcp_Socket_Error", "1": "Make_Connection_Error", "2": "Epoll_Ctl_Error", "3": "Deal_ClientData_Error", "4": "Send_Msg_Error", "5": "Receive_Data_OverTime", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "web_eventServer": {"state": {"0": "Create_SocketServer_Error", "1": "Check_W<PERSON><PERSON>_Error", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "merge": {"state": {"0": "FlashFrame_Encode_Error", "1": "Push<PERSON><PERSON>_Error", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "event_forward": {"state": {"0": "Push<PERSON><PERSON>_Error", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}, "flash": {"state": {"0": "FlashFrame_Null", "1": "FlashInfo_Null", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "Fetch_Msg_Empty", "24": "Malloc_Error", "25": "Hung_Once", "26": "Get_Time_Error"}, "mask": {"0": "", "1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": "", "9": "", "10": "", "11": "", "12": "", "13": "", "14": "", "15": "", "16": "", "17": "", "18": "", "19": "", "20": "", "21": "", "22": "", "23": "", "24": "", "25": "", "26": "", "27": "", "28": "", "29": "", "30": "", "31": ""}}}