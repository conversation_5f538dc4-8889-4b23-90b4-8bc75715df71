var doSave = function (obj, type) {
    obj = document.getElementById(obj);
    let nowDate = new Date();
    let dataValue =
        nowDate.getFullYear() + '-' +
        (nowDate.getMonth() > 9 ? nowDate.getMonth() + 1 : ('0' + (nowDate.getMonth() + 1))) + '-' +
        (nowDate.getDate() > 9 ? nowDate.getDate() : ('0' + nowDate.getDate())) + '_';
    let timeValue =
        (nowDate.getHours() > 9 ? nowDate.getHours() : ('0' + nowDate.getHours())) + '-' +
        (nowDate.getMinutes() > 9 ? nowDate.getMinutes() : ('0' + nowDate.getMinutes())) + '-' +
        (nowDate.getSeconds() > 9 ? nowDate.getSeconds() : ('0' + nowDate.getSeconds()));
    let name = "";
    if (type === 'config') {
        name = location.hostname + "_" + dataValue + timeValue + "." + type;
    } else {
        name = type + "_" + dataValue + timeValue + "." + type;
    }
    if (isIE()) {//IE浏览器保存文本框内容

        // var winname = window.open('', '_blank', 'top=10000');
        // winname.document.open('text/html', 'replace');
        // winname.document.writeln(obj.value);
        // winname.document.execCommand('saveas', '', name);
        // winname.close();
        // obj.remove();
        // var fso, tf;
        // try {
        // fso = new ActiveXObject("Scripting.FileSystemObject");
        //     var Message = "请选择文件夹！\n文件将保存为"+name; //选择框提示信息
        //     var Shell = new ActiveXObject("Shell.Application");
        //     //var Folder = Shell.BrowseForFolder(0, Message, 0x0040, 0x11); //起始目录为：我的电脑
        //     var Folder = Shell.BrowseForFolder(0,Message,0); //起始目录为：桌面
        //     if (Folder != null) {
        //         // Folder = Folder.items(); // 返回 FolderItems 对象
        //         // Folder = Folder.item(); // 返回 Folderitem 对象
        //         // Folder = Folder.Path; // 返回路径
        //         Folder = Folder.self.path;
        //         if (Folder.charAt(Folder.length - 1) != "\\") {
        //             Folder = Folder + "\\";
        //         }
        //         console.log(Folder)
        //         // 创建新文件
        //         tf = fso.CreateTextFile(Folder+name, true);
        //         // 填写数据，并增加换行符
        //         tf.WriteLine(obj.value) ;
        //         // // 增加3个空行
        //         // tf.WriteBlankLines(3) ;
        //         // // 填写一行，不带换行符
        //         // tf.Write ("This is a test.");
        //         // 关闭文件
        //         tf.Close();
        //         layer.msg("导出成功！",{icon:1})
        //     }
        // } catch (e) {
        //     console.log(e.message);
        //     layer.msg('当前浏览器不支持此方法', {icon: 2});
        // }

        let blobObject = new Blob([obj.value]);
        try {
            window.navigator.msSaveOrOpenBlob(blobObject, name);
        } catch (e) {

            layer.msg(langMessage.common.browserNonsupport, {icon: 2});

            console.log(e.message);
        }
    } else {
        saveAs(obj, name);
    }

};

var saveAs = function (obj, filename) {//chrome,火狐等现代浏览器保存文本框内容
    let a = document.createElement('a');
    let blob = new Blob([obj.value]);
    a.href = URL.createObjectURL(blob);
    a.setAttribute('download', filename);
    a.setAttribute('target', '_blank');
    a.style.display = "none";
    obj.parentNode.appendChild(a);
    a.click();
    a.remove();
};
var doSaveBlob = function (obj, filename, value) {
    obj = document.getElementById(obj);
    if (isIE()) {//IE浏览器保存文本框内容
        // let fso, tf;
        try {
            window.navigator.msSaveOrOpenBlob(value, filename);
        } catch (e) {
            console.log(e.message);

            layer.msg(langMessage.common.exportFail, {icon: 2, time: 5000})

        }
    } else {
        saveAsBlob(obj, filename, value);
    }
};

var saveAsBlob = function (obj, filename, value) {//chrome,火狐等现代浏览器保存文本框内容
    let a = document.createElement('a');
    let blob = new Blob([value]);
    a.href = URL.createObjectURL(blob);
    a.setAttribute('download', filename);
    a.setAttribute('target', '_blank');
    a.style.display = "none";
    obj.parentNode.appendChild(a);
    a.click();
    a.remove();
};


// function doSave(obj) {
//     obj = document.getElementById(obj);
//     let nowDate = new Date();
//     let dataValue =
//         nowDate.getFullYear() + '-' +
//         (nowDate.getMonth() > 9 ? nowDate.getMonth() + 1 : ('0' + (nowDate.getMonth() + 1))) + '-' +
//         (nowDate.getDate() > 9 ? nowDate.getDate() : ('0' + nowDate.getDate())) + '_'
//     let timeValue =
//         (nowDate.getHours() > 9 ? nowDate.getHours() : ('0' + nowDate.getHours())) + '-' +
//         (nowDate.getMinutes() > 9 ? nowDate.getMinutes() : ('0' + nowDate.getMinutes())) + '-' +
//         (nowDate.getSeconds() > 9 ? nowDate.getSeconds() : ('0' + nowDate.getSeconds()))
//     let name = "log_" + dataValue + timeValue + ".txt";
//     if (isIE()) {//IE浏览器保存文本框内容
//         var winname = window.open('', '_blank', 'top=10000');
//         winname.document.open('text/html', 'replace');
//         winname.document.writeln(obj.value);
//         winname.document.execCommand('saveas', '', name);
//         winname.close();
//     }
//     else {
//         saveAs(obj, name);
//     }
// }
//
// function saveAs(obj, filename) {//chrome,火狐等现代浏览器保存文本框内容
//     var a = document.createElement('a');
//     a.setAttribute('href', 'data:text/html;gb2312,' + obj.value);
//     a.setAttribute('download', filename);
//     a.setAttribute('target', '_blank');
//     a.style.display = "none";
//     obj.parentNode.appendChild(a);
//     a.click();
// }
//
// function isIE()//判断浏览器类型
// {
//     if (!!window.ActiveXObject || "ActiveXObject" in window)
//         return true;
//     else
//         return false;
// }
