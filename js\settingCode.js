var codeArray = [];
var element, layer, form, table;
$(document).ready(function () {
    layui.use(['element', 'layer', 'form', 'table'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, table = layui.table;
        codeArray = langMessage.codeArray;
        table.render({
            elem: '#codeData'
            // ,cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
            , cols: [[ //标题栏
                {field: 'id', title: 'ID', hide: true}
                , {field: 'name', title: langMessage.settingCode.object.name, width: 300}
                , {field: 'code', edit: 'text', title: langMessage.settingCode.object.code}
                , {field: 'priority', edit: 'text', title: langMessage.settingCode.object.priority}
                , {field: 'id_zfcode', title: 'ID_zfcode', hide: true}
                , {field: 'zfcode', edit: 'text', title: langMessage.settingCode.object.zfcode}
            ]]
            , data: codeArray
            //,skin: 'line' //表格风格
            , even: true
            //,page: true //是否显示分页
            //,limits: [5, 7, 10]
            , limit: codeArray.length //每页默认显示的数量
            , done: function () {
            }
        });


        showConfig('code');
        //监听单元格编辑
        table.on('edit(editData)', function (obj) {
            var value = obj.value //得到修改后的值
                , data = obj.data //得到所在行所有键值
                , field = obj.field; //得到字段
            layer.msg(data.name + langMessage.settingCode.editMsg(field, value));
        });

        $("#saveCode").click(function () {
            saveCode()
        });
        $("#resetCode").click(function () {
            showConfig('code')
        })
    });
});
var saveCode = function () {
    let codeValue = {};
    $("#codeData").next().find(".layui-table tr").each(function (i) {
        if (i === 0) {
        } else {
            let t = $(this);
            let name = t.find("td[data-field='id'] .layui-table-cell").html();
            let name_zfcode = t.find("td[data-field='id_zfcode'] .layui-table-cell").html();
            let code = t.find("td[data-field='code'] .layui-table-cell").html();
            let zfcode = t.find("td[data-field='zfcode'] .layui-table-cell").html();
            let priority = t.find("td[data-field='priority'] .layui-table-cell").html();
            codeValue[name] = parseInt(parseInt(priority << 16) + parseInt(code));
            codeValue[name_zfcode] = zfcode
        }
    });
    setSe('codeValue', codeValue);
    console.log(codeValue)
    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
};


var reloadTime = 0, configInterval = null;
var show = function () {
    let codeValue = getSe('codeValue');
    if (codeValue) {
        let codeArray = [];
        $("#codeData").next().find(".layui-table tr").each(function (i) {
            if (i === 0) {
            } else {
                let t = $(this);
                let obj = {};
                obj.id = t.find("td[data-field='id'] .layui-table-cell").html();
                obj.name = t.find("td[data-field='name'] .layui-table-cell").html();
                obj.id_zfcode = t.find("td[data-field='id_zfcode'] .layui-table-cell").html();
                let value = codeValue[obj.id];
                let value_zfcode = codeValue[obj.id_zfcode] ? codeValue[obj.id_zfcode] : '';
                let code = value & '0xFFFF';
                let priority = (value >> 16) & '0xFFFF';
                obj.code = code;
                obj.priority = priority;
                obj.zfcode = value_zfcode
                // codeValue[name] = parseInt(parseInt(priority << 16) + parseInt(code));
                codeArray.push(obj)
            }
        });
        // if(!table){
        //     layui.use(['table'], function () {
        //         table = layui.table;
        //     })
        // }
        table.reload('codeData', {
            data: codeArray
        });
        clearTimeout(configInterval);
    } else {
        reloadConfig();
        reloadTime += 1;
        if (reloadTime <= 5) {
            configInterval = setTimeout(function () {
                showConfig('code')
            }, 500)
        } else {
            reloadTime = 0;
            clearTimeout(configInterval);
            layer.msg(langMessage.common.netError, {icon: 2});
        }
    }
    form.render();
};

