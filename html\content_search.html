<!DOCTYPE html>
<html lang="zh" class="index-html">
<head>
    <meta charset="UTF-8">
    <title>contentSearch</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../layui/css/layui.css">
    <link rel="stylesheet" href="../css/global.css">
    <link rel="stylesheet" href="../css/event.css">


    <script src="../js/dep/polyfill.min.js"></script>
    <script src="../js/dep/jquery-3.3.1.js"></script>
    <script src="../js/cookie.js"></script>
    <script src="../layui/layui.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../js/bufferOperation.js"></script>
    <script src="../js/tempOcx.js"></script>
    <script src="../js/ocxGlobal.js"></script>
    <script src="../js/webService.js"></script>
    <script src="../js/keepAlive.js"></script>
    <script src="../js/search.js"></script>

    <style>
        .layui-col-md10 {
            width: calc(100% - 380px);
        }

        .picture-search {
            width: 380px;
        }

        .layui-form-item {
            margin-right: 15px;
        }

        .layui-table, .layui-table-view {
            margin: 0;
        }

        .layui-laydate .layui-this {
            background-color: #0193de !important;
            color: #fff !important;
        }

        .layui-laydate-footer span[lay-type=date] {
            color: #0193de !important;
        }

        .layui-laydate-footer span:hover {
            color: #0193de;
        }

        .layui-table tbody tr:hover, .layui-table-click, .layui-table-hover {
            background-color: #dcdcdc !important;
        }

        .layui-layer-imguide {
            display: inline-block;
        }
        .magCanvas{
            border-radius:101px;
            position: fixed;
            border:5px solid #d3d3d3;
            display: none;
            width:200px;
            height:200px;
            z-index: 10001
        }

    </style>
</head>
<body style="width: 99%;" class="custom-style">
<div class="layui-row layui-col-space15 content-min">
    <div class="layui-col-md10 layui-col-lg10">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form">
                    <div style="display: none;" id="pictureOCX"></div>

                    <!--<table class="search-table common-table">-->
                    <!--<thead>-->
                    <!--<tr>-->
                    <!--<th>触发时间</th>-->
                    <!--<th>事件ID</th>-->
                    <!--<th>事件类型</th>-->
                    <!--<th>车道</th>-->
                    <!--<th>车牌类型</th>-->
                    <!--<th>车牌号</th>-->
                    <!--<th>车速</th>-->
                    <!--<th>车身颜色</th>-->
                    <!--<th>概述</th>-->
                    <!--</tr>-->
                    <!--</thead>-->
                    <!--</table>-->
                    <!--<script type="text/template" id="dataTpl">-->
                    <!--{{#  layui.each(d, function(index, item){ }}-->
                    <!--<tr data-pic1="{{ item.IMAGE_EVENT_PATH }}" data-pic2="{{ item.IMAGE_PRE_PATH }}"-->
                    <!--data-pic3="{{ item.IMAGE_LAST_PATH }}" data-pic4="{{ item.IMAGE_PLATE_PATH }}">-->
                    <!--<td>{{ item.time }}</td>-->
                    <!--<td>{{ item.ID }}</td>-->
                    <!--<td>{{ item.EVENT_TYPE }}</td>-->
                    <!--<td>{{ item.LANE_INDEX }}</td>-->
                    <!--<td>{{ item.PLATE_TYPE }}</td>-->
                    <!--<td>{{ item.PLATE_STRING }}</td>-->
                    <!--<td>{{ item.CAR_SPEED }}</td>-->
                    <!--<td>{{ item.CAR_COLOR }}</td>-->
                    <!--<td>{{ }}</td>-->
                    <!--</tr>-->
                    <!--{{#  }); }}-->
                    <!--{{#  if(d.length === 0){ }}-->
                    <!--无数据-->
                    <!--{{#  } }}-->
                    <!--</script>-->
                    <div class="stroll-table">
                        <!--<table class="search-table common-table">-->
                        <!--<tbody id="data">-->
                        <!--</tbody>-->
                        <!--</table>-->
                        <div id="testBASE"></div>
                        <table id="eventTable" lay-filter="eventTable"></table>
                        <!--<table class="layui-table"-->
                        <!--lay-data="{height:315, id:'test'}"-->
                        <!--lay-filter="test" style="display: inline">-->
                        <!--<thead>-->
                        <!--<tr>-->
                        <!--<th lay-data="{field:'id', width:100, sort: true}">触发时间</th>-->
                        <!--<th lay-data="{field:'username', width:80}">事件ID</th>-->
                        <!--<th lay-data="{field:'sex', width:80, sort: true}">事件类型</th>-->
                        <!--<th lay-data="{field:'city'}">车道</th>-->
                        <!--<th lay-data="{field:'sign'}">车牌类型</th>-->
                        <!--<th lay-data="{field:'experience', sort: true}">车牌号</th>-->
                        <!--<th lay-data="{field:'score', sort: true}">车速</th>-->
                        <!--<th lay-data="{field:'classify'}">车身颜色</th>-->
                        <!--<th lay-data="{field:'wealth',width:120, sort: true}">概述</th>-->
                        <!--</tr>-->
                        <!--</thead>-->
                        <!--</table>-->
                    </div>

                    <!--<table class="layui-table"-->
                    <!--lay-data="{height:315, url:'/demo/table/user/', page:true, id:'test'}"-->
                    <!--lay-filter="test" style="display: inline">-->
                    <!--<thead>-->
                    <!--<tr>-->
                    <!--<th lay-data="{field:'id', width:100, sort: true}">触发时间</th>-->
                    <!--<th lay-data="{field:'username', width:80}">事件ID</th>-->
                    <!--<th lay-data="{field:'sex', width:80, sort: true}">事件类型</th>-->
                    <!--<th lay-data="{field:'city'}">车道</th>-->
                    <!--<th lay-data="{field:'sign'}">车牌类型</th>-->
                    <!--<th lay-data="{field:'experience', sort: true}">车牌号</th>-->
                    <!--<th lay-data="{field:'score', sort: true}">车速</th>-->
                    <!--<th lay-data="{field:'classify'}">车身颜色</th>-->
                    <!--<th lay-data="{field:'wealth',width:120, sort: true}">概述</th>-->
                    <!--</tr>-->
                    <!--</thead>-->
                    <!--</table>-->
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md2 layui-col-lg2 picture-search">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form">
                    <div class="layui-form-item">
                        <label id="inquiry_mode" class="layui-form-label">查询方式</label>
                    </div>
                    <div class="layui-form-item">
                        <label id="Start_time" class="layui-form-label">开始时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="begin_time" class="layui-input" id="begin">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label id="End_time" class="layui-form-label">结束时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="end_time" class="layui-input" id="end">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label id="License_plate_number" class="layui-form-label">车牌号码</label>
                        <div class="layui-input-block">
                            <input type="text" name="plate_string" class="layui-input" id="plate_string">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label id="etype" class="layui-form-label">事件类型</label>
                        <div class="layui-input-block">
                            <select name="event_type" id="event_type">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label id="License_plate_type" class="layui-form-label">车牌类型</label>
                        <div class="layui-input-block">
                            <select name="plate_type" id="plate_type">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="text-align: center">
                        <button class="layui-btn" lay-submit lay-filter="formDemo" id="submit">提交</button>
                        <button type="reset" id="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button class="layui-btn" id="exportBtn">导出当前事件</button>
                    </div >
                    <div class="layui-form-item" style="text-align: center"><i class="layui-icon layui-icon-about" style="color: #555;"></i><span id="trigger_time"> 触发时间为相机时间</span></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<!--<div id="eventPicture" class="layer-pic">-->
    <!--<div class="layui-row">-->
        <!--<div class="layui-col-md6" id="prePic"></div>-->
        <!--<div class="layui-col-md6" id="eventPic"></div>-->
        <!--<div class="layui-col-md6" id="lastPic"></div>-->
        <!--<div class="layui-col-md6" id="platePic"></div>-->
    <!--</div>-->
<!--</div>-->
<div id="eventPicture" class="layer-pic">
    <div class="layui-row">
        <div class="layui-col-md8 pic-show" id="currentPic"></div>
        <div class="layui-col-md4" id="picList">
            <div class="layui-row">
                <div class="layui-col-md6 pic-show" id="prePic"></div>
                <div class="layui-col-md6 pic-show" id="eventPic"></div>
                <div class="layui-col-md6 pic-show" id="lastPic"></div>
                <div class="layui-col-md6 pic-show" id="featurePic"></div>
                <div class="layui-col-md12 pic-show" id="platePic"></div>
            </div>
        </div>
        <div class="layui-col-md12">
            <table class="layui-table" style="margin: 0;" id="detailTable">
                <thead>
                <tr>

                </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        <div class="layui-col-md12" id="timeList">
            <div class="layui-row">
                <div class="layui-col-md6 time-show" id="preTime"></div>
                <div class="layui-col-md6 time-show" id="eventTime"></div>
                <div class="layui-col-md6 time-show" id="lastTime"></div>
                <div class="layui-col-md6 time-show" id="featureTime"></div>
            </div>
        </div>
    </div>
</div>
</html>
