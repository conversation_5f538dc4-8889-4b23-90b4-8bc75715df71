/**
 * 导入配置文件
 * @param file
 */
var getFile = function (file) {
    // 如果浏览器不支持FileReader，则不处理
    if (!window.FileReader) return;

    let reader = new FileReader();
    reader.readAsText(file, "UTF-8");
    reader.onload = function (e) {
        // 读取文件内容
        const fileString = e.target.result;
        // 接下来可对文件内容进行处理
        if (fileString) {
            let config;
            try {
                config = JSON.parse(fileString);
                setConfig(config);
                layer.msg(langMessage.common.importSuc, {icon: 1})
            } catch (e) {
                console.log(e);
                layer.msg(langMessage.common.importFail, {icon: 2})
            }
        }
    }

};


// 下面这些都是以前源信升级留下来的逻辑，可以不看
/**
 * 读取并解析文件结尾中的结构体，弃用
 * @param ofile
 * @param type
 */
function get_filemd5sum(ofile, type) {
    let v1Offset = 152,
        v2Offset = 132,
        v3Offset = 112,
        v4Offset = 96,
        v5Offset = 80,
        vLength = 16,
        md5Length = 32,
        file1Offset = 0,
        file2Offset = 20,
        file3Offset = 40;
    let file = ofile;
    let tmp_md5;
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,

        // file = this.files[0],
        readSize = 0,
        fileNum = 1,
        currentChunk = 0,
        chunks = 0,
        spark = new SparkMD5.ArrayBuffer(),
        fileInfo = {},
        fileReaderConfig = new FileReader(),
        fileReader = new FileReader();

    fileReaderConfig.onload = function (e) {
        let bf = e.target.result;
        let bfLength = bf.byteLength;
        let v1 = bf.slice(bfLength - v1Offset, bfLength - v1Offset + vLength);
        let s1 = ab2str(v1);
        let v2 = bf.slice(bfLength - v2Offset, bfLength - v2Offset + vLength);
        let s2 = ab2str(v2);
        let v3 = bf.slice(bfLength - v3Offset, bfLength - v3Offset + vLength);
        let s3 = ab2str(v3);
        let v4 = bf.slice(bfLength - v4Offset, bfLength - v4Offset + vLength);
        let s4 = ab2str(v4);
        let v5 = bf.slice(bfLength - v5Offset, bfLength - v5Offset + vLength);
        let s5 = ab2str(v5);
        let MD5 = bf.slice(bfLength - md5Length, bfLength);
        let sMD5 = ab2str(MD5);
        let dv = new DataView(bf);

        fileInfo.file_len1 = dv.getUint32(file1Offset, true);
        fileInfo.version1 = s1;
        fileInfo.end1 = fileInfo.file_len1;

        fileInfo.file_len2 = dv.getUint32(file2Offset, true);
        fileInfo.version2 = s2;
        fileInfo.end2 = fileInfo.end1 + fileInfo.file_len2;

        fileInfo.file_len3 = dv.getInt32(file3Offset, true);
        fileInfo.version3 = s3;
        fileInfo.end3 = fileInfo.end2 + fileInfo.file_len3;

        fileInfo.last_app = s4;
        fileInfo.last_uImage = s5;
        fileInfo.MD5 = sMD5;
        fileInfo.end4 = fileInfo.end3 + 124;
        fileInfo.base64 = {
            file1: [],
            file2: [],
            file3: [],
            file4: []
        };
        loadNext();
    };
    fileReader.onload = function (e) {
        // console.log('read chunk nr', currentChunk + 1, 'of', chunks);
        let bf = e.target.result;
        let bfLength = bf.byteLength;
        readSize += bfLength;
        let Base64 = transformArrayBufferToBase64(bf);
        let key = 'file' + fileNum;
        fileInfo.base64[key].push(Base64);
        // let testBF = bf.slice(0,bfLength-32)
        spark.append(bf); // Append array buffer
        if (chunks) {
            currentChunk++;
            if (currentChunk === chunks) {
                fileNum++
            }
        } else {
            fileNum++
        }
        let md5_progress = Math.floor((readSize / (file.size - 32)) * 100);
        element.progress(type, md5_progress + "%");

        console.log(file.name + langMessage.analysisFile.handleProgress(md5_progress));


        if (readSize < file.size - 32) {
            loadNext();
        } else {
            // console.log(spark._buff.buffer)

            tmp_md5 = spark.end();
            console.log(tmp_md5);
            console.log(fileInfo);
            if (file.file_len3 === 0) {
                layer.msg(langMessage.analysisFile.uploadError.WITHOUT_ALGORITHM, {icon: 2});
                return
            }
            if (file.file_len2 > 0 && file.file_len1 === 0) {
                layer.msg(langMessage.analysisFile.uploadError.INCOMPLETE, {icon: 2});
                return
            }
            if (fileInfo.MD5 !== tmp_md5) {
                layer.msg(langMessage.analysisFile.uploadError.DAMAGED, {icon: 2});
                return
            }
            if (fileInfo.file_len3 > 0 && (getFileVersion(fileInfo.last_app) > getFileVersion(fileInfo.version1) || getFileVersion(fileInfo.last_uImage) > getFileVersion(fileInfo.version2))) {
                layer.msg(langMessage.analysisFile.uploadError.VERSION_LOW, {icon: 2});
                return
            }


            $("#upBtn").css({
                display: 'inline-block'
            });
            $("#upBtn").click(function () {
                getCameraParam(window.parent.parent.parent, upgradeByOcx, fileInfo);
            });
            form.render()
        }
    };

    fileReader.onerror = function () {
        console.warn('oops, something went wrong.');
    };
    fileReader.onprogress = function (e) {
        // let p = parseInt((e.loaded / e.total) * 100);
        // console.log(p);
    };

    function loadNext() {
        let start = 0,
            end = 0,
            chunkSize = 9437184, // Read in chunks of 9MB
            key = 'file_len' + fileNum,
            fileEnd = 'end' + fileNum;
        if (fileInfo[key] > chunkSize) {
            if (!chunks) {
                chunkFile(chunkSize, fileInfo[key])
            }
            start = readSize;
            end = ((start + chunkSize) >= fileInfo[fileEnd]) ? fileInfo[fileEnd] : start + chunkSize;
        } else {
            chunks = 0;
            start = readSize;
            end = fileInfo[fileEnd]
        }

        fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }

    function loadFirst() {
        let start = file.size - 156;
        let end = file.size;
        fileReaderConfig.readAsArrayBuffer(blobSlice.call(file, start, end));
    }

    function chunkFile(chunkSize, fileSize) {
        // let chunkSize = 3145728, // Read in chunks of 3MB
        chunks = Math.ceil(fileSize / chunkSize);
        currentChunk = 0;
    }

    loadFirst();
}


/**
 * 剪切版本号
 * @param version
 * @returns {number}
 */
var getFileVersion = function (version) {
    let regEn = /[`~!@#$%^&*()_+<>?:"{},\/;'\-[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
    let pos;
    if (regEn.test(version)) {
        pos = version.search(regEn);
    }
    if (regCn.test(version)) {
        pos = version.search(regCn);
    }
    return parseInt(version.substring(pos + 1));
};
var upgradeByOcx = function (file) {
    upProgress(file, 1)

    // if (file.file_len1 > 0 && getFileVersion(file.version1) >= getFileVersion(get_name(window.parent.parent.parent, 'softwareversion'))) {
    //     type = 1;
    //     let upSize, upInterval,upProgress;
    //     let app = '';
    //     for (let i = 0; i < file.base64.file1.length; i++) {
    //         app = app + file.base64.file1[i]
    //     }
    //     ocx.UPDATE(0, '*************', app)
    // }
};
/**
 * 升级逻辑，弃用
 * @param file
 * @param type
 */
var upProgress = function (file, type) {
    let ip = location.hostname;
    let fileLength = 'file_len1', fileVersion = 'version1', nowVersion = 'softwareversion', fileBase64 = 'file1',
        typeName = langMessage.analysisFile.uploadName.FILE_SYSTEM, upBase64, upSize, upInterval, progress;

    if (type === 1) {
        fileLength = 'file_len2';
        fileVersion = 'version2';
        nowVersion = 'kernelversion';
        fileBase64 = 'file2';
        typeName = langMessage.analysisFile.uploadName.KERNEL;
    }
    if (file[fileLength] > 0 && getFileVersion(file[fileVersion]) >= getFileVersion(get_name(window.parent.parent.parent, nowVersion))) {
        upBase64 = '';
        for (let i = 0; i < file.base64[fileBase64].length; i++) {
            upBase64 = upBase64 + file.base64[fileBase64][i]
        }
        upInterval = setInterval(function () {
            upSize = ocx.UPDATE(type, ip, upBase64);
            progress = parseInt(upSize / file[fileLength] * 100);
            console.log(typeName + langMessage.analysisFile.uploadProgress(progress) + '%');
            if (progress === 100) {
                clearInterval(upInterval);
                if (type === 1) {
                    upProgress(file, 0)
                } else if (type === 0) {
                    let fileBase64 = '';
                    for (let i = 0; i < file.base64['file3'].length; i++) {
                        fileBase64 = fileBase64 + file.base64['file3'][i]
                    }
                    let param = {
                        upgrade_len: file.file_len3,
                        upgrade_data: fileBase64
                    };
                    initWebService(webserviceCMD.CMD_UPGRADE_APP, param)
                }
            }
        }, 1000)
    } else {
        if (type === 1) {
            upProgress(file, 0)
        } else if (type === 0) {
            let fileBase64 = '';
            for (let i = 0; i < file.base64['file3'].length; i++) {
                fileBase64 = fileBase64 + file.base64['file3'][i]
            }
            let param = {
                upgrade_len: file.file_len3,
                upgrade_data: fileBase64
            };
            initWebService(webserviceCMD.CMD_UPGRADE_APP, param)
        }

    }
};