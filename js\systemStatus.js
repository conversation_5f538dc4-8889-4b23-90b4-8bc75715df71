var element, layer, form;
$(document).ready(function () {
    layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate', 'slider', 'table'], function () {
        element = layui.element, layer = layui.layer, form = layui.form;
        getStatus()
    })
});

var getStatus = function () {
    initWebService(webserviceCMD.CMD_GET_STATUS, null, status2JSON)
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        status2JSON({
            "ret": 0,
            "desc": "success",
            extern_light_status: 0,
            jdc_feature: 0,
            fjdc_feature: 0,
            "device_uid": "F1FDEEF701E1FFE1004E000800130000",
            "device_type": "",
            "manufacturer": "",
            "hardware_ver": "TSIC-H2-HW900A",
            "software_ver": "16051223122",
            "communication_ver": "100820 its0.1",
            "configuration_ver": "110224 mb0.2",
            "image_width": 4096,
            "image_height": 2160,
            "process_width": 4096,
            "process_height": 2160,
            "board_name": "Unknown",
            "ip_addr": "************",
            "net_mask": "*************",
            "gate_way": "**********",
            "port": 3128,
            "transmit_addr": "************",
            "transmit_port": 12012,
            "ftp_ip": "0.0.0.0",
            "ftp_Port": 21,
            "ftp_data": 0,
            "ftp_user": "topsky",
            "ftp_password": "topsky",
            "ftp_video_days": 5,
            "ftp_kk_days": 5,
            "ftp_wz_days": 5,
            "channel_num": 1,
            "lane_direction": 0,
            "lane_start": 0,
            "cart_right_line_use": 0,
            "cart_right_line_x0": 0,
            "cart_right_line_y0": 0,
            "cart_right_line_x1": 0,
            "cart_right_line_y1": 0,
            "up_calibration_queue_length": 0,
            "up_calibration_line_use": 0,
            "up_calibration_line_x0": 0,
            "up_calibration_line_y0": 0,
            "up_calibration_line_x1": 0,
            "up_calibration_line_y1": 0,
            "down_calibration_queue_length": 0,
            "down_calibration_line_use": 0,
            "down_calibration_line_x0": 0,
            "down_calibration_line_y0": 0,
            "down_calibration_line_x1": 0,
            "down_calibration_line_y1": 0,
            "zone_x0": 6,
            "zone_y0": 98,
            "zone_x1": 98,
            "zone_y1": 98,
            "zone_x2": 80,
            "zone_y2": 38,
            "zone_x3": 34,
            "zone_y3": 40,
            "min_plate_width": 56,
            "max_plate_width": 102,
            "lane_num": 4,
            "lane0_x0": 7,
            "lane0_y0": 97,
            "lane0_x1": 34,
            "lane0_y1": 39,
            "lane0_type": 0,
            "lane1_x0": 30,
            "lane1_y0": 98,
            "lane1_x1": 37,
            "lane1_y1": 66,
            "lane1_type": 0,
            "lane1_direction": 0,
            "lane1_kind": 1,
            "lane1_leftwait_zone": 0,
            "lane1_special": 0,
            "lane1_gongjiao_start1": 0,
            "lane1_gongjiao_end1": 0,
            "lane1_gongjiao_start2": 0,
            "lane1_gongjiao_end2": 0,
            "lane1_gongjiao_start3": 0,
            "lane1_gongjiao_end3": 0,
            "lane2_min_speed": 0,
            "lane2_max_speed": 0,
            "lane2_x0": 53,
            "lane2_y0": 99,
            "lane2_x1": 53,
            "lane2_y1": 68,
            "lane2_type": 0,
            "lane2_direction": 0,
            "lane2_kind": 2,
            "lane2_leftwait_zone": 0,
            "lane2_special": 0,
            "lane2_gongjiao_start1": 0,
            "lane2_gongjiao_end1": 0,
            "lane2_gongjiao_start2": 0,
            "lane2_gongjiao_end2": 0,
            "lane2_gongjiao_start3": 0,
            "lane2_gongjiao_end3": 0,
            "lane3_min_speed": 0,
            "lane3_max_speed": 0,
            "lane3_x0": 77,
            "lane3_y0": 98,
            "lane3_x1": 70,
            "lane3_y1": 68,
            "lane3_type": 0,
            "lane3_direction": 0,
            "lane3_kind": 2,
            "lane3_leftwait_zone": 0,
            "lane3_special": 0,
            "lane3_gongjiao_start1": 0,
            "lane3_gongjiao_end1": 0,
            "lane3_gongjiao_start2": 0,
            "lane3_gongjiao_end2": 0,
            "lane3_gongjiao_start3": 0,
            "lane3_gongjiao_end3": 0,
            "lane4_min_speed": 0,
            "lane4_max_speed": 0,
            "lane4_x0": 98,
            "lane4_y0": 94,
            "lane4_x1": 81,
            "lane4_y1": 44,
            "lane4_type": 0,
            "lane4_direction": 0,
            "lane4_kind": 2,
            "lane4_leftwait_zone": 0,
            "lane4_special": 0,
            "lane4_gongjiao_start1": 0,
            "lane4_gongjiao_end1": 0,
            "lane4_gongjiao_start2": 0,
            "lane4_gongjiao_end2": 0,
            "lane4_gongjiao_start3": 0,
            "lane4_gongjiao_end3": 0,
            "lane5_min_speed": 0,
            "lane5_max_speed": 0,
            "velo_enable": 0,
            "camera_height": 0,
            "camera_img_top": 0,
            "camera_img_down": 0,
            "coef": 100,
            "min_speed": 0,
            "max_speed": 0,
            "ai_mode": 0,
            "light_line_mode": 0,
            "light_line_single_use": 1,
            "light_line_single_x0": 23,
            "light_line_single_y0": 63,
            "light_line_single_x1": 87,
            "light_line_single_y1": 61,
            "light_line0_use": 0,
            "light_line0_x0": 0,
            "light_line0_y0": 0,
            "light_line0_x1": 0,
            "light_line0_y1": 0,
            "light_line1_use": 0,
            "light_line1_x0": 0,
            "light_line1_y0": 0,
            "light_line1_x1": 0,
            "light_line1_y1": 0,
            "light_line2_use": 0,
            "light_line2_x0": 0,
            "light_line2_y0": 0,
            "light_line2_x1": 0,
            "light_line2_y1": 0,
            "light_line3_use": 0,
            "light_line3_x0": 0,
            "light_line3_y0": 0,
            "light_line3_x1": 0,
            "light_line3_y1": 0,
            "light_line4_use": 0,
            "light_line4_x0": 0,
            "light_line4_y0": 0,
            "light_line4_x1": 0,
            "light_line4_y1": 0,
            "light_enable": 1,
            "out_light_enable": 0,
            "out_light_people": 0,
            "out_light_left": 0,
            "out_light_straight": 0,
            "out_light_right": 0,
            "red_light_delay": 3,
            "yellow_mode": 0,
            "yellow_mode_start": 0,
            "yellow_mode_end": 0,
            "red_light_enhance": 4,
            "red_light_enhance_begin_time": 0,
            "red_light_enhance_end_time": 1439,
            "light_num": 2,
            "light0_x0": 2614,
            "light0_y0": 20,
            "light0_w": 61,
            "light0_h": 145,
            "light0_direction": 2,
            "light0_kind": 1,
            "light0_shape": 2,
            "light0_type": 0,
            "light1_x0": 2688,
            "light1_y0": 19,
            "light1_w": 60,
            "light1_h": 144,
            "light1_direction": 2,
            "light1_kind": 2,
            "light1_shape": 0,
            "light1_type": 0,
            "left_line_use": 1,
            "left_line_x0": 32,
            "left_line_y0": 33,
            "left_line_x1": 21,
            "left_line_y1": 48,
            "right_line_use": 0,
            "right_line_x0": 0,
            "right_line_y0": 0,
            "right_line_x1": 0,
            "right_line_y1": 0,
            "straight_line_use": 1,
            "straight_line_x0": 43,
            "straight_line_y0": 28,
            "straight_line_x1": 63,
            "straight_line_y1": 27,
            "detect_signal": 0,
            "hmd_enable": 0,
            "line_signal": 14,
            "y_line_signal": 0,
            "extern_signal": 0,
            motuoche_cjl_type: 1,
            motuoche_wpcjl_province: '沪',
            "direction_mask": 1052688,
            "front_flash": 0,
            "b_line_signal": 0,
            "j_line_signal": 0,
            "kakou_feature": 0,
            "weizhang_feature": 0,
            "is_time_reboot": 0,
            "reboot_time": 0,
            "start_flash": 0,
            "end_flash": 0,
            "weizhang_video": 0,
            "wpc_lmd": 0,
            "yongdu": 0,
            "dczyxcd": 0,
            "dczyxcd_lane1": 0,
            "dczyxcd_lane2": 0,
            "dczyxcd_lane3": 0,
            "dczyxcd_lane4": 0,
            "dczyxcd_lane5": 0,
            "dczyxcd_zxhc": 0,
            "dczyxcd_dxkc": 0,
            "dczyxcd_hp": 0,
            "hczykcd": 0,
            "hczykcd_lane1": 0,
            "hczykcd_lane2": 0,
            "hczykcd_lane3": 0,
            "hczykcd_lane4": 0,
            "hczykcd_lane5": 0,
            "hczykcd_zxhc": 0,
            "hczykcd_zxhc2": 0,
            "hczykcd_qxhc": 0,
            "cxcjl": 0,
            "cxcjl_xxkc": 0,
            "cxcjl_qxkc": 0,
            "cxcjl_zxkc": 0,
            "cxcjl_dxkc": 0,
            "cxcjl_qxhc": 0,
            "cxcjl_zxhc2": 0,
            "cxcjl_zxhc": 0,
            "cxcjl_xxkc_blue": 0,
            "cxcjl_xxkc_yellow": 0,
            "cxcjl_qxkc_blue": 0,
            "cxcjl_qxkc_yellow": 0,
            "cxcjl_zxkc_blue": 0,
            "cxcjl_zxkc_yellow": 0,
            "cxcjl_dxkc_blue": 0,
            "cxcjl_dxkc_yellow": 0,
            "cxcjl_qxhc_blue": 0,
            "cxcjl_qxhc_yellow": 0,
            "cxcjl_zxhc2_blue": 0,
            "cxcjl_zxhc2_yellow": 0,
            "cxcjl_zxhc_blue": 0,
            "cxcjl_zxhc_yellow": 0,
            "hmd_wflx": 0,
            "wshcjl_start_time1": 0,
            "wshcjl_end_time1": 0,
            "wshcjl_start_time2": 0,
            "wshcjl_end_time2": 0,
            "wshcjl_start_time3": 0,
            "wshcjl_end_time3": 0,
            "flash_light_lane1": 0,
            "flash_light_lane2": 0,
            "flash_light_lane3": 0,
            "flash_light_lane4": 0,
            "flash_light_lane5": 0,
            "kakou_img_num": 0,
            "road_direct": "南向北",
            "road_code": "320103004",
            "wzdd": "888",
            "road_name": "测试",
            "locate_thresh": 3,
            "ocr_thresh": 3,
            "jugment_signal": 35,
            "head_hz": "",
            "head_letter": "",
            "speed_mode": 0,
            "radar_type": 0,
            "wei_ting_x0": 0,
            "wei_ting_y0": 0,
            "wei_ting_x1": 0,
            "wei_ting_y1": 0,
            "wei_ting_x2": 0,
            "wei_ting_y2": 0,
            "wei_ting_x3": 0,
            "wei_ting_y3": 0,
            "wei_ting_kind": 0,
            "wei_ting_time1": 3000,
            "wei_ting_time2": 3000,
            "wei_ting_time3": 3000,
            "people1_x0": 0,
            "people1_y0": 0,
            "people1_x1": 0,
            "people1_y1": 0,
            "people1_x2": 0,
            "people1_y2": 0,
            "people1_x3": 0,
            "people1_y3": 0,
            "people2_x0": 0,
            "people2_y0": 0,
            "people2_x1": 0,
            "people2_y1": 0,
            "people2_x2": 0,
            "people2_y2": 0,
            "people2_x3": 0,
            "people2_y3": 0,
            "people3_x0": 0,
            "people3_y0": 0,
            "people3_x1": 0,
            "people3_y1": 0,
            "people3_x2": 0,
            "people3_y2": 0,
            "people3_x3": 0,
            "people3_y3": 0,
            "people4_x0": 0,
            "people4_y0": 0,
            "people4_x1": 0,
            "people4_y1": 0,
            "people4_x2": 0,
            "people4_y2": 0,
            "people4_x3": 0,
            "people4_y3": 0,
            "people5_x0": 0,
            "people5_y0": 0,
            "people5_x1": 0,
            "people5_y1": 0,
            "people5_x2": 0,
            "people5_y2": 0,
            "people5_x3": 0,
            "people5_y3": 0,
            "under_ground1_x0": 0,
            "under_ground1_y0": 0,
            "under_ground1_x1": 0,
            "under_ground1_y1": 0,
            "under_ground1_x2": 0,
            "under_ground1_y2": 0,
            "under_ground1_x3": 0,
            "under_ground1_y3": 0,
            "under_ground2_x0": 0,
            "under_ground2_y0": 0,
            "under_ground2_x1": 0,
            "under_ground2_y1": 0,
            "under_ground2_x2": 0,
            "under_ground2_y2": 0,
            "under_ground2_x3": 0,
            "under_ground2_y3": 0,
            "under_ground3_x0": 0,
            "under_ground3_y0": 0,
            "under_ground3_x1": 0,
            "under_ground3_y1": 0,
            "under_ground3_x2": 0,
            "under_ground3_y2": 0,
            "under_ground3_x3": 0,
            "under_ground3_y3": 0,
            "under_ground4_x0": 0,
            "under_ground4_y0": 0,
            "under_ground4_x1": 0,
            "under_ground4_y1": 0,
            "under_ground4_x2": 0,
            "under_ground4_y2": 0,
            "under_ground4_x3": 0,
            "under_ground4_y3": 0,
            "under_ground5_x0": 0,
            "under_ground5_y0": 0,
            "under_ground5_x1": 0,
            "under_ground5_y1": 0,
            "under_ground5_x2": 0,
            "under_ground5_y2": 0,
            "under_ground5_x3": 0,
            "under_ground5_y3": 0,
            "return_enable": 0,
            "return_x0": 0,
            "return_y0": 0,
            "return_x1": 0,
            "return_y1": 0,
            "return_x2": 0,
            "return_y2": 0,
            "return_x3": 0,
            "return_y3": 0,
            "dui_xiang_enable": 0,
            "dui_xiang_x0": 0,
            "dui_xiang_y0": 0,
            "dui_xiang_x1": 0,
            "dui_xiang_y1": 0,
            "dui_xiang_x2": 0,
            "dui_xiang_y2": 0,
            "dui_xiang_x3": 0,
            "dui_xiang_y3": 0,
            "iMatch_X_Threshold": 400,
            "iMatch_Y_Threshold": 600,
            "iGray_Threshold": 15,
            "iPlate_Blue_Grad_Threshold": 100,
            "iPlate_Yellow_Grad_Threshold": 100,
            "iPlate_Blue_Skip_Threshold": 1,
            "iPlate_Yellow_Skip_Threshold": 1,
            "iIs_All": 0,
            "iNight_Gray_Threshold": 10,
            "iDay_To_Night_Threshold": 30,
            "iKakou_Detect_Area": 80,
            "iDetect_Precision": 8,
            "iDelay_Time": 800,
            "iDelay_Dis_img2": 0,
            "iSame_Plate_Time": 0,
            "nxxx_code": 42,
            "wzdt_code": 0,
            "chd_code": 4,
            "yx_code": 88,
            "wfcsgd_code": 1,
            "bd_code": 5,
            "jdxx_code": 23,
            "wt_code": 99,
            "jdcbzjdcd_code": 25,
            "jdcwfgd_code": 0,
            "lkzl_code": 31,
            "zcxx_code": 0,
            "jzzg_code": 11,
            "jzyg_code": 11,
            "cjlwss_code": 90,
            "cjlhc_code": 11,
            "cjljlc_code": 11,
            "cjlwf1_code": 12,
            "cjlwf2_code": 13,
            "cjlwf3_code": 14,
            "cjlwf4_code": 15,
            "cjlwf5_code": 16,
            "cjlwf6_code": 87,
            "cjlwf7_code": 95,
            "cjlwf8_code": 96,
            "hccjl_code": 14,
            "kccjl_code": 15,
            "dczyxcd_code": 24,
            "hczykcd_code": 87,
            "lrxr_code": 0,
            "rxdwt_code": 0,
            "wgxwt_code": 0,
            "jttx_code": 0,
            "dwxz_code": 0,
            "zzbrzx_code": 0,
            "yxtc_code": 0,
            "bmxdt_code": 0,
            "dtyxtx_code": 0,
            "yzblr_code": 0,
            "jzhcszd_code": 14,
            "bkcd_code": 101,
            "lxbd_code": 0,
            "wxaqd_code": 0,
            "dsj_code": 0,
            "bdbsyzxd_code": 0,
            "wfzyyjcd_code": 0,
            "jzf_code": 0,
            "js_code": 0,
            "zwbsyzxd_code": 71,
            "fjdcchd_code": 104,
            "fjdcnx_code": 103,
            "fjdccjl_code": 101,
            "fjdcbdtk_code": 102,
            "fjdkk_code": 100,
            "xrchd_code": 104,
            "jdcchd_code": 103,
            "whpc_code": 0,
            "jdcc_code": 0,
            "jpg_quality": 70,
            "unique_id": "CFBFE2E2CC4C9FFD32B9B9348B65A0A7",
            "auth_status": 0,
            "auth_time": "0",
            "arithmet_status": 1,
            "nnie_status": 1,
            "wshcjl_week0": 1,   //外省市小型车闯禁令的周日
            "wshcjl_week1": 1,   //外省市小型车闯禁令的周一
            "wshcjl_week2": 1,   //外省市小型车闯禁令的周二
            "wshcjl_week3": 1,   //外省市小型车闯禁令的周三
            "wshcjl_week4": 1,   //外省市小型车闯禁令的周四
            "wshcjl_week5": 1,   //外省市小型车闯禁令的周五
            "wshcjl_week6": 1,   //外省市小型车闯禁令的周六
            'wshcjl_province': '鲁',
            'wshcjl_city': 'B,C',


            whxx_num: 1,
            whxx0_date_type: 0,
            whxx0_date: 6,
            whxx0_start_time: 0,
            whxx0_end_time: 4,
            whxx0_local_province: "沪",
            whxx0_local_city: 6,
            whxx0_local_plate_endnum: 7,
            whxx0_nonlocal_plate_endnum: 7,
            kakou_img_count: 0
        })
    }
};



