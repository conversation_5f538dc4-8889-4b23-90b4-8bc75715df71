// var element, layer, form, colorpicker, laydate, slider, table;
// layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate', 'slider', 'table'], function () {
//     element = layui.element, layer = layui.layer, form = layui.form, colorpicker = layui.colorpicker, laydate = layui.laydate, slider = layui.slider, table = layui.table;
//
// })
$(document).ready(function () {
    // initInput();
    $(".input-number").off('focus').on('focus', function (e) {
        $(this).attr("data-oval", $(this).val()); //将当前值存入自定义属性
    }).off('blur').on('blur', function (e) {
        let oldVal = ($(this).attr("data-oval")); //获取原值
        let newVal = ($(this).val()); //获取当前值
        if (oldVal !== newVal) {
            //不相同则做操作
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let v = $(this);
            let value = 0;
            if (newVal > 0) {
                value = newVal > 100 ? 100 : newVal
            } else if (newVal < 0) {
                value = newVal < 0 ? 0 : newVal
            } else {
                value = newVal == '' ? 0 : newVal
            }
            v.val(value);
            let num = $(this).parents('.index-point');
            let name = num[0].className.split(" ");
            name = name[0].split("-");
            name = name[0];
            let index = $(e.currentTarget).parents(".layui-colla-content").find("." + name + "-point").index(num);
            let x = Math.round(($(num).find('.input-number')[0].value) * containerW / 100);
            let y = Math.round(($(num).find('.input-number')[1].value) * containerH / 100);
            $("#" + name + (index + 1)).css({
                left: x - 5,
                top: y - 5
            });
            drawPath(drawCanvas, drawContainer);
        }
    });
    $(".input-up").off("click").on("click", function (e) {
        let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
        let drawContainer = $("#drawContent").find(".draw-container")[0].id;
        let {containerW, containerH, pictureW, pictureH} = getContainerParam();
        let v = $(e.currentTarget).parent().next();
        let num = $(e.currentTarget).parents('.index-point');
        let name = num[0].className.split(" ");
        name = name[0].split("-");
        name = name[0];
        let index = $(e.currentTarget).parents(".layui-colla-content").find("." + name + "-point").index(num);
        v.val(parseInt(v.val()) + 1 > 100 ? 100 : parseInt(v.val()) + 1);
        let x = Math.round(($(num).find('.input-number')[0].value) * containerW / 100);
        let y = Math.round(($(num).find('.input-number')[1].value) * containerH / 100);
        $("#" + name + (index + 1)).css({
            left: x - 5,
            top: y - 5
        });
        drawPath(drawCanvas, drawContainer);
    });
    $(".input-down").off("click").on("click", function (e) {
        let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
        let drawContainer = $("#drawContent").find(".draw-container")[0].id;
        let {containerW, containerH, pictureW, pictureH} = getContainerParam();
        let v = $(e.currentTarget).parent().next();
        v.val(parseInt(v.val()) - 1 < 0 ? 0 : parseInt(v.val()) - 1);
        let num = $(e.currentTarget).parents('.index-point');
        let name = num[0].className.split(" ");
        name = name[0].split("-");
        name = name[0];
        let index = $(e.currentTarget).parents(".layui-colla-content").find("." + name + "-point").index(num);
        let x = Math.round(($(num).find('.input-number')[0].value) * containerW / 100);
        let y = Math.round(($(num).find('.input-number')[1].value) * containerH / 100);
        $("#" + name + (index + 1)).css({
            left: x - 5,
            top: y - 5
        });
        drawPath(drawCanvas, drawContainer);
    });
});
$.fn.vals = function (val) {
    // Our plugin implementation code goes here.
    let arr = [];
    this.find("option").each(function (i) {
        arr.push($(this).val());
    });
    // let v = $(this).val();
    for (let i = 0; i < arr.length; i++) {
        arr[i] = parseInt(arr[i], 16);
    }
    let index = arr.indexOf(parseInt(val));
    $(this).get(0).selectedIndex = index;
};
// layui.use('form', function () {
//     form = layui.form;
//
// });

/**
 * 判断坐标对象是否都为0
 * @param point
 * @returns {boolean}
 */
var checkExist = function (point) {
    let result = false;
    let t1 = typeof point && !isNaN(point.length);
    if (t1) {
        for (let i = 0; i < point.length; i++) {
            // po = [];
            let result = checkExist(point[i]);
            return result;
        }
    } else {
        if (point.x != 0 || point.y != 0) {
            result = true;
        }
        return result;
    }
};
/**
 * 展开数组
 * @param point 传入的数组
 * @param arr 传出的数组
 */
var array2obj = function (point, arr) {
    let t1 = typeof point && !isNaN(point.length);
    if (t1) {
        for (let i = 0; i < point.length; i++) {
            array2obj(point[i], arr)
        }
    } else {
        arr.push(point)
    }
};
/**
 * 将实际坐标归一化
 * @param point
 * @returns {Array}
 */
var coordinate2Value = function (point) {
    let t1 = typeof point && !isNaN(point.length);
    if (t1) {
        let po = [];
        for (let i = 0; i < point.length; i++) {
            // po = [];
            let t = coordinate2Value(point[i]);
            po.push(t)
        }
        return po
    } else {
        let o = {};
        let {containerW, containerH} = getContainerParam();
        let x = Math.round((point.x) / containerW * 100);
        let y = Math.round((point.y) / containerH * 100);
        x > 100 ? x = 100 : "";
        y > 100 ? y = 100 : "";
        x < 0 ? x = 0 : "";
        y < 0 ? y = 0 : "";
        o.x = x;
        o.y = y;
        return o
    }
};
/**
 * 将归一化坐标转换为实际坐标
 * @param point
 * @returns {Array}
 */
var commonCoordinate = function (point) {
    let t1 = typeof point && !isNaN(point.length);
    if (t1) {
        let po = [];
        for (let i = 0; i < point.length; i++) {
            let t = commonCoordinate(point[i]);
            po.push(t)
        }
        return po
    } else {
        let o = {};
        let {containerW, containerH} = getContainerParam();
        o.x = Math.round((point.x / 100) * containerW);
        o.y = Math.round((point.y / 100) * containerH);
        return o
    }
};
/**
 * 根据现有坐标计算实际坐标
 * @param point 传入的现有坐标
 * @param pictureW 图片宽度
 * @param pictureH 图片高度
 * @returns {Array}
 */
var computeCoordinate = function (point, pictureW, pictureH) {
    let t1 = typeof point && !isNaN(point.length);
    if (t1) {
        let po = [];
        for (let i = 0; i < point.length; i++) {
            // po = [];
            let t = computeCoordinate(point[i], pictureW, pictureH);
            po.push(t)
        }
        return po
    } else {
        let o = {};
        let {containerW, containerH} = getContainerParam();
        o.x = Math.round((point.x / pictureW) * containerW);
        o.y = Math.round((point.y / pictureH) * containerH);
        return o
    }
};
/**
 * 将时间转换为分钟或以30分钟为分隔的个数
 * @param date 时间
 * @param unit 转换格式：1为分钟，30为以30分钟分隔
 * @returns {number}
 */
var date2Value = function (date, unit) {
    let reg = /(\d+):(\d+):(\d+)/;
    let value = 0;
    if (reg.test(date)) {
        let arr = date.match(reg);
        if (unit === 1) {
            value = parseInt(arr[1] * 60) + parseInt(arr[2])
        } else if (unit === 30) {
            let m = arr[2] >= 30 ? 1 : 0;
            value = parseInt(arr[1] * 2) + m
        }
    }
    return value;
};
/**
 * 页面CheckBox选择联动
 * @param dataArray 传入的layui-filter值，对应需要禁用区域的className
 */
var lineCheckConfig = ['detectPressingLine', 'illegalChange', 'speedConfig', 'bigCarSmallCar', 'passengerCar', 'frontFlash', 'illegalChangeLight', 'illegalJump'];
var disableFun = function (dataArray) {
    let lineType = getSe("lineType");
    let lineNum = 0;
    if (lineType) {
        lineNum = lineType.lineNum;
    }

    for (let i = 0; i < dataArray.length; i++) {
        let type = dataArray[i];
        layui.use(['form'], function () {
            let form = layui.form;
            form.on('checkbox(' + type + ')', function (data) {
                type = type.replace(/([A-Z])/g, "-$1").toLowerCase();
                if (data.elem.checked === true) {
                    enableFun(type);
                    if (type === 'prohibitType') {
                        $("table.prohibitType td:nth-child(2) input").attr("disabled", "disable");
                        $("table.prohibitType td:nth-child(3) input").attr("disabled", "disable");
                    }
                    if (lineCheckConfig.indexOf(type) > -1) {
                        for (let i = lineNum; i < 5; i++) {
                            if (i < 0) {
                                i = 0
                            }
                            disableFunction(type + "-line:nth-child(" + i + ")");
                        }
                    }
                } else {
                    disableFunction(type);
                    $("." + type + " input:checkbox").each(function () {
                        this.checked = false
                    })
                }
                $("#radarType").attr("disabled", 'disabled');
                form.render()
            });
        })
    }
};
var enableFun = function (type) {
    $("." + type).removeClass('dis');
    $("." + type).removeAttr("disabled");
    $("." + type + " input").removeAttr("disabled");
    $("." + type + " button").removeAttr("disabled");
    $("." + type + " select").removeAttr("disabled");
    $("." + type + " button").removeClass("layui-btn-disabled");

};
var disableFunction = function (type) {
    $("." + type).addClass('dis');
    $("." + type).attr("disabled", 'disabled');
    $("." + type + " input").attr("disabled", 'disabled');
    $("." + type + " button").attr("disabled", 'disabled');
    $("." + type + " select").attr("disabled", 'disabled');
    $("." + type + " button").addClass("layui-btn-disabled");
    // $("." + type + " select").val(0);
    $("." + type + " select").val('');
    $("." + type + " input.input-number").val(0);
};
/**
 * 获取页面上有ID的input text元素并返回一个数组
 * @returns {Array}
 */
var getAllInputHaveID = function () {
    let arr = [];
    $("input:text").each(function () {
        if (this.id != '') {
            arr.push(this.id)
        }
    });
    return arr;
};
/**
 * 获取同类名CheckBox所有值相加的和
 * @param type 传入的className
 * @returns {number}
 */
var getCheckboxValue = function (type) {
    let value = 0;
    $("input:checkbox." + type + ":checked").each(function (i) {
        value += parseInt($(this).val());
    });
    return value
};
var getRdioValue = function (type) {
    return parseInt($('input[name="' + type + '"]:checked').val())
};
/**
 * 根据原始图片中点的坐标获取此点在放大图片中的坐标
 * @param realX 实际点的x或y
 * @param pW 图片相对应的宽度或长度
 * @param lastX 放大区域起始点的x或y
 * @param lastW 放大区域的宽度或长度
 * @param containerW 放大所在容器的宽度或长度
 * @returns {number}
 */
var getContainerPosition = function (realX, pW, lastX, lastW, containerW) {
    /*
   * realX:实际点的x或y
   * pW:图片相对应的宽度或长度
   * lastX:放大区域起始点的x或y
   * lastW:放大区域的宽度或长度
   * containerW:放大所在容器的宽度或长度
   * */
    return parseInt(containerW * (realX * containerW - lastX * pW) / (pW * lastW));
    // return parseInt(containerW * (realX  - lastX * pW) / pW / lastW);
};
/**
 * 获取所选ID的Dom的值，并进行简单处理
 * @param id
 * @returns {*|void|jQuery}
 */
var getIDInput = function (id) {
    let v = $("#" + id).val();
    if (id === 'headHZ' || id === 'roadName' || id === 'roadDirect' || id === 'roadCode' || id === 'selfRoadCode' || id === 'headLetter') {
        if (!v) {
            v = ""
        } else {
            //去空操作
            v = v.replace(/\s*/g, "");
        }
    } else {
        if (!v) {
            v = 0
        } else {
            if (id === 'coef') {
                v = parseFloat(v);
            } else {
                v = parseInt(v);
            }
        }
    }
    return v
};
/**
 * 根据放大图片中的坐标获取此点在原始图片中的坐标
 * @param currentX 当前的坐标点的x或y
 * @param pW 图片相对应的宽度或长度
 * @param lastX 放大区域起始点的x或y
 * @param lastW 放大区域的宽度或长度
 * @param containerW 放大所在容器的宽度或长度
 * @returns {number}
 */
var getRealPosition = function (currentX, pW, lastX, lastW, containerW) {
    /*
    * currentX:当前的坐标点的x或y
    * pW:图片相对应的宽度或长度
    * lastX:放大区域起始点的x或y
    * lastW:放大区域的宽度或长度
    * containerW:放大所在容器的宽度或长度
    * */
    return parseInt(pW * (lastW * currentX + lastX * containerW) / (containerW * containerW))
};

var getSelectValue = function (id) {
    if ($("#" + id + " option:selected").val()) {
        return parseInt($("#" + id + " option:selected").val())
    } else {
        return 0
    }
};

var newDIV = function (type, doubleNum, num, group, groupNum, x, y) {
    console.log('newDIV function：type = ' + type)
    let groupType = "";
    if (groupNum) {
        groupType = group + '-group' + groupNum
    }
    let doubleType = "";
    if (doubleNum) {
        doubleType = type + '-can' + doubleNum
    }
    let poDiv = $('<div></div>');
    poDiv.attr({
        "class": "mini-box " + type + "-can " + doubleType + " " + groupType,
        "id": type + num,
    });
    poDiv.css({
        "left": x - 5,
        "top": y - 5
    });
    return poDiv;
};

var status2JSON = function (data) {
    if (data) {
        let info = data;
        for (let key in info) {
            switch (key) {
                case 'product_function':
                    info[key] = (info[key] === 0 ? langMessage.setting.UNKNOWN : info[key]);
                    break;
                case 'has_hdd':
                    info[key] = langMessage.setting.statusInfo.has_hdd[info[key]];
                    break;
                case 'partition_mask':
                    if (info[key] === 7) {
                        info[key] = langMessage.setting.statusInfo.NORMAL
                    } else {
                        info[key] = formatInfo(info[key], langMessage.setting.statusInfo.NOT_EXIST);
                    }
                    break;
                case 'fs_mask':
                    if (info[key] === 7) {
                        info[key] = langMessage.setting.statusInfo.NORMAL
                    } else {
                        info[key] = formatInfo(info[key], langMessage.setting.statusInfo.ABNORMAL);
                    }
                    break;
                case 'disk_checking':
                    info[key] = formatInfo(info[key], langMessage.setting.statusInfo.CHECKING);
                    break;
                case 'disk_formating':
                    info[key] = formatInfo(info[key], langMessage.setting.statusInfo.FORMATTING);
                    break;
                case 'fs_status':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NORMAL, langMessage.setting.statusInfo.DAMAGED);
                    break;
                case 'has_sd':
                    // if (Object.keys(langMessage.setting.statusInfo.has_sd).indexOf(info[key].toString()) - 1) {
                    if (langMessage.setting.statusInfo.has_sd[info[key]]) {
                        info[key] = langMessage.setting.statusInfo.has_sd[info[key]]
                    } else {
                        info[key] = langMessage.setting.statusInfo.ABNORMAL
                    }
                    break;
                case 'sd_fs_status':
                    // if (Object.keys(langMessage.setting.statusInfo.sd_fs_status).indexOf(info[key].toString()) - 1) {
                    if (langMessage.setting.statusInfo.sd_fs_status[info[key]]) {
                        info[key] = langMessage.setting.statusInfo.sd_fs_status[info[key]]
                    } else {
                        info[key] = langMessage.setting.statusInfo.ABNORMAL
                    }
                    break;
                case 'ftp_connect_status':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.LOGIN, langMessage.setting.statusInfo.DISCONNECT);
                    break;
                case 'config_status':
                    info[key] = langMessage.setting.statusInfo.config_status[info[key]];
                    break;
                case 'extern_light_status':
                    // if (Object.keys(langMessage.setting.statusInfo.extern_light_status).indexOf(info[key].toString()) - 1) {
                    if (langMessage.setting.statusInfo.extern_light_status[info[key]]) {
                        info[key] = langMessage.setting.statusInfo.extern_light_status[info[key]]
                    } else {
                        info[key] = langMessage.setting.statusInfo.ABNORMAL
                    }
                    break;
                case 'radar_status':
                    // if (Object.keys(langMessage.setting.statusInfo.radar_status).indexOf(info[key].toString()) - 1) {
                    if (langMessage.setting.statusInfo.radar_status[info[key]]) {
                        info[key] = langMessage.setting.statusInfo.radar_status[info[key]]
                    } else {
                        info[key] = langMessage.setting.statusInfo.ABNORMAL
                    }
                    break;
                case 'data_status':
                    info[key] = langMessage.setting.statusInfo.data_status[info[key]];
                    break;
                case 'log_status':
                    info[key] = langMessage.setting.statusInfo.log_status[info[key]];
                    break;
                case 'device_time':
                    info[key] = (info[key] === 0 ? langMessage.setting.statusInfo.NONE : info[key]);
                    break;
                case 'ntp_success_time':
                    info[key] = (info[key] === 0 ? langMessage.setting.statusInfo.NONE : info[key]);
                    break;
                case 'camera_kind':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.ABNORMAL, info[key], 1);
                    break;
                case 'config_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'timing_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'last_image_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'last_video_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'last_kakou_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'last_violate_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'ftp_login_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'ftp_access_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
                case 'ftp_error_time':
                    info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NONE, info[key]);
                    break;
            }
            // if (key === 'cs_status' || key === 'dsp_status' || key === 'hjbl_status' || key === 'extern_light_status' || key === 'ntp_connect_status' || key === 'cmd_connect_status' || key === 'data_connect_status' || key === 'frame_err'
            //     || key === 'kakou_status' || key === 'light_status') {
            if (key === 'cs_status' || key === 'dsp_status' || key === 'hjbl_status' || key === 'ntp_connect_status' || key === 'cmd_connect_status' || key === 'data_connect_status' || key === 'frame_err'
                || key === 'kakou_status' || key === 'light_status') {
                info[key] = checkStatus(info[key], langMessage.setting.statusInfo.NORMAL, langMessage.setting.statusInfo.ABNORMAL)
            }
            if (key === 'left_status' || key === 'straight_status' || key === 'right_status') {
                info[key] = formatStatus(info[key])
            }
        }
        assignmentValue(info);
        // setSe(data, 'statusInfo')
    }
};
let checkStatus = function (status, info1, info2, s) {
    let checkS = 0;
    if (s) {
        checkS = s
    }
    if (status === checkS) {
        return info1;
    } else {
        return info2;
    }
};
let formatInfo = function (status, info) {
    let mask = '';

    if (!('0x0001' & status)) {
        mask += langMessage.setting.statusInfo.disk[1]
    }
    if (!('0x0002' & status)) {
        mask += langMessage.setting.statusInfo.disk[2]
    }
    if (!('0x0004' & status)) {
        mask += langMessage.setting.statusInfo.disk[4]
    }
    mask += info;
    return mask;
};
let formatStatus = function (status) {
    if (status === 0) {
        return langMessage.setting.statusInfo.NORMAL;
    } else if (status === -1) {
        return langMessage.setting.statusInfo.CROWD;
    } else if (status === -2) {
        return langMessage.setting.statusInfo.EXTREME + langMessage.setting.statusInfo.CROWD;
    }
};

var assignmentValue = function (data) {
    for (let key in data) {
        $("#" + key).html(data[key]);
        if (key === 'sd_capacity') {
            $("#" + key).html(data[key] + 'MB');
        }
    }
};
var assignmentInputValue = function (data) {
    for (let key in data) {
        $("#" + key).val(data[key]);
    }
    form.render();
};
var setTypeArray = function (data, type, container) {
    for (let i = 1; i < 6; i++) {
        let name = type + i;
        if (data[i - 1] !== undefined) {
            container[name] = data[i - 1];
        }
    }
};

var getTypeArray = function (data, type) {
    let types = [];
    for (let i = 1; i < 6; i++) {
        let name = type + i;
        types[i - 1] = data[name];
    }
    return types
};

var coordinate2Real = function (point, id, type) {

    let p = {};
    p.id = id;
    p.type = type;
    // if (id === 'redStop') {
    //     p.use = point[0].use;
    //     point = point[0].position;
    // }
    p.position = commonCoordinate(point);
    return p;
};
var getPoint = function (type, value, num) {
    let points = [];
    let reg = /^light\d+$/;
    if (reg.test(type)) {
        let point1 = {}, point2 = {};
        let xName = type + '_x0';
        let yName = type + '_y0';
        let wName = type + '_w';
        let hName = type + '_h';
        point1.x = value[xName];
        point1.y = value[yName];
        point2.x = point1.x + value[wName];
        point2.y = point1.y + value[hName];
        points.push(point1, point2);
        return points
    }
    for (let i = 0; i < num; i++) {
        let point = {};
        let xName = type + "_x" + i;
        let yName = type + "_y" + i;
        point.x = value[xName];
        point.y = value[yName];
        points.push(point)
    }
    return points;
};

var setPoint = function (type, value, num, container) {
    let reg = /^light\d+$/;
    if (reg.test(type)) {
        let xName = type + '_x0';
        let yName = type + '_y0';
        let wName = type + '_w';
        let hName = type + '_h';
        container[xName] = Math.min(value[0].x, value[1].x);
        container[yName] = Math.min(value[0].y, value[1].y);
        container[wName] = Math.abs(value[1].x - value[0].x);
        container[hName] = Math.abs(value[1].y - value[0].y);
    }
    if (value.length > 0) {
        for (let i = 0; i < num; i++) {
            let xName = type + "_x" + i;
            let yName = type + "_y" + i;
            container[xName] = value[i].x;
            container[yName] = value[i].y;
        }
    }
};


var setSize = function (w, h, f) {
    if (f) {
        f.find("#drawContent").css({width: w, height: h});
        f.find("canvas.draw-canvas").attr({width: w, height: h});
        f.find("img.draw-img").css({width: w, height: h});
        f.find("div.draw-container").css({width: w, height: h});
        f.find("div.clickZone").css({width: w, height: h});
        f.find("div.point").css({width: w, height: h});
    }
    $("#drawContent").css({width: w, height: h});
    $("canvas.draw-canvas").attr({width: w, height: h});
    $("img.draw-img").css({width: w, height: h});
    $("div.draw-container").css({width: w, height: h});
    $("div.clickZone").css({width: w, height: h});
    $("div.point").css({width: w, height: h});
};
var showRadioValue = function (type, values) {
    $('input[name="' + type + '"][value="' + values + '"]').prop('checked', 'checked')
}
var showCheckboxValue = function (type, values) {
    let detectCheck = $('input:checkbox.' + type);
    detectCheck.each(function (i) {
        if (this.value & values) {
            this.checked = true;
            // this.disabled = false;
            let filter = $(this).attr('lay-filter');
            if (filter) {
                filter = filter.replace(/([A-Z])/g, "-$1").toLowerCase();
                $("." + filter).removeClass("dis");
                $("." + filter + " input").removeAttr("disabled");
                $("." + filter + " button").removeAttr("disabled");
                $("." + filter + " select").removeAttr("disabled");
                $("." + filter + " button").removeClass("layui-btn-disabled");
                if (filter === "speedConfig") {
                    let lineType = getSe("lineType");
                    let lineNum = lineType.lineNum;
                    for (let i = lineNum; i < 5; i++) {
                        if (i < 0) {
                            i = 0
                        }
                        disableFunction("lineSpeedConfig tr:nth-child(" + i + ")");
                    }
                }
            }
        }
    })
};
var validatePoint = function (value) {
    let result = false;
    let t1 = typeof value && !isNaN(value.length);
    if (t1) {
        for (let i = 0; i < value.length; i++) {
            result = validatePoint(value[i])
        }
    } else {
        if (value.x !== 0 || value.y !== 0) {
            result = true;
            return result
        }
    }
    return result;
};
// var validatePoint = function (value, result) {
//     // let result = false;
//     let t1 = typeof value && !isNaN(value.length);
//     if (t1) {
//         for (let i = 0; i < value.length; i++) {
//             result = validatePoint(value[i], result)
//         }
//     } else {
//         if (value.x !== 0 || value.y !== 0) {
//             result = true;
//             return result
//         } else {
//             result = result ? result : false;
//         }
//     }
//     return result;
// };
var value2Date = function (value, unit) {
    if (!value) {
        value = 0
    }
    value = parseInt(value);
    let hour = 0, minute = 0;
    if (unit === 1) {
        hour = parseInt(value / 60);
        minute = value % 60;

    } else if (unit === 30) {
        hour = value / 2;
        minute = (value % 2) * 30
    }
    if (hour < 10) {
        hour = '0' + hour
    }
    if (minute < 10) {
        minute = '0' + minute
    }
    return hour + ':' + minute + ':00'
};
var Real2signal = function (point, id, type, pictureW, pictureH) {
    let p = {};
    p.id = id;
    p.type = type;
    p.position = computeCoordinate(point, pictureW, pictureH);
    return p;
};
var reloadConfig = function (callbackParam) {
    clearSession(['loginUser', 'globalInfo', 'projectConfig', 'noMsg', 'eventTime', 'osdMsg', 'rpmConfig', 'noCheckTime', 'fileInit', 'SDC', 'langMessage']);
    initWebService(webserviceCMD.CMD_GET_CONFIG, null, parseValue2JSON, callbackParam);
    let mode = getSe("projectConfig").mode;
    if (mode === 'local-dev') {
        parseValue2JSON({
            "ret": 0,
            "desc": "success",
            extern_light_status: 0,
            jdc_feature: 0,
            fjdc_feature: 0,
            "device_uid": "F1FDEEF701E1FFE1004E000800130000",
            "device_type": "",
            "manufacturer": "",
            "hardware_ver": "TSIC-H2-HW900A",
            "software_ver": "16051223122",
            "communication_ver": "100820 its0.1",
            "configuration_ver": "110224 mb0.2",
            "image_width": 4096,
            "image_height": 2160,
            "process_width": 4096,
            "process_height": 2160,
            "board_name": "Unknown",
            "ip_addr": "************",
            "net_mask": "*************",
            "gate_way": "**********",
            "port": 3128,
            "transmit_addr": "************",
            "transmit_port": 12012,
            "forward_plateform_ip": "0.0.0.0",
            "forward_plateform_port": 21,
            "ftp_ip": "0.0.0.0",
            "ftp_Port": 21,
            "ftp_data": 0,
            "ftp_user": "topsky",
            "ftp_password": "topsky",
            "ftp_video_days": 5,
            "ftp_kk_days": 5,
            "ftp_wz_days": 5,
            "channel_num": 1,
            "lane_direction": 0,
            "lane_start": 0,
            "cart_right_line_use": 0,
            "cart_right_line_x0": 0,
            "cart_right_line_y0": 0,
            "cart_right_line_x1": 0,
            "cart_right_line_y1": 0,
            "up_calibration_queue_length": 0,
            "up_calibration_line_use": 0,
            "up_calibration_line_x0": 0,
            "up_calibration_line_y0": 0,
            "up_calibration_line_x1": 0,
            "up_calibration_line_y1": 0,
            "down_calibration_queue_length": 0,
            "down_calibration_line_use": 0,
            "down_calibration_line_x0": 0,
            "down_calibration_line_y0": 0,
            "down_calibration_line_x1": 0,
            "down_calibration_line_y1": 0,
            "zone_x0": 6,
            "zone_y0": 98,
            "zone_x1": 98,
            "zone_y1": 98,
            "zone_x2": 80,
            "zone_y2": 38,
            "zone_x3": 34,
            "zone_y3": 40,
            "min_plate_width": 56,
            "max_plate_width": 102,
            "lane_num": 4,
            "lane0_x0": 7,
            "lane0_y0": 97,
            "lane0_x1": 34,
            "lane0_y1": 39,
            "lane0_type": 0,
            "lane1_x0": 30,
            "lane1_y0": 98,
            "lane1_x1": 37,
            "lane1_y1": 66,
            "lane1_type": 0,
            "lane1_direction": 0,
            "lane1_kind": 1,
            "lane1_leftwait_zone": 0,
            "lane1_special": 0,
            "lane1_gongjiao_start1": 0,
            "lane1_gongjiao_end1": 0,
            "lane1_gongjiao_start2": 0,
            "lane1_gongjiao_end2": 0,
            "lane1_gongjiao_start3": 0,
            "lane1_gongjiao_end3": 0,
            "lane2_min_speed": 0,
            "lane2_max_speed": 0,
            "lane2_min_limit_speed": 10,
            "lane2_max_limit_speed": 20,
            "lane2_x0": 53,
            "lane2_y0": 99,
            "lane2_x1": 53,
            "lane2_y1": 68,
            "lane2_type": 0,
            "lane2_direction": 0,
            "lane2_kind": 2,
            "lane2_leftwait_zone": 0,
            "lane2_special": 0,
            "lane2_gongjiao_start1": 0,
            "lane2_gongjiao_end1": 0,
            "lane2_gongjiao_start2": 0,
            "lane2_gongjiao_end2": 0,
            "lane2_gongjiao_start3": 0,
            "lane2_gongjiao_end3": 0,
            "lane3_min_speed": 0,
            "lane3_max_speed": 0,
            "lane3_x0": 77,
            "lane3_y0": 98,
            "lane3_x1": 70,
            "lane3_y1": 68,
            "lane3_type": 0,
            "lane3_direction": 0,
            "lane3_kind": 2,
            "lane3_leftwait_zone": 0,
            "lane3_special": 0,
            "lane3_gongjiao_start1": 0,
            "lane3_gongjiao_end1": 0,
            "lane3_gongjiao_start2": 0,
            "lane3_gongjiao_end2": 0,
            "lane3_gongjiao_start3": 0,
            "lane3_gongjiao_end3": 0,
            "lane4_min_speed": 0,
            "lane4_max_speed": 0,
            "lane4_x0": 98,
            "lane4_y0": 94,
            "lane4_x1": 81,
            "lane4_y1": 44,
            "lane4_type": 0,
            "lane4_direction": 0,
            "lane4_kind": 2,
            "lane4_leftwait_zone": 0,
            "lane4_special": 0,
            "lane4_gongjiao_start1": 0,
            "lane4_gongjiao_end1": 0,
            "lane4_gongjiao_start2": 0,
            "lane4_gongjiao_end2": 0,
            "lane4_gongjiao_start3": 0,
            "lane4_gongjiao_end3": 0,
            "lane5_min_speed": 0,
            "lane5_max_speed": 0,
            "velo_enable": 0,
            "camera_height": 0,
            "camera_img_top": 0,
            "camera_img_down": 0,
            "coef": 100,
            "min_speed": 0,
            "max_speed": 0,
            "ai_mode": 0,
            "light_line_mode": 0,
            "light_line_single_use": 1,
            "light_line_single_x0": 23,
            "light_line_single_y0": 63,
            "light_line_single_x1": 87,
            "light_line_single_y1": 61,
            "light_line0_use": 0,
            "light_line0_x0": 0,
            "light_line0_y0": 0,
            "light_line0_x1": 0,
            "light_line0_y1": 0,
            "light_line1_use": 0,
            "light_line1_x0": 0,
            "light_line1_y0": 0,
            "light_line1_x1": 0,
            "light_line1_y1": 0,
            "light_line2_use": 0,
            "light_line2_x0": 0,
            "light_line2_y0": 0,
            "light_line2_x1": 0,
            "light_line2_y1": 0,
            "light_line3_use": 0,
            "light_line3_x0": 0,
            "light_line3_y0": 0,
            "light_line3_x1": 0,
            "light_line3_y1": 0,
            "light_line4_use": 0,
            "light_line4_x0": 0,
            "light_line4_y0": 0,
            "light_line4_x1": 0,
            "light_line4_y1": 0,
            "light_enable": 1,
            "out_light_enable": 0,
            "out_light_people": 0,
            "out_light_left": 0,
            "out_light_straight": 0,
            "out_light_right": 0,
            "red_light_delay": 3,
            "yellow_mode": 0,
            "yellow_mode_start": 0,
            "yellow_mode_end": 0,
            "red_light_enhance": 4,
            "red_light_enhance_begin_time": 0,
            "red_light_enhance_end_time": 1439,
            "light_num": 2,
            "light0_x0": 2614,
            "light0_y0": 20,
            "light0_w": 61,
            "light0_h": 145,
            "light0_direction": 2,
            "light0_kind": 1,
            "light0_shape": 2,
            "light0_type": 0,
            "light1_x0": 2688,
            "light1_y0": 19,
            "light1_w": 60,
            "light1_h": 144,
            "light1_direction": 2,
            "light1_kind": 2,
            "light1_shape": 0,
            "light1_type": 0,
            "left_line_use": 1,
            "left_line_x0": 32,
            "left_line_y0": 33,
            "left_line_x1": 21,
            "left_line_y1": 48,
            "right_line_use": 0,
            "right_line_x0": 0,
            "right_line_y0": 0,
            "right_line_x1": 0,
            "right_line_y1": 0,
            "straight_line_use": 1,
            "straight_line_x0": 43,
            "straight_line_y0": 28,
            "straight_line_x1": 63,
            "straight_line_y1": 27,
            "detect_signal": 0,
            "hmd_enable": 0,
            "line_signal": 14,
            "y_line_signal": 0,
            "extern_signal": 0,
            motuoche_cjl_type: 1,
            motuoche_wpcjl_province: '沪',
            "direction_mask": 1052688,
            "front_flash": 0,
            "b_line_signal": 0,
            "j_line_signal": 0,
            "kakou_feature": 0,
            "weizhang_feature": 0,
            "is_time_reboot": 0,
            "reboot_time": 0,
            "start_flash": 0,
            "end_flash": 0,
            "weizhang_video": 0,
            "wpc_lmd": 0,
            "yongdu": 0,
            "dczyxcd": 0,
            "dczyxcd_lane1": 0,
            "dczyxcd_lane2": 0,
            "dczyxcd_lane3": 0,
            "dczyxcd_lane4": 0,
            "dczyxcd_lane5": 0,
            "dczyxcd_zxhc": 0,
            "dczyxcd_dxkc": 0,
            "dczyxcd_hp": 0,
            "hczykcd": 0,
            "hczykcd_lane1": 0,
            "hczykcd_lane2": 0,
            "hczykcd_lane3": 0,
            "hczykcd_lane4": 0,
            "hczykcd_lane5": 0,
            "hczykcd_zxhc": 0,
            "hczykcd_zxhc2": 0,
            "hczykcd_qxhc": 0,
            "cxcjl": 0,
            "cxcjl_xxkc": 0,
            "cxcjl_qxkc": 0,
            "cxcjl_zxkc": 0,
            "cxcjl_dxkc": 0,
            "cxcjl_qxhc": 0,
            "cxcjl_zxhc2": 0,
            "cxcjl_zxhc": 0,
            "cxcjl_xxkc_blue": 0,
            "cxcjl_xxkc_yellow": 0,
            "cxcjl_qxkc_blue": 0,
            "cxcjl_qxkc_yellow": 0,
            "cxcjl_zxkc_blue": 0,
            "cxcjl_zxkc_yellow": 0,
            "cxcjl_dxkc_blue": 0,
            "cxcjl_dxkc_yellow": 0,
            "cxcjl_qxhc_blue": 0,
            "cxcjl_qxhc_yellow": 0,
            "cxcjl_zxhc2_blue": 0,
            "cxcjl_zxhc2_yellow": 0,
            "cxcjl_zxhc_blue": 0,
            "cxcjl_zxhc_yellow": 0,
            "hmd_wflx": 0,
            "wshcjl_start_time1": 0,
            "wshcjl_end_time1": 0,
            "wshcjl_start_time2": 0,
            "wshcjl_end_time2": 0,
            "wshcjl_start_time3": 0,
            "wshcjl_end_time3": 0,
            "flash_light_lane1": 0,
            "flash_light_lane2": 0,
            "flash_light_lane3": 0,
            "flash_light_lane4": 0,
            "flash_light_lane5": 0,
            "kakou_img_num": 0,
            "road_direct": "南向北",
            "road_code": "320103004",
            "wzdd": "888",
            "road_name": "测试",
            "locate_thresh": 3,
            "ocr_thresh": 3,
            "jugment_signal": 35,
            "head_hz": "",
            "head_letter": "",
            "speed_mode": 0,
            "radar_type": 0,
            "wei_ting_x0": 0,
            "wei_ting_y0": 0,
            "wei_ting_x1": 0,
            "wei_ting_y1": 0,
            "wei_ting_x2": 0,
            "wei_ting_y2": 0,
            "wei_ting_x3": 0,
            "wei_ting_y3": 0,
            "wei_ting_kind": 0,
            "wei_ting_time1": 3000,
            "wei_ting_time2": 3000,
            "wei_ting_time3": 3000,
            "people1_x0": 0,
            "people1_y0": 0,
            "people1_x1": 0,
            "people1_y1": 0,
            "people1_x2": 0,
            "people1_y2": 0,
            "people1_x3": 0,
            "people1_y3": 0,
            "people2_x0": 0,
            "people2_y0": 0,
            "people2_x1": 0,
            "people2_y1": 0,
            "people2_x2": 0,
            "people2_y2": 0,
            "people2_x3": 0,
            "people2_y3": 0,
            "people3_x0": 0,
            "people3_y0": 0,
            "people3_x1": 0,
            "people3_y1": 0,
            "people3_x2": 0,
            "people3_y2": 0,
            "people3_x3": 0,
            "people3_y3": 0,
            "people4_x0": 0,
            "people4_y0": 0,
            "people4_x1": 0,
            "people4_y1": 0,
            "people4_x2": 0,
            "people4_y2": 0,
            "people4_x3": 0,
            "people4_y3": 0,
            "people5_x0": 0,
            "people5_y0": 0,
            "people5_x1": 0,
            "people5_y1": 0,
            "people5_x2": 0,
            "people5_y2": 0,
            "people5_x3": 0,
            "people5_y3": 0,
            "under_ground1_x0": 0,
            "under_ground1_y0": 0,
            "under_ground1_x1": 0,
            "under_ground1_y1": 0,
            "under_ground1_x2": 0,
            "under_ground1_y2": 0,
            "under_ground1_x3": 0,
            "under_ground1_y3": 0,
            "under_ground2_x0": 0,
            "under_ground2_y0": 0,
            "under_ground2_x1": 0,
            "under_ground2_y1": 0,
            "under_ground2_x2": 0,
            "under_ground2_y2": 0,
            "under_ground2_x3": 0,
            "under_ground2_y3": 0,
            "under_ground3_x0": 0,
            "under_ground3_y0": 0,
            "under_ground3_x1": 0,
            "under_ground3_y1": 0,
            "under_ground3_x2": 0,
            "under_ground3_y2": 0,
            "under_ground3_x3": 0,
            "under_ground3_y3": 0,
            "under_ground4_x0": 0,
            "under_ground4_y0": 0,
            "under_ground4_x1": 0,
            "under_ground4_y1": 0,
            "under_ground4_x2": 0,
            "under_ground4_y2": 0,
            "under_ground4_x3": 0,
            "under_ground4_y3": 0,
            "under_ground5_x0": 0,
            "under_ground5_y0": 0,
            "under_ground5_x1": 0,
            "under_ground5_y1": 0,
            "under_ground5_x2": 0,
            "under_ground5_y2": 0,
            "under_ground5_x3": 0,
            "under_ground5_y3": 0,
            "return_enable": 0,
            "return_x0": 0,
            "return_y0": 0,
            "return_x1": 0,
            "return_y1": 0,
            "return_x2": 0,
            "return_y2": 0,
            "return_x3": 0,
            "return_y3": 0,
            "dui_xiang_enable": 0,
            "dui_xiang_x0": 0,
            "dui_xiang_y0": 0,
            "dui_xiang_x1": 0,
            "dui_xiang_y1": 0,
            "dui_xiang_x2": 0,
            "dui_xiang_y2": 0,
            "dui_xiang_x3": 0,
            "dui_xiang_y3": 0,
            "iMatch_X_Threshold": 400,
            "iMatch_Y_Threshold": 600,
            "iGray_Threshold": 15,
            "iPlate_Blue_Grad_Threshold": 100,
            "iPlate_Yellow_Grad_Threshold": 100,
            "iPlate_Blue_Skip_Threshold": 1,
            "iPlate_Yellow_Skip_Threshold": 1,
            "iIs_All": 0,
            "iNight_Gray_Threshold": 10,
            "iDay_To_Night_Threshold": 30,
            "iKakou_Detect_Area": 80,
            "iDetect_Precision": 8,
            "iDelay_Time": 800,
            "iDelay_Dis_img2": 0,
            "iSame_Plate_Time": 0,
            "nxxx_code": 42,
            "wzdt_code": 0,
            "chd_code": 4,
            "yx_code": 88,
            "wfcsgd_code": 1,
            "bd_code": 5,
            "jdxx_code": 23,
            "wt_code": 99,
            "jdcbzjdcd_code": 25,
            "jdcwfgd_code": 0,
            "lkzl_code": 31,
            "zcxx_code": 0,
            "jzzg_code": 11,
            "jzyg_code": 11,
            "cjlwss_code": 90,
            "cjlhc_code": 11,
            "cjljlc_code": 11,
            "cjlwf1_code": 12,
            "cjlwf2_code": 13,
            "cjlwf3_code": 14,
            "cjlwf4_code": 15,
            "cjlwf5_code": 16,
            "cjlwf6_code": 87,
            "cjlwf7_code": 95,
            "cjlwf8_code": 96,
            "hccjl_code": 14,
            "kccjl_code": 15,
            "dczyxcd_code": 24,
            "hczykcd_code": 87,
            "lrxr_code": 0,
            "rxdwt_code": 0,
            "wgxwt_code": 0,
            "jttx_code": 0,
            "dwxz_code": 0,
            "zzbrzx_code": 0,
            "yxtc_code": 0,
            "bmxdt_code": 0,
            "dtyxtx_code": 0,
            "yzblr_code": 0,
            "jzhcszd_code": 14,
            "bkcd_code": 101,
            "lxbd_code": 0,
            "wxaqd_code": 0,
            "dsj_code": 0,
            "bdbsyzxd_code": 0,
            "wfzyyjcd_code": 0,
            "jzf_code": 0,
            "js_code": 0,
            "zwbsyzxd_code": 71,
            "fjdcchd_code": 104,
            "fjdcnx_code": 103,
            "fjdccjl_code": 101,
            "fjdcbdtk_code": 102,
            "fjdkk_code": 100,
            "xrchd_code": 104,
            "jdcchd_code": 103,
            "whpc_code": 0,
            "jdcc_code": 0,
            "jpg_quality": 70,
            "unique_id": "CFBFE2E2CC4C9FFD32B9B9348B65A0A7",
            "auth_status": 0,
            "auth_time": "0",
            "arithmet_status": 1,
            "nnie_status": 1,
            "wshcjl_week0": 1,   //外省市小型车闯禁令的周日
            "wshcjl_week1": 1,   //外省市小型车闯禁令的周一
            "wshcjl_week2": 1,   //外省市小型车闯禁令的周二
            "wshcjl_week3": 1,   //外省市小型车闯禁令的周三
            "wshcjl_week4": 1,   //外省市小型车闯禁令的周四
            "wshcjl_week5": 1,   //外省市小型车闯禁令的周五
            "wshcjl_week6": 1,   //外省市小型车闯禁令的周六
            'wshcjl_province': '鲁',
            'wshcjl_city': 'B,C',


            whxx_num: 1,
            whxx0_date_type: 0,
            whxx0_date: 6,
            whxx0_start_time: 0,
            whxx0_end_time: 4,
            whxx0_local_province: "沪",
            whxx0_local_city: 6,
            whxx0_local_plate_endnum: 7,
            whxx0_nonlocal_plate_endnum: 7,
            kakou_img_count: 0
        })
    }
};


var parseValue2JSON = function (value, callback) {
    let data = value;
    if (data.ret === 0 && data.desc === 'success') {
        //存储图片
        let imgInfo = {};
        let imgWidth = data['image_width'];
        let imgHeight = data['image_height'];
        let globalInfo = checkGlobal();
        if (imgWidth && imgHeight) {
            imgInfo.width = imgWidth;
            imgInfo.height = imgHeight;
            imgInfo.multiple = imgInfo.width / globalInfo.w;
            imgInfo.containerWidth = globalInfo.w;
            imgInfo.containerHeight = imgInfo.height / imgInfo.multiple;
            globalInfo.h = imgInfo.containerHeight;
            setSe('globalInfo', globalInfo);
            imgInfo.src = "../../img/404.png";
        } else {
            imgInfo.width = globalInfo.w;
            imgInfo.height = globalInfo.h;
            imgInfo.multiple = 1;
            imgInfo.containerWidth = globalInfo.w;
            imgInfo.containerHeight = globalInfo.h;
            imgInfo.src = "../../img/404.png";
        }
        setSe('imgInfo', imgInfo);

        //设备信息
        let deviceInfo = {};
        //deviceInfo.board_name = data['board_name'];
        deviceInfo.device_uid = data['device_uid'];
        deviceInfo.device_type = data['device_type'];
        deviceInfo.manufacturer = data['manufacturer'];
        deviceInfo.hardware_ver = data['hardware_ver'];
        deviceInfo.software_ver = data['software_ver'];
        deviceInfo.communication_ver = data['communication_ver'];
        deviceInfo.configuration_ver = data['configuration_ver'];
        deviceInfo.ip_addr = data['ip_addr'];
        deviceInfo.net_mask = data['net_mask'];
        deviceInfo.gate_way = data['gate_way'];
        deviceInfo.port = data['port'];
        deviceInfo.transmit_addr = data['transmit_addr'];
        deviceInfo.transmit_port = data['transmit_port'];
        deviceInfo.unique_id = data['unique_id'];
        deviceInfo.auth_status = data['auth_status'];
        deviceInfo.auth_time = data['auth_time'] == 0 ? '永久' : data['auth_time'];
        deviceInfo.arithmet_status = data['arithmet_status'];
        deviceInfo.nnie_status = data['nnie_status'];


        setSe('deviceInfo', deviceInfo);
        let deviceName = data['board_name'];
        setSe('deviceName', deviceName);
        let is_time_reboot = data['is_time_reboot'];
        setSe('isReboot', is_time_reboot);
        let reboot_time = data['reboot_time'];
        setSe('rebootTime', reboot_time);

        //检测区
        let surveyValue = getPoint('zone', data, 4);
        setSe('surveyValue', surveyValue);
        if (validatePoint(surveyValue)) {
            let survey = coordinate2Real(surveyValue, 'survey', 'common-rect');
            setSe('survey', survey);
        }

        let noParkingValue = getPoint('wei_ting', data, 4);
        setSe('noParkingValue', noParkingValue);
        if (validatePoint(noParkingValue)) {
            let noParking = coordinate2Real(noParkingValue, 'noParking', 'common-rect');
            setSe('noParking', noParking);
        }

        //掉头检测区域
        let aroundValue = getPoint('return', data, 4);
        setSe('aroundValue', aroundValue);
        if (validatePoint(aroundValue)) {
            let around = coordinate2Real(aroundValue, 'around', 'common-rect');
            setSe('around', around);
        }


        let faceValue = getPoint('dui_xiang', data, 4);
        setSe('faceValue', faceValue);
        if (validatePoint(faceValue)) {
            let face = coordinate2Real(faceValue, 'face', 'common-rect');
            setSe('face', face);
        }

        let plateMin = data.min_plate_width;
        setSe('plateMin', plateMin);

        let plateMax = data.max_plate_width;
        setSe('plateMax', plateMax);

        let noParkingType = data.wei_ting_kind;
        setSe('noParkingType', noParkingType);

        //左转分界线
        let turnLeftValue = {};
        let turnLeftValueUse = data['left_line_use'];
        if (turnLeftValueUse) {
            let position = [];
            position.push(getPoint('left_line', data, 2));
            turnLeftValue = position;
            if (validatePoint(turnLeftValue)) {
                let turnLeft = coordinate2Real(turnLeftValue, 'turnLeft', 'common-line');
                turnLeft.use = turnLeftValueUse;
                setSe('turnLeft', turnLeft);
            }

        }
        setSe('turnLeftValue', turnLeftValue);


        //直行分界线
        let goStraightValue = {};
        let goStraightValueUse = data['straight_line_use'];
        if (goStraightValueUse) {
            let position = [];
            position.push(getPoint('straight_line', data, 2));
            goStraightValue = position;
            if (validatePoint(goStraightValue)) {
                let goStraight = coordinate2Real(goStraightValue, 'goStraight', 'common-line');
                goStraight.use = goStraightValueUse;
                setSe('goStraight', goStraight);
            }
        }
        setSe('goStraightValue', goStraightValue);


        //右转分界线
        let turnRightValue = {};
        let turnRightValueUse = data['right_line_use'];
        if (turnRightValueUse) {
            let position = [];
            position.push(getPoint('right_line', data, 2));
            turnRightValue = position;
            if (validatePoint(turnRightValue)) {
                let turnRight = coordinate2Real(turnRightValue, 'turnLeft', 'common-line');
                turnRight.use = turnRightValueUse;
                setSe('turnRight', turnRight);
            }
        }
        setSe('turnRightValue', turnRightValue);

        //大车右转让行线
        let cartTurnRightValue = {};
        let cartTurnRightValueUse = data['cart_right_line_use'];
        if (cartTurnRightValueUse) {
            let position = [];
            position.push(getPoint('cart_right_line', data, 2));
            cartTurnRightValue = position;
            if (validatePoint(cartTurnRightValue)) {
                let cartTurnRight = coordinate2Real(cartTurnRightValue, 'cartTurnRight', 'common-line');
                cartTurnRight.use = cartTurnRightValueUse;
                setSe('cartTurnRight', cartTurnRight);
            }
        }
        setSe('cartTurnRightValue', cartTurnRightValue);

        //上行标定线
        let upCalibrationValue = {};
        let upCalibrationValueUse = data['up_calibration_line_use'];
        if (upCalibrationValueUse) {
            let position = [];
            position.push(getPoint('up_calibration_line', data, 2));
            upCalibrationValue = position;
            if (validatePoint(upCalibrationValue)) {
                let upCalibration = coordinate2Real(upCalibrationValue, 'upCalibration', 'common-line');
                upCalibration.use = upCalibrationValueUse;
                upCalibration.up_calibration_queue_length = data['up_calibration_queue_length'];
                setSe('upCalibration', upCalibration);
            }
        }
        setSe('upCalibrationValue', upCalibrationValue);

        //下行标定线
        let downCalibrationValue = {};
        let downCalibrationValueUse = data['down_calibration_line_use'];
        if (downCalibrationValueUse) {
            let position = [];
            position.push(getPoint('down_calibration_line', data, 2));
            downCalibrationValue = position;
            if (validatePoint(downCalibrationValue)) {
                let downCalibration = coordinate2Real(downCalibrationValue, 'downCalibration', 'common-line');
                downCalibration.use = downCalibrationValueUse;
                downCalibration.down_calibration_queue_length = data['down_calibration_queue_length'];
                setSe('downCalibration', downCalibration);
            }
        }
        setSe('downCalibrationValue', downCalibrationValue);
        //车道线
        let lineValue = [];
        let lineType = {};
        let lines = [];
        lineType.lineNum = data.lane_num + 1;
        // lineType.lineStart = data.lane_direction;
        lineType.lineStart = 0
        lineType.lineStartNum = data.lane_start;
        // let floorValue = [], peopleValue = [];
        let floorValue = {
            floor1: [],
            floor2: [],
            floor3: [],
            floor4: [],
            floor5: [],
        }, peopleValue = {
            people1: [],
            people2: [],
            people3: [],
            people4: [],
            people5: [],
        };
        for (let i = 0; i < lineType.lineNum; i++) {
            let name = 'lane' + i;
            let line = getPoint(name, data, 2);
            lineValue.push(line);
            let type = {};
            type.type = data[name + '_type'];
            if (i >= 1) {
                type.direction = data[name + '_direction'];
                type.define = data[name + '_kind'];
                type.special = data[name + '_special'];
                type.time1start = data[name + '_gongjiao_start1'];
                type.time1end = data[name + '_gongjiao_end1'];
                type.time2start = data[name + '_gongjiao_start2'];
                type.time2end = data[name + '_gongjiao_end2'];
                type.time3start = data[name + '_gongjiao_start3'];
                type.time3end = data[name + '_gongjiao_end3'];
                type.leftWait = data[name + '_leftwait_zone'];
                // type.time2 = value2Date(data[name + '_gongjiao_start2'], 1) + ' - ' + value2Date(data[name + '_gongjiao_end2'], 1);
                // type.time3 = value2Date(data[name + '_gongjiao_start3'], 1) + ' - ' + value2Date(data[name + '_gongjiao_end3'], 1);


                //虚拟地感线圈
                let floorName = 'under_ground' + (i);
                let floor = getPoint(floorName, data, 4);
                let floorFlag = floor[0].x === 0 && floor[0].y === 0
                    && floor[1].x === 0 && floor[1].y === 0
                    && floor[2].x === 0 && floor[2].y === 0
                    && floor[3].x === 0 && floor[3].y === 0;
                if (!floorFlag) {
                    // floorValue.push(floor);
                    floorValue['floor' + i].push(floor);
                }

                //礼让行人检测区
                let peopleName = 'people' + (i);
                let people = getPoint(peopleName, data, 4);
                let peopleFlag = people[0].x === 0 && people[0].y === 0
                    && people[1].x === 0 && people[1].y === 0
                    && people[2].x === 0 && people[2].y === 0
                    && people[3].x === 0 && people[3].y === 0;
                if (!peopleFlag) {
                    // peopleValue.push(people);
                    peopleValue['people' + i].push(people);
                }
            }
            lines.push(type);
        }
        lineType.lines = lines;
        setSe('lineValue', lineValue);
        if (validatePoint(lineValue)) {
            let line = coordinate2Real(lineValue, 'line', 'common-line');
            setSe('line', line);
            setSe('lineType', lineType);
        }

        // let floorValue1 = [];
        // for (let i = 0; i < floorValue.length; i++) {
        //     let f = [];
        //     for (let j = 0; j < floorValue[i].length / 2; j++) {
        //         let f1 = [];
        //         f1.push(floorValue[i][2 * j]);
        //         f1.push(floorValue[i][2 * j + 1]);
        //         f.push(f1)
        //     }
        //     floorValue1.push(f)
        // }
        // setSe('floorValue', floorValue1);
        // if (validatePoint(floorValue1)) {
        //     let floor = coordinate2Real(floorValue1, 'floor', 'common-line-group');
        //     setSe('floor', floor);
        // }
        for (let m = 1; m < 6; m++) {
            let floorValue1 = [];
            for (let i = 0; i < floorValue['floor' + m].length; i++) {
                let f = [];
                for (let j = 0; j < floorValue['floor' + m][i].length / 2; j++) {
                    let f1 = [];
                    f1.push(floorValue['floor' + m][i][2 * j]);
                    f1.push(floorValue['floor' + m][i][2 * j + 1]);
                    f.push(f1)
                }
                floorValue1.push(f)
            }
            setSe('floor' + m + 'Value', floorValue1);
            if (validatePoint(floorValue1)) {
                let floor = coordinate2Real(floorValue1, 'floor' + m, 'common-line-group');
                setSe('floor' + m, floor);
            }
        }


        // setSe('peopleValue', peopleValue);
        // if (validatePoint(peopleValue)) {
        //     let people = coordinate2Real(peopleValue, 'people', 'common-rect-group');
        //     setSe('people', people);
        // }
        for (let m = 1; m < 6; m++) {
            setSe('people' + m + 'Value', peopleValue['people' + m]);
            if (validatePoint(peopleValue)) {
                let people = coordinate2Real(peopleValue['people' + m], 'people' + m, 'common-rect-group');
                setSe('people' + m, people);
            }
        }

        //红灯停止线
        let redStopType = data.light_line_mode;
        let redStopValue = [];
        if (redStopType === 0) {
            //直线停止线
            let pos = getPoint('light_line_single', data, 2);
            redStopValue.push(pos);
        } else if (redStopType === 1) {
            //Z型停止线
            let position = [];
            for (let i = 0; i < 5; i++) {
                let name = 'light_line' + i;
                if (data[name + '_use']) {
                    position.push(getPoint(name, data, 2))
                }
            }
            redStopValue = position;
        }
        setSe('redStopValue', redStopValue);
        if (validatePoint(redStopValue)) {
            let redStop = {};
            if (redStopType === 0) {
                redStop = coordinate2Real(redStopValue, 'redStop', 'common-line');
                redStop.use = data['light_line_single_use'];
            } else if (redStopType === 1) {
                redStop = coordinate2Real(redStopValue, 'redStop', 'common-line-group');
                let use = [];
                for (let i = 0; i < 5; i++) {
                    let name = 'light_line' + i;
                    use.push(data[name + '_use']);
                }
                redStop.use = use;
            }
            setSe('redStop', redStop);
            setSe('redStopType', redStopType);
        }


        //信号灯
        let signalType = {};
        signalType.signalNum = data.light_num;
        signalType.signal = data.light_enable;
        signalType.extendSignal = data.out_light_enable;
        signalType.RS485 = data.ocr_thresh;
        if (signalType.extendSignal) {
            signalType.signalPeopleNumber = data.out_light_people;
            signalType.signalLeftNumber = data.out_light_left;
            signalType.signalStraightNumber = data.out_light_straight;
            signalType.signalRightNumber = data.out_light_right;
        }
        signalType.signalTime = data.red_light_delay;
        signalType.signalYellow = data.yellow_mode;
        signalType.signalStronger = data.red_light_enhance;
        if (signalType.signalYellow) {
            signalType.yellowStartTime = data.yellow_mode_start;
            signalType.yellowEndTime = data.yellow_mode_end;
        }
        if (signalType.signalStronger) {
            signalType.signalStrongStartTime = data.red_light_enhance_begin_time;
            signalType.signalStrongEndTime = data.red_light_enhance_end_time;
        }
        signalType.signalIP = data.signal_ip
        let signalValue = [];
        let signals = [];
        for (let i = 0; i < signalType.signalNum; i++) {
            let name = 'light' + i;
            let signal = getPoint(name, data, 0);
            signalValue.push(signal);
            let type = {};
            type.signalCategory = data[name + '_direction'];
            type.signalDirection = data[name + '_kind'];
            type.signalShape = data[name + '_shape'];
            type.signalType = data[name + '_type'];
            signals.push(type);
        }
        signalType.signals = signals;
        setSe('signalType', signalType);

        setSe('signalValue', signalValue);
        if (validatePoint(signalValue)) {
            let imgInfo = getSe('imgInfo');
            let pictureW, pictureH;
            if (imgInfo) {
                pictureH = imgInfo.height;
                pictureW = imgInfo.width;
            } else {
                pictureH = 600;
                pictureW = 800;
            }
            let signal = Real2signal(signalValue, 'signal', 'fixed-rect-group', pictureW, pictureH);
            setSe('signal', signal);
        }


        //事件检测
        let detects = {};
        // detectNoLicense 无牌车灵敏度弃用（wpc_lmd）
        let detectArray = [
            'detect-signal', 'extend-signal', 'pressing-line', 'kakou-img-num', 'return-enable', 'detect-big-car', 'big-car-type', 'big-car-select',
            'dui-xiang-enable', 'illegal-change', 'direction-mask', 'detect-front-flash', 'detect-truck', 'truck-type', 'truck-car-select', 'detect-prohibit',
            'prohibit-car-type', 'prohibit-plate-blue', 'prohibit-plate-yellow', 'detect-jam', 'black-list', 'overLineParking', 'illegalParking1', 'illegalParking2', 'detectNoLicense',
            'other-provinces', 'time1start', 'time1end', 'time2start', 'time2end', 'time3start', 'time3end', 'black-enable', 'startFlash', 'endFlash', 'front-flash-select',
            'kakou-feature', 'weizhang-feature', 'weizhang-video', 'illegal-change-light', 'illegal-jump', 'fjdc-type', 'jdc-feature', 'fjdc-feature', 'wshcjl-province', 'wshcjl-city',
            'whxx', 'kakou-img-count', 'motuocheCjlType', 'motuocheWpcjlProvince', 'moto-local-city'
        ];
        for (let i = 0; i < detectArray.length; i++) {
            switch (i) {
                case 0:
                    detects[detectArray[0]] = data.detect_signal;
                    break;
                case 1:
                    detects[detectArray[1]] = data.extern_signal;
                    break;
                case 2:
                    detects[detectArray[2]] = data.y_line_signal;
                    break;
                case 3:
                    detects[detectArray[3]] = data.kakou_img_num;
                    break;
                case 4:
                    detects[detectArray[4]] = data.return_enable;
                    break;
                case 5:
                    detects[detectArray[5]] = data.dczyxcd;
                    break;
                case 6:
                    let value = 0;
                    if (data.dczyxcd_zxhc === 1) {
                        value += 4;
                    }
                    if (data.dczyxcd_dxkc === 1) {
                        value += 2;
                    }
                    if (data.dczyxcd_hp === 1) {
                        value += 1;
                    }
                    detects[detectArray[6]] = value;
                    break;
                case 7:
                    detects[detectArray[7]] = getTypeArray(data, 'dczyxcd_lane');
                    break;
                case 8:
                    detects[detectArray[8]] = data.dui_xiang_enable;
                    break;
                case 9:
                    detects[detectArray[9]] = data.line_signal;
                    break;
                case 10:
                    detects[detectArray[10]] = data.direction_mask;
                    break;
                case 11:
                    detects[detectArray[11]] = data.front_flash;
                    break;
                case 12:
                    detects[detectArray[12]] = data.hczykcd;
                    break;
                case 13:
                    let value2 = 0;
                    if (data.hczykcd_zxhc === 1) {
                        value2 += 4;
                    }
                    if (data.hczykcd_zxhc2 === 1) {
                        value2 += 2;
                    }
                    if (data.hczykcd_qxhc === 1) {
                        value2 += 1;
                    }
                    detects[detectArray[13]] = value2;
                    break;
                case 14:
                    detects[detectArray[14]] = getTypeArray(data, 'hczykcd_lane');
                    break;
                case 15:
                    detects[detectArray[15]] = data.cxcjl;
                    break;
                case 16:
                    let value3 = 0;
                    if (data.cxcjl_zxhc === 1) {
                        value3 += 1;
                    }
                    if (data.cxcjl_zxhc2 === 1) {
                        value3 += 2;
                    }
                    if (data.cxcjl_qxhc === 1) {
                        value3 += 4;
                    }
                    if (data.cxcjl_dxkc === 1) {
                        value3 += 8;
                    }
                    if (data.cxcjl_zxkc === 1) {
                        value3 += 16;
                    }
                    if (data.cxcjl_qxkc === 1) {
                        value3 += 32;
                    }
                    if (data.cxcjl_xxkc === 1) {
                        value3 += 64
                    }
                    detects[detectArray[16]] = value3;
                    break;
                case 17:
                    let value4 = 0;
                    if (data.cxcjl_zxhc_blue === 1) {
                        value4 += 1;
                    }
                    if (data.cxcjl_zxhc2_blue === 1) {
                        value4 += 2;
                    }
                    if (data.cxcjl_qxhc_blue === 1) {
                        value4 += 4;
                    }
                    if (data.cxcjl_dxkc_blue === 1) {
                        value4 += 8;
                    }
                    if (data.cxcjl_zxkc_blue === 1) {
                        value4 += 16;
                    }
                    if (data.cxcjl_qxkc_blue === 1) {
                        value4 += 32;
                    }
                    if (data.cxcjl_xxkc_blue === 1) {
                        value4 += 64
                    }
                    detects[detectArray[17]] = value4;
                    break;
                case 18:
                    let value5 = 0;
                    if (data.cxcjl_zxhc_yellow === 1) {
                        value5 += 1;
                    }
                    if (data.cxcjl_zxhc2_yellow === 1) {
                        value5 += 2;
                    }
                    if (data.cxcjl_qxhc_yellow === 1) {
                        value5 += 4;
                    }
                    if (data.cxcjl_dxkc_yellow === 1) {
                        value5 += 8;
                    }
                    if (data.cxcjl_zxkc_yellow === 1) {
                        value5 += 16;
                    }
                    if (data.cxcjl_qxkc_yellow === 1) {
                        value5 += 32;
                    }
                    if (data.cxcjl_xxkc_yellow === 1) {
                        value5 += 64
                    }
                    detects[detectArray[18]] = value5;
                    break;
                case 19:
                    detects[detectArray[19]] = data.yongdu;
                    break;
                case 20:
                    detects[detectArray[20]] = data.hmd_wflx;
                    break;
                case 21:
                    let real3 = parseInt(data.wei_ting_time3 / 1000);
                    real3 = checkRange(real3, 3, 180);
                    detects[detectArray[21]] = real3;
                    break;
                case 22:
                    let real1 = parseInt(data.wei_ting_time1 / 1000);
                    real1 = checkRange(real1, 3, 180);
                    detects[detectArray[22]] = real1;
                    break;
                case 23:
                    let real2 = parseInt(data.wei_ting_time2 / 1000);
                    real2 = checkRange(real2, 3, 180);
                    detects[detectArray[23]] = real2;
                    break;
                case 24:
                    detects[detectArray[24]] = data.wpc_lmd;
                    break;
                case 25:
                    let value6 = 0;
                    if (data.wshcjl_week0 === 1) {
                        value6 += 1;
                    }
                    if (data.wshcjl_week6 === 1) {
                        value6 += 2;
                    }
                    if (data.wshcjl_week5 === 1) {
                        value6 += 4;
                    }
                    if (data.wshcjl_week4 === 1) {
                        value6 += 8;
                    }
                    if (data.wshcjl_week3 === 1) {
                        value6 += 16;
                    }
                    if (data.wshcjl_week2 === 1) {
                        value6 += 32;
                    }
                    if (data.wshcjl_week1 === 1) {
                        value6 += 64
                    }
                    detects[detectArray[25]] = value6;
                    break;
                case 26:
                    detects[detectArray[26]] = data.wshcjl_start_time1;
                    break;
                case 27:
                    detects[detectArray[27]] = data.wshcjl_end_time1;
                    break;
                case 28:
                    detects[detectArray[28]] = data.wshcjl_start_time2;
                    break;
                case 29:
                    detects[detectArray[29]] = data.wshcjl_end_time2;
                    break;
                case 30:
                    detects[detectArray[30]] = data.wshcjl_start_time3;
                    break;
                case 31:
                    detects[detectArray[31]] = data.wshcjl_end_time3;
                    break;
                case 32:
                    detects[detectArray[32]] = data.hmd_enable;
                    break;
                case 33:
                    detects[detectArray[33]] = data.start_flash;
                    break;
                case 34:
                    detects[detectArray[34]] = data.end_flash;
                    break;
                case 35:
                    detects[detectArray[35]] = getTypeArray(data, 'flash_light_lane');
                    break;

                case 36:
                    detects[detectArray[36]] = data.kakou_feature;
                    break;
                case 37:
                    detects[detectArray[37]] = data.weizhang_feature;
                    break;
                case 38:
                    detects[detectArray[38]] = data.weizhang_video;
                    break;
                case 39:
                    detects[detectArray[39]] = data.b_line_signal;
                    break;
                case 40:
                    detects[detectArray[40]] = data.j_line_signal;
                    break;
                case 41:
                    detects[detectArray[41]] = data.fjdc_type
                    break;
                case 42:
                    detects[detectArray[42]] = data.jdc_feature
                    break;
                case 43:
                    detects[detectArray[43]] = data.fjdc_feature
                    break;
                case 44:
                    detects[detectArray[44]] = data.wshcjl_province
                    break;
                case 45:
                    detects[detectArray[45]] = data.wshcjl_city
                    break;
                case 46:
                    let week_xianXing = langMessage.settingDetect.week_xianXing
                    let number_xianXing = langMessage.settingDetect.number_xianXing
                    let city = langMessage.settingDetect.city
                    let whxx = {
                        num: data.whxx_num,
                        data: []
                    }
                    for (let j = 0; j < data.whxx_num; j++) {
                        let whxx_date_type = data['whxx' + j + '_date_type']
                        let whxx_date = []
                        if (whxx_date_type === 0) {
                            for (let k = 0; k < week_xianXing.length; k++) {
                                if (data['whxx' + j + '_date'] & week_xianXing[k].value) {
                                    whxx_date.push(week_xianXing[k].value)
                                }
                            }
                        } else {
                            for (let k = 0; k < number_xianXing.length; k++) {
                                if (data['whxx' + j + '_date'] & number_xianXing[k].value) {
                                    whxx_date.push(number_xianXing[k].value)
                                }
                            }
                        }
                        let local_plate_endnum = [] //nonlocal_plate_endnum = []
                        for (let k = 0; k < number_xianXing.length; k++) {
                            if (data['whxx' + j + '_local_plate_endnum'] & number_xianXing[k].value) {
                                local_plate_endnum.push(number_xianXing[k].value)
                            }
                        }
                        // for (let k = 0; k < number_xianXing.length; k++) {
                        //     if (data['whxx' + j + '_nonlocal_plate_endnum'] & number_xianXing[k].value) {
                        //         nonlocal_plate_endnum.push(number_xianXing[k].value)
                        //     }
                        // }
                        let local_city = []
                        for (let k = 0; k < city.length; k++) {
                            if (data['whxx' + j + '_local_city'] & city[k].value) {
                                local_city.push(city[k].value)
                            }
                        }
                        let obj = {
                            "whxx_date_type": whxx_date_type,
                            "whxx_date": whxx_date,     //0：周日，1：周一，2：周二......
                            "whxx_start_time": data['whxx' + j + '_start_time'],    //分钟值
                            "whxx_end_time": data['whxx' + j + '_end_time'],
                            "local_province": data['whxx' + j + '_local_province'],
                            "local_city": local_city,
                            "local_plate_endnum": local_plate_endnum,
                            // "nonlocal_plate_endnum": nonlocal_plate_endnum
                        }
                        whxx.data.push(obj)
                    }
                    detects[detectArray[46]] = whxx
                    break;
                case 47:
                    detects[detectArray[47]] = data.kakou_img_count
                    break;
                case 48:
                    detects[detectArray[48]] = data.motuoche_cjl_type
                    break;
                case 49:
                    detects[detectArray[49]] = data.motuoche_wpcjl_province
                    break;
                case 50:
                    detects[detectArray[50]] = data.moto_local_city
                    break;
            }
        }
        setSe('detects', detects);

        // 安全带、打手机的置信度
        setSe('SafeBelt_score', data.hasOwnProperty('SafeBelt_score') ? data.SafeBelt_score : 1)
        setSe('CallPhone_score', data.hasOwnProperty('CallPhone_score') ? data.CallPhone_score : 1)

        //system
        let systemValue = {};
        systemValue.cameraHeight = data.camera_height;
        systemValue.cameraImgDown = data.camera_img_down;
        systemValue.cameraImgTop = data.camera_img_top;
        let coef = parseFloat(data.coef / 100);
        let realCoef = checkRange(coef, 0.5, 2);
        systemValue.coef = realCoef;
        systemValue.jpgQuality = data.jpg_quality;
        systemValue['detect-license'] = data.jugment_signal;
        // systemValue['detect-line-speed'] = data.ss;
        systemValue['detect-speed'] = data.velo_enable;
        systemValue.headHZ = data.head_hz;
        systemValue.headLetter = data.head_letter;
        systemValue.locateThresh = data.locate_thresh;
        systemValue.maxSpeed = data.max_speed;
        systemValue.minSpeed = data.min_speed;
        // systemValue.ocrThresh = data.ocr_thresh;
        systemValue.roadCode = data.wzdd;
        systemValue.roadDirect = data.road_direct;
        systemValue.roadName = data.road_name;
        systemValue.selfRoadCode = data.road_code;
        systemValue.speedType = data.speed_mode;
        systemValue.radarType = data.radar_type;
        systemValue.AIType = data.ai_mode;
        for (let j = 1; j < 6; j++) {
            let name1 = "lane" + j + "MaxSpeed";
            let name2 = "lane" + j + "_max_speed";
            let name3 = "lane" + j + "MinSpeed";
            let name4 = "lane" + j + "_min_speed";
            systemValue[name1] = data[name2];
            systemValue[name3] = data[name4];
            let name11 = "lane" + j + "MaxLimitSpeed";
            let name21 = "lane" + j + "_max_limit_speed";
            let name31 = "lane" + j + "MinLimitSpeed";
            let name41 = "lane" + j + "_min_limit_speed";
            systemValue[name11] = data[name21];
            systemValue[name31] = data[name41];
        }
        setSe('systemValue', systemValue);

        //扩展参数
        let extendArray = [
            'iMatch_X_Threshold', 'iMatch_Y_Threshold', 'iGray_Threshold', 'iPlate_Blue_Grad_Threshold',
            'iPlate_Yellow_Grad_Threshold', 'iPlate_Blue_Skip_Threshold', 'iPlate_Yellow_Skip_Threshold',
            'iIs_All', 'iNight_Gray_Threshold', 'iDay_To_Night_Threshold', 'iKakou_Detect_Area', 'iDetect_Precision',
            'iDelay_Time', 'iDelay_Dis_img2', 'iSame_Plate_Time'
        ];
        let extendParams = {};
        for (let i = 0; i < extendArray.length; i++) {
            if (data[extendArray[i]]) {
                extendParams[extendArray[i]] = data[extendArray[i]]
            } else {
                extendParams[extendArray[i]] = 0;
            }
        }
        setSe('extendParams', extendParams);

        //违法代码
        let codeValue = {};
        for (let i in data) {
            let name = i;
            if (name === 'road_code') {
                continue
            }
            let reg = /^\w+_code+$/;
            if (reg.test(i)) {
                codeValue[name] = data[name]
            }
            let reg_zfcode = /^\w+_zfcode+$/;
            if (reg_zfcode.test(i)) {
                codeValue[name] = data[name]
            }
        }
        setSe('codeValue', codeValue);
        console.log(codeValue)

        //事件存储设置
        let eventStorage = {};
        eventStorage.transmit_addr = data['transmit_addr'];
        eventStorage.transmit_port = data['transmit_port'];
        eventStorage.forward_plateform_ip = data['forward_plateform_ip'];
        eventStorage.forward_plateform_port = data['forward_plateform_port'];
        eventStorage.ftp_ip = data['ftp_ip'];
        eventStorage.ftp_Port = data['ftp_Port'];
        eventStorage.ftp_data = data['ftp_data'];
        eventStorage.ftp_user = data['ftp_user'];
        eventStorage.ftp_password = data['ftp_password'];
        eventStorage.ftp_video_days = data['ftp_video_days'];
        eventStorage.ftp_kk_days = data['ftp_kk_days'];
        eventStorage.ftp_wz_days = data['ftp_wz_days'];
        eventStorage['1400_ip'] = data['1400_ip'];
        eventStorage['1400_port'] = data['1400_port'];
        eventStorage['1400_username'] = data['1400_username'];
        eventStorage['1400_password'] = data['1400_password'];
        eventStorage['1400_device_id'] = data['1400_device_id'];


        // eventStorage['ftp_upload_kk'] = data['ftp_upload_kk'] ? data['ftp_upload_kk'] : 0;
        // eventStorage['ftp_upload_wf'] = data['ftp_upload_wf'] ? data['ftp_upload_wf'] : 0;
        // eventStorage['ftp_file_path'] = data['ftp_file_path'] ? data['ftp_file_path'] : '';
        // eventStorage['ftp_file_name'] = data['ftp_file_name'] ? data['ftp_file_name'] : '';
        setSe('eventStorage', eventStorage);


        //callback
        if (callback) {
            callback()
        }
    }

};
var getDevice = function () {
    let deviceInfo = getSe('deviceInfo');
    if (deviceInfo) {
        for (let i in deviceInfo) {
            if (i === 'board_name' || i === 'device_uid') {
                $("#" + i).val(deviceInfo[i])
            } else {
                let innerMsg = deviceInfo[i];
                if (i === 'auth_status') {
                    let authStatus = getSe("projectConfig").auth_status;
                    innerMsg = '<i class="layui-icon layui-icon-close-fill warning"></i>' + langMessage.setting.UNAUTHORIZED;
                    if (deviceInfo[i] > 0) {
                        innerMsg = '<i class="layui-icon layui-icon-ok-circle auth"></i>' + (authStatus[deviceInfo[i]] ? authStatus[deviceInfo[i]] : langMessage.setting.UNKNOWN_AUTHORIZED)
                    }

                } else if (i === 'arithmet_status') {
                    innerMsg = deviceInfo[i] === 1 ? '<i class="layui-icon layui-icon-ok-circle auth"></i>' + langMessage.setting.INITIALIZED_SUC : '<i class="layui-icon layui-icon-close-fill warning"></i>' + langMessage.setting.INITIALIZED_FAIL;
                } else if (i === 'nnie_status') {
                    innerMsg = '<i class="layui-icon layui-icon-loading doing"></i>' + langMessage.setting.INITIALIZING;
                    if (deviceInfo[i] < 2) {
                        innerMsg = deviceInfo[i] === 1 ? '<i class="layui-icon layui-icon-ok-circle auth"></i>' + langMessage.setting.INITIALIZED_SUC : '<i class="layui-icon layui-icon-close-fill warning"></i>' + langMessage.setting.INITIALIZED_FAIL;
                    }
                }
                $("#" + i).html(innerMsg)
            }
        }
    }
    let deviceName = getSe("deviceName");
    if (deviceName) {
        $("#board_name").val(deviceName)
    }
};

var numberRange = function (id, min, max, isFloat, isClick, u) {
    let unit = u || 1;
    $("#" + id).off("change").off('blur').off('focus').off("input propertychange").on("input propertychange", function (e) {
        let v = $(this);
        let value = min;
        let pVal = isFloat ? parseFloat(v.val()) : parseInt(v.val());
        if (!isNum(pVal)) {
            v.val(min);
            return
        }
        if (pVal > max) {
            value = max
        } else if (pVal < min) {
            value = min
        } else {
            value = v.val() === '' ? min : v.val()
        }
        v.val(value)
    });
    if (isClick) {
        let cliBtn = "." + id.replace(/([A-Z])/g, "-$1").toLowerCase();
        $(cliBtn + " .input-up").off("click").on("click", function (e) {
            let v = $("#" + id + ".input-number");
            v.val(parseInt(v.val()) + unit > max ? max : parseInt(v.val()) + unit);
        });
        $(cliBtn + " .input-down").off("click").on("click", function (e) {
            let v = $("#" + id + ".input-number");
            v.val(parseInt(v.val()) - unit < min ? min : parseInt(v.val()) - unit);
        });
    }
};
var isNum = function (val) {

    let regPos = /^\d+(\.\d+)?$/; //非负浮点数
    let regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
    if (regPos.test(val) || regNeg.test(val)) {
        return true;
    } else {
        return false;
    }
};

var checkRange = function (val, min, max) {
    let minVal = min || 0;
    let maxVal = max || 100;
    let value = minVal;
    value = (val < minVal ? minVal : val);
    value = (val > maxVal ? maxVal : val);
    return value;
};
