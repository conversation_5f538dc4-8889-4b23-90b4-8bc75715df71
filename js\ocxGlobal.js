var ocx;
var cameraIP = location.hostname
// var cameraIP = '*************'
var getDate = function (timestamp, tick) {
    let time = new Date(parseInt(timestamp) * 1000);
    return formatTime(time, tick)
};
var timestampToTime = function (timestamp) {
    let date = new Date(timestamp),//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        ss = date.getUTCMilliseconds();
    return formatTime(date, ss)
};
var formatTime = function (time, tick) {
    let y = time.getFullYear(),
        m = time.getMonth() + 1,
        d = time.getDate(),
        h = time.getHours(),
        mm = time.getMinutes(),
        s = time.getSeconds();
    time = null;
    return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d) + " "
        + (h < 10 ? "0" + h : h) + ":" + (mm < 10 ? "0" + mm : mm) + ":" + (s < 10 ? "0" + s : s)
        + "." + tick;
};
var date2stamp = function (time) {
    let t = time.replace(/-/g, "/");
    let da = new Date(t);
    return parseInt(Date.parse(da) / 1000);
};
var getAccount = function () {
    let ip = cameraIP;
    let ret = ocx.account_password(ip);
    console.log(ret);
};

var getCameraUrl = function (channel) {
    let auth = "";
    let stream = "";
    let ip = cameraIP;
    let projectConfig = getSe("projectConfig");
    channel = channel ? channel : projectConfig.camera_channel;
    if (projectConfig.camera_username && projectConfig.camera_password) {
        auth = projectConfig.camera_username + ":" + projectConfig.camera_password + "@"
    }
    if (projectConfig.camera_stream) {
        stream = projectConfig.camera_stream
    }
    return 'rtsp://' + auth + ip + '/' + stream + channel;
};
var ocxDialog = function () {
    console.log("ocx_JumpProtect");
    let ret = ocx.JumpProtect();
    console.log("JumpProtect:" + ret);
    return ret;
};


var saveAuthcode = function (path, code) {
    console.log("ocx.Save_authcode");
    // path = path.myReplace("\\", "\\\\");
    let ret = ocx.Save_authcode(path, code);
    console.log("saveAuth:" + ret);
    return ret
};
var readAuthFile = function (unique_id, callback) {
    let path = ocxPopOut();
    let result = {
        msg: langMessage.authorization.matchFail,
        result: false
    };
    if (path) {
        if (path !== 'error') {
            console.log("ocx.Read_authfile");
            let base = ocx.Read_authfile(path, unique_id);
            if (base !== 'FALSE') {
                try {
                    base = JSON.parse(base);
                    if (base['Executive Outcomes'] === 'TRUE') {
                        result.base = base['Base64'];
                        result.result = true;
                        result.fileName = base['File_alike_name'];
                    } else if (base['Filecount']) {
                        let msg = langMessage.authorization.matchMultiple(base['File_alike_name']);
                        msg = msg.slice(0, -1);
                        result.msg = msg;
                        result.result = false;
                    }
                } catch (e) {
                    console.log(e);
                }
            }
        }
        if (callback) {
            callback(result);
        }
    }

};
var objGETValue = function (obj, key) {
    for (let key in obj) {
        return obj
    }
};
var base642pic = function (data) {
    if (data !== 'FALSE') {
        let imgSrc = "data:image/jpeg;base64," + data;
        return imgSrc
    } else {
        return '../../img/404.png'
    }
};
var sendPath = function (path) {
    console.log("ocx.get_picBase64");
    // path = path.replace(/\\/g, "\\\\")
    let base = ocx.get_picBase64(path);
    let pic = '../../img/404.png';
    let blob;
    if (base !== 'FALSE') {
        let baseOBJ = JSON.parse(base);
        let pathData = baseOBJ.Base_data;
        pic = base642pic(pathData);
        blob = base64ToBlob(pic);
        base = "";
        pathData = "";
        baseOBJ = null;
    }
    return blob
};
/**
 * 获取图片rgba信息
 * @param path
 * @param w
 * @param h
 * @param callback
 */
var getRGBA = function (path, w, h, callback) {
    console.log('ocx.Get_PicRgba');
    let rgba = ocx.Get_PicRgba(path, w, h);
    let width = w, height = h;
    let c = document.createElement('canvas');
    if (rgba !== 'FALSE') {
        let rgbaOBJ = JSON.parse(rgba);
        let pathData = rgbaOBJ.Rgba_data;
        pathData = str2array(pathData);
        width = rgbaOBJ.Jpeg_width;
        height = rgbaOBJ.Jpeg_height;
        c.width = w;
        c.height = h;
        let ctx = c.getContext("2d");
        let d = ctx.getImageData(0, 0, w, h);
        let imageData = d.data;
        // imageData.set(pathData);
        setImageData(imageData, pathData);
        ctx.putImageData(d, 0, 0);
        callback(c, width, height);
        rgba = "";
        imageData = null;
        d = null;
        c = null;
        ctx = null;
        rgbaOBJ = null;
        pathData = null;
    } else {
        c.width = w;
        c.height = h;
        let ctx = c.getContext("2d");
        let bigImg = document.createElement("img"); //创建一个img元素
        bigImg.src = "../img/404.png";   //给img元素的src属性赋值
        bigImg.onload = function () {
            let width = bigImg.width;
            let height = bigImg.height;
            ctx.clearRect(0, 0, w, h);
            ctx.drawImage(bigImg, 0, 0, width, height, 0, 0, w, h);
            callback(c, width, height);
            c = null;
            ctx = null;
        };
    }

};
// var testI;
// var setImageData = function (imageData, pathData) {
//
//     testI = 0;
//     let item = 0;
//     // while (item >= 0) {
//         item = _setImageData(imageData, pathData);
//     // }
//
// }
// var _setImageData = function (imageData, pathData) {
//     // for (let i = pathData.length - 1; i >= 0; i--){
//     //     imageData[i]= 0
//     // }
//     let item;
//     for (; item = pathData[testI++];) {
//     // for (; pathData[testI++];) {
//         // imageData[testI - 1] = item;
//     }
//     return item
// };
var setImageData = function (imageData, pathData) {
    for (let i = pathData.length - 1; i >= 0; i--) {
        imageData[i] = pathData[i]
    }
};

/**
 * 双击事件图片
 * @param type
 * @param path
 * @param x
 * @param y
 * @param w
 * @param h
 * @param showClick
 * @param drawRect
 * @param amplify
 */
var imageCanvas = function (type, path, x, y, w, h, showClick, drawRect, amplify, car_x, car_y, car_w, car_h) {
    let picD = $("#" + type);
    let picDom = picD[0];
    let picW = parseInt(picDom.clientWidth);
    let picH = parseInt(picDom.clientHeight);
    //图像宽高默认为4的倍数
    picW = parseInt(picW / 4) * 4;
    picH = parseInt(picH / 4) * 4;
    let s = showClick || false;
    let d = drawRect || false;
    let a = amplify || false;
    let can = $("<canvas id=\"" + type + "Can\" height='" + picH + "' width='" + picW + "'>");
    picD.html(can);
    let c = $("#" + type + "Can")[0];
    let ctx = c.getContext('2d');
    ctx.clearRect(0, 0, picW, picH);
    if (s) {
        picD.off("click").on("click", function () {
            $(".pic-show").removeClass("show");
            $(".time-show").removeClass("show");
            let index = $(this).index();
            $(this).addClass("show");
            $($(".time-show")[index]).addClass("show");
            let am = false;
            if (type === 'platePic') {
                am = true
            }
            imageCanvas('currentPic', path, x, y, w, h, null, true, am, car_x, car_y, car_w, car_h)
        })
    }
    // let paths = sendPath(path);
    getRGBA(path, picW, picH, function (img, width, height) {
        ctx.drawImage(img, 0, 0, picW, picH, 0, 0, picW, picH);
        if (d && x >= 0 && y >= 0 && w > 0 && h > 0) {
            let rectX = parseInt(x / width * picW);
            let rectY = parseInt(y / height * picH);
            let rectW = parseInt(w / width * picW);
            let rectH = parseInt(h / height * picH);
            ctx.strokeStyle = 'red';
            ctx.lineWidth = "1";
            ctx.rect(rectX, rectY, rectW, rectH);
            rectX = null, rectY = null;
            rectH = null;
            rectW = null;
        }
        if (d && car_x >= 0 && car_y >= 0 && car_w > 0 && car_h > 0) {
            let rectX = parseInt(car_x / width * picW);
            let rectY = parseInt(car_y / height * picH);
            let rectW = parseInt(car_w / width * picW);
            let rectH = parseInt(car_h / height * picH);
            ctx.strokeStyle = 'red';
            ctx.lineWidth = "1";
            ctx.rect(rectX, rectY, rectW, rectH);
            rectX = null, rectY = null;
            rectH = null;
            rectW = null;
        }
        ctx.stroke();
        let multipleX = width / picW;
        let multipleY = height / picH;
        if (type === 'currentPic') {
            let magCan = $("<canvas class=\"magCanvas\" id=\"" + type + "MagCan\" height=200 width=200>");
            $("#" + type + "Can").after(magCan);
            let mc = document.getElementById(type + "MagCan");
            let mctx = mc.getContext("2d");
            c.onmouseout = function () {
                mc.style.display = "none";
            };
            c.onmousemove = function (e) {
                if (img && !a) {
                    let bigImg = document.createElement('img');
                    // let imgSrc = sendPath(path);
                    // let blob = base64ToBlob(imgSrc);
                    // bigImg.onload = function(){
                    //     window.URL.revokeObjectURL(bigImg.src);
                    // };
                    // bigImg.src = window.URL.createObjectURL(blob);
                    let blob = sendPath(path);
                    bigImg.onload = function () {
                        window.URL.revokeObjectURL(bigImg.src);
                    };
                    bigImg.src = window.URL.createObjectURL(blob);
                    mc.style.display = "block";
                    mctx.clearRect(0, 0, 300, 300);
                    let location = getLocation(type + "Can", e.clientX, e.clientY);// getLocation()
                    let centerPoint = {
                        x: location.x,
                        y: location.y
                    };
                    let originalRadius = 300;
                    mctx.drawImage(bigImg, (centerPoint.x * multipleX - 50 < 0 ? 0 : centerPoint.x * multipleX - 50),// biggerImg:规定要使用的图像、画布或视频;
                        (centerPoint.y * multipleY - 50 < 0 ? 0 : centerPoint.y * multipleY - 50), originalRadius,// centerPoint.y
                        originalRadius, 0, 0, originalRadius, originalRadius);// originalRadius:被剪切图像的高度；
                    mc.style.left = e.pageX + "px";
                    mc.style.top = e.pageY + "px";
                } else {
                    mctx.clearRect(0, 0, 300, 300);// clearRect() 方法清空给定矩形内的指定像素。
                    mc.style.display = "none";
                }
            };
        }
        can = null;
        c = null;
        ctx = null;

    });
};

/**
 * 双击事件图片
 * @param type
 * @param path
 * @param x
 * @param y
 * @param w
 * @param h
 * @param showClick
 * @param drawRect
 * @param amplify
 */
var imageCanvas_pictureMode = function (type, path, x, y, w, h, showClick, drawRect, amplify) {
    let picD = $("#" + type);
    // let picDom = picD[0];
    let picW = w;//parseInt(picDom.clientWidth);
    let picH = h;//parseInt(picDom.clientHeight);
    //图像宽高默认为4的倍数
    picW = parseInt(picW / 4) * 4;
    picH = parseInt(picH / 4) * 4;
    let s = showClick || false;
    let d = drawRect || false;
    let a = amplify || false;
    let can = $("<canvas id=\"" + type + "Can\" height='" + picH + "' width='" + picW + "'>");
    picD.html(can);
    let c = $("#" + type + "Can")[0];
    let ctx = c.getContext('2d');
    ctx.clearRect(0, 0, picW, picH);
    if (s) {
        picD.off("click").on("click", function () {
            $(".pic-show").removeClass("show");
            $(".time-show").removeClass("show");
            let index = $(this).index();
            $(this).addClass("show");
            $($(".time-show")[index]).addClass("show");
            let am = false;
            if (type === 'platePic') {
                am = true
            }
            imageCanvas_pictureMode('currentPic', path, x, y, w, h, null, true, am)
        })
    }
    // let paths = sendPath(path);
    getRGBA(path, picW, picH, function (img, width, height) {
        ctx.drawImage(img, 0, 0, picW, picH, 0, 0, picW, picH);
        if (d && x >= 0 && y >= 0 && w > 0 && h > 0) {
            let rectX = parseInt(x / width * picW);
            let rectY = parseInt(y / height * picH);
            let rectW = parseInt(w / width * picW);
            let rectH = parseInt(h / height * picH);
            ctx.strokeStyle = 'red';
            ctx.lineWidth = "1";
            ctx.rect(rectX, rectY, rectW, rectH);
            rectX = null, rectY = null;
            rectH = null;
            rectW = null;
        }
        ctx.stroke();
        let multipleX = width / picW;
        let multipleY = height / picH;
        if (type === 'currentPic') {
            let magCan = $("<canvas class=\"magCanvas\" id=\"" + type + "MagCan\" height=200 width=200>");
            $("#" + type + "Can").after(magCan);
            let mc = document.getElementById(type + "MagCan");
            let mctx = mc.getContext("2d");
            c.onmouseout = function () {
                mc.style.display = "none";
            };
            c.onmousemove = function (e) {
                if (img && !a) {
                    let bigImg = document.createElement('img');
                    // let imgSrc = sendPath(path);
                    // let blob = base64ToBlob(imgSrc);
                    // bigImg.onload = function(){
                    //     window.URL.revokeObjectURL(bigImg.src);
                    // };
                    // bigImg.src = window.URL.createObjectURL(blob);
                    let blob = sendPath(path);
                    bigImg.onload = function () {
                        window.URL.revokeObjectURL(bigImg.src);
                    };
                    bigImg.src = window.URL.createObjectURL(blob);
                    mc.style.display = "block";
                    mctx.clearRect(0, 0, 300, 300);
                    let location = getLocation(type + "Can", e.clientX, e.clientY);// getLocation()
                    let centerPoint = {
                        x: location.x,
                        y: location.y
                    };
                    let originalRadius = 300;
                    mctx.drawImage(bigImg, (centerPoint.x * multipleX - 50 < 0 ? 0 : centerPoint.x * multipleX - 50),// biggerImg:规定要使用的图像、画布或视频;
                        (centerPoint.y * multipleY - 50 < 0 ? 0 : centerPoint.y * multipleY - 50), originalRadius,// centerPoint.y
                        originalRadius, 0, 0, originalRadius, originalRadius);// originalRadius:被剪切图像的高度；
                    mc.style.left = e.pageX + "px";
                    mc.style.top = e.pageY + "px";
                } else {
                    mctx.clearRect(0, 0, 300, 300);// clearRect() 方法清空给定矩形内的指定像素。
                    mc.style.display = "none";
                }
            };
        }
        can = null;
        c = null;
        ctx = null;

    });
};

/**
 * 获得放大的坐标
 * @param x
 * @param y
 * @returns {{x: number, y: number}}
 */
var getLocation = function (id, x, y) {
    let c = document.getElementById(id);
    let bbox = c.getBoundingClientRect();// getBoundingClientRect()方法返回元素的大小及其相对于视口的位置。
    return {
        x: (x - bbox.left) * (c.width / bbox.width),
        y: (y - bbox.top) * (c.height / bbox.height)
    };
};
var displayImage = function (url, callback) {
    var j = new JpegImage();
    j.onload = function () {
        let c = document.createElement('canvas');
        c.width = j.width;
        c.height = j.height;
        let ctx = c.getContext("2d");
        let d = ctx.getImageData(0, 0, j.width, j.height);
        j.copyToImageData(d);
        ctx.putImageData(d, 0, 0);
        callback(c, j.width, j.height);
        c = null;
    };
    j.load(url);
};

var image404 = function (ctx, w, h) {
    // let c = $("#" + type)[0];
    // let ctx = c.getContext('2d');
    let bigImg = document.createElement("img"); //创建一个img元素
    bigImg.src = "../img/404.png";   //给img元素的src属性赋值
    bigImg.onload = function () {
        let width = bigImg.width;
        let height = bigImg.height;
        ctx.clearRect(0, 0, w, h);
        ctx.drawImage(bigImg, 0, 0, width, height, 0, 0, w, h);
    };
};
var initOCX = function () {
    let ret = ocx.test_ocx_Ini();
    console.log("init:" + ret);
};


var initListen = function (callback) {
    let ip = cameraIP;
    let sdc = getSe("SDC");
    let port = 60000;
    if (sdc) {
        port = getSe("projectConfig")['newEventPort']
    } else {
        port = getSe("projectConfig")['oldEventPort']
    }
    console.log("ocx.LISTEN_INI");
    let result = ocx.LISTEN_INI(ip, port);
    if (result !== 'FALSE') {
        callback(true)
    } else {
        callback(false)
    }
};

var initStreamType = function (channel) {
    initWebService(webserviceCMD.CMD_GET_VIDEO, null, getStreamTypeCallback, channel);
};
var getStreamTypeCallback = function (res, channel) {
    let flag = false, width = 0, height = 0;
    /**
     * stream_type
     * 1:h264
     * 2:h265
     * 99:其他
     * */
    if (res.stream_type === 1) {
        flag = true
    }
    if (res.stream_w && res.stream_h) {
        width = res.stream_w;
        height = res.stream_h;
    }
    if (res.stream_username && res.stream_password) {
        let projectConfig = getSe("projectConfig");
        projectConfig.camera_username = res.stream_username;
        projectConfig.camera_password = res.stream_password;
        setSe('projectConfig', projectConfig);
    }

    initRTSP(flag, channel, width, height)
};


var find_ocx = function () {
    try {
        //插件ProgID
        let ocx = new ActiveXObject('TS_PLAYER_PLUGIN.TS_PLAYER_PLUGINCtrl.1');
    } catch (e) {
        console.log('ocx error', e);
        return false;
    }
    return true;
};
var getVersion = function () {
    console.log("ocx.get_Current_version");
    let version = {
        main: 0,
        sub: 0,
        revise: 0
    };
    try {
        let nowVersion = ocx.get_Current_version();
        console.log("ocx.get_Current_version()：", nowVersion);
        nowVersion = JSON.parse(nowVersion);
        version.main = parseInt(nowVersion.Main);
        version.sub = parseInt(nowVersion.Sub);
        version.revise = parseInt(nowVersion.Revise);
    } catch (e) {
        console.log(e);
    }
    return version;
};


var ocxDestroyAll = function (ocx) {
    console.log("ocx.DestroyAll");
    try {
        ocx.DestroyAll();
    } catch (e) {
        console.log(e);
    }
};
var appendOcx = function () {
    $("body").append("<div style='display: none'><OBJECT ID=\"ocx\" width=\"100%\" height=\"100%\" classid=\"clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA\"></OBJECT></div>");
};
var ocxUpgrade = function (type, ip, base) {
    console.log("ocx.UPDATE" + type);
    let status = ocx.UPDATE(type, ip, base);
    console.log("UPDATE" + type + ":" + status);
    return status
};
String.prototype.myReplace = function (f, e) {
    //把f替换成e
    var reg = new RegExp(f, "g"); //创建正则RegExp对象
    return this.replace(reg, e);
};
var ocxDestroy = function (win) {
    let w;
    if (win) {
        w = win
    } else {
        w = window;
    }
    let ocx = w.document.getElementById("ocx");
    ocxDestroyAll(ocx);
};

var base64ToBlob = function (urlData) {
    let arr = urlData.split(',');
    let mime = arr[0].match(/:(.*?);/)[1] || 'image/png';
    // 去掉url的头，并转化为byte
    let bstr = atob(arr[1]),
        n = bstr.length,
        // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
        u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], {
        type: mime
    });
};

var exportPicture = function (type, path, data) {
    console.log("ocx.Export_pic");
    let status = ocx.Export_pic(type, path, data);
    console.log("Export_pic:" + status);
    return status
};

var ocxPopOut = function () {
    console.log("ocx.Popout");
    let path = "";
    let status = ocx.Popout();
    if (status === "FALSE") {
        path = false
    } else if (status === "FOLDER SELECT ERROR") {
        path = "error"
    } else {
        path = status;
    }
    console.log("Popout:" + status);
    return path
};


// 4.22
// ocx新接口


var initFilePath = function () {
    let ip = cameraIP;
    let param = {
        Camera1: ip
    };
    let ret = ocx.FIle_Path_Init(JSON.stringify(param));
    console.log("ocx.FIle_Path_Init:param=", JSON.stringify(param), "ret=", ret);
    let ocxMsg = getSe("projectConfig").ocxMsg || {};
    let result = ocxMsg[ret] || {result: false, msg: langMessage.authorization.authMsg.UNKNOWN};
    return result;
};

var commandRTSP = function (param) {
    let ret = ocx.command(JSON.stringify(param));
    console.log("ocx.command:param=", JSON.stringify(param), "ret=", ret);
    return ret;
};
/**
 * @param flag true:h264     false:h265/其他
 * @param channel
 * @param w
 * @param h
 * */
var initRTSP = function (flag, channel, w, h) {
    $("#RTSPError").css({display: "none"});
    try {
        let play = 1;//h264
        if (!flag) {
            play = 2//h265
        }
        let param = {
            CMD: 'INIT',
            PARAMS: "[channel|11]"
        };
        let ret = commandRTSP(param);
        console.log("rtsp-init:" + ret);
        if (ret !== 'FALSE') {
            $("#RTSPError").css({display: "none"});
            playRTSP(channel, play, w, h);
        } else {
            RTSPError(langMessage.ocxGlobal.ocxError.INIT_FAIL)
        }
    } catch (e) {
        console.log(e)
    }
};
/**
 * @param play 1:h264     2:h265
 * @param channel
 * @param w
 * @param h
 * */
var playRTSP = function (channel, play, w, h) {
    let param = {
        CMD: 'PLAY',
        IP: cameraIP,
        PARAMS: '[channel|0][url|' + getCameraUrl(channel) + ']',
        STREAM_WIDTH: w,
        STREAM_HEIGHT: h,
        VIDEOTYPE: play,
    };
    let ret = commandRTSP(param);
    // if (ret === 'FALSE') {
    //     $("#ocx").html("视频播放失败请刷新重试！");
    //     stopRTSP(channel)
    // }
    console.log("rtsp-play:" + ret);
    if (ret === '401') {
        RTSPMessage();
        return
    } else if (ret === '-10057') {
        RTSPError(langMessage.ocxGlobal.ocxError.TIMEOUT);
        return
    } else if (ret === '-2') {
        RTSPError(langMessage.ocxGlobal.ocxError.PLAY_WIDTH_ERROR + ret);
        return
    } else if (ret === '-3') {
        RTSPError(langMessage.ocxGlobal.ocxError.PLAY_RADIO_ERROR + ret);
        return
    } else if (ret !== '0') {
        RTSPError(langMessage.ocxGlobal.ocxError.PLAY_FAIL + ret);
        return
    }
    $("#RTSPError").css({display: "none"});
    $("#playVideo").removeClass('video-play').addClass('video-stop');
};

var stopRTSP = function () {
    let param = {
        CMD: 'STOP',
        PARAMS: "[channel|0]"
    };
    try {
        let ret = commandRTSP(param);
        console.log("rtsp-stop:" + ret);
    } catch (e) {
        console.log(e)
    }
};
var closeRTSP = function () {
    let param = {
        CMD: 'CLOSE',
    };
    try {
        let ret = commandRTSP(param);
        console.log("rtsp-close:" + ret);
    } catch (e) {
        console.log(e)
    }
};
var removeVideo = function () {
    stopRTSP();
    closeRTSP();
    $("#playVideo").removeClass('video-stop').addClass('video-play');
};
var ThreadStatus = function () {
    let result = false;
    try {
        console.log("ocx_ThreadStatus");
        let ip = cameraIP;
        let ret = ocx.ThreadStatus(ip);
        console.log("ThreadStatus:" + ret);
        if (ret === "TRUE") {
            result = true
        }

    } catch (e) {
        result = e;
        console.log(e)
    } finally {
        return result
    }
};
var stopThread = function () {
    console.log("ocx.STOPThread");
    let ip = cameraIP;
    let ret = ocx.STOPThread(ip);
    setTimeout(function () {
        ocxSQL({ORDER: 'Asc'})
    }, 2000)
    console.log("STOPThread:" + ret);
};
var getEventStatus = function () {
    // console.log('##############getEventStatus2##################')
    let ip = cameraIP;
    let status = ocx.eventWeb(ip);
    try {
        status = JSON.parse(status)
    } catch (e) {
        console.log(e)
    }
    return status
};

var savePathConfig = function (path) {
    console.log("ocx.set_pic_save_dir");
    let result = false;
    let ip = cameraIP;
    if (find_ocx()) {
        result = ocx.set_pic_save_dir(path.img, ip);
    }
    return result;
};

var initPathConfig = function () {
    console.log("ocx.getCurrentPicDir");
    let path = {
        img: "",
        record: "",
        log: ""
    };
    if (find_ocx()) {
        let imgPath = getCommonConf("PICPATH");
        let dbPath = getCommonConf("DBPATH");
        if (imgPath !== 'FALSE') {
            path.img = imgPath.myReplace("\\\\\\\\", "\\");
            path.db = dbPath.myReplace("\\\\\\\\", "\\");
            path.record = "";
            path.log = ""
        }
    }
    return path;
};

var ocxSQL = function (params) {
    let data = [];
    let ip = cameraIP;
    try {
        if (sqlStatus === 0) {
            let path = initPathConfig();
            let openDB = ocx.sqlite_open(ip, path.db);
            if (openDB == -1) {
                openDB = ocx.sqlite_open(ip, path.db);
                if (openDB == -1) {
                    layer.msg(langMessage.ocxGlobal.ocxError.OPEN_FAIL, {icon: 2});
                    return
                }
            }
            console.log("open database " + openDB);
            if (openDB === 0) {
                sqlStatus = 1;
            }
        }
        console.log("ocx.SQL_Select");
        let p = {
            IP: ip
        };
        for (let index in params) {
            p[index] = params[index]
        }
        let eventData = ocx.SQL_Select(JSON.stringify(p));
        console.log(eventData);

        if (eventData !== 'FALSE') {
            let d = eventData.replace(/\\/g, "\\\\");
            // let JSONdata = eventData.replace(/\\\\/g, "////")


            data = JSON.parse(d);
            console.log("ocx.SQL_Select:data.length=" + data.length)
            let eventConfig = getSe('eventConfig');
            let carType = getSe("carDetailType");
            $(data).each(function (i) {
                data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
                data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
                data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
                data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
                data[i].CAR_TYPE = eventConfig.carType[data[i]['CAR_TYPE']];
                if (data[i]['CHE_XING']) {
                    data[i].CHE_XING = carType.get(data[i]['CHE_XING']);
                }
                if (data[i]['IMAGE_PRE_PATH']) {
                    data[i].preTime = getTime(data[i]['IMAGE_PRE_PATH'])
                }
                if (data[i]['IMAGE_LAST_PATH']) {
                    data[i].lastTime = getTime(data[i]['IMAGE_LAST_PATH'])
                }
                if (data[i]['IMAGE_FEATURE_PATH']) {
                    data[i].featureTime = getTime(data[i]['IMAGE_FEATURE_PATH'])
                }
            });

        }
    } catch (e) {
        console.log(e);
        layer.msg(langMessage.ocxGlobal.ocxError.SEARCH_FAIL, {icon: 2})
    }
    return data;
};
var getTime = function (picString) {
    let index = picString.lastIndexOf("\\");
    let time = picString.slice(index + 1, index + 18);
    let newTime = time.slice(0, 4) + "/" + time.slice(4, 6) + "/" + time.slice(6, 8) + " "
        + time.slice(8, 10) + ":" + time.slice(10, 12) + ":" + time.slice(12, 14);
    return timestampToTime(new Date(newTime).getTime() + parseInt(time.slice(14)));
};
var initialize_ocx = function (id) {
    let result = find_ocx();
    let flag = {
        result: false,
        msg: langMessage.ocxGlobal.ocxError.WITHOUT_INSTALL
    };
    let version = 0;
    let href = '../setup.exe';
    if (id !== 'eventVideo' && id !== 'indexVideo' && id !== "authContainer") {
        href = '../../setup.exe'
    }
    let container = $("#" + id);
    if (result === true) {
        container.append("<OBJECT ID=\"ocx\" width=\"100%\" height=\"100%\" classid=\"clsid:20C7BA3F-0E5A-4E5F-849F-FE2D7CB7EFCA\"></OBJECT>");
        ocx = document.getElementById("ocx");
        //getDeviceVersion();
        try {
            version = getVersion();
            let nowVersion = getSe("projectConfig").index_ocx;
            // if (compareVersion(version.main + "." + version.sub, nowVersion.main + "." + nowVersion.sub, null, true) >= 0) {
            if (compareVersion(version.main + "." + version.sub + "." + version.revise, nowVersion.main + "." + nowVersion.sub + "." + version.revise) >= 0) {
                // if ((version.main > nowVersion.main) ||
                //     (version.main === nowVersion.main && version.sub > nowVersion.sub) ||
                //     (version.main === nowVersion.main && version.sub === nowVersion.sub && version.revise >= nowVersion.revise)) {
                let fileInit = getSe("fileInit");
                if (!fileInit) {
                    fileInit = initFilePath();
                    setSe("fileInit", fileInit);
                }
                if (!fileInit.result) {
                    flag.result = false;
                    flag.msg = fileInit.msg;
                    flag.offset = fileInit.offset || 'auto';
                    ocxDestroy();
                    container.html("");
                    return flag
                }
                let streamType = getCommonConf("RTSPWAY");
                let type = ['UDP', 'TCP'];
                let height = container[0].offsetHeight - 30;
                if (id === 'indexVideo') {
                    $("#ocx").attr({
                        width: (container[0].offsetWidth),
                        height: (height),
                    });
                    // container.append(control);
                    let channel = getSe("projectConfig").camera_channel;
                    $("#videoT2Value").html(type[streamType]);
                    setDataset($("#videoT2Value")[0], "type", streamType);
                    setTimeout(function () {
                        initStreamType(channel)
                    }, 1000);
                } else if (id === 'eventVideo') {
                    $("#ocx").attr({
                        width: (container[0].offsetWidth),
                        height: (height),
                    });
                    $("#videoT2Value").html(type[streamType]);
                    setDataset($("#videoT2Value")[0], "type", streamType);
                    // container.append(control);
                    //initRTSP()
                }
                flag.result = true;
                flag.msg = "";
                return flag

            } else {
                flag.msg = langMessage.ocxGlobal.ocxError.MATCH_FAIL
            }
        } catch (e) {
            console.log(e);
        } finally {
            let projectConfig = getSe("projectConfig");
            projectConfig.ocx_version = version;
            setSe('projectConfig', projectConfig);
        }
    }
    let msg = langMessage.ocxGlobal.ocxError.DOWNLOAD;
    if (id !== 'pictureOCX') {
        container.html("");
        container.append("<a id='ocx_down' href=" + href + ">" + msg + "</a>");
    }
    return flag
};

var getCommonConf = function (key) {
    console.log("ocx.getCurrentconf");
    let ip = cameraIP;
    let value = ocx.getCurrentconf(ip, key);
    console.log("getCurrentconf", value);
    return value
};

var setRTSPWay = function (type, callback) {
    console.log("ocx.RTSP_Way");
    let ip = cameraIP;
    let ret = ocx.RTSP_Way(ip, type);
    let result = false;
    console.log("RTSP_Way", ret);
    if (ret === 'TRUE') {
        result = true
    }
    if (callback) {
        callback(result);
    }
};

var RTSPError = function (msg) {
    removeVideo();
    $("#RTSPError").css({display: "block", width: '100%', top: 0, left: 0, height: '30px'});
    $("#errorMsg").html(msg);

};
var RTSPMessage = function () {
    removeVideo();
    console.log(layer);
    layer.open({
        id: 'RTSPErrorMsg',
        type: 1,
        title: langMessage.ocxGlobal.rtspError.INPUT_ERROR,
        closeBtn: 0,
        move: false,
        success: function (layero, index) {
            console.log(layero, index);
            let tmpDom = layero[0];
            $("#RTSPError").css({display: "block", top: 0, left: 0, width: '100%', height: '80%'});
            $(tmpDom).find(".layui-layer-title").css({color: "#c52616"})
        },
        btn: [langMessage.common.confirm, langMessage.common.cancel],
        yes: function (index, layero) {
            let username = $(layero[0]).find("input[name='username']").val();
            let password = $(layero[0]).find("input[name='password']").val();
            if (username && password) {
                layer.close(index);
                console.log("close");
                setStreamInfo(username, password, function (res) {
                    if (res.ret === 0) {
                        let channel = getSe("projectConfig").camera_channel;
                        $(layero[0]).find("#RTSPInputError").html("");
                        $("#RTSPError").css({display: "none"});
                        setTimeout(function () {
                            initStreamType(channel)
                        }, 1000);
                    } else {
                        RTSPError(langMessage.ocxGlobal.ocxError.TIMEOUT)
                    }

                })

            } else {
                $(layero[0]).find(".layui-layer-title").html(langMessage.ocxGlobal.rtspError.IS_EMPTY)
            }

        },
        btn2: function (index, layero) {
            layer.close(index);
        },
        content: '<div id="RTSPForm" style="margin-top: 20px;">\n' +
            '    <form class="layui-form" action="">\n' +
            '        <div class="layui-form-item">\n' +
            '            <label class="layui-form-label">' + langMessage.ocxGlobal.username + '</label>\n' +
            '            <div class="layui-input-inline">\n' +
            '                <input type="text" name="username" required  lay-verify="required" placeholder="' + langMessage.ocxGlobal.userPlaceholder + '" autocomplete="off" class="layui-input">\n' +
            '            </div>\n' +
            '        </div>\n' +
            '        <div class="layui-form-item">\n' +
            '            <label class="layui-form-label">' + langMessage.ocxGlobal.password + '</label>\n' +
            '            <div class="layui-input-inline">\n' +
            '                <input type="password" name="password" required lay-verify="required" placeholder="' + langMessage.ocxGlobal.pwdPlaceholder + '" autocomplete="off" class="layui-input">\n' +
            '            </div>\n' +
            '        </div>\n' +
            // '        <div class="layui-form-item">\n' +
            // '            <div class="layui-input-block">\n' +
            // '                <button class="layui-btn" lay-submit lay-filter="RTSPForm">确定</button>\n' +
            // '            </div>\n' +
            // '            <div class="layui-input-block">\n' +
            // '                <button class="layui-btn layui-btn-normal"  lay-filter="RTSPForm">取消</button>\n' +
            // '            </div>\n' +
            // '        </div>\n' +
            '    </form>\n' +
            '</div>'
    });
};

// var testMessage = function () {
//     console.log(layer);
//     layer.open({
//         id: 'RTSPErrorMsg',
//         type: 1,
//         title: langMessage.ocxGlobal.rtspError.INPUT_ERROR,
//         closeBtn: 0,
//         move: false,
//         success: function (layero, index) {
//             console.log(layero, index);
//             let tmpDom = layero[0];
//             $("#RTSPError").css({display: "block", top: 0, left: 0, width: '100%', height: '80%'});
//             $(tmpDom).find(".layui-layer-title").css({color: "#c52616"})
//         },
//         btn: ['确定', '取消'],
//         yes: function (index, layero) {
//             let username = $(layero[0]).find("input[name='username']").val();
//             let password = $(layero[0]).find("input[name='password']").val();
//             if (username && password) {
//                 layer.close(index);
//                 console.log("close");
//                 testMessage();
//
//             } else {
//                 $(layero[0]).find(".layui-layer-title").html(langMessage.ocxGlobal.rtspError.IS_EMPTY)
//             }
//
//         },
//         btn2: function (index, layero) {
//             layer.close(index);
//         },
//         content: '<div id="RTSPForm" style="margin-top: 20px;">\n' +
//         '    <form class="layui-form" action="">\n' +
//         '        <div class="layui-form-item">\n' +
//         '            <label class="layui-form-label">用户名</label>\n' +
//         '            <div class="layui-input-inline">\n' +
//         '                <input type="text" name="username" required  lay-verify="required" placeholder="请输入相机用户名" autocomplete="off" class="layui-input">\n' +
//         '            </div>\n' +
//         '        </div>\n' +
//         '        <div class="layui-form-item">\n' +
//         '            <label class="layui-form-label">密码</label>\n' +
//         '            <div class="layui-input-inline">\n' +
//         '                <input type="password" name="password" required lay-verify="required" placeholder="请输入相机密码" autocomplete="off" class="layui-input">\n' +
//         '            </div>\n' +
//         '        </div>\n' +
//         // '        <div class="layui-form-item">\n' +
//         // '            <div class="layui-input-block">\n' +
//         // '                <button class="layui-btn" lay-submit lay-filter="RTSPForm">确定</button>\n' +
//         // '            </div>\n' +
//         // '            <div class="layui-input-block">\n' +
//         // '                <button class="layui-btn layui-btn-normal"  lay-filter="RTSPForm">取消</button>\n' +
//         // '            </div>\n' +
//         // '        </div>\n' +
//         '    </form>\n' +
//         '</div>'
//     });
// };

var changePic = function (that, type) {
    let index = parseInt(getDataset($(that).parents("#eventControl").siblings(".layui-layer-content").find("#eventPicture")[0]).index);
    if (type === "prev") {
        index = index - 1
    } else if (type === 'next') {
        index = index + 1
    }
    let data = $("div[lay-id='eventTable'] .layui-table-body").find("tr")[index];
    if (!data) {
        layer.msg(langMessage.ocxGlobal.eventError, {icon: 2});
        return;
    }
    setDataset($("#eventPicture")[0], "index", index);
    let changeData = getTDValue(data);
    showCanvas(changeData);
    showDetailTable(changeData);
    $(".layui-layer-title").html(langMessage.ocxGlobal.eventId + changeData.ID);
    changeData = null;
};
var getTDValue = function (obj) {
    let TDs = $(obj).find("td");
    let data = {};
    TDs.each(function (i) {
        let key = getDataset(TDs[i]).field;
        let value = $(TDs[i]).find(".layui-table-cell").html();
        data[key] = value;
    });
    return data;
};

var getDeviceTime = function () {
    console.log("ocx.GetDeviceTime");
    let time = ocx.getDeviceTime(cameraIP);
    return date2stamp(time)
};

var getDeviceVersion = function () {
    console.log("ocx.getDeviceVersion");
    let version = ocx.getDeviceVersion(cameraIP);
    console.log("deviceVersion:", version);
    let v = version.slice(4);
    let newAgreement = false;
    if (compareVersion(v, "8.0.2") >= 0) {
        newAgreement = true;
    }
    setSe("SDC", newAgreement);
};
var uploadLicence = function (path) {
    console.log("ocx.UploadLicence");
    let result = ocx.UploadLicence(cameraIP, path);
    console.log("UploadLicence:", result);
    if (result === 'FALSE') {
        return false
    }
    return true
};

var getLicencePath = function () {
    console.log("ocx.SelectFileDialog");
    let result = ocx.SelectFileDialog();
    console.log("SelectFileDialog:", result);
    if (result === 'FALSE') {
        return false
    }
    return result
};

var showEventDetail = function (data, _index, w, h) {
    // let fW_c = window.parent.document.getElementById("frame_c").offsetWidth;
    // let fH_c = window.parent.document.getElementById("frame_c").offsetHeight;
    // console.log('showEventDetail layer.open w h:',w,h)
    layer.open({
        title: langMessage.event.eventTable.ID + "：" + data.ID,
        type: 1,
        area: [w, h],
        //offset: [((fH_c - h) / 2) + 'px', (fW_c / 2 + 20) + 'px'],//'auto',
        success: function (layero, index) {
            let iframe = "<iframe id='iframebar' src='about:blank' frameBorder=0  marginHeight=0 marginWidth=0 style='position:absolute;visibility:inherit;top:0px;left:0px;height:" + h + ";width:" + w + ";z-index:-1' filter='progid:DXImageTransform.Microsoft.Alpha(style=0,opacity=0)'></iframe>"
            $('.layui-layer-page').append(iframe)
            setDataset($("#eventPicture")[0], "index", _index);
            showCanvas(data);
            showDetailTable(data);
            setTimeout(function () {
                $(".layui-layer-page").append("<span id='eventControl' class='img-control'><a href=\"javascript:;\" class=\"layui-layer-iconext layui-layer-imgprev\"></a><a href=\"javascript:;\" class=\"layui-layer-iconext layui-layer-imgnext\"></a></span>");
                $("#eventControl .layui-layer-imgprev").off('click').on('click', function (e) {
                    changePic(this, 'prev')
                });
                $("#eventControl .layui-layer-imgnext").off('click').on('click', function (e) {
                    changePic(this, 'next')
                })
            }, 10);
            data = null;
        },
        end: function (layero, index) {
            clearDetailTable();
        },
        content: $('#eventPicture') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
    });
};
//CAR_PRE_X,CAR_PRE_Y,CAR_PRE_W,CAR_PRE_H,
//  CAR_EVENT_X,CAR_EVENT_Y,CAR_EVENT_W,CAR_EVENT_H,
//  CAR_LAST_X,CAR_LAST_Y,CAR_LAST_W,CAR_LAST_H,
var showCanvas = function (data) {
    // console.log('#################showCanvas###################')
    // console.log(JSON.stringify(data))
    clearDetailTable();
    $(".layer-pic").addClass("event-height");
    imageCanvas('currentPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], null, true, null, data['CAR_EVENT_X'], data['CAR_EVENT_Y'], data['CAR_EVENT_W'], data['CAR_EVENT_H']);

    if (data['IMAGE_PLATE_PATH'] !== '') {
        imageCanvas('platePic', data['IMAGE_PLATE_PATH'], 0, 0, 0, 0, true);
    }
    if (data['IMAGE_PRE_PATH'] !== '') {
        imageCanvas('prePic', data['IMAGE_PRE_PATH'], data['IMAGE_PRE_X'], data['IMAGE_PRE_Y'], data['IMAGE_PRE_W'], data['IMAGE_PRE_H'], true, null, null, data['CAR_PRE_X'], data['CAR_PRE_Y'], data['CAR_PRE_W'], data['CAR_PRE_H'])
    }
    if (data['IMAGE_EVENT_PATH'] !== '') {
        // if (data['IMAGE_PRE_PATH'] === '') {
        //     imageCanvas('prePic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true, null, null, data['CAR_EVENT_X'], data['CAR_EVENT_Y'], data['CAR_EVENT_W'], data['CAR_EVENT_H']);
        //     $("#prePic").addClass("show");
        // } else {
        //     imageCanvas('eventPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true, null, null, data['CAR_EVENT_X'], data['CAR_EVENT_Y'], data['CAR_EVENT_W'], data['CAR_EVENT_H']);
        //     $("#eventPic").addClass("show");
        // }
        imageCanvas('eventPic', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'], true, null, null, data['CAR_EVENT_X'], data['CAR_EVENT_Y'], data['CAR_EVENT_W'], data['CAR_EVENT_H']);
        $("#eventPic").addClass("show");
    }
    if (data['IMAGE_LAST_PATH'] !== '') {
        imageCanvas('lastPic', data['IMAGE_LAST_PATH'], data['IMAGE_LAST_X'], data['IMAGE_LAST_Y'], data['IMAGE_LAST_W'], data['IMAGE_LAST_H'], true, null, null, data['CAR_LAST_X'], data['CAR_LAST_Y'], data['CAR_LAST_W'], data['CAR_LAST_H'])
    }
    if (data['IMAGE_FEATURE_PATH'] !== '') {
        imageCanvas('featurePic', data['IMAGE_FEATURE_PATH'], 0, 0, 0, 0, true);
    }
};
var showDetailTable = function (data) {
    let tHead = "";
    let $trTemp = $("<tr></tr>");
    tHead = "<th>" + langMessage.event.eventTable.EVENT_TYPE + "</th><th>" + langMessage.event.eventTable.LANE_INDEX + "</th><th>" + langMessage.event.eventTable.PLATE_TYPE + "</th><th>" + langMessage.event.eventTable.PLATE_STRING + "</th><th>" + langMessage.event.eventTable.CAR_SPEED + "</th><th>" + langMessage.event.eventTable.CAR_COLOR + "</th><th>" + langMessage.event.eventTable.CAR_TYPE + "</th>";
    // 往行里面追加 td单元格
    if (data['CHE_XING']) {
        tHead += '<th>' + langMessage.event.eventTable.CHE_XING + '</th>'
    }
    // tHead+='<th>概述</th>';
    $trTemp.append("<td>" + data.EVENT_TYPE + "</td>");
    $trTemp.append("<td>" + data.LANE_INDEX + "</td>");
    $trTemp.append("<td>" + data.PLATE_TYPE + "</td>");
    $trTemp.append("<td>" + data.PLATE_STRING + "</td>");
    $trTemp.append("<td>" + data.CAR_SPEED + "</td>");
    $trTemp.append("<td>" + data.CAR_COLOR + "</td>");
    $trTemp.append("<td>" + data.CAR_TYPE + "</td>");
    if (data['CHE_XING']) {
        $trTemp.append("<td>" + data.CHE_XING + "</td>");
    }
    // $trTemp.append("<td> </td>");
    $("#detailTable tbody tr").remove();
    $trTemp.appendTo("#detailTable tbody");
    $("#detailTable thead tr").html(tHead);
    if (data['preTime']) {
        $("#preTime").html(langMessage.event.eventTable.IMAGE_EVENT_PATH + data['preTime']);
    }
    if (data['time']) {
        if (!data['preTime']) {
            $("#preTime").html(langMessage.event.eventTable.IMAGE_EVENT_PATH + data['time']).addClass("show");
        } else {
            $("#eventTime").html(langMessage.event.eventTable.IMAGE_PRE_PATH + data['time']).addClass("show");
        }
    }
    if (data['lastTime']) {
        $("#lastTime").html(langMessage.event.eventTable.IMAGE_LAST_PATH + data['lastTime']);
    }
    if (data['featureTime']) {
        $("#featureTime").html(langMessage.event.eventTable.IMAGE_FEATURE_PATH + data['featureTime']);
    }
};
var clearDetailTable = function () {
    $(".layer-pic").removeClass("event-height");
    $(".pic-show").removeClass("show");
    $(".time-show").removeClass("show");
    $("#detailTable tbody").html("");
    $("#detailTable thead tr").html("");
    $("#currentPic").html("");
    $("#featurePic").html("").off("click");
    $("#eventPic").html("").off("click");
    $("#platePic").html("").off("click");
    $("#prePic").html("").off("click");
    $("#lastPic").html("").off("click");
    $("#timeList .layui-row div").html("")
};

var exportData = function (data, path) {
    let dataL = 0;
    let exportData = {};
    data.map(function (item) {
        let type = item.EVENT_TYPE;
        if (!exportData[type]) {
            dataL++;
            exportData[type] = [];
        }
        if (item['IMAGE_PLATE_PATH'] !== '') {
            exportData[type].push(item['IMAGE_PLATE_PATH'])
        }
        if (item['IMAGE_PRE_PATH'] !== '') {
            exportData[type].push(item['IMAGE_PRE_PATH'])
        }
        if (item['IMAGE_EVENT_PATH'] !== '') {
            exportData[type].push(item['IMAGE_EVENT_PATH'])
        }
        if (item['IMAGE_LAST_PATH'] !== '') {
            exportData[type].push(item['IMAGE_LAST_PATH'])
        }
    });
    let result = false;
    let exportL = 0;
    for (let key in exportData) {
        let r = exportPictureTask(path + "\\" + key, exportData[key]);
        if (!r) {
            break;
        } else {
            exportL++;
        }
    }
    if (exportL === dataL) {
        result = true;
    }
    return result;
};
var setStreamInfo = function (user, pwd, callback) {
    let param = {
        stream_username: user,
        stream_password: pwd
    };
    initWebService(webserviceCMD.CMD_SET_VIDEO, param, callback, null, callback);
};

// 老接口
// var initFilePath = function () {
//     let ip = cameraIP;
//     console.log("ocx.FIle_Path_Init");
//     let ret = ocx.FIle_Path_Init(ip);
//     console.log("ocx.FIle_Path_Init:" + ret)
// };
// var ThreadStatus = function () {
//     let result = false;
//     try {
//         console.log("ocx_ThreadStatus");
//         let ret = ocx.ThreadStatus();
//         console.log("ThreadStatus:" + ret);
//         if (ret === "TRUE") {
//             result = true
//         }
//
//     } catch (e) {
//         result = e;
//         console.log(e)
//     } finally {
//         return result
//     }
// };
// var stopThread = function () {
//     console.log("ocx.STOPThread");
//     let ret = ocx.STOPThread();
//     console.log("STOPThread:" + ret);
// };
// var initRTSP = function (flag, type, w, h) {
//     try {
//         console.log("ocx.command:init");
//         let play = "H264";
//
//         if (!flag) {
//             play = "H265"
//         }
//         let ret = ocx.command('INIT', '[xnn| 11]', play, w, h);
//         console.log("rtsp-init:" + ret);
//         showRTSP(type, play, w, h);
//     } catch (e) {
//         console.log(e)
//     }
// };
// var showRTSP = function (channel, play, w, h) {
//     console.log("ocx.command:play");
//     let ret = ocx.command('PLAY', '[channal|0][url|' + getCameraUrl(channel) + ']', play, w, h);
//     console.log("rtsp-play:" + ret);
// };
// var stopRTSP = function (channel) {
//     console.log("ocx.command:stop");
//     let ret = ocx.command('STOP', '[channal|0][url|' + getCameraUrl(channel) + ']', "", 0, 0);
//     // console.log("ocx.command:close")
//     // let cRet = ocx.command('CLOSE', '');
//     console.log("rtsp-stop:" + ret);
// };

// var initRTSP = function (flag, type) {
//     if (flag) {
//         console.log("ocx.command:init");
//         let ret = ocx.command('INIT', '[xnn| 11]');
//         console.log("rtsp-init:" + ret);
//         showRTSP(type);
//     } else {
//         errorRTSP();
//     }
// };
// var showRTSP = function (channel) {
//     console.log("ocx.command:play");
//     let ret = ocx.command('PLAY', '[channal|0][url|' + getCameraUrl(channel) + ']');
//     console.log("rtsp-play:" + ret);
// };
// var initStreamType = function (type) {
//     initWebService(13, null, getStreamTypeCallback,type);
// };
// var getStreamTypeCallback = function (res,type) {
//     let flag = false;
//     if (res.stream_type === '1') {
//         flag = true
//     };
//     initRTSP(flag,type)
// };
// var stopRTSP = function (channel) {
//     console.log("ocx.command:stop");
//     let ret = ocx.command('STOP', '[channal|0][url|' + getCameraUrl(channel) + ']');
//     // console.log("ocx.command:close")
//     // let cRet = ocx.command('CLOSE', '');
//     console.log("rtsp-stop:" + ret);
// };
// var closeRTSP = function () {
//     let ip = cameraIP;
//     console.log("ocx.command:close");
//     let cRet = ocx.command('CLOSE', '');
//     console.log(cRet);
// };
// var getEventStatus = function () {
//     let status = ocx.eventWeb();
//     try {
//         status = JSON.parse(status)
//     } catch (e) {
//         console.log(e)
//     }
//     return status
// };
// var selectEvent = function (param) {
//     let events = {};
//     let getEvents = ocx.select_event(param);
//     if (events !== "FALSE") {
//         try {
//             events = JSON.parse(getEvents)
//         } catch (e) {
//             console.log(e)
//         }
//     }
//     return events
// };
//
// var selectDB = function (params) {
//     if (sqlStatus === 0) {
//         let path = initPathConfig();
//         let openDB = ocx.sqlite_open(path.img + "sql.db");
//         if (openDB == -1) {
//             path = initPathConfig();
//             openDB = ocx.sqlite_open(path.img + "sql.db");
//             if (openDB == -1) {
//                 layer.msg('无法打开文件！请联系厂家。', {icon: 2});
//                 return
//             }
//         }
//         console.log("open database " + openDB);
//         if (openDB === 0) {
//             sqlStatus = 1;
//         }
//     }
//     let keyIndex = 0;
//     let conditions = "";
//     let condition = {
//         'begin_time': 'EVENT_TIME>=',
//         'end_time': 'EVENT_TIME<=',
//         'plate_type': 'PLATE_TYPE=',
//         'event_type': 'EVENT_TYPE=',
//         'plate_string': 'PLATE_STRING like "%'
//     };
//     let limit = "";
//     if (params.limit) {
//         limit = " Limit " + params.limit;
//     }
//     let order = "Desc";
//     if (params.order) {
//         order = params.order
//     }
//     console.log(params);
//     Object.keys(params).forEach(function (key) {
//         if (params[key]) {
//             if (key === 'limit' || key === "order") {
//                 return
//             }
//             if (keyIndex === 0) {
//                 conditions += " WHERE "
//             } else {
//                 conditions += " AND "
//             }
//             keyIndex++;
//             conditions += condition[key] + params[key];
//             if (key === 'plate_string') {
//                 conditions += '%"'
//             }
//         }
//     });
//     let sql = "select " +
//         "ID" +
//         ",EVENT_TYPE" +
//         ",PLATE_TYPE" +
//         ",PLATE_STRING" +
//         ",LANE_INDEX" +
//         ",CAR_COLOR" +
//         ",CAR_SPEED" +
//         ",EVENT_TIME" +
//         ",EVENT_TICK" +
//         ",IMAGE_COUNT" +
//         ",IMAGE_PRE_PATH" +
//         ",IMAGE_PRE_X" +
//         ",IMAGE_PRE_Y" +
//         ",IMAGE_PRE_W" +
//         ",IMAGE_PRE_H" +
//         ",IMAGE_EVENT_PATH" +
//         ",IMAGE_EVENT_X" +
//         ",IMAGE_EVENT_Y" +
//         ",IMAGE_EVENT_W" +
//         ",IMAGE_EVENT_H" +
//         ",IMAGE_LAST_PATH" +
//         ",IMAGE_LAST_X" +
//         ",IMAGE_LAST_Y" +
//         ",IMAGE_LAST_W" +
//         ",IMAGE_LAST_H" +
//         ",IMAGE_PLATE_PATH" +
//         ",TIMESTAMP" +
//         " from 'EVENT_TBL' " +
//         conditions + " Order By EVENT_TIME " + order + ",EVENT_TICK " + order + limit;
//     console.log("ocx.sqlite_select");
//     let eventData = ocx.sqlite_select(sql);
//     console.log(eventData);
//     let data = [];
//     if (eventData !== 'FALSE') {
//         let d = eventData.replace(/\\/g, "\\\\");
//         // let JSONdata = eventData.replace(/\\\\/g, "////")
//
//         try {
//             data = JSON.parse(d);
//             let wrapData = [];
//
//             let eventConfig = getSe('eventConfig');
//             $(data).each(function (i) {
//                 data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
//                 data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
//                 data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
//                 data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
//             });
//         } catch (e) {
//             console.log(e);
//             layer.msg('查询出错！', {icon: 2})
//         }
//     }
//
//
//     //
//     // var getTpl = dataTpl.innerHTML
//     //     , view = document.getElementById('data');
//     // laytpl(getTpl).render(data, function (html) {
//     //     view.innerHTML = html;
//     // });
//     return data;
// };
// var ocxPath = function (callback) {
//     let result = ocx.Modif_Path();
//     if (result === "TRUE") {
//         initPath();
//         callback(true)
//     } else {
//         console.log(result);
//         callback(false)
//     }
// };
// var ocxAuth = function (u_id, callback) {
//     console.log("ocx.GET_Auth");
//     let status = ocx.GET_Auth(u_id);
//     let result = {
//         msg: '当前文件夹下没有匹配文件或授权码文件打开错误！',
//         result: false
//     };
//     if (status !== 'FALSE') {
//         try {
//             let base = JSON.parse(status);
//             if (base['Executive Outcomes'] === 'TRUE') {
//                 result.base = base['Base64'];
//                 result.result = true;
//             } else if (base['Filecount']) {
//                 let msg = '匹配到' + base['Filecount'] + '个相同的授权文件：' + base['File_alike_name'];
//                 msg = msg.slice(0, -1);
//                 result.msg = msg;
//                 result.result = false;
//             }
//         } catch (e) {
//             console.log(e)
//         }
//         if (callback) {
//             callback(result)
//         }
//     }
//
// };
// var ocxSQL = function (params) {
//     let data = [];
//     try {
//         if (sqlStatus === 0) {
//             let path = initPathConfig();
//             let openDB = ocx.sqlite_open(path.img + "sql.db");
//             if (openDB == -1) {
//                 path = initPathConfig();
//                 openDB = ocx.sqlite_open(path.img + "sql.db");
//                 if (openDB == -1) {
//                     layer.msg('无法打开文件！请联系厂家。', {icon: 2});
//                     return
//                 }
//             }
//             console.log("open database " + openDB);
//             if (openDB === 0) {
//                 sqlStatus = 1;
//             }
//         }
//         console.log("ocx.SQL_Select");
//         let eventData = ocx.SQL_Select(JSON.stringify(params));
//         //console.log(eventData);
//
//         if (eventData !== 'FALSE') {
//             let d = eventData.replace(/\\/g, "\\\\");
//             // let JSONdata = eventData.replace(/\\\\/g, "////")
//
//
//             data = JSON.parse(d);
//             let eventConfig = getSe('eventConfig');
//             $(data).each(function (i) {
//                 data[i].time = getDate(data[i]['EVENT_TIME'], data[i]['EVENT_TICK']);
//                 data[i].EVENT_TYPE = eventConfig.eventType[data[i]['EVENT_TYPE']];
//                 data[i].PLATE_TYPE = eventConfig.plateType[data[i]['PLATE_TYPE']];
//                 data[i].CAR_COLOR = eventConfig.carColor[data[i]['CAR_COLOR']];
//             });
//
//         }
//     } catch (e) {
//         console.log(e);
//         layer.msg('查询出错！', {icon: 2})
//     }
//     return data;
// };


//yx接口弃用
// var initOnvif = function () {
//     console.log("ocx.ONVIF_INI");
//     let ip = cameraIP;
//     let result = ocx.ONVIF_INI(ip, 'admin', '12345');
//     if (result === "FALSE") {
//         return false
//     }
//     return true;
// };
// var getOnvifTraficLight = function () {
//     console.log("ocx.trafficLight_get");
//     let data = ocx.trafficLight_get();
//     //let data="[{\"x\":0.297136,\"y\":0.246815},{\"x\":0.297136,\"y\":0.283439},{\"x\":0.319809,\"y\":0.283439},{\"x\":0.319809,\"y\":0.246815},{\"x\":0.442200,\"y\":0.255000},{\"x\":0.442200,\"y\":0.264500},{\"x\":0.449500,\"y\":0.264500},{\"x\":0.449500,\"y\":0.255000},{\"x\":0.459000,\"y\":0.260300},{\"x\":0.459000,\"y\":0.279700},{\"x\":0.477800,\"y\":0.279700},{\"x\":0.477800,\"y\":0.260300},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000},{\"x\":0.000000,\"y\":0.000000}]"
//     let allPosition = [];
//     if (data !== 'FALSE') {
//         data = JSON.parse(data);
//         allPosition = value2Coordinate(data);
//         for (let i = 0; i < allPosition.length; i++) {
//             let item = {};
//             item.type = "fixed-rect";
//             item.id = 'videoSignal' + fixName[i];
//             item.position = allPosition[i];
//             showGroupPoint(item, item.id, 'drawVideoSignal');
//             // setSe('videoSignal'+fixName[i], item);
//         }
//     } else {
//         return false;
//     }
//     return true;
// };
// var setOnvifTraficLight = function () {
//     console.log("ocx.trafficLight_set");
//     let data = getSe('videoSignalValue');
//     data = JSON.stringify(data);
//     let result = ocx.trafficLight_set(data);
//     if (result === 'TRUE') {
//         getOnvifTraficLight();
//     }
//     return result;
// };
// var getOnvifOSD = function () {
//     console.log("ocx.OSD_get");
//     let data = ocx.OSD_get();
//     if (data !== 'FALSE') {
//         data = JSON.parse(data);
//         setSe('sOSD', data[0]);
//         return true
//     }
//     return false
// };
// var setOnvifOSD = function () {
//     console.log("ocx.OSD_set");
//     let data = getSe('sOSD');
//     data = JSON.stringify(data);
//     let result = ocx.OSD_set(data);
//     return result;
// };


// var imageCanvas = function (type, path, x, y, w, h) {
//     let picDom = $("#" + type)[0];
//     let picW = parseInt(picDom.clientWidth);
//     let picH = parseInt(picDom.clientHeight);
//     //图像宽高默认为4的倍数
//     picW = parseInt(picW / 4) * 4;
//     picH = parseInt(picH / 4) * 4;
//     let can = $("<canvas id=\"" + type + "Can\" height='" + picH + "' width='" + picW + "'>");
//     $("#" + type).html(can);
//     let c = $("#" + type + "Can")[0];
//     let ctx = c.getContext('2d');
//     ctx.clearRect(0, 0, picW, picH);
//     // let paths = sendPath(path);
//     getRGBA(path, picW, picH, function (img, width, height) {
//         ctx.drawImage(img, 0, 0, picW, picH, 0, 0, picW, picH);
//         if (x >= 0 && y >= 0 && w > 0 && h > 0) {
//             let rectX = parseInt(x / width * picW);
//             let rectY = parseInt(y / height * picH);
//             let rectW = parseInt(w / width * picW);
//             let rectH = parseInt(h / height * picH);
//             ctx.strokeStyle = 'red';
//             ctx.lineWidth = "1";
//             ctx.rect(rectX, rectY, rectW, rectH);
//             rectX = null, rectY = null;
//             rectH = null;
//             rectW = null;
//         }
//         ctx.stroke();
//         let multipleX = width / picW;
//         let multipleY = height / picH;
//         if (type === 'prePic' || type === 'eventPic' || type === 'lastPic') {
//             let magCan = $("<canvas class=\"magCanvas\" id=\"" + type + "MagCan\" height=200 width=200>");
//             $("#" + type + "Can").after(magCan);
//             let mc = document.getElementById(type + "MagCan");
//             let mctx = mc.getContext("2d");
//             c.onmouseout = function () {
//                 mc.style.display = "none";
//             };
//             c.onmousemove = function (e) {
//                 if (img) {
//                     let bigImg = document.createElement('img');
//                     // let imgSrc = sendPath(path);
//                     // let blob = base64ToBlob(imgSrc);
//                     // bigImg.onload = function(){
//                     //     window.URL.revokeObjectURL(bigImg.src);
//                     // };
//                     // bigImg.src = window.URL.createObjectURL(blob);
//                     let blob = sendPath(path);
//                     bigImg.onload = function () {
//                         window.URL.revokeObjectURL(bigImg.src);
//                     };
//                     bigImg.src = window.URL.createObjectURL(blob);
//                     mc.style.display = "block";
//                     mctx.clearRect(0, 0, 300, 300);
//                     let location = getLocation(type + "Can", e.clientX, e.clientY);// getLocation()
//                     let centerPoint = {
//                         x: location.x,
//                         y: location.y
//                     };
//                     let originalRadius = 300;
//                     mctx.drawImage(bigImg, (centerPoint.x * multipleX - 50 < 0 ? 0 : centerPoint.x * multipleX - 50),// biggerImg:规定要使用的图像、画布或视频;
//                         (centerPoint.y * multipleY - 50 < 0 ? 0 : centerPoint.y * multipleY - 50), originalRadius,// centerPoint.y
//                         originalRadius, 0, 0, originalRadius, originalRadius);// originalRadius:被剪切图像的高度；
//                     mc.style.left = e.pageX + "px";
//                     mc.style.top = e.pageY + "px";
//                 } else {
//                     mctx.clearRect(0, 0, 300, 300);// clearRect() 方法清空给定矩形内的指定像素。
//                     mc.style.display = "none";
//                 }
//             };
//         }
//         can = null;
//         c = null;
//         ctx = null;
//
//     });
// };
