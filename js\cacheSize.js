/**
 * 获取本地存储的使用情况
 * @param t
 */
var get_cache_size = function (t) {
  t = t === undefined ? "l" : t;
  let obj = "";
  if (t === 'l') {
    if (!window.localStorage) {
      console.log(langMessage.common.nonsupportLocal);
    } else {
      obj = window.localStorage;
    }
  } else {
    if (!window.sessionStorage) {
      console.log(langMessage.common.nonsupportSession);
    } else {
      obj = window.sessionStorage;
    }
  }
  if (obj !== "") {
    let size = 0;
    for (let item in obj) {
      if (obj.hasOwnProperty(item)) {
        size += obj.getItem(item).length;
      }
    }
    console.log(langMessage.common.getCacheSize(size));
  }
};

