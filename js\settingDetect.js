var element, layer, form, laydate, table;
var myWhxx = []

$(document).ready(function () {
    numberRange("detectNoLicense", 0, 3, false, true);
    numberRange("overLineParking", 3, 180, false, true);
    numberRange("illegalParking1", 3, 180, false, true);
    numberRange("illegalParking2", 3, 180, false, true);
    $("#saveDetect").off("click").on("click", function () {
        saveDetect()
    });
    $("#resetDetect").off("click").on("click", function () {
        showDetect()
    });
    layui.use(['element', 'layer', 'form', 'laydate', 'table'], function () {
        element = layui.element, layer = layui.layer, form = layui.form, laydate = layui.laydate, table = layui.table;
        let disableArray =
            [
                'detectNoLicense', 'detectPressingLine', 'strictDetect', 'illegalChange', 'overLineParking', 'illegalBorrowing',
                'illegalParking', 'blacklist', 'bigCarSmallCar', 'prohibitType', 'passengerCar', 'otherProvinces', 'frontFlash',
                'illegalChangeLight', 'illegalJump', 'nonMotoDetect', 'xianXing'
            ];
        laydate.render({
            elem: '#time_1'
            , type: 'time'
            , range: true
        });
        laydate.render({
            elem: '#time_2'
            , type: 'time'
            , range: true
        });
        laydate.render({
            elem: '#time_3'
            , type: 'time'
            , range: true
        });
        laydate.render({
            elem: '#xian_xing_time'
            , type: 'time'
            , range: true
        });
        laydate.render({
            elem: '#flashTime'
            , type: 'time'
            , range: true
        });
        disableFun(disableArray);
        loadLine("press");//车道线
        loadLine("illegalChange");//车道线
        loadLine("illegalLight");//车道线
        loadLine("illegalJump");//车道线
        loadLightORAttribute("listLight");//车道爆闪灯
        loadLightORAttribute("listAttribute");//车道属性（小车道）
        loadLightORAttribute("listAttributeTwo");//车道属性（客车道）
        loadTable("plateTable");//表格（车类型和牌子类型）
        loadWeek("weekDay", 'other-provinces');//加载周天
        loadWeekXianXing("xianxing_date_0", 'xianxing-date-0');//加载周天
        loadType("violationType");//插入违法类型
        loadProvince("wshcjl_province")//加载省份
        loadProvince("local_plate_province")//加载省份
        loadProvince("motuocheWpcjlProvince")//加载省份
        loadCity("wshcjl_city", 'wshcjl-city', 'disabled')//加载城市
        loadCity("local_plate_city", 'local-plate-city', '')//加载城市
        loadCity("moto_local_city", 'moto-local-city', 'disabled')//加载城市
        loadNumberXianXing('xianxing_date_1', 'xianxing-date-1')
        loadNumberXianXing('local_plate_endnum', 'local-plate-endnum')
        //loadNumberXianXing('nonlocal_plate_endnum', 'nonlocal-plate-endnum')
        form.on('checkbox(prohibitCarType)', function (data) {
            if (data.elem.checked) {
                $(this).parent().parent().find('input:checkbox').removeAttr('disabled');
            } else {
                $(this).parent().parent().find('input:checkbox.prohibit-plate-blue')[0].checked = false;
                $(this).parent().parent().find('input:checkbox.prohibit-plate-yellow')[0].checked = false;
                $(this).parent().parent().find('input:checkbox.prohibit-plate-blue').attr('disabled', 'disabled');
                $(this).parent().parent().find('input:checkbox.prohibit-plate-yellow').attr('disabled', 'disabled');
            }
            form.render()
        });
        form.on('select(local_plate_province)', function (data) {
            if (data.value === "无") {
                $('.local-plate-city').attr('disabled', 'disabled');
                let valueArr = langMessage.settingDetect.city;
                $('input[class="local-plate-city"][value="all"]').prop('checked', false)
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="local-plate-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            } else {
                $('.local-plate-city').removeAttr('disabled');
            }
            form.render()
        });
        form.on('select(xianxingDateType)', function (data) {
            if (data.value === "0") {
                $('#xianxing_date_0').css({display: 'inline-block'})
                $('#xianxing_date_1').css({display: 'none'})
            } else if (data.value === "1") {
                $('#xianxing_date_0').css({display: 'none'})
                $('#xianxing_date_1').css({display: 'inline-block'})
            } else {
                $('#xianxing_date_0').css({display: 'none'})
                $('#xianxing_date_1').css({display: 'none'})
            }
        });
        form.on('checkbox(xianxingDate0All)', function (data) {
            let valueArr = langMessage.settingDetect.week_xianXing
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="xianxing-date-0"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="xianxing-date-0"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        form.on('checkbox(xianxingDate1All)', function (data) {
            let valueArr = langMessage.settingDetect.number_xianXing
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="xianxing-date-1"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="xianxing-date-1"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        form.on('checkbox(localPlateEndnumAll)', function (data) {
            let valueArr = langMessage.settingDetect.number_xianXing
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="local-plate-endnum"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="local-plate-endnum"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        // form.on('checkbox(nonlocalPlateEndnumAll)', function (data) {
        //     let valueArr = langMessage.settingDetect.number_xianXing
        //     if (data.elem.checked) {
        //         for (let i = 0; i < valueArr.length; i++) {
        //             $('input[class="nonlocal-plate-endnum"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
        //         }
        //     } else {
        //         for (let i = 0; i < valueArr.length; i++) {
        //             $('input[class="nonlocal-plate-endnum"][value="' + valueArr[i].value + '"]').prop('checked', false)
        //         }
        //     }
        //     form.render()
        // });
        form.on('checkbox(localPlateCityAll)', function (data) {
            let valueArr = langMessage.settingDetect.city;
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="local-plate-city"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="local-plate-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        form.on('checkbox(motoLocalCityAll)', function (data) {
            let valueArr = langMessage.settingDetect.city;
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="moto-local-city"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="moto-local-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        form.on('checkbox(wshcjlCityAll)', function (data) {
            let valueArr = langMessage.settingDetect.city;
            if (data.elem.checked) {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="wshcjl-city"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                }
            } else {
                for (let i = 0; i < valueArr.length; i++) {
                    $('input[class="wshcjl-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                }
            }
            form.render()
        });
        form.on('checkbox(detectKakou)', function (data) {
            if (data.elem.checked) {
                $("#kakou-img-count").removeAttr("disabled");
                if (!$('#kakou-img-count').val()) {
                    $('#kakou-img-count').val(1)
                }
            } else {
                $("#kakou-img-count").attr("disabled", 'disabled');
            }
            form.render()
        });
        form.on('checkbox(motoDetect)', function (data) {
            if (data.elem.checked) {
                $('.moto-detect input').removeAttr("disabled")
                if ($("#motoJd")[0].checked) {
                    $('.moto-jd input').removeAttr("disabled")
                } else {
                    $('.moto-jd input').attr("disabled", 'disabled')
                }
                if ($("#detectMotoPressingLine")[0].checked) {
                    $('.detect-moto-pressing-line input').removeAttr("disabled")
                } else {
                    $('.detect-moto-pressing-line input').attr("disabled", 'disabled')
                }
            } else {
                $('.moto-detect input').attr("disabled", 'disabled')
                // $(".moto-detect input:checkbox").each(function () {
                //     this.checked = false
                // })
                $('.moto-jd input').attr("disabled", 'disabled')
                $('.detect-moto-pressing-line input').attr("disabled", 'disabled')
                $('#motuocheCjlType').attr("disabled", 'disabled')
                // $('#motuocheCjlType').val('')
                $('.moto-local-city').attr("disabled", 'disabled')
                // let valueArr = langMessage.settingDetect.city;
                // for (let i = 0; i < valueArr.length; i++) {
                //     $('input[class="moto-local-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                // }
                $('#motuocheWpcjlProvince').attr("disabled", 'disabled')
                // $('#motuocheWpcjlProvince').val('')
            }
            form.render()
        });
        form.on('checkbox(motoJd)', function (data) {
            if (data.elem.checked) {
                $('.moto-jd input').removeAttr("disabled")
            } else {
                $('.moto-jd input').attr("disabled", 'disabled')
                // $(".moto-jd input:checkbox").each(function () {
                //     this.checked = false
                // })
            }
            form.render()
        });
        form.on('checkbox(detectMotoPressingLine)', function (data) {
            if (data.elem.checked) {
                $('.detect-moto-pressing-line input').removeAttr("disabled")
            } else {
                $('.detect-moto-pressing-line input').attr("disabled", 'disabled')
                // $(".moto-jd input:checkbox").each(function () {
                //     this.checked = false
                // })
            }
            form.render()
        });
        form.on('checkbox(motoCjl)', function (data) {
            if (data.elem.checked) {
                $('#motuocheCjlType').removeAttr("disabled")
                if ($('#motuocheCjlType').val() === '1') {
                    $('.moto-local-city').removeAttr("disabled")
                    $('#motuocheWpcjlProvince').removeAttr("disabled")
                } else {
                    $('.moto-local-city').attr("disabled", 'disabled')
                    $('#motuocheWpcjlProvince').attr("disabled", 'disabled')
                }
            } else {
                $('#motuocheCjlType').attr("disabled", 'disabled')
                // $('#motuocheCjlType').val('')
                $('.moto-local-city').attr("disabled", 'disabled')
                // let valueArr = langMessage.settingDetect.city;
                // for (let i = 0; i < valueArr.length; i++) {
                //     $('input[class="moto-local-city"][value="' + valueArr[i].value + '"]').prop('checked', false)
                // }
                $('#motuocheWpcjlProvince').attr("disabled", 'disabled')
                // $('#motuocheWpcjlProvince').val('')
            }
            form.render()
        });
        form.on('select(motuocheCjlType)', function (data) {
            if (data.value === '1') {
                $('.moto-local-city').removeAttr("disabled")
                $('#motuocheWpcjlProvince').removeAttr("disabled")
            } else {
                $('.moto-local-city').attr("disabled", 'disabled')
                $('#motuocheWpcjlProvince').attr("disabled", 'disabled')
            }
            form.render()
        });
        showDetect();
        $("#exportBlackBtn").off('click').on('click', function () {
            initWebService(webserviceCMD.CMD_EXPORT_BLACK_LIST, null, getBlack);
        });
        $('#importBlackBtn').off('click').on('click', function () {
            let importBtn = $("#importBlackBtn");
            importBtn.addClass("layui-btn-disabled");
            $("#importBlack").val("");
            $("#blackName").val("");
            $("#importBlack").click();
            $("#importBlack").off("change").on("change", function (e) {
                let files = this.files[0];
                $("#blackName").val(files.name);
                analysisBlack(files, function (info) {
                    if (info.md5 === info.now_md5) {
                        let param = {
                            blacklist: info.base_64
                        };
                        initWebService(webserviceCMD.CMD_IMPORT_BLACK_LIST, param, getBlackSuc, null);
                    } else {
                        layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});
                        importBtn.removeClass("layui-btn-disabled");
                    }
                }, function (e) {

                    layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});
                    console.log(e)
                });

            });
        });
        $("#addXianXing").off("click").on("click", function () {
            showWhxxConfig('add')
        });
    });
});
var getBlackSuc = function (data) {
    console.log(data);
    if (data.ret === 0) {
        $("#importBlackBtn").removeClass("layui-btn-disabled");

        layer.msg(langMessage.authorization.authMsg.UPLOAD_SUC, {icon: 1, time: 2000});

    }
};
var getBlack = function (data) {
    let exts = '.conf';
    let code = data.blacklist;
    let blob = dataURLtoBlob(code);
    analysisBlack(blob, function (info) {
        console.log(info);
        if (info.md5 === info.now_md5) {
            doSaveBlob('exportBlackBtn', location.hostname + '-blacklist' + exts, blob);
        } else {

            layer.msg(langMessage.authorization.authMsg.DAMAGED, {icon: 2, time: 5000});

        }
    }, function (e) {

        layer.msg(langMessage.authorization.authMsg.UNKNOWN, {icon: 2, time: 5000});

        console.log(e)
    });
};
var showDetect = function () {
    onloadConfig('../../config/event.json', 'eventConfig', function (status) {
        if (status === true) {
            let flashConfig = getSe("eventConfig").flashConfig;
            let flashAppend = "";
            Object.keys(flashConfig).forEach(function (key) {
                flashAppend += '<option value="' + key + '">' + flashConfig[key] + '</option>';
            });
            let flash = $(".front-flash-select");
            for (let i = 0; i < flash.length; i++) {
                $(flash[i]).html(flashAppend)
            }
            showConfig('detect');
        } else {

            layer.msg(langMessage.setting.errorMsg.FLASH_ERROR, {icon: 2});

            $(".front-flash-select").attr("disabled", "disabled")
        }
    });
};

let error_type = langMessage.setting.error_type;
var saveDetect = function () {
    // let oldDetects = getSe('detects');
    // let odV, ndV = getCheckboxValue('detect-signal');
    // if (oldDetects) {
    //     odV = oldDetects['detect-signal'];
    //     if (odV & 0x8000) {
    //         ndV += parseInt('0x8000')
    //     }
    //     if (odV & 0x40000) {
    //         ndV += parseInt('0x40000')
    //     }
    // }
    let ndV = getCheckboxValue('detect-signal');
    let detectArray = [
        'extend-signal', 'pressing-line', 'kakou-img-num', 'return-enable', 'detect-big-car', 'big-car-type',
        'dui-xiang-enable', 'illegal-change', 'direction-mask', 'detect-front-flash', 'detect-truck', 'truck-type', 'detect-prohibit',
        'prohibit-car-type', 'prohibit-plate-blue', 'prohibit-plate-yellow', 'detect-jam', 'black-list', 'other-provinces', 'black-enable',
        'kakou-feature', 'weizhang-feature', 'weizhang-video', 'illegal-change-light', 'illegal-jump', 'fjdc-type', 'jdc-feature', 'fjdc-feature', 'wshcjl-province', 'wshcjl-city',
        'whxx', 'kakou-img-count', 'moto-local-city'
    ];
    let detects = {};
    detects['detect-signal'] = ndV;
    for (let i = 0; i < detectArray.length; i++) {
        if (detectArray[i] === 'jdc-feature' || detectArray[i] === 'fjdc-feature') {//radio组件
            detects[detectArray[i]] = getRdioValue(detectArray[i]);
        } else if (detectArray[i] === 'wshcjl-province') {
            detects[detectArray[i]] = $('#wshcjl_province').val()
        } else if (detectArray[i] === 'wshcjl-city') {
            let v = 0
            let checkedBoxes = $('input[class="wshcjl-city"]:checked')
            checkedBoxes.each(function () {
                if ($(this).val() !== 'all') {
                    v = v + Number(($(this).val()))
                }
            });
            detects['wshcjl-city'] = v
        } else if (detectArray[i] === 'moto-local-city') {
            let v = 0
            let checkedBoxes = $('input[class="moto-local-city"]:checked')
            checkedBoxes.each(function () {
                if ($(this).val() !== 'all') {
                    v = v + Number(($(this).val()))
                }
            });
            detects['moto-local-city'] = v
        } else if (detectArray[i] === 'whxx') {
            let whxx = JSON.parse(JSON.stringify(myWhxx))
            whxx.map(function (item, index) {
                delete item.id;
                delete item.LAY_TABLE_INDEX;
                return item;
            });
            detects[detectArray[i]] = {
                num: whxx.length,
                data: whxx
            }
        } else if (detectArray[i] === 'kakou-img-count') {
            detects['kakou-img-count'] = Number($('#kakou-img-count').val())
        } else {//checkbox组件
            detects[detectArray[i]] = getCheckboxValue(detectArray[i]);
        }
    }
    detects.detectNoLicense = parseInt($("#detectNoLicense").val());
    detects.overLineParking = parseInt($("#overLineParking").val());
    detects.illegalParking1 = parseInt($("#illegalParking1").val());
    detects.illegalParking2 = parseInt($("#illegalParking2").val());
    let time1 = $("#time_1").val().split(' - ');
    let time2 = $("#time_2").val().split(' - ');
    let time3 = $("#time_3").val().split(' - ');
    if (time1.length >= 2) {
        detects.time1start = date2Value(time1[0], 1);
        detects.time1end = date2Value(time1[1], 1);
    }
    if (time2.length >= 2) {
        detects.time2start = date2Value(time2[0], 1);
        detects.time2end = date2Value(time2[1], 1);
    }
    if (time3.length >= 2) {
        detects.time3start = date2Value(time3[0], 1);
        detects.time3end = date2Value(time3[1], 1);
    }
    let flashTime = $("#flashTime").val().split(' - ');
    if (flashTime.length >= 2) {
        detects.startFlash = date2Value(flashTime[0], 1);
        detects.endFlash = date2Value(flashTime[1], 1);
    } else {
        detects.startFlash = 0;
        detects.endFlash = 0;
    }
    if ($('.detect-big-car')[0].checked) {
        let arr = [];
        $(".big-car-select").each(function (i) {
            arr[i] = parseInt($(this).find('option:selected').val())
        });
        detects['big-car-select'] = arr;
    }
    if ($('.detect-truck')[0].checked) {
        let arr = [];
        $(".truck-car-select").each(function (i) {
            arr[i] = parseInt($(this).find('option:selected').val())
        });
        detects['truck-car-select'] = arr;
    }
    if ($('.detect-front-flash')[0].checked) {
        let arr = [];
        $(".front-flash-select").each(function (i) {
            arr[i] = parseInt($(this).find('option:selected').val())
        });
        detects['front-flash-select'] = arr;
    }

    detects.motuocheCjlType = parseInt($('#motuocheCjlType').val());
    detects.motuocheWpcjlProvince = $('#motuocheWpcjlProvince').val();
    console.log(detects)
    setSe('detects', detects);

    // 安全带、打手机的置信度
    setSe('SafeBelt_score', $('#SafeBeltScore').val())
    setSe('CallPhone_score', $('#CallPhoneScore').val())
    if (detects['extend-signal'] & 0x0004) {
        if (!/^(\+)?\d+(\d+)?$/.test($('#SafeBeltScore').val()) || Number($('#SafeBeltScore').val()) < 1 || Number($('#SafeBeltScore').val()) > 100) {
            layer.msg(error_type[27], {icon: 2});
            return
        }
    }
    if (detects['extend-signal'] & 0x0008) {
        if (!/^(\+)?\d+(\d+)?$/.test($('#CallPhoneScore').val()) || Number($('#CallPhoneScore').val()) < 1 || Number($('#CallPhoneScore').val()) > 100) {
            layer.msg(error_type[28], {icon: 2});
            return
        }
    }
    //检测闯禁令(外省市小型车)：本地车牌必配
    if (detects['detect-signal'] & 0x10000000) {

    }
    //尾号限行：日期、起效时间段、不受限行车牌、限行车牌尾号必配
    if (detects['extend-signal'] & 0x00000800) {
        if (detects.whxx.length === 0) {
            layer.msg(error_type[29], {icon: 2});
            return
        }
    }
    if (detects['detect-signal'] & 0x0200) {
        let count = detects['kakou-img-count']
        if (!count) {
            layer.msg(error_type[33], {icon: 2});
            return
        }
    }
    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
};
var reloadTime = 0, configInterval;
var show = function () {
    let detects = getSe('detects');
    if (detects) {
        for (let d in detects) {
            let reg = /^\w+-\w+/;
            if (reg.test(d)) {
                if (d === 'jdc-feature' || d === 'fjdc-feature') {//radio组件
                    showRadioValue(d, detects[d]);
                } else if (d === 'wshcjl-province') {
                    $('#wshcjl_province').val(detects[d])
                } else if (d === 'wshcjl-city') {
                    let city = langMessage.settingDetect.city
                    let all = 0
                    for (let j = 0; j < city.length; j++) {
                        all = all + city[j].value
                        if (city[j].value & detects[d]) {
                            $('input[class="wshcjl-city"][value="' + city[j].value + '"]').prop('checked', 'checked')
                        }
                    }
                    if (detects[d] === all) {
                        $('input[class="wshcjl-city"][value="all"]').prop('checked', 'checked')
                    }
                } else if (d === 'moto-local-city') {
                    let city = langMessage.settingDetect.city
                    let all = 0
                    for (let j = 0; j < city.length; j++) {
                        all = all + city[j].value
                        if (city[j].value & detects[d]) {
                            $('input[class="moto-local-city"][value="' + city[j].value + '"]').prop('checked', 'checked')
                        }
                    }
                    if (detects[d] === all) {
                        $('input[class="moto-local-city"][value="all"]').prop('checked', 'checked')
                    }
                } else if (d === 'whxx') {
                    console.log('whxx')
                } else if (d === 'kakou-img-count') {
                    $('#kakou-img-count').val(detects[d])
                } else {//checkbox组件
                    showCheckboxValue(d, detects[d]);
                }
            }
        }
        for (let t in detects['truck-car-select']) {
            $($(".truck-car-select")[t]).val(detects['truck-car-select'][t])
        }
        for (let t in detects['big-car-select']) {
            $($(".big-car-select")[t]).val(detects['big-car-select'][t])
        }
        for (let t in detects['front-flash-select']) {
            $($(".front-flash-select")[t]).val(detects['front-flash-select'][t])
        }
        $("#detectNoLicense").val(detects.detectNoLicense);
        $("#overLineParking").val(parseInt(detects.overLineParking));
        $("#illegalParking1").val(parseInt(detects.illegalParking1));
        $("#illegalParking2").val(parseInt(detects.illegalParking2));
        $("#time_1").val(value2Date(detects['time1start'], 1) + ' - ' + value2Date(detects['time1end'], 1));
        $("#time_2").val(value2Date(detects['time2start'], 1) + ' - ' + value2Date(detects['time2end'], 1));
        $("#time_3").val(value2Date(detects['time3start'], 1) + ' - ' + value2Date(detects['time3end'], 1));
        $("#flashTime").val(value2Date(detects['startFlash'], 1) + ' - ' + value2Date(detects['endFlash'], 1));
        $('#SafeBeltScore').val(getSe('SafeBelt_score'))
        $('#CallPhoneScore').val(getSe('CallPhone_score'))
        $('#motuocheCjlType').val(detects.motuocheCjlType)
        $('#motuocheWpcjlProvince').val(detects.motuocheWpcjlProvince)
        if (detects['extend-signal'] & 0x00004000) {
            $('#motuocheCjlType').removeAttr("disabled")
            if (detects.motuocheCjlType === 1) {
                $('.moto-local-city').removeAttr("disabled")
                $('#motuocheWpcjlProvince').removeAttr("disabled")
            }
        }
        //尾号限行
        /**
         * whxx:{
            num:2,
            data:[
                {
                    "id":1, //自己添加的
                    "whxx_date_type": 0,
                    "whxx_date": 3,     //0：周日，1：周一，2：周二......
                    "whxx_start_time": 0,    //分钟值
                    "whxx_end_time": 4,
                    "local_province": "沪",
                    "local_city": 7,
                    "local_plate_endnum": 7,
                    "nonlocal_plate_endnum": 7
                },
                {
                    "id":2, //自己添加的
                    "whxx_date_type": 1,
                    "whxx_date": 7,
                    "whxx_start_time": 4,
                    "whxx_end_time": 180,
                    "local_province": "沪",
                    "local_city": 3,
                    "local_plate_endnum": 7,
                    "nonlocal_plate_endnum": 7
                }
            ]
        }
         * */
        if (detects['whxx']) {
            myWhxx = JSON.parse(JSON.stringify(detects['whxx'].data))
            myWhxx = myWhxx.map(function (item, index) {
                item.id = index + 1;
                return item;
            });
            renderTable(myWhxx)
        }
        clearTimeout('configInterval');
    } else {
        reloadConfig();
        reloadTime += 1;
        if (reloadTime <= 5) {
            configInterval = setTimeout(function () {
                showConfig('detects')
            }, 500)
        } else {
            reloadTime = 0;
            clearTimeout(configInterval);

            layer.msg(langMessage.common.netError, {icon: 2});


        }
    }
    form.render();
};

var renderTable = function (data) {
    table.render({
        elem: '#xianXingTable',
        cols: [[ //表头
            {type: 'numbers', title: '编号', width: 100},
            {
                field: 'whxx_date', title: '日期'
                , templet: function (d1) {
                    let pre = ""
                    let v
                    if (d1.whxx_date_type === 0) {
                        pre = "星期:"
                        v = langMessage.settingDetect.week_xianXing
                    } else {
                        pre = "日期尾号:"
                        v = langMessage.settingDetect.number_xianXing
                    }
                    let arr = []
                    for (let j = 0; j < d1.whxx_date.length; j++) {
                        for (let i = 0; i < v.length; i++) {
                            if (d1.whxx_date[j] === v[i].value) {
                                arr.push(v[i].name)
                            }
                        }
                    }
                    return pre + arr.join(',')
                }
            },
            {
                field: 'whxx_start_time', title: '开始时间', width: 100
                , templet: function (d1) {
                    return value2Date(d1.whxx_start_time, 1)
                }
            },
            {
                field: 'whxx_end_time', title: '结束时间', width: 100
                , templet: function (d1) {
                    return value2Date(d1.whxx_end_time, 1)
                }
            },
            {
                field: 'local_province', title: '不受限行车牌'
                , templet: function (d1) {
                    if (d1.local_province === '无') {
                        return '无'
                    } else {
                        let v = langMessage.settingDetect.city
                        let arr = []
                        for (let j = 0; j < d1.local_city.length; j++) {
                            for (let i = 0; i < v.length; i++) {
                                if (d1.local_city[j] === v[i].value) {
                                    arr.push(d1.local_province + v[i].name)
                                }
                            }
                        }
                        return arr.join(',')
                    }
                }
            },
            {
                field: 'local_plate_endnum', title: '限行车牌尾号', width: 150
                , templet: function (d1) {
                    let v = langMessage.settingDetect.number_xianXing
                    let arr = []
                    for (let j = 0; j < d1.local_plate_endnum.length; j++) {
                        for (let i = 0; i < v.length; i++) {
                            if (d1.local_plate_endnum[j] === v[i].value) {
                                arr.push(v[i].name)
                            }
                        }
                    }
                    return arr.join(',')
                }
            },
            // {
            //     field: 'nonlocal_plate_endnum', title: '外地车牌尾号', width: 150
            //     , templet: function (d1) {
            //         let v = langMessage.settingDetect.number_xianXing
            //         let arr = []
            //         for (let j = 0; j < d1.nonlocal_plate_endnum.length; j++) {
            //             for (let i = 0; i < v.length; i++) {
            //                 if (d1.nonlocal_plate_endnum[j] === v[i].value) {
            //                     arr.push(v[i].name)
            //                 }
            //             }
            //         }
            //         return arr.join(',')
            //     }
            // },
            {fixed: 'right', title: '操作', toolbar: '#xianXingTableOp', width: 150}
        ]],
        data: data
    });
    //监听事件
    table.on('tool(xianXing_Table)', function (obj) {
        switch (obj.event) {
            case "edit":
                showWhxxConfig('edit', obj.data)
                break;
            case "del":
                obj.del()
                myWhxx = myWhxx.filter(function (item) {
                    return item.id !== obj.data.id;
                });
                break;
        }
    });
}
var showWhxxConfig = function (type, data) {
    if (type === 'add') {
        if (myWhxx.length === 10) {
            layer.msg(error_type[32], {icon: 7});
            return
        }
        if (myWhxx.length > 0) {
            $('#xianxing_date_type').attr("disabled", true);
            $('#xianxing_date_type').val(myWhxx[0].whxx_date_type)
            if (myWhxx[0].whxx_date_type === 0) {
                $('#xianxing_date_0').css({display: 'inline-block'})
                $('#xianxing_date_1').css({display: 'none'})
            } else {
                $('#xianxing_date_0').css({display: 'none'})
                $('#xianxing_date_1').css({display: 'inline-block'})
            }
        } else {
            $('#xianxing_date_type').attr("disabled", false);
        }
    }
    layer.open({
        type: 1,//不能为0，为0时关闭弹框再次打开只出现遮罩，不出现弹框    0（信息框，默认）1（页面层）2（iframe层）3（加载层）4（tips层）
        shadeClose: true,
        area: '960px',//此处不能设为auto，不然显示不出来
        title: "配置",//题目
        content: $('#whxxConfig'),
        success: function (layero, index) {
            $('input[class="xianxing-date-0"]').prop('checked', false)
            $('input[class="xianxing-date-1"]').prop('checked', false)
            $('input[class="local-plate-city"]').prop('checked', false)
            $('input[class="local-plate-endnum"]').prop('checked', false)
            // $('input[class="nonlocal-plate-endnum"]').prop('checked', false)
            if (type === 'edit') {
                if (myWhxx.length > 1) {
                    $('#xianxing_date_type').attr("disabled", true);
                } else {
                    $('#xianxing_date_type').attr("disabled", false);
                }
                $('#xianxing_date_type').val(data.whxx_date_type)
                if (data.whxx_date_type === 0) {
                    $('#xianxing_date_0').css({display: 'inline-block'})
                    $('#xianxing_date_1').css({display: 'none'})
                    let valueArr = langMessage.settingDetect.week_xianXing
                    for (let j = 0; j < data.whxx_date.length; j++) {
                        for (let i = 0; i < valueArr.length; i++) {
                            if (valueArr[i].value === data.whxx_date[j]) {
                                $('input[class="xianxing-date-0"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                            }
                        }
                    }
                } else {
                    $('#xianxing_date_0').css({display: 'none'})
                    $('#xianxing_date_1').css({display: 'inline-block'})
                    let valueArr = langMessage.settingDetect.number_xianXing
                    for (let j = 0; j < data.whxx_date.length; j++) {
                        for (let i = 0; i < valueArr.length; i++) {
                            if (valueArr[i].value === data.whxx_date[j]) {
                                $('input[class="xianxing-date-1"][value="' + valueArr[i].value + '"]').prop('checked', 'checked')
                            }
                        }
                    }
                }
                $("#xian_xing_time").val(value2Date(data.whxx_start_time, 1) + ' - ' + value2Date(data.whxx_end_time, 1));
                $('#local_plate_province').val(data.local_province)
                if (data.local_province === "无") {
                    $('.local-plate-city').attr('disabled', 'disabled');
                } else {
                    $('.local-plate-city').removeAttr('disabled');
                }
                for (let i = 0; i < data.local_city.length; i++) {
                    $('input[class="local-plate-city"][value="' + data.local_city[i] + '"]').prop('checked', 'checked')
                }
                for (let i = 0; i < data.local_plate_endnum.length; i++) {
                    $('input[class="local-plate-endnum"][value="' + data.local_plate_endnum[i] + '"]').prop('checked', 'checked')
                }
                //for (let i = 0; i < data.nonlocal_plate_endnum.length; i++) {
                //    $('input[class="nonlocal-plate-endnum"][value="' + data.nonlocal_plate_endnum[i] + '"]').prop('checked', 'checked')
                //}
            }
            form.render()
        },
        btn: ['确定', '取消'],
        yes: function (index, layero) {
            whxxConfirm(index, type, data ? data.id : null)
        },
        btn2: function (index, layero) {
            layer.close(index);
        },
    });
}
var whxxConfirm = function (index, type, id) {
    let whxx_date_type = Number($('#xianxing_date_type').val())
    let checkedBoxes
    if (whxx_date_type === 0) {
        checkedBoxes = $('input[class="xianxing-date-0"]:checked')
    } else {
        checkedBoxes = $('input[class="xianxing-date-1"]:checked')
    }
    let whxx_date = []
    checkedBoxes.each(function () {
        if ($(this).val() !== 'all') {
            whxx_date.push(Number($(this).val()))
        }
    });
    let whxx_start_time = 0, whxx_end_time = 0
    let xian_xing_time = $("#xian_xing_time").val().split(' - ');
    if (xian_xing_time.length >= 2) {
        whxx_start_time = date2Value(xian_xing_time[0], 1);
        whxx_end_time = date2Value(xian_xing_time[1], 1);
    }
    let local_province = $('#local_plate_province').val()
    let local_city = []
    let checkedBoxes1 = $('input[class="local-plate-city"]:checked')
    checkedBoxes1.each(function () {
        if ($(this).val() !== 'all') {
            local_city.push(Number($(this).val()));
        }
    });
    let local_plate_endnum = []
    let checkedBoxes2 = $('input[class="local-plate-endnum"]:checked')
    checkedBoxes2.each(function () {
        if ($(this).val() !== 'all') {
            local_plate_endnum.push(Number($(this).val()))
        }
    });
    //let nonlocal_plate_endnum = []
    // let checkedBoxes3 = $('input[class="nonlocal-plate-endnum"]:checked')
    // checkedBoxes3.each(function () {
    //     if ($(this).val() !== 'all') {
    //         nonlocal_plate_endnum.push(Number($(this).val()))
    //     }
    // });
    if ((whxx_date_type !== 0 && whxx_date_type !== 1) ||
        whxx_date.length === 0 ||
        local_province === '' ||
        (local_province !== '' && local_province !== '无' && local_city.length === 0) ||
        local_plate_endnum.length === 0) {
        layer.msg(error_type[30], {icon: 2});
        return
    }
    if (whxx_start_time >= whxx_end_time) {
        layer.msg(error_type[31], {icon: 2});
        return
    }
    let obj = {
        "whxx_date_type": whxx_date_type,
        "whxx_date": whxx_date,
        "whxx_start_time": whxx_start_time,
        "whxx_end_time": whxx_end_time,
        "local_province": local_province,
        "local_city": local_city,
        "local_plate_endnum": local_plate_endnum,
        //"nonlocal_plate_endnum": nonlocal_plate_endnum
    }
    if (type === 'add') {
        obj.id = myWhxx.length + 1
        myWhxx.push(obj)
    } else if (type === 'edit') {
        myWhxx = myWhxx.map(function (item, index) {
            if (item.id === id) {
                item.whxx_date_type = obj.whxx_date_type
                item.whxx_date = obj.whxx_date
                item.whxx_start_time = obj.whxx_start_time
                item.whxx_end_time = obj.whxx_end_time
                item.local_province = obj.local_province
                item.local_city = obj.local_city
                item.local_plate_endnum = obj.local_plate_endnum
                //item.nonlocal_plate_endnum = obj.nonlocal_plate_endnum
            }
            return item
        });
    }
    layer.close(index)
    table.reload('xianXingTable', {data: myWhxx})
}
var analysisBlack = function (ofile, callback, error) {
    let file = ofile;
    let filename = file.name;
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice,
        fileInfo = {},
        fileReader = new FileReader();
    let start = 0;
    let end = file.size;
    let versionLength = 8,
        md5Length = 16,
        md5Start = 48;
    let baseLength = 0, lE = true;
    if (file.type === 'IE') {
        file = file.file;
        end = file.size
    }
    let spark = new SparkMD5.ArrayBuffer();
    fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));

    fileReader.onload = function (e) {
        let bf = e.target.result;

        let bfLength = bf.byteLength;

        let md5_b = bf.slice(versionLength, versionLength + md5Length);
        let md5 = buf2hex(md5_b).toUpperCase();


        let md5_bf = bf.slice(md5Start);
        spark.append(md5_bf);
        let now_md5 = spark.end().toUpperCase();
        let base = ab2str(bf);
        let base_64 = btoa(base);

        fileInfo = {
            filename,
            md5,
            now_md5,
            base_64,
        };

        if (callback) {
            callback(fileInfo);
        }
    };

    fileReader.onerror = function (e) {
        if (error) {
            error(e);
        }
    };
};

/**
 * 车道线
 * @param id
 */
var loadLine = function (id) {
    let list = langMessage.settingDetect.line.list;
    let length = list.length;
    let clazz = "";
    switch (id) {
        case "press":
            clazz = "pressing-line detect-pressing-line-line";
            break;
        case "illegalChange":
            clazz = "illegal-change illegal-change-line";
            break;
        case "illegalLight":
            clazz = "illegal-change-light";
            break;
        case "illegalJump":
            clazz = "illegal-jump";
            break;
        default:
            clazz = "";
            break;
    }
    let string1 = "";
    let string2 = "";
    for (let i = 0; i < length; i++) {
        string1 = "<input type='checkbox' class='" + clazz + "' title='" + list[i].name + "' disabled lay-skin='primary' value='" + list[i].value + "'>";
        string2 = string2 + string1;
    }
    let divA = $("#" + id);
    divA.append(string2);

};

/**
 * 加载车道的属性或者曝闪灯
 * @param strs 属性还是曝闪灯
 */
var loadLightORAttribute = function (strs) {
    let list1 = langMessage.settingDetect.flashingLight.list[0];
    let list2 = "";
    let ddl = "";
    let divClass = "";
    let selectClass = "";
    let divID = "";
    let std = "";
    let list;
    if (strs === "listLight") {
        list2 = langMessage.settingDetect.flashingLight.list[1];
        divID = "light";
        divClass = "layui-form-item front-flash-line";
        selectClass = "front-flash-select";
        ddl = "</select>";
    } else if (strs === "listAttribute") {
        list2 = langMessage.settingDetect.flashingLight.list[2];
        divID = "attributes";
        divClass = "layui-form-item big-car-small-car-line";
        selectClass = "big-car-select";
        list = langMessage.settingDetect.flashingLight.list[2].selects;
        for (let i = 0; i < list.length; i++) {
            std = "<option value='" + list[i].value + "'>" + list[i].name + "</option>";
            ddl = ddl + std;
        }
        ddl = ddl + "</select>";

    } else if (strs === "listAttributeTwo") {
        list2 = langMessage.settingDetect.flashingLight.list[3];
        divID = "attributesTwo";
        divClass = "layui-form-item passenger-car-line";
        selectClass = "truck-car-select";
        list = langMessage.settingDetect.flashingLight.list[3].selects;
        for (let i = 0; i < list.length; i++) {
            std = "<option value='" + list[i].value + "'>" + list[i].name + "</option>";
            ddl = ddl + std;
        }
        ddl = ddl + "</select>";
    }

    let length = list1.length;
    let string1, str, divA;
    str = "";
    for (let i = 0; i < length; i++) {
        string1 = "<div class='" + divClass + "'> <label>" + list1[i].name + list2.name + "</label> <div class='layui-input-inline'><select class='" + selectClass + "' disabled>" + ddl + "</div></div>";
        str = str + string1;
    }
    ;
    divA = $("#" + divID);
    divA.append(str);

};

/**
 * 加载表格（车类型和牌子类型）
 * @param id 需要插入进的table id
 */
var loadTable = function (id) {
    var list = langMessage.settingDetect.table;
    var length = list.length;
    var string1, str, divA, divID, type, value;
    str = "";
    for (var i = 0; i < length; i++) {
        type = list[i].type;
        value = list[i].value;
        string1 = "<tr> " +
            "<td class='prohibit-type'> " +
            "<input type='checkbox' class='prohibit-car-type' title='" + type + "' lay-filter='prohibitCarType' disabled lay-skin='primary' value='" + value + "'> " +
            "</td> " +
            "<td> " +
            "<input type='checkbox' class='prohibit-plate-blue' title='" + list[i].plate[0] + "' disabled lay-skin='primary' value='" + value + "'>" +
            "</td>" +
            "<td> " +
            "<input type='checkbox' class='prohibit-plate-yellow' title='" + list[i].plate[1] + "' disabled lay-skin='primary' value='" + value + "'>" +
            "</td> " +
            "</tr>";
        str = str + string1;
    }
    ;
    divID = id;
    divA = $("#" + divID);
    divA.append(str);

};

/**
 * 加载周天
 * @param id 需要插入进的div id
 */
var loadWeek = function (id, className) {
    var list = langMessage.settingDetect.week;
    var length = list.length;
    var string1, str, divA, divID, day, value;
    str = "";
    for (var i = 0; i < length; i++) {
        day = list[i].day;
        value = list[i].value;
        string1 = "<input type='checkbox' class=" + className + " title='" + day + "' disabled lay-skin='primary' value='" + value + "'>";
        str = str + string1;
    }
    divID = id;
    divA = $("#" + divID);
    divA.prepend(str);

};
var loadWeekXianXing = function (id, className) {
    var list = langMessage.settingDetect.week_xianXing;
    var length = list.length;
    var string1, str, divA, divID, name, value;
    str = "";
    for (var i = 0; i < length; i++) {
        name = list[i].name;
        value = list[i].value;
        string1 = "<input type='checkbox' class=" + className + " title='" + name + "' lay-skin='primary' value='" + value + "'>";
        str = str + string1;
    }
    divID = id;
    divA = $("#" + divID);
    divA.append(str);

};

/**
 * 插入违法类型
 * @param id 需要插入进的div id
 */
var loadType = function (id) {
    var list = langMessage.settingDetect.typeOfViolation;
    var length = list.length;
    var string1, str, divA, divID, type, value;
    str = "";
    for (var i = 0; i < length; i++) {
        type = list[i].type;
        value = list[i].value;
        string1 = "<input type='checkbox' class='black-list' title='" + type + "' disabled lay-skin='primary' value='" + value + "'>";
        str = str + string1;
    }
    ;
    divID = id;
    divA = $("#" + divID);
    divA.prepend(str);

};

/**
 * 加载省份
 * @param id 需要插入进的select id
 */
var loadProvince = function (id) {
    var list = langMessage.settingDetect.province;
    var length = list.length;
    var string1, str, divA, divID, name, value;
    str = "";
    for (var i = 0; i < length; i++) {
        name = list[i].name;
        value = list[i].value;
        string1 = "<option value='" + value + "'>" + name + "</option>";
        str = str + string1;
    }
    divID = id;
    divA = $("#" + divID);
    divA.append(str);
};

/**
 * 加载城市
 * @param id 需要插入进的div id
 */
var loadCity = function (id, className, disabled) {
    var list = langMessage.settingDetect.city;
    var length = list.length;
    var string1, str, divA, divID, name, value;
    str = "";
    for (var i = 0; i < length; i++) {
        name = list[i].name;
        value = list[i].value;
        string1 = "<input type='checkbox' class=" + className + " title='" + name + "' " + disabled + " lay-skin='primary' value='" + value + "'/>";
        str = str + string1;
    }
    divID = id;
    divA = $("#" + divID);
    divA.append(str);
};

/**
 * 加载编号
 * @param id 需要插入进的div id
 */
var loadNumberXianXing = function (id, className) {
    var list = langMessage.settingDetect.number_xianXing;
    var length = list.length;
    var string1, str, divA, divID, name, value;
    str = "";
    for (var i = 0; i < length; i++) {
        name = list[i].name;
        value = list[i].value;
        string1 = "<input type='checkbox' class=" + className + " title='" + name + "' lay-skin='primary' value='" + value + "'/>";
        str = str + string1;
    }
    divID = id;
    divA = $("#" + divID);
    divA.append(str);
};

