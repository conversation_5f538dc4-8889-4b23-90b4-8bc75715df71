var form, element, layer;
//JavaScript代码区域
$(document).ready(function () {
    window.parent.setNavValue(2);
    layui.use(['element', 'layer', 'form', 'colorpicker', 'laydate'], function () {
        element = layui.element, layer = layui.layer, form = layui.form;
        // reloadConfig(getDevice);
        element.render();
    });
    let globalInfo = checkGlobal();
    if (globalInfo.w > 600) {
        $("#settingFrame").css({
            minWidth: '1650px'
        });
    }
    $(".layui-nav-child a").click(function (e) {
        // setTimeout(setIframeHeight, 200);
        let node = getDataset(e.currentTarget).href;
        $("#frame_setting").attr("src", node);
        let childWindow = $("#frame_setting")[0].contentWindow;
        let ocxExist = childWindow.$("#ocx");
        if (ocxExist.length) {
            console.log("ocxLength:" + ocxExist.length);
            ocxExist.addClass("common-none");
            ocxExist.remove();
            console.log("after remove ocxLength:" + childWindow.$("#ocx").length)
        }
    });
});
// var computeCoordinate = function (point, pictureW, pictureH) {
//     let t1 = typeof point && !isNaN(point.length);
//     if (t1) {
//         let po = []
//         for (let i = 0; i < point.length; i++) {
//             // po = [];
//             let t = computeCoordinate(point[i], pictureW, pictureH)
//             po.push(t)
//         }
//         return po
//     } else {
//         let o = {}
//         let imgInfo = JSON.parse(sessionStorage.getItem('imgInfo'));
//         let containerW, containerH;
//         if (imgInfo) {
//             containerW = imgInfo.containerWidth;
//             containerH = imgInfo.containerHeight;
//         } else {
//             containerW = 800;
//             containerH = 600;
//         }
//         o.x = parseInt((point.x / pictureW) * containerW);
//         o.y = parseInt((point.y / pictureH) * containerH);
//         return o
//     }
// }
// var commonCoordinate = function (point) {
//     let imgInfo = JSON.parse(sessionStorage.getItem('imgInfo'));
//     let containerHeight, containerWidth;
//     if (imgInfo) {
//         containerHeight = imgInfo.containerHeight
//         containerWidth = imgInfo.containerWidth
//     } else {
//         containerHeight = 600;
//         containerWidth = 800
//     }
//
//     let t1 = typeof point && !isNaN(point.length);
//     if (t1) {
//         let po = []
//         for (let i = 0; i < point.length; i++) {
//             let t = commonCoordinate(point[i])
//             po.push(t)
//         }
//         return po
//     } else {
//         let o = {}
//         o.x = parseInt((point.x / 100) * containerWidth);
//         o.y = parseInt((point.y / 100) * containerHeight);
//         return o
//     }
// }


// var getSe = function (type) {
//     return JSON.parse(sessionStorage.getItem(type));
// }


// var sendWebService = function (cmd, param) {
//     let o = {};
//     for (let i = 0; i < 6; i++) {
//         switch (i) {
//             case 0:
//                 if (cmd === 0) {
//                     o.msg = 'get-config'
//                 }
//                 break;
//             case 1:
//                 if (cmd === 1) {
//                     o.msg = 'get-update'
//                 }
//                 break;
//             case 2:
//                 if (cmd === 2) break;
//             case 3:
//                 if (cmd === 3) {
//                     o.msg = 'get-status'
//                 }
//                 break;
//             case 4:
//                 if (cmd === 4) {
//                     o.msg = 'get-version'
//                 }
//                 break;
//             case 5:
//                 if (cmd === 5) {
//                     o.msg = 'get-log'
//                 }
//                 break;
//         }
//     }
//     o.params = {
//         "channel_id": 0,
//         "web_cmd": cmd,
//     };
//     if (param) {
//         for (let index in param) {
//             o.params[index] = param[index]
//         }
//         console.log(param)
//     }
//     let ip = location.host;
//     ip='*************';
//     var wsData;
//     //SOAP 1.1 请求报文格式，1.2在网上可以找到
//     wsData = '<?xml version="1.0" encoding="utf-8"?>';
//     wsData = wsData + '<SOAP-ENV:Envelope  xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xmlns:xsd="http://www.w3.org/2001/XMLSchema"  xmlns:ns1="http://webservice.service.seisys.cn/">';
//     wsData = wsData + '<SOAP-ENV:Body>';
//     wsData = wsData + '<ns1:CommonOperate>'; //这里就是发布的方法名和xml文档中的命名空间地址（图中画线部分）
//     wsData = wsData + '<arg0>';
//     wsData = wsData + JSON.stringify(o.params);
//     wsData = wsData + '</arg0>';
//     wsData = wsData + '</ns1:CommonOperate>';
//     wsData = wsData + '</SOAP-ENV:Body>';
//     wsData = wsData + '</SOAP-ENV:Envelope>';
//     var wsURL = "http://"+ip+":12333";
//     $.ajax({
//         type: "POST", //访问WebService使用Post方式请求
//         contentType: "text/xml; charset=utf-8", //WebService 会返回Json类型
//         url: wsURL, //调用WebService
//         data: wsData, //Email参数
//         dataType: 'json',
//         timeout: 3000,
//         success: function (response) { //回调函数，result，返回值
//             // var text = response;
//             // alert('结果'+'\n'+text); //SOAP响应报文格式
//             // document.getElementById("data").innerHTML = text;
//         },
//         error: function (XMLHttpRequest, textStatus, errorThrown) {
//             if (textStatus === "error" || textStatus==="timeout") {
//                 console.log("Webservice请求失败")
//                 layer.msg('Webservice请求失败', {icon: 2});
//             } else {
//                 console.log("Webservice请求成功")
//             }
//             let response = XMLHttpRequest.responseText;
//             let reg = /{.*}/
//             if (reg.test(response)) {
//                 let reData = reg.exec(response);
//                 let d = reData[0];
//                 let getData = JSON.parse(d);
//                 if (getData.ret == 0) {
//                     console.log(o.msg + ':' + getData.desc)
//                     console.log(getData)
//                     if (cmd === 0) {
//                         parseValue2JSON(getData);
//                     } else if (cmd === 1) {
//                         layer.msg('配置下发成功', {icon: 1});
//                         sendWebService(0)
//                     }
//                 }
//
//             } else {
//
//             }
//         }
//     });
// }
// var setSe = function (data, type) {
//     sessionStorage.setItem(type, JSON.stringify(data))
// }
// var setTypeArray = function (data, type, container) {
//     for (let i = 1; i < 6; i++) {
//         let name = type + i;
//         if (data[i - 1] !== "") {
//             container[name] = data[i - 1];
//         }
//     }
// }
// var setPoint = function (type, value, num, container) {
//     let reg = /^light\d+$/;
//     if (reg.test(type)) {
//         let xName = type + '_x0';
//         let yName = type + '_y0';
//         let wName = type + '_w';
//         let hName = type + '_h';
//         container[xName] = value[0].x;
//         container[yName] = value[0].y;
//         container[wName] = Math.abs(value[1].x - value[0].x);
//         container[hName] = Math.abs(value[1].y - value[0].y);
//     }
//     for (let i = 0; i < num; i++) {
//         let xName = type + "_x" + i;
//         let yName = type + "_y" + i;
//         container[xName] = value[i].x;
//         container[yName] = value[i].y;
//     }
// }

// var validatePoint = function (value) {
//     let result = false;
//     let t1 = typeof value && !isNaN(value.length);
//     if (t1) {
//         for (let i = 0; i < value.length; i++) {
//             result = validatePoint(value[i])
//         }
//     } else {
//         if (value.x !== 0 || value.y !== 0) {
//             result = true;
//             return result
//         }
//     }
//     return result;
// }