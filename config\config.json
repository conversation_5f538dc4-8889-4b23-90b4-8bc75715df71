﻿{
  "index_ocx": {
    "main": 3,
    "sub": 1,
    "revise": 0
  },
  "allowedAuth": [
    "TOPSKY_H_HOLO",
    "TOPSKY_H_HOLO_M",
    "TOPSKY_H_HOLO_X",
    "TOPSKY_T_HOLO",
    "TOPSKY_T_HOLO_M",
    "TOPSKY_T_HOLO_X",
    "TOPSKY_H_HOLOS",
    "TOPSKY_H_HOLOS_M",
    "TOPSKY_H_HOLOS_X",
    "TOPSKY_T_HOLOS",
    "TOPSKY_T_HOLOS_M",
    "TOPSKY_T_HOLOS_X",
    "TOPSKY_T_YZWT",
    "TOPSKY_T_YZWT_M",
    "TOPSKY_T_YZWT_X",
    "TOPSKY_T_HWCS_X",
    "JOYWARE_T_HOLOS_X",
    "JOYWARE_H_HOLOS_X"
  ],
  "protocol": "1",
  "copyright": "© 2019",
  "index_reboot_time": 30,
  "index_auth_time": 30,
  "camera_username": "admin",
  "camera_password": "HuaWei123",
  "camera_stream": "/LiveMedia/ch1/Media",
  "camera_channel": "1",
  "oldEventPort": 60000,
  "newEventPort": 3128,
  "default_color": {
    "surveyColor": "#FF0000",
    "noParkingColor": "#ed0973",
    "aroundColor": "#0193de",
    "faceColor": "#f18800",
    "lineColor": "#00FFFF",
    "lineNumberColor": "#00FFFF",
    "plateColor": "#00ee00",
    "people1Color": "#eec700",
    "people2Color": "#eec700",
    "people3Color": "#eec700",
    "people4Color": "#eec700",
    "people5Color": "#eec700",
    "floor1Color": "#8D00FF",
    "floor2Color": "#8D00FF",
    "floor3Color": "#8D00FF",
    "floor4Color": "#8D00FF",
    "floor5Color": "#8D00FF",
    "redLightColor": "#637acd",
    "turnLeftColor": "#f8810a",
    "goStraightColor": "#1794e7",
    "turnRightColor": "#0cf42b",
    "cartTurnRightColor": "#0443CC",
    "upCalibrationColor": "#fc2a0a",
    "downCalibrationColor": "#fc2a0a",
    "redStopColor": "#e3f211",
    "signalColor": "#4909f9",
    "biggerColor": "#000"
  },
  "logo_size": "s",
  "auth_status": {
    "0": "未授权",
    "10001": "车头复合电警",
    "10002": "车头卡口基础特征识别",
    "10003": "车头AI特征识别",
    "10004": "货车异常行驶检测",
    "10005": "",
    "10071": "交通事件检测",
    "10072": "能见度检测",
    "10081": "智能路口复合电警",
    "10082": "交通参数检测",
    "10083": "连续变道抓拍",
    "10084": "",
    "10085": "未依次交替通行抓拍",
    "10086": "变道不打灯抓拍",
    "70001": "车头卡口电警综合算法",
    "70071": "AI事件综合算法",
    "70081": "智能路口路段综合算法",
    "30081": "源信智能路口综合算法",
    "99999": "测试版本"
  },
  "settingShow": {
    "drawSurvey": {
      "show": [
        "survey",
        "line",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ],
      "save": [
        "survey",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ]
    },
    "drawLine": {
      "show": [
        "survey",
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop"
      ],
      "save": [
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop"
      ]
    },
    "drawLineSurvey": {
      "show": [
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop",
        "survey",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ],
      "save": [
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop",
        "survey",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ]
    },
    "drawSignal": {
      "show": [
        "survey",
        "signal"
      ],
      "save": [
        "signal"
      ]
    },
    "drawPreview": {
      "show": [
        "survey",
        "noParking",
        "around",
        "face",
        "line",
        "signal"
      ],
      "save": []
    }
  },
  "ocxMsg": {
    "TRUE": {
      "result": true,
      "msg": "成功"
    },
    "0": {
      "result": true,
      "msg": "成功"
    },
    "FALSE": {
      "result": false,
      "msg": "失败"
    },
    "-1": {
      "result": false,
      "msg": "失败"
    },
    "INITED": {
      "result": true,
      "msg": "视频流已经初始化"
    },
    "Protected Mode": {
      "result": false,
      "msg": "系统处于保护模式，无法使用插件，请关闭浏览器的保护模式。",
      "offset": "rb"
    },
    "Json Is Error": {
      "result": false,
      "msg": "参数有误"
    },
    "Create Conf_File Failed": {
      "result": false,
      "msg": "创建文件失败"
    },
    "Conf File Clean Failed": {
      "result": false,
      "msg": "清空文件失败"
    },
    "Add_New_Conf Failed": {
      "result": false,
      "msg": "添加新配置失败"
    },
    "G_VARIABLE MALLOC FAILED": {
      "result": false,
      "msg": "配置变量分配内存失败"
    },
    "ALREADY INIT": {
      "result": true,
      "msg": "已经初始化"
    },
    "EVENT NOT STOP": {
      "result": false,
      "msg": "事件线程未停止"
    },
    "ERROR CODE": {
      "result": false,
      "msg": "读取图片路径失败"
    },
    "PATH SELECT ERROR": {
      "result": false,
      "msg": "路径选择错误"
    }
  },
  "newestOcxVersion": {
    "main": 3,
    "sub": 3,
    "revise": 2
  },
  "web_version": "2025年6月24日18:29:54",
  "mode": "production"
}
