var imgStatus = [
  './../../img/green.png',
  './../../img/red.png',
  './../../img/yellow.png'
];
var lightArray = [
  'left_light',
  'straight_light',
  'right_light',
  'return_light',
  'people_light'
];
var getLightStatus = function () {
  initWebService(webserviceCMD.CMD_GET_RED_LIGHT, null, getLightCallback);
};
var layer, form;
$(document).ready(function () {
  layui.use(['element', 'layer', 'form', 'laydate'], function () {
    layer = layui.layer, form = layui.form;
    getLightStatus()
  });

});
var getLightCallback = function (getData) {
  $(lightArray).each(function (i) {
    $("#" + lightArray[i]).attr({
      src: imgStatus[getData[lightArray[i]]]
    })
  });
  setTimeout(getLightStatus, 1000)
};
