<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>systemVersion</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/systemVersion.js"></script>
    <style>
        .layui-form-label{
            width: 130px;
        }
        .layui-form-item {
            margin-bottom: 5px;
        }
        span {
            width: 400px;
            position: absolute;
            padding: 9px 15px 9px 0px;
        }
    </style>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-form layui-show">
    <fieldset class="layui-elem-field">
        <legend id="deviceVersion">设备版本信息</legend>
        <div class="layui-field-box layui-form">
            <div class="layui-form-item">
                <label id="deviceUID" class="layui-form-label">设备UID：</label>
                <div class="layui-input-inline">
                    <span id="device_uid"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="softwareVersion"  class="layui-form-label">软件版本：</label>
                <div class="layui-input-inline">
                    <span id="software_ver"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="hardwareVersion" class="layui-form-label">硬件版本：</label>
                <div class="layui-input-inline">
                    <span id="hardware_ver"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="lowLevelVersion" class="layui-form-label">low level 版本：</label>
                <div class="layui-input-inline">
                    <span id="low_level_version"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="midLevelVersion" class="layui-form-label">mid level 版本：</label>
                <div class="layui-input-inline">
                    <span id="mid_level_version"></span>
                </div>
            </div>
            <!--<div class="layui-form-item">-->
                <!--<label class="layui-form-label">dsp 框架版本：</label>-->
                <!--<div class="layui-input-inline">-->
                    <!--<span id="dsp_framework_version"></span>-->
                <!--</div>-->
            <!--</div>-->
            <div class="layui-form-item">
                <label id="armVersion"class="layui-form-label">arm 框架版本：</label>
                <div class="layui-input-inline">
                    <span id="arm_framework_version"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="wkmodelVersion"class="layui-form-label">wkmodel 版本：</label>
                <div class="layui-input-inline">
                    <span id="wkmodel_version"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="codeloaderVersion"class="layui-form-label">codeloader 版本：</label>
                <div class="layui-input-inline">
                    <span id="codeloader_version"></span>
                </div>
            </div>
            <div class="layui-form-item">
                <label id="aiVersion"class="layui-form-label">AI驱动库版本：</label>
                <div class="layui-input-inline">
                    <span id="ai_driver_version"></span>
                </div>
            </div>
        </div>
    </fieldset>
    <button id="fresh" class="layui-btn layui-btn-default" onclick="getVersion()">刷新状态</button>
</div>
</body>
</html>
