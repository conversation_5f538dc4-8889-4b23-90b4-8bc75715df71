/**
 * Create by jjy
 * 2020/7/21
 */

var langMessage = {
    menuTitle: {
        PREVIEW: "Preview",
        EVENT: "Event",
        SEARCH: "Search",
        SNAP: "Snap",
        CONFIGURATION: "Configuration",
        AUTHORIZE: "Authorize",
        HELP: "Help"
    },
    index: {},
    common: {
        skip: "skip",
        error: "error",
        confirm: "determine",
        cancel: "cancel",
        hint: "hint",
        warning: "warning",
        fail: "fail",
        restart: "restart",
        jsonError: "An unexpected error occurred that caused the request to fail to parse",
        restartAlgorithm: "Restarting algorithm, please wait...",
        checkFolder: "Please select a folder！",
        browserError: "The current browser does not support this method, please modify the browser settings！",
        browserNonsupport: "The current browser does not support this method",
        editSuc: "Successfully modified",
        editFail: "fail to edit",
        nonsupportVideo: "This video stream is not currently supported！",
        nonsupportLocal: "The browser does not support localStorage",
        nonsupportSession: "The browser does not support sessionStorage",
        violationError: "Event type acquisition failed!",
        importSuc: "Imported successfully!",
        importFail: "Import failed!",
        exportSuc: "Export successfully!",
        exportFail: "Export failed!",
        exporting: "Exporting",
        saveSuc: "Saved successfully！",
        saveFail: "Save failed！",
        undeveloped: "This feature is not yet available！",
        wait: "please wait...",
        netError: "Network error, please try again",
        timeout: "Request timed out",
        requestNone: "Request does not exist",
        getCacheSize: function (size) {
            return 'Currently used storage：' + (size / 1024).toFixed(2) + 'KB'
        },
    },
    login: {
        loginSuc: "login successful",
        loginFail: "Login failed: user name or password is wrong!",
        text: {
            login: "Login",
            skip: "skip"
        },
        title: {
            rememberMe: "rememberMe",
        },
        placeholder: {
            username: "Username",
            password: "Password"
        }
    },
    ocxGlobal: {
        eventError: 'No more events!',
        eventId: 'Event ID：',
        ocxError: {
            ERROR: "Cannot use plugin！",
            INIT_FAIL: "Video initialization failed, please try to replay！",
            OPEN_FAIL: "Cannot open file! Please contact the developer.",
            SEARCH_FAIL: "Query error",
            SELECT_FAIL: "Selection failed!",
            WITHOUT_INSTALL: "Cannot operate without installing plugin",
            MATCH_FAIL: "The plug-in version is too low to operate!",
            MATCH_FAIL1: "The plug-in version does not match and cannot be operated！",
            DOWNLOAD: "Please click here to download the plug-in, please close browsing when installing",
            TIMEOUT: "Network exception or connection timeout. Please waiting.",
            PLAY_FAIL: "Play Failed.Please contact the manufacturer:",
            PLAY_WIDTH_ERROR: "Video width or height error",
            PLAY_RADIO_ERROR: "Resolution not supported"
        },
        update: "The plugin is updated, please click to download！",
        low: "The plug-in version in RPM package is lower than the plug-in version used by the current computer, please contact the developer!",
        versionNone: "The plug-in version was not obtained！",
        username: 'Username',
        password: 'Password',
        rtspError: {
            INPUT_ERROR: 'Wrong camera username or password, please re-enter',
            IS_EMPTY: 'The camera username or password cannot be empty'
        },
        userPlaceholder: 'Please enter the camera username',
        pwdPlaceholder: 'Please enter the camera password'
    },
    setting: {
        msgArray: [
            'Get configuration',
            'Set configuration',
            'Snap a picture',
            'Status query',
            'Version query',
            'Export real-time logs',
            'Export resident log',
            'Check the status of traffic lights',
            'Upgrade algorithm firmware',
            'Restore algorithm default value',
            'Restart camera',
            'Get authorization token',
            'Import authorization file',
            'Get the main stream encoding format',
            'Get osd configuration',
            'Set up osd configuration',
            'Authorization interface to obtain authorization information',
            'Issue blacklist',
            'Get blacklist'
        ],
        saveConfigSuc: 'Saved successfully！<br>(Save configuration only save, camera configuration needs to be issued)',
        freshSceneMsg: '<div>Refreshing the scene does not retain the unsaved configuration. Are you sure to refresh?</div><input type="checkbox" id="noMsg">No more reminders',
        confirmRestart: "Whether to restart？",
        confirmRestore: "Are you sure to restore factory settings? This operation will not preserve the current configuration！",
        confirmRestoreWithoutInternet: "Are you sure to restore factory settings? This operation will keep the current network configuration！",
        confirmRecover: "Do you confirm simple algorithm recovery? This operation will restore the algorithm to the default state and restart the program！",
        UNKNOWN: "unknown",
        UNAUTHORIZED: "unauthorized",
        UNKNOWN_AUTHORIZED: "Unknown authorization type",
        INITIALIZING: "initializing",
        INITIALIZED_FAIL: "initialization failed",
        INITIALIZED_SUC: "Initialization successful",
        provinceSelect: "Please select province",
        citySelect: "Please select city",
        countySelect: "Please select a county/district",
        errorMsg: {
            SPEED_ERROR: "The minimum speed cannot be greater than the maximum speed",
            SIGNAL_ERROR: "The signal light width cannot be less than 22！",
            FLASH_ERROR: "Failed to obtain the configuration of exposure flash",
            ERROR: "The configuration has the above errors and cannot be delivered！",
            LOCAL_ERROR: "The local storage path cannot be set to empty！",
            DATE_ERROR: "The date can only be set between 1970 and 2037, please check the system time！",
            TIME_ERROR: "The time is wrong, please reset！"
        },
        tipsMsg: {
            people: "To draw courteous pedestrians, please tick on the event detection page",
            floor: "To draw a virtual sense of ground, please check the event detection page"
        },
        warning_type: {
            0: 'Lane is too wide',
            1: 'The lane corresponding to the stop line is not configured',
            2: 'Excessive detection area',
            3: 'The stop line is too close to the upper edge of the detection area'
        },
        warningMsg: "The configuration has the above warning, whether to confirm the delivery？",
        subMsg: "Configuration is successfully delivered",
        error_type: {
            0: 'The detection area is not a convex quadrilateral',
            1: 'The motion analysis area is not a convex quadrilateral',
            2: 'The detection area is not within the motion analysis area',
            3: 'Wrong license plate width',
            4: 'The number of lanes cannot be 0',
            5: 'Lane line is not configured',
            6: 'Lane lines are not all in the same direction',
            7: 'Lane lines intersect or the sequence is wrong',
            8: 'The number of semaphores cannot be 0',
            9: 'The signal light width and height are too large or overlapped',
            10: 'The stop line exceeds the upper edge of the detection zone',
            11: 'Multiple stop lines are not continuous or overlapped',
            12: 'The position of the stop line in the waiting area can not be lower than the corresponding stop line',
            13: 'The parking area is not a convex quadrilateral',
            14: 'The parking violation zone is not within the motion analysis zone',
            15: 'Stop time range 3~180 seconds',
            16: 'Road number and road name cannot be empty',
            17: 'The intersection direction, intersection number, road number, and road name cannot contain spaces',
            18: 'Device name cannot be empty',
            19: 'Speed correction coefficient range0.5~2.0',
            20: 'The distance between the camera and the upper edge of the detection area is greater than the lower edge',
            21: 'resize is too large',
            22: 'resize is too small',
            23: 'Quality factor',
            24: 'The minimum speed limit is greater than or equal to the maximum speed limit',
            25: 'The left boundary line crosses the right boundary line or the position is wrong',
            26: 'The straight boundary line cannot be lower than the lower edge of the left and right boundary lines',
            27: 'Error in setting the confidence parameter for detecting the seat belt. Please enter a value between 1-100.',
            28: 'Error in setting the confidence parameter for detecting mobile phone usage. Please enter a value between 1-100.',
            29: 'Please configure the parameters related to license plate restriction.',
            30: 'The date, time period, and license plate parameters cannot be left empty.',
            31: 'The time period cannot be left empty, and the end time must be greater than the start time.',
            32: 'Up to 10 sets of rules are supported for configuration at most.',
            33: 'You have checked to detect checkpoint events. Please select the number of checkpoint images'
        },
        statusInfo: {
            NONE: "None",
            NORMAL: "normal",
            ABNORMAL: "abnormal",
            NOT_EXIST: "does not exist",
            CHECKING: "Checking",
            FORMATTING: "Formatting",
            DAMAGED: "File system damage cannot be repaired",
            LOGIN: "login",
            DISCONNECT: "disconnect",
            CROWD: "Congestion",
            EXTREME: "extremely",
            product_function: {
                0: "unknown"
            },
            data_status: {
                0: "normal",
                1: "Abnormal error in data area, need to check",
                2: "The disk is full or the directory does not exist"
            },
            log_status: {
                0: "normal",
                1: "Abnormal error in log area, need to check",
                2: "The disk is full or the directory does not exist"
            },
            has_hdd: {
                0: "No hard drive mounted",
                1: "SATA hard disk",
                2: "USB hard disk",
                3: "USB hard disk, and selected as system storage"
            },
            disk: {
                1: "Data partition",
                2: "Swap partition",
                4: "MISC partition",
            },
            has_sd: {
                0: "SD card does not exist",
                1: "SD card detected",
                2: "SD card detected, and selected SD card for storage",
                "-1": "abnormal",
            },
            sd_fs_status: {
                0: "SD card is not mounted",
                1: "SD card mounting"
            },
            config_status: {
                0: "normal",
                "-1": "Camera resolution is too large",
                "-2": "Semaphore is not configured",
                "-3": "Detection area configuration error",
                "-4": "The number of lanes is 0",
                "-5": "The lane is too wide or the detection area is too large",
                "-6": "Semaphore configuration error",
                "-7": "Mid-level error",
                "-1000": "dsp loading error",
                "-2000": "unauthorized",
            },
            extern_light_status: {
                0: "Unused equipment",
                1: "Normal",
                "-1": "Failed to open device",
                "-2": "Failed to open device",
                "-3": "Read device timeout",
            },
            radar_status: {
                0: "Unconnected",
                1: "Normal"
            }
        }
    },
    analysisFile: {
        handleProgress: function (progress) {
            return "  Processing, please wait, completed" + progress + "%"
        },
        uploadProgress: function (progress) {
            return 'Upload progress：' + progress + '%'
        },
        uploadError: {
            WITHOUT_ALGORITHM: "The file does not contain the algorithm package and cannot be upgraded！",
            INCOMPLETE: "The upgrade firmware is incomplete and cannot be upgraded",
            DAMAGED: "damaged file！",
            VERSION_LOW: "The kernel or app version is too low to upgrade the algorithm firmware！"
        },
        uploadName: {
            FILE_SYSTEM: "File system",
            KERNEL: "Kernel"
        }
    },
    authorization: {
        authInfo: {
            old: 'By applying for an authorization token, the authorization token (.token) is used to exchange the corresponding authorization code file (.lic) to authorize the camera',
            new: 'By applying for an authorization token, the authorization token (.token) is used to exchange the corresponding authorization code file (.lic ) License the camera.'
        },
        matchFail: "There is no matching file or authorization code file under the current folder. Open error!",
        matchMultiple: function (fileName) {
            return 'Match multiple identical authorization files！[' + fileName + ']';
        },
        exportMsg: function (result) {
            return "<i class=\"layui-icon layui-icon-tips\"></i>File export as：" + result
        },
        authMsg: {
            UNSUPPORTED: "Unsupported file format！",
            DAMAGED: "File is damaged, please try again！",
            UNKNOWN: "unknown mistake！",
            EXPORT_FAIL: "Export failed！",
            EXPORT_SUC: "Export successfully！",
            IMPORT_FAIL: "Import authorization failed！",
            IMPORT_CONFIRM: "This program has been authorized, whether to re-authorize？",
            IMPORT_SUC: "Authorization succeeded! (Effect after authorized restart) Do you confirm restart？",
            UPLOAD_SUC: "Upload successfully！",
            UPLOAD_FAIL: "Upload failed！",
            NO_SELECTED: "No file selected！",
            NO_AUTH: "You are not authorized yet"
        },
    },
    drawCanvas: {
        drawMsg: {
            LINE_FIRST: "Please set the lane line first",
            ANCHOR_EXCEED: "The number of anchor points exceeds",
            AMPLIFY_FIRST: "Please zoom in before drawing",
            AMPLIFY_ERROR: "Error zooming in",
            SIGNAL_FIRST: "Please set the number of semaphores first",
            NOT_SELECT: "The object to be cleared is not selected",
            anchorExceedNumber: function (num) {
                return "Anchor point cannot exceed" + num + "number"
            },
            lineExceedNumber: function (num) {
                return "Line cannot exceed" + num + "article"
            },
        },

        recover: "Restore original image",
        amplify: "Partial zoom",
        line: "line"
    },
    event: {
        frequent: "Frequent operation! please wait！",
        createError: "Could not create file! Please contact the manufacturer。",
        checkTime: '<div>Please confirm the time before receiving the event！</div><input type="checkbox" id="noCheckTime">No more reminders',
        eventTable: {
            time: "Trigger time",
            ID: "Event ID",
            EVENT_TYPE: "Event type",
            LANE_INDEX: "Lane number",
            PLATE_TYPE: "Plate type",
            PLATE_STRING: "Plate number",
            CAR_SPEED: "Speed",
            CAR_COLOR: "Color",
            CAR_TYPE: "Vehicle Type",
            IMAGE_EVENT_PATH: "Second picture",
            IMAGE_EVENT_X: "Second picture X",
            IMAGE_EVENT_Y: "Second picture Y",
            IMAGE_EVENT_W: "Second picture W",
            IMAGE_EVENT_H: "Second picture H",
            IMAGE_PRE_PATH: "First picture",
            IMAGE_PRE_X: "First picture X",
            IMAGE_PRE_Y: "First picture Y",
            IMAGE_PRE_W: "First picture W",
            IMAGE_PRE_H: "First picture H",
            IMAGE_LAST_PATH: "Third picture",
            IMAGE_LAST_X: "Third picture X",
            IMAGE_LAST_Y: "Third picture Y",
            IMAGE_LAST_W: "Third picture W",
            IMAGE_LAST_H: "Third picture H",
            IMAGE_PLATE_PATH: "License Plate Picture",
            IMAGE_FEATURE_PATH: "close up",
            CHE_XING: "Subdivision models",
            preTime: "Last time",
            lastTime: "Next time",
            featureTime: "Close-up time",
            desc: "Overview",
        },
        serverDisconnected: "Server connection lost",
        receptionFail: "Event reception failed",
        text: {
            query: "Query Type",
            startTime: "Start Time",
            endTime: "End Time",
            plateString: "Plate Number",
            eventType: "Event Type",
            plateType: "Plate Type",
            submit: "Submit",
            reset: "Reset",
            exportBtn: "Export Current Events",
            trigger: "Trigger time is camera time"
        },
    },
    extendParams: {
        object: {
            iMatch_X_Threshold: {

                desc1: 'X distance threshold',
                desc2: 'Value range (1-12000) The current default value is 400 Read and write permissions: read and write'
            },
            iMatch_Y_Threshold: {

                desc1: 'Y distance threshold',
                desc2: 'Value range (1-12000) The current default value is 600 Read and write permissions: read and write'
            },
            iGray_Threshold: {

                desc1: 'Gray threshold',
                desc2: 'Value range (1-255) The current default value is 15 Read and write permissions: read and write'
            },
            iPlate_Blue_Grad_Threshold: {
                desc1: 'Blue license plate gradient threshold',
                desc2: 'Value range (1-300) The current default value is 100 Read and write permissions: read and write'
            },
            iPlate_Yellow_Grad_Threshold: {
                desc1: 'Yellow license plate gradient threshold',
                desc2: 'Value range (1-300) The current default value is 100 Read and write permissions: read and write'
            },
            iPlate_Blue_Skip_Threshold: {
                desc1: 'Number of frame skips for blue license plate',
                desc2: 'Value range (0-300) The current default value is 1 Read and write permissions: read and write'
            },
            iPlate_Yellow_Skip_Threshold: {
                desc1: 'Yellow license plate skipped frame number',
                desc2: 'Value range (0-300) The current default value is 1 Read and write permissions: read and write'
            },
            iIs_All: {
                desc1: 'Display logo',
                desc2: 'Value range (0-300) The current default value is 1 Read and write permissions: read and write'
            },
            iNight_Gray_Threshold: {
                desc1: 'Night threshold',
                desc2: 'Value range (0-255) The current default value is 10 Read and write permissions: read and write'
            },
            iDay_To_Night_Threshold: {
                desc1: 'Day to night transition threshold',
                desc2: 'Value range (0-255) The current default value is 30 Read and write permissions: read and write'
            },
            iKakou_Detect_Area: {
                desc1: 'Bayonet detection area ratio',
                desc2: 'Value range (0-100) The current default value is 80 Read and write permissions: read and write'
            },
            iDetect_Precision: {
                desc1: 'Detection accuracy',
                desc2: 'Value range (0-100) The current default value is 8 Read and write permissions: read and write'
            },
            iDelay_Time: {
                desc1: 'delay time',
                desc2: 'Value range (0-2000) The current default value is 800. Read and write permissions: read and write'
            },
            iDelay_Dis_img2: {
                desc1: 'Delay distance of the second picture',
                desc2: 'Value range (0-40) The current default value is 0 Read and write permissions: read and write'
            },
            iSame_Plate_Time: {
                desc1: 'The same license plate repeatedly reported time threshold',
                desc2: 'Value range (0-100) The current default value is 50 Read and write permissions: read and write'
            },
        }
    },
    pictureOSD: {
        saveMsg: '<div>Save the configuration will remove the extra black bars outside the picture, confirm to save？</div><input type="checkbox" id="osdMsg">No more reminders',
        getInfoFail: "ailed to obtain osd type information",
        osdPositionTips: 'When the up/down overlapping mode is selected, the mouse can be moved to the upper/lower edge of the left scene map. When the mouse shape changes to the up/down arrow, the overlapping area can be expanded or reduced'
    },
    search: {
        exportConfig: {
            "-1": "cancel selection",
            "-2": "File selection error",
            "-3": "Error opening file",
            "-4": "Retry again error",
            "-5": "User gave up and retry",
            "-6": "Unexpected error",
            "-7": "Parameter error"
        },
        unknownPlate: ['无牌车', '污损车牌'],
        removeDuplication: function (total) {
            return "Altogether remove duplicates " + total + " strip"
        }
    },
    settingCode: {
        object: {
            id: "ID",
            name: "Illegal act",
            code: "Brigade illegal code",
            priority: "priority",
            zfcode: "National standard illegal code"
        },
        editMsg: function (field, value) {
            return '[' + field + '] ' + ' change to：' + value
        }
    },
    content: {
        text: {
            preview: "preview",
            event: "event",
            search: "search",
            snap: "snap",
            configuration: "configuration",
            authorize: "authorize",
            help: "help"
        }
    },
    contentAuthorization: {
        text: {
            authTitle: "Authorization code authorization",
            authInfo: "By applying for an authorization token, the authorization token (. Token) is used to exchange the corresponding authorization code file (. LIC) to authorize the camera.",
            authFile: "Select authorization code directory",
            importAuth: "authorization",
            exportAuth: "Request authorization token",
            exportFile: "Export authorization file",
            authExportInfo: "(Note 1: the default saving path of authorization token is the download path of browser)",
            authImportInfo: "(Note 2: during authorization, you only need to select the authorization code directory, and the program will search the corresponding authorization file in the directory.)",
            authorizationText2: "Code scanning authorization",
            authorizationText3: "Scan the QR code below for authorization. After you enter the authorization page and fill in the relevant information correctly, you will be given a free trial license.",
            note3: "(Note: as the trial version is authorized to be sent via email, please make sure that the email address is correctly filled in!)"
        }
    },
    contentEvent: {
        text: {
            videoT1Value: "Mainstream code",
            mainStream: "Mainstream code",
            subStream: "Subflow code",
            snapPlateNumber: "Capture the license plate numbe",
            recognitionPlateNumber: "Identify license plate number",
            pictureMode: "Picture mode",
            listMode: "List mode",
            td_eventPicName: "Panorama",
            td_platePicName: "License plate image",
            td_plateNumName: "license plate",
            td_eventType: "Event Type",
            TIME_: "Time:",
            EVENT_TYPE_: "Event type:",
            LANE_INDEX_: "Lane number:",
            PLATE_TYPE_: "Plate type:",
            PLATE_STRING_: "Plate number:",
            CAR_SPEED_: "Speed:",
            CAR_COLOR_: "Color:",
            CAR_TYPE_: "Vehicle Type:",
            CHE_XING_: "Subdivision models:"
        },
        title: {
            hidePicture: "hide picture",
            receiveEvent: "receive event"
        }
    },
    contentIndex: {
        text: {
            subStream: "Subflow code"
        },
        clazz: {
            videoT1Value: "Mainstream code"
        }
    },
    contentPicture: {
        text: {
            Interval_time: "Interval time："
        }
    },
    contentSearch: {
        text: {
            inquiry_mode: "Inquiry mode",
            Start_time: "Start time",
            End_time: "End time",
            License_plate_number: "Plate No.",
            etype: "Event type",
            License_plate_type: "Plate type",
            submit: "submit",
            reset: "reset",
            exportBtn: "export",
            trigger_time: "the trigger time is camera time."
        }
    },
    contentSetting: {
        text: {
            system: "System",
            Device_information: "Device information",
            Device_status: "Device status",
            Module_status: "Module status",
            Version_information: "Version information",
            TPM: "Equipment maintenance",
            Local_configuration: "Local configuration",
            User: "User",
            Internet: "Internet",
            Advanced_applications: "Advanced applications",
            Video: "Video<span class=\'layui-nav-more\'></span>",
            Video_parameters: "Video parameters",
            Image: "Image<span class=\'layui-nav-more\'></span>",
            Event_OSD_crossword: "Event OSD crossword",
            Disk: "Disk<span class=\'layui-nav-more\'></span>",
            disc: "disc",
            Algorithm: "Algorithm<span class=\'layui-nav-more\'></span>",
            Detection_area: "Detection area",
            Detection_area1: 'Draw detection area',
            Lane_lines: "Lane lines",
            Signal_lamp: "Signal lamp",
            Traffic_light_status: "Traffic light status",
            Event_detection: "Event detection",
            System_configuration: "System configuration",
            Traffic_expansion_parameters: "Traffic expansion parameters",
            Illegal_code: "Illegal code",
            Configuration_Preview: "Configuration Preview"
        }
    },
    loginIng: {
        clazz: {
            login: "LOGIN"
        },
        placeholder: {
            username: "Username",
            password: "Password",
        },
        title: {
            rememberMe: "RememberMe"
        }
    },
    internetSenior: {
        text: {
            serverAddress: "Server address",
            FTPSet: "FTP server settings",
            dataBit: "Data bits：",
            userName: "UserName:",
            password: "Password：",
            saveDat: "FTP storage time (unit: days)",
            video: "Video：",
            bayonet: "Bayonet：",
            violation: "Violations：",
            saveStorage: "Save",
            reset: "Reset",
            saveType: "FTP storage type",
            bayonetType: "Checkpoint",
            violationType: "Illegal",
            savePath: "FTP storage path",
            pathList: "Directory structure：",
            saveName: "FTP storage file name",
            forwardPlateform: 'Aggregation and forwarding platform settings'
        },
        clazz: {
            address: "address：",
            port: "port："
        },
        tips: {
            nameList: "命名项",
            pleaseSelect: "请选择",
            pleaseInput: "请输入",
            id_1400: "设备ID请输入20个数字或字符"
        },
        cmd: {
            fileList: [
                {
                    code: 0,
                    desc1: '保存在根目录',
                    desc2: '根目录：'
                },
                {
                    code: 1,
                    desc1: '使用一级目录',
                    desc2: '一级目录：'
                },
                {
                    code: 2,
                    desc1: '使用二级目录',
                    desc2: '二级目录：'
                },
                {
                    code: 3,
                    desc1: '使用三级目录',
                    desc2: '三级目录：'
                },
                {
                    code: 4,
                    desc1: '使用四级目录',
                    desc2: '四级目录：'
                },
                {
                    code: 5,
                    desc1: '使用五级目录',
                    desc2: '五级目录：'
                },
                {
                    code: 6,
                    desc1: '使用六级目录',
                    desc2: '六级目录：'
                }
            ],
            filePath: [
                {
                    code: "xjip",
                    desc: "设备IP"
                },
                {
                    code: "sbbh",
                    desc: "设备编号"
                },
                {
                    code: "wflx",
                    desc: "违法类型"
                },
                {
                    code: "bzdm",
                    desc: "国标违法代码"
                },
                {
                    code: "ym",
                    desc: "时间（年月）"
                },
                {
                    code: "ymd",
                    desc: "时间（年月日）"
                },
                {
                    code: "hour",
                    desc: "时间（小时）"
                },
                {
                    code: "cdfx",
                    desc: "车道行驶方向"
                },
                {
                    code: "cdno",
                    desc: "车道号"
                },
                {
                    code: "0",
                    desc: "自定义"
                }
            ],
            fileName: [
                {
                    code: "sbbh",
                    desc: "设备编号"
                },
                {
                    code: "bzdm",
                    desc: "国标违法代码"
                },
                {
                    code: "time",
                    desc: "时间"
                },
                {
                    code: "timeMs",
                    desc: "时间（带毫秒）"
                },
                {
                    code: "cphm",
                    desc: "车牌号码"
                },
                {
                    code: "cpys",
                    desc: "车牌颜色"
                },
                {
                    code: "cpzb",
                    desc: "车牌坐标"
                },
                {
                    code: "cx",
                    desc: "车型"
                },
                {
                    code: "csys",
                    desc: "车身颜色"
                },
                {
                    code: "cb",
                    desc: "车标"
                },
                {
                    code: "clsd",
                    desc: "车辆速度"
                },
                {
                    code: "cdfx",
                    desc: "车道行驶方向"
                },
                {
                    code: "cdno",
                    desc: "车道号"
                },
                {
                    code: "0",
                    desc: "自定义"
                }
            ],
        }
    },
    pictureOSDs: {
        image: {
            configImg: "../../img/404.png",
        },
        text: {
            refreshPic: "refresh",
            saveConfig: "save",
            clearConfig: "clear",
            resetConfig: "reset",
            mode: "Superposition mode：",
            doubleStyle: "Overlay style",
            doubleInfo: "Superimposed information",
            doubleText: "Overlay content：",
            title: "Overlay content：",
            font: "typeface：",
            background: "typeface：",
            xCoordinate: "X：",
            yCoordinate: "Y:",
            fontSize: "font size：",
            dateStyle: "Date format：",
            timeStyle: "Time format："
        },
        placeholder: {
            checkOSD: "OSD enabled",
            fontColors: "colour",
            fontColors2: "colour"
        }
    },
    picture_OSD: {
        date: {
            "title": "Superimposed characters"
            , "list": [
                {
                    value: 0,
                    title: "License plate number：",
                    type: "License plate number",
                    checked: true,
                    filter: 'cphm',
                    space: 0,
                    enter: 0,
                    area: 0
                },
                {
                    value: 1,
                    title: "Illegal act：",
                    type: "Illegal act",
                    checked: true,
                    filter: 'wfxw',
                    space: 0,
                    enter: 0
                }, {
                    value: 2,
                    title: "time：",
                    type: "time",
                    checked: false,
                    filter: 'sj',
                    space: 0,
                    enter: 0
                }, {
                    value: 3,
                    title: "plate color：",
                    type: "plate color",
                    checked: true,
                    filter: 'cpys',
                    space: 0,
                    enter: 0
                }, {
                    value: 4,
                    title: "Vehicle Type：",
                    type: "Vehicle Type",
                    checked: false,
                    filter: 'cllx',
                    space: 0,
                    enter: 0
                }, {
                    value: 5,
                    title: "address：",
                    type: "address",
                    checked: true,
                    filter: 'dd',
                    space: 0,
                    enter: 0
                }, {
                    value: 6,
                    title: "Lane number：",
                    type: "Lane number",
                    checked: false,
                    filter: 'cdh',
                    space: 0,
                    enter: 0
                }, {
                    value: 7,
                    title: "Lane direction：",
                    type: "Lane direction",
                    checked: false,
                    filter: 'cdfx',
                    space: 0,
                    enter: 0
                }, {
                    value: 8,
                    title: "Driving direction：",
                    type: "Driving direction",
                    checked: false,
                    filter: 'xsfx',
                    space: 0,
                    enter: 0
                }, {
                    value: 9,
                    title: "Red light on time：",
                    type: "Red light on time",
                    checked: false,
                    filter: 'hdlsj',
                    space: 0,
                    enter: 0
                }, {
                    value: 10,
                    title: "Road name：",
                    type: "Road name",
                    checked: false,
                    filter: 'dlmc',
                    space: 0,
                    enter: 0
                }, {
                    value: 11,
                    title: "speed：",
                    type: "speed",
                    checked: false,
                    filter: 'cs',
                    space: 0,
                    enter: 0
                }, {
                    value: 12,
                    title: "High and low speed limit：",
                    type: "High and low speed limit",
                    checked: false,
                    filter: 'gdxs',
                    space: 0,
                    enter: 0
                }, {
                    value: 13,
                    title: "device ID：",
                    type: "device ID",
                    checked: false,
                    filter: 'sbbh',
                    space: 0,
                    enter: 0
                }, {
                    value: 14,
                    title: "the color of car：",
                    type: "the color of car",
                    checked: false,
                    filter: 'csys',
                    space: 0,
                    enter: 0
                }, {
                    value: 15,
                    title: "Vehicle Brands：",
                    type: "Vehicle Brands",
                    checked: false,
                    filter: 'clpp',
                    space: 0,
                    enter: 0
                }, {
                    value: 16,
                    title: "Illegal code：",
                    type: "Illegal code",
                    checked: false,
                    filter: 'wfdm',
                    space: 0,
                    enter: 0
                }, {
                    value: 17,
                    title: "Custom 1：",
                    type: "Custom 1",
                    checked: false,
                    filter: 'zdy1',
                    space: 0,
                    enter: 0
                }, {
                    value: 18,
                    title: "Custom 2：",
                    type: "Custom 2",
                    checked: false,
                    filter: 'zdy2',
                    space: 0,
                    enter: 0
                }, {
                    value: 19,
                    title: "Custom 2：",
                    type: "Custom 2",
                    checked: false,
                    filter: 'zdy3',
                    space: 0,
                    enter: 0
                }
            ]
            , selected: false
            , filter: ""
        },
        image: {
            OSDImg: "../../img/404.png",
        },
        text: {
            refreshPic: "refresh",
            save: "save",
            clear: "clear",
            reset: "reset",
            doubleStyle: "Overlay style",
            doubleWay: "Superposition mode：",
            on: "In the picture",
            out: "Outside the picture",
            fontSize: "font size：",
            beforeColor: "ForeColor：",
            backColor: "Backcolor：",
            beginX: "Starting X coordinate：",
            beginY: "Starting Y coordinate：",
        },
        clazz: {
            doubleInfo: "Superimposed information"
        },
        placeholder: {
            frontColorInput: "ForeColor",
            backColorInput: "Backcolor",
        },
        title: {
            illegalDescription: "Illegal description",
            code: "Illegal code",
            illegalTime: "Illegal time",
            overSpeedRatio: "Over speed ratio",
            laneDirection: "Lane direction",
            laneName: "Lane name",
            speedLimit: "High and low speed limit",
            laneNumber: "Lane number",
            plateNumber: "license plate",
            plateKind: "Vehicle type",
            carColor: "colour",
            plateColor: "colour",
            bodyLength: "Body length",
            runSpeed: "Travel speed",
            drivingDirection: "Direction",
            Logo: "Auto Logos",
            redLightTime: "Red light time of vehicle logo",
            deviceID: "Device ID",
            illegalLocation: "Illegal place",
            antiCounterfeiting: "Security code",
            information1: "Custom message 1",
            information2: "Custom message 2",
            information3: "Custom message 3",
            information4: "Custom message 4"
        }
    },
    settingCodes: {
        text: {
            saveCode: "saveCode",
            resetCode: "resetCode"
        }
    },
    settingDetect: {
        text: {
            general: "General functions",
            sensitivity: "Sensitivity of unlicensed vehicles：",
            electricPolice: "Multi function electric alarm",
            parkingTime: "Stop time：",
            stopTime: "1,2 picture intervals：",
            pictureInterval2: "1,2 pictures interval：",
            bayonetPolice: "Bayonet police",
            effectiveTime: "Effective period",
            ramp: "Ramp alarm",
            saveDetect: "save",
            resetDetect: "reset",
            time: "Effective period 1",
            time2: "Effective period 2",
            time3: "Effective period 3",
            xianXingTime: "Effective period",
            exportBlackBtn: "Export blacklist",
            importBlackBtn: "Import blacklist",
            confidence: 'Confidence'
        },
        clazz: {
            min: "second",
            pictureInterval: "2,3 picture interval:"
        },
        title: {
            shanghaiC: "Detection of breaking the ban (Shanghai c)",
            coachCar: "Detection of breaking the ban (coach car)",
            trucks: "Detection of breaking the ban (truck)",
            otherProvinces: "Detection of breaking the ban (small cars in other provinces and cities)",
            xianXing: "Traffic restriction",
            phone: "Detection of mobile phone calls",
            checkSeatBelts: "Testing seat belts",
            noDrivingLights: "No lamp detected",
            violationSpeed: "Speed violation detected",
            pre_card: "Front card event exposure",
            bayonet: "Detection bayonet",
            moto: "Detection motorcycles",
            MotoKaKou: 'Detecting motorcycle checkpoints',
            motoCjl: 'Detect motorcycle violation ban',
            motorcycles: "Detection of non motor vehicles",
            violation: "Detection of illegal use of special lanes",
            viewBayonet: "Close-up view of bayonet",
            violationFeature: "Violation feature picture",
            video: "Violation video",
            unlicensedCars: "Detection of unlicensed vehicles",
            redLight: "Detection of red light running",
            threePictures: "Three pictures were collected by bayonet",
            loiter: "Detection of intersection detention",
            wheel: "Turn signals are not used to detect cornering",
            dcv: "Vehicle for detecting hazardous chemicals",
            obp: "Detect overtaking through the lane",
            output: "Output trajectory",
            retrograde: "Detection of retrograde",
            turns: "Detection of large turns and small turns",
            straight: "Check the left turn and stop going straight",
            nonMotor: "Detect that the crane turns right without stopping and giving way",
            pedestrianDetection: "Detection of courteous pedestrians",
            alternately: "Alternate traffic is not detected",
            coilOutput: "Detection of ground induction coil output",
            zebra: "Detection of zebra crossing U-turn",
            affectsNormalTraffic: "Detection of U-turn affecting normal traffic",
            changes: "Detection of continuous lane change",
            strictViolation: "Strict violation detection",
            strictRedLight: "Strict detection of red light running",
            strictlyPass: "Strict inspection by the way",
            strictPressureLine: "Strict line pressing detection",
            strictRetrograde: "Strict retrograde detection",
            changeWay: "Detection of illegal lane change",
            withoutTurningLights: "Detecting lane change without using turn signal",
            detectJump: "Detect Illegal Jump",
            detectWeiFaZaiHuo: "Detect illegal cargo loading",

            NonMotoRed: "Detect non motor vehicles running red lights",
            NonMotoRet: "Detect non motor vehicle retrograde",
            NonMotoVeh: "Detection of non motor vehicles",
            NonMotoNoHel: "Testing non motor vehicles without helmets",
            NonMotoKaKou: "Detection of non motor vehicle bayonets",
            NonMotoOverload: "Detect overloading of non-motorized vehicles",

            illegalWays: "Detect illegal ways",
            left: "Take the left",
            goStraight: "Go straight by the way",
            right: "On the right",
            parking: "Detection of over-line parking",
            trucksOccupying: "Detection of trucks occupying the passenger lane",
            heavyTruck: "Heavy truck",
            mediumTruck: "Medium truck",
            lightTruck: "Light truck",
            vehicleBan: "Ban on vehicle detection",
            blacklist: "Detection of break ban (blacklist)",
            largeVehiclesOccupyingSmallLanes: "Detection of large vehicles occupying small lanes",
            heavyTrucks: "heavy truck",
            largeBuses: "large buses",
            yellowLicensePlate: "yellow license plate",
            pressureLine: "Detection pressure line",
            illegalParking: "Detection of illegal parking",
            congestionDetection: "Congestion detection"
        },
        line: {
            list: [
                {
                    name: "Lane line 0",
                    value: "0x0001"
                },
                {
                    name: "Lane line 1",
                    value: "0x0002"
                },
                {
                    name: "Lane line 2",
                    value: "0x0004"
                },
                {
                    name: "Lane line 3",
                    value: "0x0008"
                }
                ,
                {
                    name: "Lane line 4",
                    value: "0x0010"
                },
                {
                    name: "Lane line 5",
                    value: "0x0020"
                }
            ],
            attributes: [
                {
                    name: "Lane 1 attributes：",
                    selectText1: "ordinary",
                    selectText2: "Small lane",
                    selectText3: "Passenger carriageway"
                },
                {
                    name: "Lane 2 attributes：",
                    selectText1: "ordinary",
                    selectText2: "Small lane",
                    selectText3: "Passenger carriageway"
                },
                {
                    name: "Lane 3 attributes：",
                    selectText1: "ordinary",
                    selectText2: "Small lane",
                    selectText3: "Passenger carriageway"
                },
                {
                    name: "Lane 4 attributes：",
                    selectText1: "ordinary",
                    selectText2: "Small lane",
                    selectText3: "Passenger carriageway"
                },
                {
                    name: "Lane 5 attributes：",
                    selectText1: "ordinary",
                    selectText2: "Small lane",
                    selectText3: "Passenger carriageway"
                }
            ]
        },
        flashingLight: {
            list: [
                [
                    {
                        name: "lane 1 "
                    },
                    {
                        name: "lane 2 "
                    },
                    {
                        name: "lane 3 "
                    },
                    {
                        name: "lane 4 "
                    },
                    {
                        name: "lane 5 "
                    }
                ],
                {
                    name: "Exposure flash：",
                },
                {
                    name: "Attributes：",
                    selects: [
                        {
                            name: "ordinary",
                            value: "0"
                        },
                        {
                            name: "Small lane",
                            value: "1"
                        }
                    ]
                },
                {
                    name: "Attributes：",
                    selects: [
                        {
                            name: "ordinary",
                            value: "0"
                        },
                        {
                            name: "Passenger carriageway",
                            value: "1"
                        }
                    ]
                }
            ]
        },
        table: [
            {
                type: "Heavy truck",
                value: "0x0001",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Medium truck",
                value: "0x0002",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Light truck",
                value: "0x0004",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Large buses",
                value: "0x0008",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Medium bus",
                value: "0x0010",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Light bus",
                value: "0x0020",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            },
            {
                type: "Minibus",
                value: "0x0040",
                plate: [
                    "Blue license plate",
                    "Yellow license plate"
                ]
            }
        ],
        week: [
            {
                day: "Monday",
                value: "64"
            },
            {
                day: "Tuesday",
                value: "32"
            },
            {
                day: "Wednesday",
                value: "16"
            },
            {
                day: "Thursday",
                value: "8"
            },
            {
                day: "Friday",
                value: "4"
            },
            {
                day: "Saturday",
                value: "2"
            },
            {
                day: "Sunday",
                value: "1"
            }
        ],
        typeOfViolation: [
            {
                type: "Violation type 1",
                value: "0x01"
            },
            {
                type: "Violation type 2",
                value: "0x02"
            },
            {
                type: "Violation type 3",
                value: "0x04"
            },
            {
                type: "Violation type 4",
                value: "0x08"
            },
            {
                type: "Violation type 5",
                value: "0x10"
            },
            {
                type: "Violation type 6",
                value: "0x20"
            },
            {
                type: "Violation type 7",
                value: "0x40"
            },
            {
                type: "Violation type 8",
                value: "0x80"
            }
        ],
        province: [
            {
                "value": "京",
                "name": "京",
            },
            {
                "value": "津",
                "name": "津",
            },
            {
                "value": "沪",
                "name": "沪",
            },
            {
                "value": "渝",
                "name": "渝",
            },
            {
                "value": "冀",
                "name": "冀",
            },
            {
                "value": "豫",
                "name": "豫",
            },
            {
                "value": "云",
                "name": "云",
            },
            {
                "value": "辽",
                "name": "辽",
            },
            {
                "value": "黑",
                "name": "黑",
            },
            {
                "value": "湘",
                "name": "湘",
            },
            {
                "value": "皖",
                "name": "皖",
            },
            {
                "value": "闽",
                "name": "闽",
            },
            {
                "value": "鲁",
                "name": "鲁",
            },
            {
                "value": "新",
                "name": "新",
            },
            {
                "value": "苏",
                "name": "苏",
            },
            {
                "value": "浙",
                "name": "浙",
            },
            {
                "value": "赣",
                "name": "赣",
            },
            {
                "value": "鄂",
                "name": "鄂",
            },
            {
                "value": "桂",
                "name": "桂",
            },
            {
                "value": "甘",
                "name": "甘",
            },
            {
                "value": "晋",
                "name": "晋",
            },
            {
                "value": "蒙",
                "name": "蒙",
            },
            {
                "value": "陕",
                "name": "陕",
            },
            {
                "value": "吉",
                "name": "吉",
            },

            {
                "value": "贵",
                "name": "贵",
            },

            {
                "value": "粤",
                "name": "粤",
            },
            {
                "value": "青",
                "name": "青",
            },
            {
                "value": "藏",
                "name": "藏",
            },
            {
                "value": "川",
                "name": "川",
            },
            {
                "value": "宁",
                "name": "宁",
            },
            {
                "value": "琼",
                "name": "琼",
            }
        ],
        city: [
            {
                "value": 1,
                "name": "A",
            },
            {
                "value": 2,
                "name": "B",
            },
            {
                "value": 4,
                "name": "C",
            },
            {
                "value": 8,
                "name": "D",
            },
            {
                "value": 16,
                "name": "E",
            },
            {
                "value": 32,
                "name": "F",
            },
            {
                "value": 64,
                "name": "G",
            },
            {
                "value": 128,
                "name": "H",
            },
            {
                "value": 256,
                "name": "I",
            },
            {
                "value": 512,
                "name": "J",
            },
            {
                "value": 1024,
                "name": "K",
            },
            {
                "value": 2048,
                "name": "L",
            },
            {
                "value": 4096,
                "name": "M",
            },
            {
                "value": 8192,
                "name": "N",
            },
            {
                "value": 16384,
                "name": "O",
            },
            {
                "value": 32768,
                "name": "P",
            },
            {
                "value": 65536,
                "name": "Q",
            },
            {
                "value": 131072,
                "name": "R",
            },
            {
                "value": 262144,
                "name": "S",
            },
            {
                "value": 524288,
                "name": "T",
            },
            {
                "value": 1048576,
                "name": "U",
            },
            {
                "value": 2097152,
                "name": "V",
            },
            {
                "value": 4194304,
                "name": "W",
            },
            {
                "value": 8388608,
                "name": "X",
            },

            {
                "value": 16777216,
                "name": "Y",
            },

            {
                "value": 33554432,
                "name": "Z",
            }
        ],
        week_xianXing: [
            {
                name: "周一",
                value: 2
            },
            {
                name: "周二",
                value: 4
            },
            {
                name: "周三",
                value: 8
            },
            {
                name: "周四",
                value: 16
            },
            {
                name: "周五",
                value: 32
            },
            {
                name: "周六",
                value: 64
            },
            {
                name: "周日",
                value: 1
            }
        ],
        number_xianXing: [
            {
                name: 0,
                value: 1
            },
            {
                name: 1,
                value: 2
            },
            {
                name: 2,
                value: 4
            },
            {
                name: 3,
                value: 8
            },
            {
                name: 4,
                value: 16
            },
            {
                name: 5,
                value: 32
            },
            {
                name: 6,
                value: 64
            },
            {
                name: 7,
                value: 128
            },
            {
                name: 8,
                value: 256
            },
            {
                name: 9,
                value: 512
            }
        ]
    },
    settingExtend: {
        text: {
            trafficDetection: "Extended parameter setting of traffic detection",
            parameterList: "parameter list",
            defaultAllocation: "Default configuration",
            highSensitivity: "High sensitivity",
            mediumSensitivity: "Medium sensitivity (recommended)",
            lowSensitivity: "Low sensitivity",
            parameterValue: "Parameter value:",
            recommendBtn: "Recommended value",
            valueBtn: "Original value",
            unitS: "Company：",
            description: "explain",
            saveExtend: "save",
            resetExtend: "reset"
        }
    },
    settingLine: {
        text: {
            refreshPic: "refresh",
            saveConfig: "Save",
            clearNowConfig: "Clear",
            showConfig: "Reset",
            attributes: "Lane line related attributes",
            lineBtn: "Draw lane lines",
            lane: "Number of lanes：",
            begin: "Start lane：",
            left: "Left>right",
            right: "right>Left",
            floorBtn: "Virtual ground sense",
            peopleBtn: "Yield Here To Peds",
            straightLine: "straight line",
            ZLine: "Z line",
            modeSelection: "mode selection："
        },
        clazz: {
            direction: "direction：",
            definition: "Driving definition：",
            waitArea: "Waiting area：",
            property: "Special properties：",
            kind: "style：",
            real: "solid line",
            false: "dotted line",
            redStopLine: "red stop line",
            line0: "Lane Line 0 Attribute",
            line1: "Lane Line 1 Attribute",
            line2: "Lane Line 2 Attribute",
            line3: "Lane Line 3 Attribute",
            line4: "Lane Line 4 Attribute",
            line5: "Lane Line 5 Attribute",
            virtualGround: "Virtual ground sense detection line",
            pedestrian: "Polite pedestrian detection area",
            up: "Up",
            down: "Down",
            left1: "Left row",
            straight: "straight",
            right1: "Right row",
            turnAround: "Turn around",
            leftStraight: "left straight",
            rightStraight: "right straight",
            turnLeft: "Turn left",
            leftRight: "left and right",
            turnLeftRightStraight: "Turn left and right",
            turnLeftStraight: "Left straight U turn",
            turnLeftRight: "Turn around",
            no: "no",
            have: "Have",
            ordinary: "ordinary",
            emergency: "Emergency Vehicle Lane",
            nonMotorized: "Non motorized Lane",
            bus: "Bus lane",
            diversion: "Diversion zone",
            single: "one-way street",
            noLefTurn: "No left turn",
            noRightTurn: "No right turn",


        },
        placeholder2: {
            color: "color",
            time1: "Please select time period 1",
            time2: "Please select time period 2",
            time3: "Please select time period 3"

        }
    },
    settingPreview: {
        text: {
            refreshPic: "refreshPic",
            sendConfig: "sendConfig"
        }
    },
    settingSignal: {
        image: {
            configImg: "../../img/404.png"
        },
        text: {
            refreshPic: "Refresh",
            saveConfig: "Save",
            clearNowConfig: "Clear",
            showConfig: "Reset",
            lightInfo: "Signal light information",
            left: "left",
            straight: "straight",
            right: "right",
            pedestrian: "pedestrian",
            delay: "Red light running delay judgment：",
            draw: "Draw semaphore",
            biggerBtn: "Partial zoom",
            signalBtn: "Draw semaphore",
            num: "Number of semaphores：",
            define: "Semaphore definition",
            style: "style：",
            solid: "solid line",
            dashed: "dotted line"
        },
        title: {
            signalJudge: "Enable traffic light judgment",
            extendSignal: "External traffic light signal",
            signalStronger: "Red light strengthened",
            signalYellow: "Night yellow light time setting",

        },
        clazz: {
            begin: "from:",
            to: "to:",

        },
        placeholder: {
            signalColor: "color",
            signalTime: "S"

        },
        lightList: [
            {
                name: "Lamp 1",
                id: "type_1"
            },
            {
                name: "Lamp 2",
                id: "type_2"
            },
            {
                name: "Lamp 3",
                id: "type_3"
            },
            {
                name: "Lamp 4",
                id: "type_4"
            },
            {
                name: "Lamp 5",
                id: "type_5"
            },
            {
                name: "Lamp 6",
                id: "type_6"
            }
        ],
        signalTypeList: [
            [
                {
                    name: "Single headlight",
                    value: "0"
                },
                {
                    name: "Three headlights",
                    value: "2"
                },
                {
                    name: "Four headlights",
                    value: "3"
                },
                {
                    name: "Forced long red",
                    value: "1"
                },
                {
                    name: "Strengthen only",
                    value: "4"
                }
            ],
            [
                {
                    name: "Left row",
                    value: "0x0001"
                },
                {
                    name: "straight",
                    value: "0x0002"
                },
                {
                    name: "Right row",
                    value: "0x0004"
                },
                {
                    name: "Turn around",
                    value: "0x0008"
                },
                {
                    name: "Straight left",
                    value: "0x0003"
                },
                {
                    name: "Right straight",
                    value: "0x0006"
                },
                {
                    name: "Turn left",
                    value: "0x0009"
                },
                {
                    name: "left and right",
                    value: "0x0005"
                },
                {
                    name: "U-turn straight left",
                    value: "0x000f"
                },
                {
                    name: "U-turn straight",
                    value: "0x000b"
                },
                {
                    name: "U-turn",
                    value: "0x000d"
                }
            ],
            [
                {
                    name: "Round lights",
                    value: "0"
                },
                {
                    name: "Digital light",
                    value: "1"
                },
                {
                    name: "Arrow light",
                    value: "2"
                },
                {
                    name: "Turn left and turn around",
                    value: "3"
                },
                {
                    name: "other",
                    value: "4"
                }
            ],
            [
                {
                    name: "Motor vehicle signal lights",
                    value: "0"
                },
                {
                    name: "Lane signal",
                    value: "1"
                },
                {
                    name: "Direction indicator",
                    value: "2"
                },
                {
                    name: "Flashing siren signal",
                    value: "3"
                },
                {
                    name: "Countdown digital display",
                    value: "4"
                },
                {
                    name: "Non-motor vehicle signal lights",
                    value: "5"
                },
                {
                    name: "Pedestrian crossing signal",
                    value: "6"
                },
                {
                    name: "other",
                    value: "7"
                }
            ]
        ]
    },
    setStatus: {
        image: {
            img1: "./../../img/turnLeft.png",
            img2: "./../../img/goStraight.png",
            img3: "./../../img/turnRight.png",
            img4: "./../../img/turnAround.png",
            img5: "./../../img/people.png",
            left_light: "./../../img/green.png",
            straight_light: "./../../img/green.png",
            right_light: "./../../img/green.png",
            return_light: "./../../img/green.png",
            people_light: "./../../img/green.png",
        },
        text: {
            status: "Traffic light status"
        }
    },
    setSurvey: {
        text: {
            refreshPic: "Refresh",
            saveConfig: "Save",
            clear: "Clear",
            show: "Reset",
            plateSize: "License plate size",
            biggerBtn: "Local zoom",
            min: "License plate minimum width：",
            max: "License plate maximum width：",
            targetDetectionArea: "Target detection zone",
            surveyBtn: "Drawing detection",
            violationDetectionArea: "Violation detection zone",
            noParkingBtn: "Violation detection",
            dividingLine: "Left straight right dividing line",
            turnLeftBtn: "Turn left line",
            goStraightBtn: "Straight dividing line",
            turnRightBtn: "Turn right line",
            cartTurnRightBtn: "Crane right transfer line",
            queueCalibration: "Queue length calibration line",
            queueLengthText: "Queue length (m)：",
            queueLengthText1: "Queue length (m)：",
            upCalibrationBtn: "Uplink calibration line",
            downCalibrationBtn: "Downlink calibration line",
            turnDetectionArea: "U-turn detection area",
            aroundBtn: "U-turn detection",
            oppositeStraight: "Direct straight detection area",
            faceBtn: "Go straight"

        },
        clazz: {
            type: "style：",
            solid: "solid line",
            dashed: "dotted line",
            kind: "species：",
            ordinary: "ordinary",
            sidewalk: "sidewalk",
            Gridlines: "Gridlines"

        },
        placeholder: {
            surveyColor: "color",
            noParkingColor: "color",
            turnLeftColor: "color",
            goStraightColor: "color",
            turnRightColor: "color",
            cartTurnRightColor: "color",
            upCalibrationColor: "color",
            downCalibrationColor: "color",
            aroundColor: "color",
            faceColor: "color"
        }
    },
    settingLineSurvey: {
        text: {
            refreshPic: "refresh",
            saveConfig: "Save",
            clearNowConfig: "Clear",
            showConfig: "Reset",
            attributes: "Lane line related attributes",
            lineBtn: "Draw lane lines",
            lane: "Number of lanes：",
            begin: "Start lane：",
            left: "Left>right",
            right: "right>Left",
            floorBtn: "Virtual ground sense",
            peopleBtn: "Yield Here To Peds",
            straightLine: "straight line",
            ZLine: "Z line",
            modeSelection: "mode selection：",
            clear: "Clear",
            show: "Reset",
            plateSize: "License plate size",
            biggerBtn: "Local zoom",
            min: "License plate minimum width：",
            max: "License plate maximum width：",
            targetDetectionArea: "Target detection zone",
            surveyBtn: "Drawing detection",
            violationDetectionArea: "Violation detection zone",
            noParkingBtn: "Violation detection",
            dividingLine: "Left straight right dividing line",
            turnLeftBtn: "Turn left line",
            goStraightBtn: "Straight dividing line",
            turnRightBtn: "Turn right line",
            cartTurnRightBtn: "Crane right transfer line",
            queueCalibration: "Queue length calibration line",
            queueLengthText: "Queue length (m)：",
            queueLengthText1: "Queue length (m)：",
            upCalibrationBtn: "Uplink calibration line",
            downCalibrationBtn: "Downlink calibration line",
            turnDetectionArea: "U-turn detection area",
            aroundBtn: "U-turn detection",
            oppositeStraight: "Direct straight detection area",
            faceBtn: "Go straight"
        },
        clazz: {
            direction: "direction：",
            definition: "Driving definition：",
            waitArea: "Waiting area：",
            property: "Special properties：",
            real: "solid line",
            false: "dotted line",
            redStopLine: "red stop line",
            line0: "Lane Line 0 Attribute",
            line1: "Lane Line 1 Attribute",
            line2: "Lane Line 2 Attribute",
            line3: "Lane Line 3 Attribute",
            line4: "Lane Line 4 Attribute",
            line5: "Lane Line 5 Attribute",
            virtualGround: "Virtual ground sense detection line",
            pedestrian: "Polite pedestrian detection area",
            up: "Up",
            down: "Down",
            left1: "Left row",
            straight: "straight",
            right1: "Right row",
            turnAround: "Turn around",
            leftStraight: "left straight",
            rightStraight: "right straight",
            turnLeft: "Turn left",
            leftRight: "left and right",
            turnLeftRightStraight: "Turn left and right",
            turnLeftStraight: "Left straight U turn",
            turnLeftRight: "Turn around",
            no: "no",
            have: "Have",
            ordinary: "ordinary",
            emergency: "Emergency Vehicle Lane",
            nonMotorized: "Non motorized Lane",
            bus: "Bus lane",
            diversion: "Diversion zone",
            single: "one-way street",
            noLefTurn: "No left turn",
            noRightTurn: "No right turn",
            type: "style：",
            solid: "solid line",
            dashed: "dotted line",
            kind_survey: "species：",
            sidewalk: "sidewalk",
            Gridlines: "Gridlines"


        },
        placeholder2: {
            color: "color",
            time1: "Please select time period 1",
            time2: "Please select time period 2",
            time3: "Please select time period 3",
            surveyColor: "color",
            noParkingColor: "color",
            turnLeftColor: "color",
            goStraightColor: "color",
            turnRightColor: "color",
            cartTurnRightColor: "color",
            upCalibrationColor: "color",
            downCalibrationColor: "color",
            aroundColor: "color",
            faceColor: "color"

        }
    },
    setSystem: {
        text: {
            parameter: "Identification parameter configuration",
            threshold: "Detection threshold：",
            recognitionThreshold: "Identification threshold：",
            chinaCode: "Regional Chinese character code：",
            code: "Regional alphabet code：",
            crossing: "Intersection parameter configuration",
            crossingName: "Intersection name：",
            direction: "Intersection direction：",
            direction2: "East to West",
            direction3: "West to east",
            direction4: "South to North",
            direction5: "North to South",
            direction6: "Northeast to southwest",
            direction7: "Southwest to northeast",
            direction8: "Northwest to southeast",
            direction9: "Southeast to northwest",
            roadNum: "Road number：",
            num: "Intersection number",
            customNumber: "custom Number",
            NationalStandardNumber: "National Standard Number",
            province: "Please select province",
            city: "Please select a city",
            township: "Please select county/district",
            speedConfiguration: "Speed configuration",
            speedMeasurementMode: "Speed measurement mode：",
            video: "video",
            radar: "radar",
            all: "Video + radar",
            kindOfRadar: "Radar type：",
            no: "no",
            andao: "Andorra Radar",
            chuansu: "Sichuan Speed Microwave",
            huichang: "Huichang Radar",
            correctionFactor: "Correction factor：",
            height: "Camera height (cm)：",
            horizontalDistance: "Horizontal distance between the camera and the lower edge of the detection area (cm)：",
            downDistance: "Horizontal distance between the camera and the upper edge of the detection area (cm)：",
            speed1: "Lane 1：",
            speed2: "Lane 2：",
            speed3: "Lane 3：",
            speed4: "Lane 4：",
            speed5: "Lane 5：",
            // AIMode: "AI model",
            AIMode: "Log printing level",
            // AIModes: "AI model：",
            AIModes: "Log printing level：",
            mode1: "Model 1",
            mode2: "Model 2",
            eventImageCoding: "Event image coding parameter configuration",
            codingQuality: "Coding quality：",
            saveSystem: "save",
            resetSystem: "reset",
            laneSpeedConfig: "Lane speed limit setting(km/h)"
        },
        title: {
            personalizedPlate: "Personalized license plate opening",
            embassyPlate: "Embassy license plate opened",
            consulatePlate: "Consulate license plate opened",
            speedConfig: "Speed detection enable"
        },
        clazz: {
            lowSpeed: "Capture speed (low)：",
            heightSpeed: "Capture speed (high)：",
            lowLimitSpeed: "Speed limit (low)：",
            heightLimitSpeed: "Speed limit (high)：",
        }
    },
    systemConfig: {
        text: {
            pictureFile: "Picture file",
            saveUIL: "Picture save path：",
            imgBtn: "browse",
            videoFile: "Video files",
            videoSaveUIL: "Video saving path：",
            recordBtn: "browse",
            logFile: "Log file",
            logSaveUIL: "Log save path：",
            logBtn: "browse",
            defaultConfig: "default configuration"
        }
    },
    systemInfo: {
        text: {
            basicInformation: "Basic information",
            name: "name：",
            Device_GUID: "device GUID：",
            Equipment_type: "device type：",
            manufacturers: "manufacturer：",
            Hardware_version: "hardware ver：",
            Software_version: "Software ver：",
            Communication_version: "communication ver：",
            Configuration_version: "configuration ver：",
            Authorization_status: "auth version：",
            Authorization_time: "Authorization expiration time：",
            Algorithm_engine: "algorithm engine：",
            NNIE_engine: "NNIE engine：",
            Plugin_version: "ocx version：",
            web_ver: "web version：",
            saveDevice: "save",
            resetDevice: "reset"
        }
    },
    systemMaintain: {
        text: {
            equipmentMaintenance: "Equipment maintenance",
            importExport: "import/export",
            realTimeLogInfo: "No information yet",
            saveRealTimeLogInfo: "save real time logInfo",
            getRealTimeLog: "get real time log",
            logInfo: "No information yet",
            saveLogInfo: "ave information",
            getLog: "Get resident log",
            getLogS: "获取日志分析",
            deviceRestart: "restart device",
            restartDevice: "restart algorithm",
            restart: "The remote control device restarts immediately",
            saveReboot: "save",
            reset: "reset",
            simplyResume: "Simple algorithm recovery",
            defaults: "Algorithm is restored to default",
            configurationData: "configuration data",
            importConfigBtn: "import config",
            configuration: "Import settings configuration from backup file",
            exportConfig: "Export configuration file",
            load: "Export all configuration of the device to a local file (the default save path is the browser download path)"
        },
        clazz: {
            realTimeLog: "real time log",
            residentLog: "resident log",
            SLog: "重启日志分析",
            exportYUV: '导出YUV'
        },
        title: {
            autoRestart: "auto restart"
        },
        placeholder: {
            setTimeValue: "set time value"
        }
    },
    systemStatus: {
        text: {
            deviceStatusInformation: "Device status information",
            deviceUID: "device uid",
            environmentVariableStatus: "Environment variable status",
            productFeatures: "product function",
            hardDiskType: "hdd type",
            partitionInformation: "partition mask",
            systemInformation: "Hard disk file system information",
            dataAreaStatusInformation: "Data area status information",
            logStatus: "Log area status information",
            checked: "disk checking",
            format: "disk formatings",
            fileSystemStatus: "fs status",
            capacity: "hdd capacity",
            diskUsage: "Hard disk usage",
            SDCard: "SD card device",
            SDStatus: "SD card status",
            SDCapacity: "SD card capacity",
            SDUsage: "SD card usage rate",
            SDDataNumber: "Amount of backup data",
            FTPIP: "FTP IP address",
            FTPport: "FTP port number",
            FTPStatus: "FTP connection status",
            FTPNum: "Statistics of successful access to FTP ",
            FTPNumFailure: "Statistics of failed FTP access",
            lastLogin: "Last successful login time",
            lastVisit: "Last successful time",
            lastAccessFailed: "Last visit failed time",
            configurationStatus: "Configuration status",
            analysisStatus: "Analysis status",
            externalTrafficLightStatus: "External traffic light status",
            radarStatus: "Radar status",
            currentTime: "Device current time",
            NTPAddress: "NTP host address",
            NTPStatus: "NTP connection status",
            lastTimeNTP: "Last successful NTP time",
            cameraType: "Camera type",
            cameraIP: "Camera IP address",
            commandConnectionPort: "Command connection port",
            dataPort: "Data connection port",
            commandConnectionStatus: "Command connection status",
            dateStatus: "Data connection status",
            correctlyReceivedFrames: "The number of consecutive correctly received frames",
            acceptFrameStatus: "Accept frame status",
            lastConfigurationTime: "Last successful configuration time",
            lastSuccessfulTime: "Time of last successful synchronization",
            lastCameraPictureTime: "Last camera picture time",
            lastVideoTime: "Time of the last saved video",
            lastBayonetTime: "Time of the most recent recording",
            recentViolation: "Time of the most recent violation",
            videoSaveDay: "Video storage days",
            bayonetSaveDay: "Storage days for bayonet events",
            violationSaveDay: "Days of storage of violations",
            leftLane: "Congestion of the left lane",
            straightLane: "Congestion state of straight lanes",
            rightLane: "Congestion in the right lane",
            bayonetEventStatus: "Bayonet event status",
            trafficLightStatus: "Traffic light status",
            fresh: "Refresh status",
            inquiry_mode: "Inquiry mode"
        },
        clazz: {
            realTimeStatus: "实时状态",
            historyStatus: "历史状态",
            setHistoryStatus: "频率设置",
            statusAnalysis: "Status analysis",
            time: "Time",
            query: "查询",
            reset: "Reset",
            rate: '频率',
            setRateConfig: '下发配置',
            getRateConfig: '重置配置'
        }
    },
    systemModuleStatus: {
        text: {
            deviceModuleStatusInformation: "Module status information",
            calculation_: "Calculation:",
            trace_: "Trace:",
            encoding_: "Encoding:",
            vpss_: "Vpss:",
            video_record_: "Video record:",
            forwarding_: "Forwarding:",
            console_: "Console:",
            license_: "Authorization：",
            osd_: "OSD：",
            web_: "Web：",
            land_circle_: "Coil：",
            light_signal_: "Signal：",
            NTP_: "NTP：",
            Debug_: "Debug：",
            h264_: "Video creation：",
            h264_synthetic_: "Video synthesis：",
            video_maker_: "Video synthesis：",
            uploader_: "Forwarder：",
            blacklist_: "Blacklist：",
            checkuid_: "Equipment inspection：",
            command_processor_: "Background command processor：",
            remote_access_: "Remote access：",
            vframe_receiver_: "Receive video frame：",
            forward_event_: "Front end events：",
            discoverflaregy_: "Devices detected：",
            venc_h264_: "Video Coding：",
            free_frame_: "Release YUV frame：",
            web_config_: "Page configuration：",
            web_eventServer_: "Page events：",
            merge_: "Identify burst frame：",
            flash_: "Processing burst frames：",
            event_forward_: "Forwarding events：",
            fresh: "Refresh status",
            freshStatusAnalysis: "Refresh status",
            moduleStatus_success: "RUNNING",
            moduleStatus_ready: "READY",
            moduleStatus_sleep: "SLEEP",
            moduleStatus_error: "ERROR",
            moduleStatus_unactive: "UNACTIVATED",
            hasErrCode: '有错误码'
        }
    },
    systemVersion: {
        text: {
            deviceVersion: "Device version information",
            deviceUID: "device uid：",
            softwareVersion: "software ver：",
            hardwareVersion: "hardware ver：",
            lowLevelVersion: "low level version：",
            midLevelVersion: "mid level version：",
            armVersion: "arm framework version：",
            wkmodelVersion: "Wkmodel version：",
            codeloaderVersion: "Codeloader version：",
            aiVersion: "AI Driver Library Version：",
            fresh: "Refresh status"
        }
    },
    videoSignal: {
        text: {
            playParameters: "Playback parameters",
            videoWay: "video stream transmission mode：",
            submitVideo: "Save configuration",
            commonDefault: "reset",
            cameraUsernameLabel: "Camera username",
            cameraPasswordLabel: "Camera password"
        }
    },
    codeArray: [
        {
            name: 'Driving in reverse',
            id: 'nxxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'nxxx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Illegal U-turn',
            id: 'wzdt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wzdt_zfcode',
            zfcode: '0'
        },
        {
            name: 'Red light',
            id: 'chd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Red light（Turn left）',
            id: 'chdl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chdl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Red light（Tirn right）',
            id: 'chdr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chdr_zfcode',
            zfcode: '0'
        },
        {
            name: 'Pressure line',
            id: 'yx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Violation of speed regulations',
            id: 'wfcsgd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfcsgd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Lane change',
            id: 'bd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Take the road',
            id: 'jdxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdxx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Illegal stop',
            id: 'wt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wt_zfcode',
            zfcode: '0'
        },
        {
            name: 'The motor vehicle does not drive in the motor vehicle lane',
            id: 'jdcbzjdcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcbzjdcd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motor vehicles use special lanes in violation of regulations',
            id: 'jdcwfgd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcwfgd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Stuck at intersection',
            id: 'lkzl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lkzl_zfcode',
            zfcode: '0'
        }, {
            name: 'Normal driving',
            id: 'zcxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zcxx_zfcode',
            zfcode: '0'
        }, {
            name: 'No left turn',
            id: 'jzzg_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzzg_zfcode',
            zfcode: '0'
        },
        {
            name: 'No right turn',
            id: 'jzyg_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzyg_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a ban (small cars with license plates from other provinces and cities)',
            id: 'cjlwss_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwss_zfcode',
            zfcode: '0'
        }, {
            name: 'Break ban (Shanghai C)',
            id: 'cjlhc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlhc_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a ban (training car)',
            id: 'cjljlc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjljlc_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 1)',
            id: 'cjlwf1_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf1_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 2)',
            id: 'cjlwf2_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf2_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 3)',
            id: 'cjlwf3_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf3_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 4)',
            id: 'cjlwf4_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf4_zfcode',
            zfcode: '0'
        }, {
            name: 'Breaking a prohibition order (violation type 5)',
            id: 'cjlwf5_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf5_zfcode',
            zfcode: '0'
        }, {
            name: 'Breaking a prohibition order (violation type 6)',
            id: 'cjlwf6_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf6_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 7)',
            id: 'cjlwf7_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf7_zfcode',
            zfcode: '0'
        },
        {
            name: 'Breaking a prohibition order (violation type 8)',
            id: 'cjlwf8_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf8_zfcode',
            zfcode: '0'
        },
        {
            name: 'Truck break ban',
            id: 'hccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'hccjl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Bus break ban',
            id: 'kccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'kccjl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Big cars occupy small lanes',
            id: 'dczyxcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dczyxcd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Trucks occupy passenger lanes',
            id: 'hczykcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'hczykcd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Yield Here To Peds',
            id: 'lrxr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lrxr_zfcode',
            zfcode: '0'
        },
        {
            name: 'Sidewalk illegal stop',
            id: 'rxdwt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'rxdwt_zfcode',
            zfcode: '0'
        }, {
            name: 'Grid line stop',
            id: 'wgxwt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wgxwt_zfcode',
            zfcode: '0'
        },
        {
            name: 'Alternate passage',
            id: 'jttx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jttx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Turn around',
            id: 'dwxz_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dwxz_zfcode',
            zfcode: '0'
        },
        {
            name: 'Turn left and keep going straight',
            id: 'zzbrzx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zzbrzx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Over-the-line parking',
            id: 'yxtc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yxtc_zfcode',
            zfcode: '0'
        },
        {
            name: 'U-turn on zebra crossing',
            id: 'bmxdt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bmxdt_zfcode',
            zfcode: '0'
        },
        {
            name: 'U-turn affects normal traffic',
            id: 'dtyxtx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dtyxtx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Turn right is not courteous to non-motorized vehicles',
            id: 'yzblr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yzblr_zfcode',
            zfcode: '0'
        },
        {
            name: 'No trucks are allowed on the ramp',
            id: 'jzhcszd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzhcszd__zfcode',
            zfcode: '0'
        }, {
            name: 'Do not drive lights',
            id: 'bkcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bkcd_zfcode',
            zfcode: '0'
        }, {
            name: 'Continuous lane change',
            id: 'lxbd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lxbd_zfcode',
            zfcode: '0'
        }, {
            name: 'Not wearing a seat belt',
            id: 'wxaqd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wxaqd_zfcode',
            zfcode: '0'
        },
        {
            name: 'call phone',
            id: 'dsj_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dsj_zfcode',
            zfcode: '0'
        },
        {
            name: 'Change lanes without turning lights',
            id: 'bdbsyzxd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bdbsyzxd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Illegal occupation of emergency lane',
            id: 'wfzyyjcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfzyyjcd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motor vehicles occupy non-motor vehicle lanes',
            id: 'jzf_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzf_zfcode',
            zfcode: '0'
        },
        {
            name: 'Jump a queue',
            id: 'js_code',
            code: '0',
            priority: '00',
            id_zfcode: 'js_zfcode',
            zfcode: '0'
        },
        {
            name: 'Turn signal not used',
            id: 'zwbsyzxd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zwbsyzxd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non motor vehicle running red light',
            id: 'fjdcchd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcchd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non motor vehicle retrograde',
            id: 'fjdcnx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcnx_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non motor vehicle breaking ban',
            id: 'fjdccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdccjl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non motor vehicles without helmets',
            id: 'fjdcbdtk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcbdtk_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non motor vehicle bayonet',
            id: 'fjdkk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdkk_zfcode',
            zfcode: '0'
        },
        {
            name: 'Dangerous chemical vehicle',
            id: 'whpc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'whpc_zfcode',
            zfcode: '0'
        },
        {
            name: 'Overtake by passing',
            id: 'jdcc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcc_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycles without helmets',
            id: 'fjdc_passenger_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_passenger_zfcode',
            zfcode: '0'
        },
        {
            name: 'Mopeds carrying people',
            id: 'fjdc_overload_blue_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_blue_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motor vehicles other than buses and trucks carrying passengers and exceeding passengers',
            id: 'fjdc_overload_yellow_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_yellow_zfcode',
            zfcode: '0'
        },
        {
            name: 'Non-motorized vehicle carrying people',
            id: 'fjdc_overload_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_zfcode',
            zfcode: '0'
        },
        {
            name: 'Traffic restriction',
            id: 'weihaoxianxing_code',
            code: '0',
            priority: '00',
            id_zfcode: 'weihaoxianxing_zfcode',
            zfcode: '0'
        },
        {
            name: 'Illegal loading of goods',
            id: 'wfzh_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfzh_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycle violation ban',
            id: 'motocjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motocjl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycle checkpoint',
            id: 'motokk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motokk_zfcode',
            zfcode: '0'
        },
        {
            name: 'Foreign motorcycle violation ban',
            id: 'motowpcjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motowpcjl_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycle running red light',
            id: 'motochd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motochd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycles do not follow the guiding lane when driving',
            id: 'motojd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motojd_zfcode',
            zfcode: '0'
        },
        {
            name: 'Motorcycle pressing line',
            id: 'motoyx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motoyx_zfcode',
            zfcode: '0'
        }
    ],
    help: {
        briefIntroduction: "brief introduction",
        functionDescription: "Function description",
        preview: "preview",
        event: "event",
        search: "search",
        snap: "Snap",
        configuration: "Configuration",
        system: "system",
        internet: "internet",
        video: "video",
        image: "image",
        disk: "disk",
        algorithm: "algorithm",
        eventDetection: "event detection",
        generalFunctions: "general functions",
        alarm: "multi function electric alarm",
        bayonetPolice: "bayonet police",
        ramp: "ramp alarm",
        version: "Version number v1.0",
        helpDocumentation: "Web help documentation",
        text: {
            texts: "documentation"
        }
    },
    helpDescription: {
        text: {
            p: "2.Function description",
            p2: "Input the camera IP in the browser interface and add the parameters. http://*************, user name: admin, password: 12345.",
            p3: "The language can be Chinese or English (here is the Chinese version).",
            p4: "After login, the preview interface is the first to enter. If it is the first time to enter the camera, the plug-in will be required to be installed. Only after the plug-in is installed can the camera capture the picture."
        },
        image: {
            descriptionImg1: "./image001_en.png",
            descriptionImg2: "./image003_en.png"
        }
    },
    helpDetect: {
        text: {
            p: "event detection",
            p2: "Algorithm event detection. The standard of event detection is described separately. According to the different functions and scenes of front card and electric police camera, check the illegal events to be detected."
        },
        image: {
            detectImg1: "./image052_en.png",
            detectImg2: "./image053_en.png",
            detectImg3: "./image054_en.png"
        }
    },
    helpDetectAccess: {
        text: {
            p: "Bayonet police",
            p2: "Front card event flashover: you can choose the time of explosion and flash, as well as the individual flash of each lane.",
            p3: "Detection of vehicle speed violation: use with \"system configuration\" - speed configuration.",
            p4: "If the lamp cannot be turned on, it will be detected automatically.",
            p5: "Detection of seat belts: automatic detection.",
            p6: "Detection of mobile phone: automatic detection.",
            p7: "Detection of small lanes occupied by large vehicles: set the lane properties, and you can detect a certain Lane separately.",
            p8: "Detection of truck occupied passenger Lane: set Lane attribute, and a lane can be detected separately.",
            p9: "Test the vehicle type to break the ban: all lanes are tested. License plate type can be set separately."
        },
        image: {
            detectAccessImage1: "./image062_en.png",
            detectAccessImage2: "./image063_en.png",
            detectAccessImage3: "./image064_en.png",
            detectAccessImage4: "./image065_en.png",
            detectAccessImage5: "./image066_en.png",
        }
    },
    helpDetectCommon: {
        text: {
            p: "General functions",
            p2: "Detection bayonet: the vehicle passes through the camera and captures a picture of the bayonet.",
            p3: "Detection of vehicles using special lanes in violation of regulations: set in lane line interface, lane line attribute, emergency lane, non motorized vehicle lane, bus lane and other lane attributes. All vehicles passing through the lane are detected. 1, 2 pictures in the lane, 1, 2, 3 pictures should have displacement.",
            p4: "Detection of unlicensed vehicles: unlicensed vehicles include actual \"unlicensed vehicles\" and \"tainted licensed vehicles\" with unclear license plates. Unlicensed vehicles only exit the bayonet, and there are no illegal incidents. Unlicensed vehicles should also have movement, and stopped vehicles will not be detected.",
            p5: "Detection line pressing: according to the actual situation, check the lane line number in the lane line setting, and check the corresponding lane line to be detected. License plate touching lane line detection, detection license plate, wheel or a small part of the body line does not grasp. The electric alarm pressure line is below the stop line and does not exceed the stop line.",
        },
        image: {
            commonImage1: "./image055_en.png"
        }
    },
    helpDetectMultiple: {
        text: {
            d: "Multi function electric alarm",
            d2: "Detection of red light running: vehicles violate traffic lights. According to the situation of vehicle track and indicator light, the lane attribute is not recognized as straight or left turn. All three are red lights.",
            d3: "Detection of intersection detention: through lane, first, second picture green light, third picture red light. The first graph is in front of the stop line, and the second graph passes through the stop line, but goes straight to the judgment line. The vehicles ahead are congested, and the stranded vehicles affect the road traffic.",
            d4: "Detection retrograde: the first one is not required, the second and the third one are in the lane.",
            d5: "Detection of large turns and small turns: the vehicle does not pass through the center of the intersection and turns left at the zebra crossing. Set up illegal parking detection area, and choose the type of sidewalk.",
            d6: "Detection of left turn not allowed to go straight: the detection area is drawn opposite to the straight line detection area, and the left turn judgment line is drawn next to the opposite detection area. When the left turning vehicle touches the left turning judgment line, there are straight vehicles in the opposite detection area.",
            d7: "Detection of zebra crossing U-turn: set up illegal stop detection area, the type is sidewalk, and set turn detection area. It is necessary to ensure that the license plate of turning vehicle can pass through the detection area and the license plate is clear",
            d8: "Continuous lane change detection: vehicles change lanes from Lane 1 to Lane 2 and then Lane 3. Three pictures in three lanes.",
            d9: "Detection of illegal lane change: set the lane line to be photographed, and detect and capture the left and right lanes of the lane line. The first one is in the first lane, the second one is in the lane next to it. Within the scope of the solid line, the lane line shall prevail, and the stop line shall not be determined.",
            d10: "Illegal lane detection: left lane, straight lane, right lane, Lane property, movement direction, no indicator light.",
            d11: "Detection of over line parking: the three pictures are all red lights, and the vehicle does not pass through the straight line dividing line. The first one is below the stop line, the second and the third are above the stop line. Second, in the third picture, there is no displacement of the vehicle. You can choose the time interval between the second and third pictures. Above the stop line, 25% of the screen height is the detection area.",
            d12: "Detection of illegal parking: three pictures are in the illegal parking detection area. Ordinary, illegal stop. The sidewalk, the sidewalk stopped illegally. Gridlines, gridlines stop. You can select the interval between the three pictures and check the congestion detection, because the illegal stop caused by congestion will not be captured."
        },
        image: {
            multipleImg1: "./image056_en.png",
            multipleImg2: "./image057_en.png",
            multipleImg3: "./image058_en.png",
            multipleImg4: "./image059_en.png",
            multipleImg5: "./image060_en.png",
            multipleImg6: "./image061_en.png"
        }
    },
    helpDetectRamp: {
        text: {
            p: "Ramp police",
            p2: "Detection barring ban (Shanghai C): Testing Shanghai C license.",
            p3: "Detection of intrusion prohibition (coach car): detection of coach car.",
            p4: "It is forbidden to go on the ramp when testing trucks: testing trucks of different types.",
            p5: "Intrusion detection ban (small cars in other provinces and cities): can be set by day and time.",
            p6: "Intrusion detection ban (blacklist): used with industrial computers or other programs, you can set 8 types."
        },
        image: {
            rampImg1: "./image067_en.png",
            rampImg2: "./image068_en.png",
            rampImg3: "./image069_en.png"
        }
    },
    helpEvent: {
        text: {
            p: "event",
            P2: "This interface can view the event pictures and license plate numbers captured by the camera. Tick \"Receive events\" in the upper right corner to receive the event pictures captured by the camera. Click the play button in the upper left corner to view the camera screen in real time.",
            p3: "On the left is a close-up view of the license plate, and the recognized license plate characters. In the middle are the captured pictures of the vehicle bayonet and the illegal pictures. If you click \"Hide Picture\" in the upper right corner, the picture will not be displayed. Below is a snapshot of the vehicle information, scrolling continuously according to time.",
            p4: "Note: This feature requires the installation of plug-ins"
        },
        image: {
            eventImage1: "./image007_en.png"
        }
    },
    helpPicture: {
        text: {
            p: "Snap",
            p2: "Capture interface, click the play button in the lower right corner, the camera will capture real-time pictures, this interface is mainly used to check the focal length of the lens when debugging the focal length. Through the mouse wheel or keyboard CTRL+ up and down, you can zoom in and out the screen, easy to use when debugging."
        },
        image: {
            picturesImage1: "./image011_en.png"
        }
    },
    helpPreview: {
        text: {
            p: "Preview",
            p2: "Download the plug-in, close the browser when installing the plug-in, open the browser after installation, and enter the camera IP. Enter the preview interface, this interface is the real-time camera screen. Through this interface, when installing the camera, you can adjust the angle of the picture captured by the camera.",
            p3: "Note: This feature requires the installation of plug-ins"
        },
        image: {
            helpPreviewImage1: "./image005_en.png"
        }
    },
    helpSearch: {
        text: {
            p: "search",
            p2: "This interface can view the historical events of the snapshot, according to the time, license plate number, event type, license plate type and other information, to accurately search for vehicles and license plates.",
            p3: "Note: This feature requires the installation of plug-ins"
        },
        image: {
            helpSearchImager1: "./image009_en.png"
        }
    },
    helpSetting: {
        text: {
            p: "2.Function description",
            p2: "The configuration interface allows you to configure various camera parameters."
        }
    },
    helpSettingAlgorithm: {
        text: {
            p: "algorithm",
            p2: "detection area",
            p3: "The detection area is configured with the size of the license plate, the target detection area, the parking detection area, the left-right boundary, the U-turn detection area, and the opposite straight detection area. Click the setting of each detection area. There are 4 blue marks in the detection area to indicate the range of the detection area. You can move the blue mark to move the detection area and zoom in and out at the same time.",
            p4: "Target detection area: This area is the detection area setting for all illegal types, which is used to detect the recognition results of vehicle trajectory and number plate. Under normal circumstances, the setting range should not be greater than 2/3 of the size of the image, otherwise the camera will not run Snapshot. To confirm whether the detection area is set normally, you can check the running status through the system --- equipment status, or directly observe whether the bayonet picture can be captured normally as the setting basis.",
            p5: "Violation detection area: Violation area setting, currently mainly for (detained crosswalk line/grid line area, set the area for borrowing sidewalk U-turn according to the capture requirements when the intersection turns and illegally snapped), set according to actual needs The size of the crosswalk line or grid line captured can be used as the basis.",
            p6: "Left, straight and right dividing line (left turn line orange, straight line rose red, right turn line green): left and right turn dividing line, its purpose is to detect the left and right turns of the vehicle, so it should try to cover the left turn Or the steering route of a right-turning vehicle. At the same time, it is necessary to ensure that the straight-going vehicle does not touch the two steering dividing lines. It should fit the trajectory of most vehicles as much as possible, and the position of the front of the vehicle when the vehicle is about to turn left or right To draw a left and right turn trigger line.",
            p7: "The purpose of the straight trigger line is to detect whether the vehicle is running straight and whether it runs a straight red light. Therefore, the straight trigger line must ensure that the following conditions are met:",
            p8: "•\t Should try to cover the route of vehicles going straight through the intersection to ensure that all straight vehicles can be detected;",
            p9: "•\t The trigger line should not be too long to prevent left and right turning vehicles from touching the straight trigger line;",
            p10: "•\t At the position of the trigger line for the vehicle to go straight, the pixel value and image quality of the license plate must meet the recognition requirements, mainly that the horizontal pixel value cannot be lower than 80pix.",
            p11: "U-turn detection area: This area is currently mainly aimed at capturing the illegal type of U-turn at the intersection, and frame selection of the detection area after U-turn vehicles enter the opposite lane.",
            p12: "Opposing straight detection area: This area is currently mainly used to set the trajectory of the vehicle driving when the left turn is not allowed to go straight. The specific effect needs to be adjusted according to the actual capture effect.",
            p13: "Lane line",
            p14: "In most cases, draw lane lines from left to right and bottom to top based on the current lane, and define lane attributes (lane type, lane number, and lane number) based on actual lane conditions.",
            p15: "Lane line related attributes: draw a line according to the actual lane on the screen, from the bottom edge of the screen to the stop line. The starting lane can choose any lane. Draw lane lines from left to right. Lane line 1 has no attributes, and lane line 2 defines the left lane attribute \"Left\", including driving direction, lane direction attributes, and lane attributes. Lane 3 defines the left lane attribute \"right straight\". Lane 4 defines the attribute \"non-motorized\" of the left lane.",
            p16: "Virtual coil: This area is mainly for the setting of virtual detection coils when the video vehicle detector function is used. According to the actual width of each lane, draw 2 tripwires in the current lane. The purple line segment, from bottom to top, the bottom is line 1, and the top is line 2.",
            p17: "Polite Pedestrian: When setting the Polite Pedestrian detection area, it should be selected according to the principle of drawing from the left to the right from the 1st lane. The detection area of each lane should be set as far as possible to ensure that the width of the left and right is more than half the lane. Ensure to detect the range of pedestrian movement trajectory to facilitate illegal judgment.",
            p18: "Red stop line: draw z-shaped or straight stop line according to the actual stop line. When drawing a parking line, the second illegal vehicle must capture the illegal evidence collection requirements after the parking line (running a red light, guiding the lane, etc.), and the drawn stop line should be as far as possible higher than the actual parking line by 1 license plate pixel.",
            p19: "Signal light",
            p20: "Signal light information：",
            p21: "   （1）Configure \"Enable Traffic Light Judgment-Video Detection\" or \"External Traffic Light Signal-External Red Light Signal Detection\" according to the actual situation;",
            p22: "   （2）When setting the external red light signal, according to the port access situation of the red light detector, configure the \"left, straight, right, pedestrian\" red light access port (currently most of the red light signal left transfer port 1, direct connection Port 2, right transfer port 3, pedestrian red light connection port);",
            p23: "   （3）The red light running delay judgment, the default is 3, adjust according to the actual situation.",
            p24: "   （4）For scenes where the red light signal is not obvious, you can check \"Red light enhancement\". There are 4 levels in total, with 4 being the highest.",
            p25: "   （5）Set the night yellow light time according to the actual yellow light time.",
            p26: "Draw the semaphore:",
            p27: "Click to zoom in part to enlarge the image of the signal light, and then click to draw the signal light. The detection area contains the signal light.",
            p28: "Frame the traffic lights in the image screen and fill in the corresponding configuration parameters, the camera can analyze and judge the real-time status of the traffic lights through the live screen. According to the actual needs of the site and the number of traffic lights that need to be set, set the number of red lights, and configure the red light attributes correctly below.",
            p29: "Semaphore definition:",
            p30: "Select the type of signal light according to the actual situation, and the direction of the signal light. For round head red lights, the category should be configured as \"motor vehicle signal lights\", and for arrow lights, the category should be configured as \"direction indicator lights\".",
            p31: "Traffic light status",
            p32: "Check the status of traffic lights detected by the camera in real time.",
            p33: "Event detection",
            p34: "According to the front card or the police camera, select the event that needs to be detected and captured. All event types, detection conditions and other options are described separately later.",
            p35: "System Configuration",
            p36: "Local Chinese character code, regional letter code, according to the camera installation location, select the main Chinese characters and letters of the local license plate.",
            p37: "The intersection name, intersection direction, road number, and intersection number can be entered according to the actual road name.",
            p38: "Speed test, video or radar speed measurement. The camera height and the horizontal distance between the camera and the detection area are filled in according to the actual distance. You can set a separate speed limit for each lane.",
            p39: "Event image encoding parameter configuration, the encoding quality defaults to 80, a large value is good for picture quality, a large picture size, a small value, a poor picture quality, and a small picture size. You can choose an appropriate value according to actual needs.",
            p40: "Traffic extended parameters",
            p41: "Adjust the various parameters of the camera capture event, it is best not to change, it is recommended to use the default value.",
            p42: "Illegal code",
            p43: "According to the requirements of various places, enter the illegal code of the traffic police platform to set the priority of the illegal event.",
            p44: "Configuration preview",
            p45: "After all parameter modification, save, you must go to the configuration preview interface to click \"Send Configuration\" to send the modified and saved parameters to the camera."
        },
        image: {
            images1: "./image034_en.png",
            images2: "./image035_en.png",
            images3: "./image036_en.png",
            images4: "./image037_en.png",
            images5: "./image038_en.png",
            images6: "./image039_en.png",
            images7: "./image040_en.png",
            images8: "./image041_en.png",
            images9: "./image042_en.png",
            images10: "./image043_en.png",
            images11: "./image044_en.png",
            images12: "./image045_en.png",
            images13: "./image046_en.png",
            images14: "./image047_en.png",
            images15: "./image048_en.png",
            images16: "./image049_en.png",
            images17: "./image050_en.png",
            images18: "./image051_en.png",
        }
    },
    helpSettingDisk: {
        text: {
            P: "Disk",
            p2: "Disk: function is under development..."
        }
    },
    helpSettingImage: {
        text: {
            p: "Image",
            P2: "Event OSD overlap: configure the character overlap of the event."
        },
        image: {
            imagesImage1: "./image033_en.png"
        }
    },
    helpSettingInternet: {
        text: {
            p: "internet",
            p2: "Advanced applications",
            p3: "Advanced application, you can set the server address of the event storage, and ftp address."
        },
        image: {
            internetImg1: "./image029_en.png"
        }
    },
    helpSettingSystem: {
        text: {
            p: "system",
            p2: "This page displays the main information of the system, and the default page entered is the system information page.",
            p3: "device Information",
            p4: "display software and hardware information of the camera, including various version information of the device, authorization status, etc. You can also modify the name of the device.",
            p5: "device status",
            p6: "display camera information",
            p7: "version Information",
            p8: "display the software version of the camera",
            p9: "equipment maintenance",
            p10: "View camera logs, restart the camera, import and export configuration files and other functions.",
            p11: "Local configuration",
            p12: "Set the storage path for pictures, videos and logs. Click the browse button to select the path folder. The default configuration is a directory differentiated by ip under the TSPlayerFiles directory of the C drive.",
            p13: "Note: This feature requires the installation of plug-ins",
            p14: "User",
            p15: "The function is under development..."
        },
        image: {
            system_img1: "./image013_en.png",
            system_img2: "./image015_en.png",
            system_img3: "./image017_en.png",
            system_img4: "./image019_en.png",
            system_img5: "./image021_en.png",
            system_img6: "./image023_en.png",
        }
    },
    helpSettingVideo: {
        text: {
            p: "video",
            P2: "Video parameters",
            p3: "Set video playback mode."
        },
        image: {
            videos_img1: "./image031_en.png"
        }
    },
    helpSynopsis: {
        text: {
            p: "1. Introduction",
            p2: "Enter the IP address (https://IP address) in the browser, and the operation steps are as follows after successful login:",
            p3: "Step 1: Click \"App Desktop\"> \"ENTER\" in turn, the login interface appears.",
            p4: "Step 2: Log in for the first time, the default account is admin, and the password is 12345.",
            p5: "Step 3: Click \"Login\".",
            p6: "After logging in for the first time, you need to manually click \"click here to download and install\" in the preview box to download and install the playback controls, and you can view the live video after completion. If the control is blocked by the browser, please follow the instructions on the page.",
            p7: "The latest camera configuration interface has been ported to the web interface. You can directly open the web page without using the console to configure the camera parameters in the camera interface.",
            p8: "To enter the web interface, enter the camera IP directly. For example, http://*************, user name admin, password 12345.",
            p9: "Browser: IE11 version or higher, 360 safe browser compatibility mode, 360 speed browser compatibility mode and cheetah browser version 6.5 or higher.",
        },
        image: {
            synopsisImg1: "./image070_en.png"
        }
    }
}
