/**
 * Create by Chelly
 * 2020/7/21
 */

var langMessage = {
    menuTitle: {
        PREVIEW: "预览",
        EVENT: "事件",
        SEARCH: "搜索",
        SNAP: "抓拍",
        CONFIGURATION: "配置",
        AUTHORIZE: "授权",
        HELP: "帮助文档"
    },
    index: {},
    common: {
        skip: "跳过动画",
        error: "错误",
        confirm: "确定",
        cancel: "取消",
        hint: "提示",
        warning: "警告",
        fail: "失败",
        restart: "重启",
        jsonError: "出现意外错误导致请求解析失败",
        restartAlgorithm: "正在重启算法请稍后...",
        checkFolder: "请选择文件夹！",
        browserError: "当前浏览器不支持此方法，请修改浏览器设置！",
        browserNonsupport: "当前浏览器不支持此方法",
        editSuc: "修改成功",
        editFail: "修改失败",
        nonsupportVideo: "当前不支持此视频流！",
        nonsupportLocal: "浏览器不支持localStorage",
        nonsupportSession: "浏览器不支持sessionStorage",
        violationError: "事件类型获取失败!",
        importSuc: "导入成功!",
        importFail: "导入失败!",
        exportSuc: "导出成功!",
        exportFail: "导出失败!",
        exporting: "正在导出",
        saveSuc: "保存成功！",
        saveFail: "保存失败！",
        undeveloped: "暂未开放此功能！",
        wait: "请稍后...",
        netError: "网络出错请重试",
        timeout: "请求超时",
        requestNone: "请求不存在",
        getCacheSize: function (size) {
            return '当前已用存储：' + (size / 1024).toFixed(2) + 'KB'
        },
    },
    login: {
        loginSuc: "登录成功",
        loginFail: "登录失败：用户名或密码错误！",
        text: {
            login: "登录",
            skip: "跳过动画"
        },
        title: {
            rememberMe: "记住密码",
        },
        placeholder: {
            username: "用户名",
            password: "密码"
        }
    },
    ocxGlobal: {
        eventError: '没有事件了！',
        eventId: '事件ID：',
        ocxError: {
            ERROR: "无法使用插件！",
            INIT_FAIL: "视频初始化失败请尝试重播！",
            OPEN_FAIL: "无法打开文件！请联系开发人员。",
            SEARCH_FAIL: "查询出错",
            SELECT_FAIL: "选择失败!",
            WITHOUT_INSTALL: "没有安装插件无法进行操作!",
            MATCH_FAIL: "插件版本过低，无法进行操作！",
            MATCH_FAIL1: "插件版本不匹配无法进行操作！",
            DOWNLOAD: "请点击此处下载插件，安装时请关闭浏览",
            TIMEOUT: "网络异常或连接超时，请稍后",
            PLAY_FAIL: "播放失败，请联系厂家：",
            PLAY_WIDTH_ERROR: "视频宽高错误",
            PLAY_RADIO_ERROR: "不支持的分辨率"
        },
        update: "插件有更新，请点击下载！",
        low: "rpm包中的插件版本低于当前电脑所使用的插件版本，请联系开发人员！",
        versionNone: "未获取到插件版本！",
        username: '用户名',
        password: '密码',
        rtspError: {
            INPUT_ERROR: '相机用户名或密码错误，请重新输入',
            IS_EMPTY: '相机用户名或密码不能为空'
        },
        userPlaceholder: '请输入相机用户名',
        pwdPlaceholder: '请输入相机密码'
    },
    setting: {
        msgArray: [
            '获取配置',
            '设置配置',
            '抓拍图片',
            '状态查询',
            '版本查询',
            '导出实时日志',
            '导出常驻日志',
            '查询红绿灯状态',
            '升级算法固件',
            '恢复算法默认值',
            '重启相机',
            '获取授权令牌',
            '导入授权文件',
            '获取主码流编码格式',
            '获取osd配置',
            '设置osd配置',
            '授权界面获取授权信息',
            '下发黑名单',
            '获取黑名单'
        ],
        saveConfigSuc: '保存成功！<br>（保存配置仅保存，相机配置需下发）',
        freshSceneMsg: '<div>刷新场景不保留未保存的配置，是否确认刷新？</div><input type="checkbox" id="noMsg">不再提醒',
        confirmRestart: "是否确认重启？",
        confirmRestore: "是否确认恢复出厂设置？此项操作不会保留当前的配置！",
        confirmRestoreWithoutInternet: "是否确认恢复出厂设置？此项操作会保留当前的网络配置！",
        confirmRecover: "是否确认简单算法恢复？此项操作会将算法恢复到默认状态，并且重启程序！",
        UNKNOWN: "未知",
        UNAUTHORIZED: "未授权",
        UNKNOWN_AUTHORIZED: "未知授权类型",
        INITIALIZING: "正在初始化",
        INITIALIZED_FAIL: "初始化失败",
        INITIALIZED_SUC: "初始化成功",
        provinceSelect: "请选择省",
        citySelect: "请选择市",
        countySelect: "请选择县/区",
        errorMsg: {
            SPEED_ERROR: "最低时速不能大于最高时速",
            SIGNAL_ERROR: "信号灯宽度不能小于22！",
            FLASH_ERROR: "曝闪灯配置获取失败",
            ERROR: "配置存在以上错误，不能进行下发！",
            LOCAL_ERROR: "本地存储的路径不能设置为空！",
            DATE_ERROR: "日期只能设置在1970~2037年之间，请检查系统时间！",
            TIME_ERROR: "时间有误，请重新设置！"
        },
        tipsMsg: {
            people: "绘制礼让行人请在事件检测页面勾选",
            floor: "绘制虚拟地感请在事件检测页面勾选"
        },
        warning_type: {
            0: '车道过宽',
            1: '车道对应停止线未配置',
            2: '检测区范围过大',
            3: '停止线距离检测区上沿过近'
        },
        warningMsg: "配置存在以上警告，是否确认下发？",
        subMsg: "配置下发成功",
        error_type: {
            0: '检测区不为凸四边形',
            1: '运动分析区不为凸四边形',
            2: '检测区未在运动分析区范围内',
            3: '车牌宽度错误',
            4: '车道数不能为0',
            5: '车道线未配置',
            6: '车道线方向不全一致',
            7: '车道线有相交或次序错误',
            8: '信号灯数不能为0',
            9: '信号灯宽高过大或有重叠',
            10: '停止线超过检测区上沿',
            11: '多停止线不连续或有重叠',
            12: '左转等待区停车线位置不能低于对应停止线',
            13: '违停区不为凸四边形',
            14: '违停区未在运动分析区范围内',
            15: '违停时间范围3~180秒',
            16: '道路编号与道路名称不能为空',
            17: '路口方向、路口编号、道路编号、道路名称均不能含有空格',
            18: '设备名称不能为空',
            19: '速度修正系数范围0.5~2.0',
            20: '相机距检测区上沿距离要大于下沿',
            21: 'resize宽高过大',
            22: 'resize宽高过小',
            23: '品质因数',
            24: '最低限速大于等于最高限速',
            25: '左边界线与右边界线交叉或位置错误',
            26: '直行边界线不能低于左右边界线下沿',
            27: '检测安全带的置信度参数设置错误，请输入1-100之间的数值',
            28: '检测打手机的置信度参数设置错误，请输入1-100之间的数值',
            29: '请配置尾号限行相关参数',
            30: '日期、时间段、车牌等参数不允许为空',
            31: '时间段不允许为空，且结束时间要大于开始时间',
            32: '最多仅支持配置10组规则',
            33: '您已勾选检测卡口事件，请选择卡口图片数量'
        },
        statusInfo: {
            NONE: "无",
            NORMAL: "正常",
            ABNORMAL: "异常",
            NOT_EXIST: "不存在",
            CHECKING: "正在检查",
            FORMATTING: "正在格式化",
            DAMAGED: "文件系统损坏 无法修复",
            LOGIN: "登录",
            DISCONNECT: "断开",
            CROWD: "拥堵",
            EXTREME: "非常",
            product_function: {
                0: "未知"
            },
            data_status: {
                0: "正常",
                1: "数据区异常错误，需要检查",
                2: "磁盘满或目录不存在"
            },
            log_status: {
                0: "正常",
                1: "日志区异常错误，需要检查",
                2: "磁盘满或目录不存在"
            },
            has_hdd: {
                0: "没有挂载硬盘",
                1: "SATA硬盘",
                2: "USB硬盘",
                3: "USB硬盘，且被选定做系统存储"
            },
            disk: {
                1: "数据分区",
                2: "交换分区",
                4: "MISC分区",
            },
            has_sd: {
                0: "SD卡不存在",
                1: "检测到SD卡",
                2: "检测到SD卡，且被选定SD卡做存储",
                "-1": "异常",
            },
            sd_fs_status: {
                0: "SD卡没有被挂载",
                1: "SD卡挂载中"
            },
            config_status: {
                0: "正常",
                "-1": "相机分辨率过大",
                "-2": "信号灯未配置",
                "-3": "检测区配置错误",
                "-4": "车道数为0",
                "-5": "车道过宽或者检测区过大",
                "-6": "信号灯配置错误",
                "-7": "中层错误",
                "-1000": "dsp加载错误",
                "-2000": "未授权",
            },
            extern_light_status: {
                0: "未使用设备",
                1: "正常",
                "-1": "打开设备失败",
                "-2": "读取设备失败",
                "-3": "读取设备超时",
            },
            radar_status: {
                0: "未连接",
                1: "正常"
            }
        }
    },
    analysisFile: {
        handleProgress: function (progress) {
            return "  正在处理，请稍等,已完成" + progress + "%"
        },
        uploadProgress: function (progress) {
            return '上传进度：' + progress + '%'
        },
        uploadError: {
            WITHOUT_ALGORITHM: "文件中未包含算法包无法升级！",
            INCOMPLETE: "升级固件不完整无法升级",
            DAMAGED: "文件损坏！",
            VERSION_LOW: "内核或app版本过低无法升级算法固件！"
        },
        uploadName: {
            FILE_SYSTEM: "文件系统",
            KERNEL: "内核"
        }
    },
    authorization: {
        authInfo: {
            old: '通过申请授权令牌，使用授权令牌（.token）换取对应的授权码文件（.lic）对相机进行授权。',
            new: '通过申请授权令牌，使用授权令牌（.token）换取对应的授权码文件（.lic）对相机进行授权。'
        },
        matchFail: "当前文件夹下没有匹配文件或授权码文件打开错误！",
        matchMultiple: function (fileName) {
            return '匹配到多个相同的授权文件！[' + fileName + ']';
        },
        exportMsg: function (result) {
            return "<i class=\"layui-icon layui-icon-tips\"></i>文件导出为：" + result
        },
        authMsg: {
            UNSUPPORTED: "不支持的文件格式！",
            DAMAGED: "文件损坏请重试！",
            UNKNOWN: "未知错误！",
            EXPORT_FAIL: "导出失败！",
            EXPORT_SUC: "导出成功！",
            IMPORT_FAIL: "导入授权失败！",
            IMPORT_CONFIRM: "此程序已授权，是否重新授权？",
            IMPORT_SUC: "授权成功！（授权重启后生效）是否确认重启？",
            UPLOAD_SUC: "上传成功！",
            UPLOAD_FAIL: "上传失败！",
            NO_SELECTED: "未选择文件！",
            NO_AUTH: "您还未授权"
        },
    },
    drawCanvas: {
        drawMsg: {
            LINE_FIRST: "请先设置车道线",
            ANCHOR_EXCEED: "锚点数量超出",
            AMPLIFY_FIRST: "请先放大再绘制",
            AMPLIFY_ERROR: "放大区域出错",
            SIGNAL_FIRST: "请先设置信号灯数量",
            NOT_SELECT: "未选择所要清除的对象",
            anchorExceedNumber: function (num) {
                return "锚点不能超过" + num + "个"
            },
            lineExceedNumber: function (num) {
                return "线不能超过" + num + "条"
            },
        },

        recover: "恢复原图",
        amplify: "局部放大",
        line: "车道"
    },
    event: {
        frequent: "频繁操作！请稍后！",
        createError: "无法创建文件！请联系厂家。",
        checkTime: '<div style="z-index:999999">接收事件前请确认已校时！</div><input type="checkbox" id="noCheckTime">不再提醒',
        eventTable: {
            time: "触发时间",
            ID: "事件ID",
            EVENT_TYPE: "事件类型",
            LANE_INDEX: "车道号",
            PLATE_TYPE: "车牌类型",
            PLATE_STRING: "车牌号码",
            CAR_SPEED: "速度",
            CAR_COLOR: "车身颜色",
            CAR_TYPE: "车辆类型",
            IMAGE_EVENT_PATH: "事件图片",
            IMAGE_EVENT_X: "事件X",
            IMAGE_EVENT_Y: "事件Y",
            IMAGE_EVENT_W: "事件W",
            IMAGE_EVENT_H: "事件H",
            IMAGE_PRE_PATH: "上一张图",
            IMAGE_PRE_X: "上一张图X",
            IMAGE_PRE_Y: "上一张图Y",
            IMAGE_PRE_W: "上一张图W",
            IMAGE_PRE_H: "上一张图H",
            IMAGE_LAST_PATH: "下一张图",
            IMAGE_LAST_X: "下一张图X",
            IMAGE_LAST_Y: "下一张图Y",
            IMAGE_LAST_W: "下一张图W",
            IMAGE_LAST_H: "下一张图H",
            IMAGE_PLATE_PATH: "车牌图片",
            IMAGE_FEATURE_PATH: "特写",
            CHE_XING: "细分车型",
            preTime: "上一张时间",
            lastTime: "下一张时间",
            featureTime: "特写时间",
            desc: "概述",
        },
        serverDisconnected: "服务器连接断开",
        receptionFail: "事件接收失败",
        text: {
            query: "查询方式",
            startTime: "开始时间",
            endTime: "结束时间",
            plateString: "车牌号码",
            eventType: "事件类型",
            plateType: "车牌类型",
            submit: "提交",
            reset: "重置",
            exportBtn: "导出当前事件",
            trigger: "触发时间为相机时间"
        },
    },
    extendParams: {
        object: {
            iMatch_X_Threshold: {

                desc1: 'X距离阈值',
                desc2: '取值范围(1-12000) 当前默认值400 读写权限：可读可写'
            },
            iMatch_Y_Threshold: {

                desc1: 'Y距离阈值',
                desc2: '取值范围(1-12000) 当前默认值600 读写权限：可读可写'
            },
            iGray_Threshold: {

                desc1: '灰度阈值',
                desc2: '取值范围(1-255) 当前默认值15 读写权限：可读可写'
            },
            iPlate_Blue_Grad_Threshold: {
                desc1: '蓝牌梯度阈值',
                desc2: '取值范围(1-300) 当前默认值100 读写权限：可读可写'
            },
            iPlate_Yellow_Grad_Threshold: {
                desc1: '黄牌梯度阈值',
                desc2: '取值范围(1-300) 当前默认值100 读写权限：可读可写'
            },
            iPlate_Blue_Skip_Threshold: {
                desc1: '蓝牌跳帧数',
                desc2: '取值范围(0-300) 当前默认值1 读写权限：可读可写'
            },
            iPlate_Yellow_Skip_Threshold: {
                desc1: '黄牌跳帧数',
                desc2: '取值范围(0-300) 当前默认值1 读写权限：可读可写'
            },
            iIs_All: {
                desc1: '显示标识',
                desc2: '取值范围(0-300) 当前默认值1 读写权限：可读可写'
            },
            iNight_Gray_Threshold: {
                desc1: '夜间阈值',
                desc2: '取值范围(0-255) 当前默认值10 读写权限：可读可写'
            },
            iDay_To_Night_Threshold: {
                desc1: '白天黑夜转换阈值',
                desc2: '取值范围(0-255) 当前默认值30 读写权限：可读可写'
            },
            iKakou_Detect_Area: {
                desc1: '卡口检测区域比例',
                desc2: '取值范围(0-100) 当前默认值80 读写权限：可读可写'
            },
            iDetect_Precision: {
                desc1: '检测精度',
                desc2: '取值范围(0-100) 当前默认值8 读写权限：可读可写'
            },
            iDelay_Time: {
                desc1: '延迟时间',
                desc2: '取值范围(0-2000) 当前默认值800 读写权限：可读可写'
            },
            iDelay_Dis_img2: {
                desc1: '第2张图片的延迟距离',
                desc2: '取值范围(0-40) 当前默认值0 读写权限：可读可写'
            },
            iSame_Plate_Time: {
                desc1: '同一车牌重复报出时间阈值',
                desc2: '取值范围(0-100) 当前默认值50 读写权限：可读可写'
            },
        }
    },
    pictureOSD: {
        saveMsg: '<div>保存配置将去除图片外多余黑条，是否确认保存？</div><input type="checkbox" id="osdMsg">不再提醒',
        getInfoFail: "osd类型信息获取失败",
        osdPositionTips: '选择图片上/下的叠字方式时，鼠标可移动至左侧场景图上/下边缘,当鼠标形状变为上下箭头时，可进行扩大或缩小叠字区域'
    },
    search: {
        exportConfig: {
            "-1": "取消选择",
            "-2": "文件选择错误",
            "-3": "打开文件错误",
            "-4": "重试再次错误",
            "-5": "用户放弃重试",
            "-6": "意外的错误",
            "-7": "参数错误"
        },
        unknownPlate: ['无牌车', '污损车牌'],
        removeDuplication: function (total) {
            return "去重共 " + total + " 条"
        }
    },
    settingCode: {
        object: {
            id: "ID",
            name: "违法行为",
            code: "总队违法代码",
            priority: "优先级",
            zfcode: "国标违法代码"
        },
        editMsg: function (field, value) {
            return '[' + field + '] ' + ' 更改为：' + value
        }
    },
    content: {
        text: {
            preview: "预览",
            event: "事件",
            search: "搜索",
            snap: "抓拍",
            configuration: "配置",
            authorize: "授权"
        }
    },
    contentAuthorization: {
        text: {
            authTitle: "授权码授权",
            authInfo: "通过申请授权令牌，使用授权令牌（.token）换取对应的授权码文件（.lic）对相机进行授权。",
            authFile: "选择授权码目录",
            importAuth: "授权",
            exportAuth: "申请授权令牌",
            exportFile: "导出授权文件",
            authExportInfo: "（注1：授权令牌默认保存路径为浏览器的下载路径）",
            authImportInfo: "（注2：授权时只需要选择授权码目录即可，程序会在目录中查找对应的授权文件）",
            authorizationText2: "扫码授权",
            authorizationText3: "扫描下方二维码进行授权。进入授权页面，正确填写相关信息后，将会为您发放免费的试用版授权。",
            note3: "（注：由于试用版授权通过邮箱发送，请务必确认邮箱填写正确！）"
        }
    },
    contentEvent: {
        text: {
            videoT1Value: "主码流",
            mainStream: "主码流",
            subStream: "子码流",
            snapPlateNumber: "抓拍车牌号",
            recognitionPlateNumber: "识别车牌号",
            pictureMode: "图片模式",
            listMode: "列表模式",
            td_eventPicName: "全 &nbsp;景 &nbsp;图",
            td_platePicName: "车牌图片",
            td_plateNumName: "车牌号码",
            td_eventType: "事件类型",
            TIME_: "时 间：",
            EVENT_TYPE_: "事件类型：",
            LANE_INDEX_: "车 道 号：",
            PLATE_TYPE_: "车牌类型：",
            PLATE_STRING_: "车牌号码：",
            CAR_SPEED_: "速 度：",
            CAR_COLOR_: "车身颜色：",
            CAR_TYPE_: "车辆类型：",
            CHE_XING_: "细分车型："
        },
        title: {
            hidePicture: "隐藏图片",
            receiveEvent: "接收事件"
        }
    },
    contentIndex: {
        text: {
            subStream: "子码流"
        },
        clazz: {
            videoT1Value: "主码流"
        }
    },
    contentPicture: {
        text: {
            Interval_time: "抓拍间隔："
        }
    },
    contentSearch: {
        text: {
            inquiry_mode: "查询方式",
            Start_time: "开始时间",
            End_time: "结束时间",
            License_plate_number: "车牌号码",
            etype: "事件类型",
            License_plate_type: "车牌类型",
            submit: "提交",
            reset: "重置",
            exportBtn: "导出当前事件",
            trigger_time: "触发时间为相机时间"
        }
    },
    contentSetting: {
        text: {
            system: "系统",
            Device_information: "设备信息",
            Device_status: "设备状态",
            Module_status: "模块状态",
            Version_information: "版本信息",
            TPM: "设备维护",
            Local_configuration: "本地配置",
            User: "用户",
            Advanced_applications: "高级应用",
            Internet: "网络",
            Video: "视频<span class=\'layui-nav-more\'></span>",
            Video_parameters: "视频参数",
            Image: "图像<span class=\'layui-nav-more\'></span>",
            Event_OSD_crossword: "事件OSD叠字",
            Disk: "存储<span class=\'layui-nav-more\'></span>",
            disc: "磁盘",
            Algorithm: "算法<span class=\'layui-nav-more\'></span>",
            Detection_area: "检测区",
            Detection_area1: '绘制检测区域',
            Lane_lines: "车道线",
            Signal_lamp: "信号灯",
            Traffic_light_status: "红绿灯状态",
            Event_detection: "视频检测抓拍类型",
            System_configuration: "系统配置",
            Traffic_expansion_parameters: "交通扩展参数",
            Illegal_code: "违法代码",
            Configuration_Preview: "配置预览"
        }
    },
    loginIng: {
        clazz: {
            login: "登 录"
        },
        placeholder: {
            username: "用户名",
            password: "密码",
        },
        title: {
            rememberMe: "记住密码"
        }
    },
    internetSenior: {
        text: {
            serverAddress: "服务器地址",
            FTPSet: "FTP服务器设置",
            dataBit: "数据位：",
            saveDat: "FTP存储时间(单位:天)",
            video: "视频：",
            bayonet: "卡口：",
            violation: "违法：",
            saveStorage: "保存配置",
            reset: "重置",
            saveType: "FTP存储类型",
            bayonetType: "卡口：",
            violationType: "违法：",
            savePath: "FTP存储路径",
            pathList: "目录结构：",
            saveName: "FTP存储文件名称",
            forwardPlateform: '汇聚转发平台设置'
        },
        clazz: {
            address: "地址：",
            port: "端口：",
            userName: "用户名：",
            password: "密码：",
        },
        tips: {
            nameList: "命名项",
            pleaseSelect: "请选择",
            pleaseInput: "请输入",
            id_1400: "设备ID请输入20个数字或字符"
        },
        cmd: {
            fileList: [
                {
                    code: 0,
                    desc1: '保存在根目录',
                    desc2: '根目录'
                },
                {
                    code: 1,
                    desc1: '使用一级目录',
                    desc2: '一级目录'
                },
                {
                    code: 2,
                    desc1: '使用二级目录',
                    desc2: '二级目录'
                },
                {
                    code: 3,
                    desc1: '使用三级目录',
                    desc2: '三级目录'
                },
                {
                    code: 4,
                    desc1: '使用四级目录',
                    desc2: '四级目录'
                },
                {
                    code: 5,
                    desc1: '使用五级目录',
                    desc2: '五级目录'
                },
                {
                    code: 6,
                    desc1: '使用六级目录',
                    desc2: '六级目录'
                }
            ],
            filePath: [
                {
                    code: "xjip",
                    desc: "设备IP"
                },
                {
                    code: "sbbh",
                    desc: "设备编号"
                },
                {
                    code: "wflx",
                    desc: "违法类型"
                },
                {
                    code: "bzdm",
                    desc: "国标违法代码"
                },
                {
                    code: "ym",
                    desc: "时间（年月）"
                },
                {
                    code: "ymd",
                    desc: "时间（年月日）"
                },
                {
                    code: "hour",
                    desc: "时间（小时）"
                },
                {
                    code: "cdfx",
                    desc: "车道行驶方向"
                },
                {
                    code: "cdno",
                    desc: "车道号"
                },
                {
                    code: "0",
                    desc: "自定义"
                }
            ],
            fileName: [
                {
                    code: "sbbh",
                    desc: "设备编号"
                },
                {
                    code: "bzdm",
                    desc: "国标违法代码"
                },
                {
                    code: "time",
                    desc: "时间"
                },
                {
                    code: "timeMs",
                    desc: "时间（带毫秒）"
                },
                {
                    code: "cphm",
                    desc: "车牌号码"
                },
                {
                    code: "cpys",
                    desc: "车牌颜色"
                },
                {
                    code: "cpzb",
                    desc: "车牌坐标"
                },
                {
                    code: "cx",
                    desc: "车型"
                },
                {
                    code: "csys",
                    desc: "车身颜色"
                },
                {
                    code: "cb",
                    desc: "车标"
                },
                {
                    code: "clsd",
                    desc: "车辆速度"
                },
                {
                    code: "cdfx",
                    desc: "车道行驶方向"
                },
                {
                    code: "cdno",
                    desc: "车道号"
                },
                {
                    code: "0",
                    desc: "自定义"
                }
            ],
        },
    },
    pictureOSDs: {
        image: {
            configImg: "../../img/404.png",
        },
        text: {
            refreshPic: "刷新场景",
            saveConfig: "保存配置",
            clearConfig: "清除叠字",
            resetConfig: "重置配置",
            mode: "叠加方式：",
            doubleStyle: "叠加样式",
            doubleInfo: "叠加信息",
            doubleText: "叠加内容：",
            title: "标题：",
            font: "字体：",
            background: "背景：",
            xCoordinate: "X坐标：",
            yCoordinate: "y坐标:",
            fontSize: "字体大小：",
            dateStyle: "日期格式：",
            timeStyle: "时间格式："
        },
        placeholder: {
            checkOSD: "OSD使能",
            fontColors: "颜色",
            fontColors2: "颜色"
        }
    },
    picture_OSD: {
        date: {
            "title": "叠加字符"
            , "list": [
                {
                    value: 0,
                    title: "车牌号码：",
                    type: "车牌号码",
                    checked: true,
                    filter: 'cphm',
                    space: 0,
                    enter: 0,
                    area: 0
                },
                {
                    value: 1,
                    title: "违法行为：",
                    type: "违法行为",
                    checked: true,
                    filter: 'wfxw',
                    space: 0,
                    enter: 0
                }, {
                    value: 2,
                    title: "时间：",
                    type: "时间",
                    checked: false,
                    filter: 'sj',
                    space: 0,
                    enter: 0
                }, {
                    value: 3,
                    title: "车牌颜色：",
                    type: "车牌颜色",
                    checked: true,
                    filter: 'cpys',
                    space: 0,
                    enter: 0
                }, {
                    value: 4,
                    title: "车辆类型：",
                    type: "车辆类型",
                    checked: false,
                    filter: 'cllx',
                    space: 0,
                    enter: 0
                }, {
                    value: 5,
                    title: "地点：",
                    type: "地点",
                    checked: true,
                    filter: 'dd',
                    space: 0,
                    enter: 0
                }, {
                    value: 6,
                    title: "车道号：",
                    type: "车道号",
                    checked: false,
                    filter: 'cdh',
                    space: 0,
                    enter: 0
                }, {
                    value: 7,
                    title: "车道方向：",
                    type: "车道方向",
                    checked: false,
                    filter: 'cdfx',
                    space: 0,
                    enter: 0
                }, {
                    value: 8,
                    title: "行驶方向：",
                    type: "行驶方向",
                    checked: false,
                    filter: 'xsfx',
                    space: 0,
                    enter: 0
                }, {
                    value: 9,
                    title: "红灯亮时间：",
                    type: "红灯亮时间",
                    checked: false,
                    filter: 'hdlsj',
                    space: 0,
                    enter: 0
                }, {
                    value: 10,
                    title: "道路名称：",
                    type: "道路名称",
                    checked: false,
                    filter: 'dlmc',
                    space: 0,
                    enter: 0
                }, {
                    value: 11,
                    title: "车速：",
                    type: "车速",
                    checked: false,
                    filter: 'cs',
                    space: 0,
                    enter: 0
                }, {
                    value: 12,
                    title: "高低限速：",
                    type: "高低限速",
                    checked: false,
                    filter: 'gdxs',
                    space: 0,
                    enter: 0
                }, {
                    value: 13,
                    title: "设备编号：",
                    type: "设备编号",
                    checked: false,
                    filter: 'sbbh',
                    space: 0,
                    enter: 0
                }, {
                    value: 14,
                    title: "车身颜色：",
                    type: "车身颜色",
                    checked: false,
                    filter: 'csys',
                    space: 0,
                    enter: 0
                }, {
                    value: 15,
                    title: "车辆品牌：",
                    type: "车辆品牌",
                    checked: false,
                    filter: 'clpp',
                    space: 0,
                    enter: 0
                }, {
                    value: 16,
                    title: "违法代码：",
                    type: "违法代码",
                    checked: false,
                    filter: 'wfdm',
                    space: 0,
                    enter: 0
                }, {
                    value: 17,
                    title: "自定义1：",
                    type: "自定义1",
                    checked: false,
                    filter: 'zdy1',
                    space: 0,
                    enter: 0
                }, {
                    value: 18,
                    title: "自定义2：",
                    type: "自定义2",
                    checked: false,
                    filter: 'zdy2',
                    space: 0,
                    enter: 0
                }, {
                    value: 19,
                    title: "自定义3：",
                    type: "自定义3",
                    checked: false,
                    filter: 'zdy3',
                    space: 0,
                    enter: 0
                }
            ]
            , selected: false
            , filter: ""
        },
        image: {
            OSDImg: "../../img/404.png",
        },
        text: {
            refreshPic: "刷新场景",
            save: "保存配置",
            clear: "清除当前绘制区域",
            reset: "重置配置",
            doubleStyle: "叠加样式",
            doubleWay: "叠加方式",
            on: "图片内",
            out: "图片外",
            fontSize: "字体大小：",
            beforeColor: "前景色：",
            backColor: "背景色：",
            beginX: "起始X坐标：",
            beginY: "起始Y坐标：",
        },
        clazz: {
            doubleInfo: "叠加信息"
        },
        placeholder: {
            frontColorInput: "前景色",
            backColorInput: "背景色",
        },
        title: {
            illegalDescription: "违法描述",
            code: "违法代码",
            illegalTime: "违法时间",
            overSpeedRatio: "超速比",
            laneDirection: "车道方向",
            laneName: "车道名称",
            speedLimit: "高低限速",
            laneNumber: "车道号",
            plateNumber: "车牌号码",
            plateKind: "车辆类型",
            carColor: "车辆颜色",
            plateColor: "车牌颜色",
            bodyLength: "车身长度",
            runSpeed: "行驶速度",
            drivingDirection: "行驶方向",
            Logo: "车标",
            redLightTime: "红灯时间",
            deviceID: "设备ID",
            illegalLocation: "违法地点",
            antiCounterfeiting: "防伪代码",
            information1: "自定义信息1",
            information2: "自定义信息2",
            information3: "自定义信息3",
            information4: "自定义信息4"
        }
    },
    settingCodes: {
        text: {
            saveCode: "保存配置",
            resetCode: "重置"
        }
    },
    settingDetect: {
        text: {
            general: "通用功能",
            sensitivity: "无牌车灵敏度：",
            electricPolice: "多功能电警",
            parkingTime: "越线停车时间：",
            stopTime: "1,2张图片间隔：",
            pictureInterval2: "1,2张图片间隔：",
            bayonetPolice: "卡口电警",
            effectiveTime: "起效时间段",
            ramp: "匝道电警",
            saveDetect: "保存配置",
            resetDetect: "重置",
            time: "起效时间段1",
            time2: "起效时间段2",
            time3: "起效时间段3",
            xianXingTime: "起效时间段",
            exportBlackBtn: "导出黑名单",
            importBlackBtn: "导入黑名单",
            confidence: '置信度'
        },
        clazz: {
            min: "秒",
            pictureInterval: "2,3张图片间隔："
        },
        title: {
            shanghaiC: "检测闯禁令(沪C)",
            coachCar: "检测闯禁令(教练车)",
            trucks: "检测闯禁令(货车)",
            otherProvinces: "检测闯禁令(外省市小型车)",
            xianXing: "尾号限行",
            phone: "检测打手机",
            checkSeatBelts: "检测安全带",
            noDrivingLights: "检测不开车灯",
            violationSpeed: "检测违反车速规定",
            pre_card: "前卡事件曝闪",
            bayonet: "检测卡口",
            moto: "检测摩托车",
            MotoKaKou: '检测摩托车卡口',
            motoCjl: '检测摩托车闯禁令',
            motorcycles: "检测非机动车",
            violation: "检测违规使用专用车道",
            viewBayonet: "卡口特写图",
            violationFeature: "违章特写图",
            video: "违章录像",
            unlicensedCars: "检测无牌车",
            redLight: "检测闯红灯",
            threePictures: "卡口采集3张图片",
            loiter: "检测路口滞留",
            wheel: "检测转弯不使用转向灯",
            dcv: "检测危化品车",
            obp: "检测借道超车",
            output: "输出轨迹",
            retrograde: "检测逆行",
            turns: "检测大弯小转",
            straight: "检测左转不让直行",
            nonMotor: "检测大车右转未停车让行",
            pedestrianDetection: "检测礼让行人",
            alternately: "检测不按交替通行",
            coilOutput: "检测地感线圈输出",
            zebra: "检测斑马线掉头",
            affectsNormalTraffic: "检测掉头影响正常通行",
            changes: "检测连续变道",
            strictViolation: "严格违章检测",
            strictRedLight: "严格闯红灯检测",
            strictlyPass: "严格借道检测",
            strictPressureLine: "严格压线检测",
            strictRetrograde: "严格逆行检测",
            changeWay: "检测变道行驶",
            withoutTurningLights: "检测变道不使用转向灯",
            detectJump: "检测加塞",
            detectWeiFaZaiHuo: "检测违法载货",

            NonMotoRed: "检测非机动车闯红灯",
            NonMotoRet: "检测非机动车逆行",
            NonMotoVeh: "检测非机动车闯禁令",
            NonMotoNoHel: "检测非机动车不戴头盔",
            NonMotoKaKou: "检测非机动车卡口",
            NonMotoOverload: "检测非机动车超载",

            illegalWays: "检测违法借道",
            left: "借道左行",
            goStraight: "借道直行",
            right: "借道右行",
            parking: "检测越线停车",
            trucksOccupying: "检测货车占用客车道",
            heavyTruck: "重型货车",
            mediumTruck: "中型货车",
            lightTruck: "轻型货车",
            vehicleBan: "检测车型禁闯令",
            blacklist: "检测闯禁令(黑名单)",
            largeVehiclesOccupyingSmallLanes: "检测大车占用小车道",
            heavyTrucks: "重型货车",
            largeBuses: "大型客车",
            yellowLicensePlate: "黄牌",
            pressureLine: "检测压线",
            illegalParking: "检测违章停车",
            congestionDetection: "拥堵检测"
        },
        line: {
            list: [
                {
                    name: "车道线0",
                    value: "0x0001"
                },
                {
                    name: "车道线1",
                    value: "0x0002"
                },
                {
                    name: "车道线2",
                    value: "0x0004"
                },
                {
                    name: "车道线3",
                    value: "0x0008"
                }
                ,
                {
                    name: "车道线4",
                    value: "0x0010"
                },
                {
                    name: "车道线5",
                    value: "0x0020"
                }
            ],
            attributes: [
                {
                    name: "车道1属性：",
                    selectText1: "普通",
                    selectText2: "小车道",
                    selectText3: "客车道"
                },
                {
                    name: "车道2属性：",
                    selectText1: "普通",
                    selectText2: "小车道",
                    selectText3: "客车道"
                },
                {
                    name: "车道3属性：",
                    selectText1: "普通",
                    selectText2: "小车道",
                    selectText3: "客车道"
                },
                {
                    name: "车道4属性：",
                    selectText1: "普通",
                    selectText2: "小车道",
                    selectText3: "客车道"
                },
                {
                    name: "车道5属性：",
                    selectText1: "普通",
                    selectText2: "小车道",
                    selectText3: "客车道"
                }
            ]
        },
        flashingLight: {
            list: [
                [
                    {
                        name: "车道1"
                    },
                    {
                        name: "车道2"
                    },
                    {
                        name: "车道3"
                    },
                    {
                        name: "车道"
                    },
                    {
                        name: "车道5"
                    }
                ],
                {
                    name: "曝闪灯："
                },
                {
                    name: "属性：",
                    selects: [
                        {
                            name: "普通",
                            value: "0"
                        },
                        {
                            name: "小车道",
                            value: "1"
                        }
                    ]
                },
                {
                    name: "属性：",
                    selects: [
                        {
                            name: "普通",
                            value: "0"
                        },
                        {
                            name: "客车道",
                            value: "1"
                        }
                    ]
                }
            ]
        },
        table: [
            {
                type: "重型货车",
                value: "0x0001",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "中型货车",
                value: "0x0002",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "轻型货车",
                value: "0x0004",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "大型客车",
                value: "0x0008",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "中型客车",
                value: "0x0010",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "轻型客车",
                value: "0x0020",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            },
            {
                type: "小型客车",
                value: "0x0040",
                plate: [
                    "蓝牌",
                    "黄牌"
                ]
            }
        ],
        week: [
            {
                day: "周一",
                value: "64"
            },
            {
                day: "周二",
                value: "32"
            },
            {
                day: "周三",
                value: "16"
            },
            {
                day: "周四",
                value: "8"
            },
            {
                day: "周五",
                value: "4"
            },
            {
                day: "周六",
                value: "2"
            },
            {
                day: "周日",
                value: "1"
            }
        ],
        typeOfViolation: [
            {
                type: "违法类型1",
                value: "0x01"
            },
            {
                type: "违法类型2",
                value: "0x02"
            },
            {
                type: "违法类型3",
                value: "0x04"
            },
            {
                type: "违法类型4",
                value: "0x08"
            },
            {
                type: "违法类型5",
                value: "0x10"
            },
            {
                type: "违法类型6",
                value: "0x20"
            },
            {
                type: "违法类型7",
                value: "0x40"
            },
            {
                type: "违法类型8",
                value: "0x80"
            }
        ],
        province: [
            {
                "value": "京",
                "name": "京",
            },
            {
                "value": "津",
                "name": "津",
            },
            {
                "value": "沪",
                "name": "沪",
            },
            {
                "value": "渝",
                "name": "渝",
            },
            {
                "value": "冀",
                "name": "冀",
            },
            {
                "value": "豫",
                "name": "豫",
            },
            {
                "value": "云",
                "name": "云",
            },
            {
                "value": "辽",
                "name": "辽",
            },
            {
                "value": "黑",
                "name": "黑",
            },
            {
                "value": "湘",
                "name": "湘",
            },
            {
                "value": "皖",
                "name": "皖",
            },
            {
                "value": "闽",
                "name": "闽",
            },
            {
                "value": "鲁",
                "name": "鲁",
            },
            {
                "value": "新",
                "name": "新",
            },
            {
                "value": "苏",
                "name": "苏",
            },
            {
                "value": "浙",
                "name": "浙",
            },
            {
                "value": "赣",
                "name": "赣",
            },
            {
                "value": "鄂",
                "name": "鄂",
            },
            {
                "value": "桂",
                "name": "桂",
            },
            {
                "value": "甘",
                "name": "甘",
            },
            {
                "value": "晋",
                "name": "晋",
            },
            {
                "value": "蒙",
                "name": "蒙",
            },
            {
                "value": "陕",
                "name": "陕",
            },
            {
                "value": "吉",
                "name": "吉",
            },

            {
                "value": "贵",
                "name": "贵",
            },

            {
                "value": "粤",
                "name": "粤",
            },
            {
                "value": "青",
                "name": "青",
            },
            {
                "value": "藏",
                "name": "藏",
            },
            {
                "value": "川",
                "name": "川",
            },
            {
                "value": "宁",
                "name": "宁",
            },
            {
                "value": "琼",
                "name": "琼",
            }
        ],
        city: [
            {
                "value": 1,
                "name": "A",
            },
            {
                "value": 2,
                "name": "B",
            },
            {
                "value": 4,
                "name": "C",
            },
            {
                "value": 8,
                "name": "D",
            },
            {
                "value": 16,
                "name": "E",
            },
            {
                "value": 32,
                "name": "F",
            },
            {
                "value": 64,
                "name": "G",
            },
            {
                "value": 128,
                "name": "H",
            },
            {
                "value": 256,
                "name": "I",
            },
            {
                "value": 512,
                "name": "J",
            },
            {
                "value": 1024,
                "name": "K",
            },
            {
                "value": 2048,
                "name": "L",
            },
            {
                "value": 4096,
                "name": "M",
            },
            {
                "value": 8192,
                "name": "N",
            },
            {
                "value": 16384,
                "name": "O",
            },
            {
                "value": 32768,
                "name": "P",
            },
            {
                "value": 65536,
                "name": "Q",
            },
            {
                "value": 131072,
                "name": "R",
            },
            {
                "value": 262144,
                "name": "S",
            },
            {
                "value": 524288,
                "name": "T",
            },
            {
                "value": 1048576,
                "name": "U",
            },
            {
                "value": 2097152,
                "name": "V",
            },
            {
                "value": 4194304,
                "name": "W",
            },
            {
                "value": 8388608,
                "name": "X",
            },

            {
                "value": 16777216,
                "name": "Y",
            },

            {
                "value": 33554432,
                "name": "Z",
            }
        ],
        week_xianXing: [
            {
                name: "周一",
                value: 2
            },
            {
                name: "周二",
                value: 4
            },
            {
                name: "周三",
                value: 8
            },
            {
                name: "周四",
                value: 16
            },
            {
                name: "周五",
                value: 32
            },
            {
                name: "周六",
                value: 64
            },
            {
                name: "周日",
                value: 1
            }
        ],
        number_xianXing: [
            {
                name: 0,
                value: 1
            },
            {
                name: 1,
                value: 2
            },
            {
                name: 2,
                value: 4
            },
            {
                name: 3,
                value: 8
            },
            {
                name: 4,
                value: 16
            },
            {
                name: 5,
                value: 32
            },
            {
                name: 6,
                value: 64
            },
            {
                name: 7,
                value: 128
            },
            {
                name: 8,
                value: 256
            },
            {
                name: 9,
                value: 512
            }
        ]
    },
    settingExtend: {
        text: {
            trafficDetection: "交通检测扩展参数设置",
            parameterList: "参数列表",
            defaultAllocation: "默认配置",
            highSensitivity: "高敏感度",
            mediumSensitivity: "中敏感度(推荐)",
            lowSensitivity: "低敏感度",
            parameterValue: "参数值：",
            recommendBtn: "推荐值",
            valueBtn: "原始值",
            unitS: "单位：",
            description: "说明",
            saveExtend: "保存配置",
            resetExtend: "重置"
        }
    },
    settingLine: {
        text: {
            refreshPic: "刷新场景",
            saveConfig: "保存配置",
            clearNowConfig: "清除当前绘制区域",
            showConfig: "重置配置",
            attributes: "车道线相关属性",
            lineBtn: "绘制车道线",
            lane: "车道数目：",
            begin: "起始车道：",
            left: "左>右",
            right: "右>左",
            floorBtn: "虚拟地感",
            peopleBtn: "礼让行人",
            straightLine: "直线",
            ZLine: "Z型线",
            modeSelection: "模式选择："
        },
        clazz: {
            direction: "方向：",
            definition: "行车定义：",
            waitArea: "待行区：",
            property: "特殊属性：",
            kind: "样式：",
            real: "实线",
            false: "虚线",
            redStopLine: "红灯停止线",
            line0: "车道线0属性",
            line1: "车道线1属性",
            line2: "车道线2属性",
            line3: "车道线3属性",
            line4: "车道线4属性",
            line5: "车道线5属性",
            virtualGround: "虚拟地感检测线",
            pedestrian: "礼让行人检测区",
            up: "上行",
            down: "下行",
            left1: "左行",
            straight: "直行",
            right1: "右行",
            turnAround: "掉头",
            leftStraight: "左直",
            rightStraight: "右直",
            turnLeft: "左掉头",
            leftRight: "左右",
            turnLeftRightStraight: "左直右掉头",
            turnLeftStraight: "左直掉头",
            turnLeftRight: "左右掉头",
            no: "无",
            have: "有",
            ordinary: "普通车道",
            emergency: "应急车道",
            nonMotorized: "非机动车道",
            bus: "公交车道",
            diversion: "导流带",
            single: "单行道",
            noLefTurn: "禁止左拐",
            noRightTurn: "禁止右拐",


        },
        placeholder2: {
            color: "颜色",
            time1: "请选择时间段1",
            time2: "请选择时间段2",
            time3: "请选择时间段3"

        }
    },
    settingPreview: {
        text: {
            refreshPic: "刷新场景",
            sendConfig: "下发配置"
        }
    },
    settingSignal: {
        image: {
            configImg: "../../img/404.png"
        },
        text: {
            refreshPic: "刷新场景",
            saveConfig: "保存配置",
            clearNowConfig: "清除当前绘制区域",
            showConfig: "重置配置",
            lightInfo: "信号灯信息",
            left: "左",
            straight: "直",
            right: "右",
            pedestrian: "人行",
            delay: "闯红灯延时判断：",
            draw: "绘制信号灯",
            biggerBtn: "局部放大",
            signalBtn: "绘制信号灯",
            num: "信号灯数：",
            define: "信号灯定义",
            style: "样式：",
            solid: "实线",
            dashed: "虚线"

        },
        title: {
            signalJudge: "使能红绿灯判断",
            extendSignal: "外接红绿灯信号",
            signalStronger: "红灯加强",
            signalYellow: "夜晚黄灯时间设置",

        },
        clazz: {
            begin: "从：",
            to: "到：",

        },
        placeholder: {
            signalColor: "颜色",
            signalTime: "秒"

        },
        lightList: [
            {
                name: "灯1",
                id: "type_1"
            },
            {
                name: "灯2",
                id: "type_2"
            },
            {
                name: "灯3",
                id: "type_3"
            },
            {
                name: "灯4",
                id: "type_4"
            },
            {
                name: "灯5",
                id: "type_5"
            },
            {
                name: "灯6",
                id: "type_6"
            }
        ],
        signalTypeList: [
            [
                {
                    name: "单头灯",
                    value: "0"
                },
                {
                    name: "三头灯",
                    value: "2"
                },
                {
                    name: "四头灯",
                    value: "3"
                },
                {
                    name: "强制长红",
                    value: "1"
                },
                {
                    name: "仅加强",
                    value: "4"
                }
            ],
            [
                {
                    name: "左行",
                    value: "0x0001"
                },
                {
                    name: "直行",
                    value: "0x0002"
                },
                {
                    name: "右行",
                    value: "0x0004"
                },
                {
                    name: "掉头",
                    value: "0x0008"
                },
                {
                    name: "左直",
                    value: "0x0003"
                },
                {
                    name: "右直",
                    value: "0x0006"
                },
                {
                    name: "左掉头",
                    value: "0x0009"
                },
                {
                    name: "左右",
                    value: "0x0005"
                },
                {
                    name: "左直右掉头",
                    value: "0x000f"
                },
                {
                    name: "左直掉头",
                    value: "0x000b"
                },
                {
                    name: "左右掉头",
                    value: "0x000d"
                }
            ],
            [
                {
                    name: "圆形灯",
                    value: "0"
                },
                {
                    name: "数字灯",
                    value: "1"
                },
                {
                    name: "箭头灯",
                    value: "2"
                },
                {
                    name: "左转掉头灯",
                    value: "3"
                },
                {
                    name: "其他",
                    value: "4"
                }
            ],
            [
                {
                    name: "机动车信号灯",
                    value: "0"
                },
                {
                    name: "车道信号灯",
                    value: "1"
                },
                {
                    name: "方向指示信号灯",
                    value: "2"
                },
                {
                    name: "闪光警号信号灯",
                    value: "3"
                },
                {
                    name: "倒计时数码显示器",
                    value: "4"
                },
                {
                    name: "非机动车信号灯",
                    value: "5"
                },
                {
                    name: "人行横道信号灯",
                    value: "6"
                },
                {
                    name: "其他",
                    value: "7"
                }
            ]
        ]
    },
    setStatus: {
        image: {
            img1: "./../../img/turnLeft.png",
            img2: "./../../img/goStraight.png",
            img3: "./../../img/turnRight.png",
            img4: "./../../img/turnAround.png",
            img5: "./../../img/people.png",
            left_light: "./../../img/green.png",
            straight_light: "./../../img/green.png",
            right_light: "./../../img/green.png",
            return_light: "./../../img/green.png",
            people_light: "./../../img/green.png",
        },
        text: {
            status: "红绿灯状态"
        }
    },
    setSurvey: {
        text: {
            refreshPic: "刷新场景",
            saveConfig: "保存配置",
            clear: "清除当前绘制区域",
            show: "重置配置",
            plateSize: "车牌大小",
            biggerBtn: "局部放大",
            min: "车牌最小宽度：",
            max: "车牌最大宽度：",
            targetDetectionArea: "目标检测区",
            surveyBtn: "绘制检测",
            violationDetectionArea: "违停检测区",
            noParkingBtn: "违停检测",
            dividingLine: "左直右分界线",
            turnLeftBtn: "左转分界线",
            goStraightBtn: "直行分界线",
            turnRightBtn: "右转分界线",
            cartTurnRightBtn: "大车右转让行线",
            queueCalibration: "排队长度标定线",
            queueLengthText: "排队长度(米)：",
            queueLengthText1: "排队长度(米)：",
            upCalibrationBtn: "上行标定线",
            downCalibrationBtn: "下行标定线",
            turnDetectionArea: "掉头检测区",
            aroundBtn: "掉头检测",
            oppositeStraight: "对向直行检测区",
            faceBtn: "对向直行"

        },
        clazz: {
            type: "样式：",
            solid: "实线",
            dashed: "虚线",
            kind: "种类：",
            ordinary: "普通",
            sidewalk: "人行道",
            Gridlines: "网格线"

        },
        placeholder: {
            surveyColor: "颜色",
            noParkingColor: "颜色",
            turnLeftColor: "颜色",
            goStraightColor: "颜色",
            turnRightColor: "颜色",
            cartTurnRightColor: "颜色",
            upCalibrationColor: "颜色",
            downCalibrationColor: "颜色",
            aroundColor: "颜色",
            faceColor: "颜色"
        }
    },
    settingLineSurvey: {
        text: {
            refreshPic: "刷新场景",
            saveConfig: "保存配置",
            clearNowConfig: "清除当前绘制区域",
            showConfig: "重置配置",
            attributes: "车道线相关属性",
            lineBtn: "绘制车道线",
            lane: "车道数目：",
            begin: "起始车道：",
            left: "左>右",
            right: "右>左",
            floorBtn: "绘制虚拟地感",
            peopleBtn: "绘制礼让行人",
            straightLine: "直线",
            ZLine: "Z型线",
            modeSelection: "模式选择：",
            clear: "清除当前绘制区域",
            show: "重置配置",
            plateSize: "车牌大小",
            biggerBtn: "局部放大",
            min: "车牌最小宽度：",
            max: "车牌最大宽度：",
            targetDetectionArea: "目标检测区",
            surveyBtn: "绘制检测",
            violationDetectionArea: "违停检测区",
            noParkingBtn: "违停检测",
            dividingLine: "左直右分界线",
            turnLeftBtn: "左转分界线",
            goStraightBtn: "直行分界线",
            turnRightBtn: "右转分界线",
            cartTurnRightBtn: "大车右转让行线",
            queueCalibration: "排队长度标定线",
            queueLengthText: "排队长度(米)：",
            queueLengthText1: "排队长度(米)：",
            upCalibrationBtn: "上行标定线",
            downCalibrationBtn: "下行标定线",
            turnDetectionArea: "掉头检测区",
            aroundBtn: "掉头检测",
            oppositeStraight: "对向直行检测区",
            faceBtn: "对向直行"
        },
        clazz: {
            direction: "方向：",
            definition: "行车定义：",
            waitArea: "待行区：",
            property: "特殊属性：",
            real: "实线",
            false: "虚线",
            redStopLine: "红灯停止线",
            line0: "车道线0属性",
            line1: "车道线1属性",
            line2: "车道线2属性",
            line3: "车道线3属性",
            line4: "车道线4属性",
            line5: "车道线5属性",
            virtualGround: "虚拟地感检测线",
            pedestrian: "礼让行人检测区",
            up: "上行",
            down: "下行",
            left1: "左行",
            straight: "直行",
            right1: "右行",
            turnAround: "掉头",
            leftStraight: "左直",
            rightStraight: "右直",
            turnLeft: "左掉头",
            leftRight: "左右",
            turnLeftRightStraight: "左直右掉头",
            turnLeftStraight: "左直掉头",
            turnLeftRight: "左右掉头",
            no: "无",
            have: "有",
            ordinary: "普通车道",
            emergency: "应急车道",
            nonMotorized: "非机动车道",
            bus: "公交车道",
            diversion: "导流带",
            single: "单行道",
            noLefTurn: "禁止左拐",
            noRightTurn: "禁止右拐",
            type: "样式：",
            solid: "实线",
            dashed: "虚线",
            kind_survey: "种类：",
            sidewalk: "人行道",
            Gridlines: "网格线"


        },
        placeholder2: {
            color: "颜色",
            time1: "请选择时间段1",
            time2: "请选择时间段2",
            time3: "请选择时间段3",
            surveyColor: "颜色",
            noParkingColor: "颜色",
            turnLeftColor: "颜色",
            goStraightColor: "颜色",
            turnRightColor: "颜色",
            cartTurnRightColor: "颜色",
            upCalibrationColor: "颜色",
            downCalibrationColor: "颜色",
            aroundColor: "颜色",
            faceColor: "颜色"

        }
    },
    setSystem: {
        text: {
            parameter: "识别参数配置",
            threshold: "检测门限：",
            recognitionThreshold: "识别门限：",
            chinaCode: "地区汉字代码：",
            code: "地区字母代码：",
            crossing: "路口参数配置",
            crossingName: "路口名称：",
            direction: "路口方向：",
            roadNum: "道路编号：",
            direction2: "东向西",
            direction3: "西向东",
            direction4: "南向北",
            direction5: "北向南",
            direction6: "东北向西南",
            direction7: "西南向东北",
            direction8: "西北向东南",
            direction9: "东南向西北",
            num: "路口编号",
            customNumber: "自定义编号",
            NationalStandardNumber: "国家标准编号",
            province: "请选择省",
            city: "请选择市",
            township: "请选择县/区",
            speedConfiguration: "速度配置",
            speedMeasurementMode: "测速模式：",
            video: "视频",
            radar: "雷达",
            all: "视频+雷达",
            kindOfRadar: "雷达类型：",
            no: "无",
            andao: "安道雷雷达",
            chuansu: "川速微波",
            huichang: "慧昌雷达",
            correctionFactor: "修正系数：",
            height: "相机高度(厘米)：",
            horizontalDistance: "相机与检测区下沿的水平距离(厘米)：",
            downDistance: "相机与检测区上沿的水平距离(厘米)：",
            speed1: "车道1：",
            speed2: "车道2：",
            speed3: "车道3：",
            speed4: "车道4：",
            speed5: "车道5：",
            // AIMode: "AI模型",
            AIMode: "日志打印等级",
            // AIModes: "AI模型：",
            AIModes: "日志打印等级：",
            mode1: "模型1",
            mode2: "模型2",
            eventImageCoding: "事件图像编码参数配置",
            codingQuality: "编码品质：",
            saveSystem: "保存配置",
            resetSystem: "重置",
            laneSpeedConfig: "车道限速设置（公里/小时）"
        },
        title: {
            personalizedPlate: "个性车牌开启",
            embassyPlate: "使馆车牌开启",
            consulatePlate: "领事馆车牌开启",
            speedConfig: "速度检测使能"
        },
        clazz: {
            lowSpeed: "抓拍速度(低)：",
            heightSpeed: "抓拍速度(高)：",
            lowLimitSpeed: "限速(低)：",
            heightLimitSpeed: "限速(高)：",
        }
    },
    systemConfig: {
        text: {
            pictureFile: "图片文件",
            saveUIL: "图片保存路径：",
            imgBtn: "浏览",
            videoFile: "录像文件",
            videoSaveUIL: "录像保存路径：",
            recordBtn: "浏览",
            logFile: "日志文件",
            logSaveUIL: "日志保存路径：",
            logBtn: "浏览",
            defaultConfig: "默认配置"
        }
    },
    systemInfo: {
        text: {
            basicInformation: "基本信息",
            name: "名称：",
            Device_GUID: "设备GUID：",
            Equipment_type: "设备类型：",
            manufacturers: "制造商：",
            Hardware_version: "硬件版本：",
            Software_version: "软件版本：",
            Communication_version: "通讯版本：",
            Configuration_version: "配置版本：",
            Authorization_status: "授权版本：",
            Authorization_time: "授权期限：",
            Algorithm_engine: "算法引擎：",
            NNIE_engine: "NNIE引擎：",
            Plugin_version: "插件版本：",
            web_ver: "web版本：",
            saveDevice: "保存配置",
            resetDevice: "重置"
        }
    },
    systemMaintain: {
        text: {
            equipmentMaintenance: "设备维护",
            importExport: "导入导出",
            realTimeLogInfo: "尚未获取到信息",
            saveRealTimeLogInfo: "保存信息",
            getRealTimeLog: "获取实时日志",
            logInfo: "尚未获取到信息",
            saveLogInfo: "保存信息",
            getLog: "获取常驻日志",
            getLogS: "获取日志分析",
            deviceRestart: "设备重启",
            restartDevice: "重启算法",
            restart: "远程控制设备立即重新启动",
            saveReboot: "保存",
            reset: "设备配置复位",
            simplyResume: "简单算法恢复",
            defaults: "算法恢复到默认值",
            configurationData: "配置数据",
            importConfigBtn: "导入配置文件",
            configuration: "从备份文件中导入设置配置",
            exportConfig: "导出配置文件",
            load: "将设备所有配置导出到本地文件（默认保存路径为浏览器下载路径）"
        },
        clazz: {
            realTimeLog: "实时日志",
            residentLog: "常驻日志",
            SLog: "重启日志分析",
            exportYUV: '导出YUV',
        },
        title: {
            autoRestart: "自动重启"
        },
        placeholder: {
            setTimeValue: "设置重启时间"
        }
    },
    systemStatus: {
        text: {
            deviceStatusInformation: "设备状态信息",
            deviceUID: "设备UID：",
            environmentVariableStatus: "环境变量状态：",
            productFeatures: "产品功能：",
            hardDiskType: "硬盘类型：",
            partitionInformation: "硬盘分区信息：",
            systemInformation: "硬盘文件系统信息：",
            dataAreaStatusInformation: "数据区状态信息：",
            logStatus: "日志区状态信息：",
            checked: "分区是否正在检查：",
            format: "分区是否正在格式化：",
            fileSystemStatus: "文件系统状态：",
            capacity: "硬盘容量：",
            diskUsage: "硬盘使用率：",
            SDCard: "SD卡设备：",
            SDStatus: "SD卡状态：",
            SDCapacity: "SD卡容量：",
            SDUsage: "SD卡使用率：",
            SDDataNumber: "备份数据量：",
            FTPIP: "FTP的IP地址：",
            FTPport: "FTP的端口号：",
            FTPStatus: "FTP连接状态：",
            FTPNum: "访问FTP成功的统计次数 ：",
            FTPNumFailure: "访问FTP失败的统计次数：",
            lastLogin: "最近一次登陆成功时间：",
            lastVisit: "最近一次访问成功时间：",
            lastAccessFailed: "最后一次访问失败时间：",
            configurationStatus: "配置状态：",
            analysisStatus: "分析状态：",
            externalTrafficLightStatus: "外接红绿灯状态：",
            radarStatus: "雷达状态：",
            currentTime: "设备当前时间：",
            NTPAddress: "NTP主机地址：",
            NTPStatus: "NTP连接状态：",
            lastTimeNTP: "最近一次成功NTP时间：",
            cameraType: "相机类型：",
            cameraIP: "相机IP地址：",
            commandConnectionPort: "命令连接端口：",
            dataPort: "数据连接端口：",
            commandConnectionStatus: "命令连接状态：",
            dateStatus: "数据连接状态：",
            correctlyReceivedFrames: "连续正确接收帧数：",
            acceptFrameStatus: "接受帧状态：",
            lastConfigurationTime: "最近一次成功配置时间：",
            lastSuccessfulTime: "最近一次成功对时时间：",
            lastCameraPictureTime: "最近一次相机图片时间：",
            lastVideoTime: "最近一次存录像的时间：",
            lastBayonetTime: "最近一次存卡口的时间：",
            recentViolation: "最近一次存违章的时间：",
            videoSaveDay: "录像存放天数：",
            bayonetSaveDay: "卡口事件存放天数：",
            violationSaveDay: "违章事件存放天数：",
            leftLane: "左行车道的拥堵状态：",
            straightLane: "直行车道的拥堵状态：",
            rightLane: "右行车道的拥堵状态：",
            bayonetEventStatus: "卡口事件状态：",
            trafficLightStatus: "红绿灯状态：",
            fresh: "刷新状态",
            inquiry_mode: "查询方式"
        },
        clazz: {
            realTimeStatus: "实时状态",
            historyStatus: "历史状态",
            setHistoryStatus: "频率设置",
            statusAnalysis: "状态分析",
            time: "时间",
            query: "查询",
            reset: "重置",
            rate: '频率',
            setRateConfig: '下发配置',
            getRateConfig: '重置配置'
        }
    },
    systemModuleStatus: {
        text: {
            deviceModuleStatusInformation: "模块状态信息",
            calculation_: "计算",
            trace_: "轨迹",
            encoding_: "编码",
            vpss_: "取流",
            video_record_: "录像",
            forwarding_: "前端",
            console_: "控制",
            license_: "授权",
            osd_: "叠字",
            web_: "页面",
            land_circle_: "线圈",
            light_signal_: "信号",
            NTP_: "校时",
            Debug_: "调试",
            h264_: "视频创建",
            h264_synthetic_: "视频合成",
            video_maker_: "录像合成",
            uploader_: "转发器",
            blacklist_: "黑名单",
            checkuid_: "设备检查",
            command_processor_: "后台命令处理器",
            remote_access_: "远程访问",
            vframe_receiver_: "接收视频帧",
            forward_event_: "前端事件",
            discovery_: "发现设备",
            venc_h264_: "视频编码",
            free_frame_: "释放YUV帧",
            web_config_: "页面配置",
            web_eventServer_: "页面事件",
            merge_: "识别爆闪帧",
            flash_: "处理爆闪帧",
            event_forward_: "转发事件",
            fresh: "刷新状态",
            freshStatusAnalysis: "刷新状态",
            moduleStatus_success: "运行中",
            moduleStatus_ready: "已就绪",
            moduleStatus_sleep: "挂起",
            moduleStatus_error: "故障",
            moduleStatus_unactive: "未激活",
            hasErrCode: '有错误码'
        }
    },
    systemVersion: {
        text: {
            deviceVersion: "设备版本信息",
            deviceUID: "设备UID：",
            softwareVersion: "软件版本：",
            hardwareVersion: "硬件版本：",
            lowLevelVersion: "low level 版本：",
            midLevelVersion: "mid level 版本：",
            armVersion: "arm 框架版本：",
            wkmodelVersion: "wkmodel 版本：",
            codeloaderVersion: "codeloader 版本：",
            aiVersion: "AI驱动库版本：",
            fresh: "刷新状态"
        }
    },
    videoSignal: {
        text: {
            playParameters: "播放参数",
            videoWay: "视频流传输方式：",
            submitVideo: "保存配置",
            commonDefault: "恢复默认",
            cameraUsernameLabel: "相机用户名",
            cameraPasswordLabel: "相机密码"
        }
    },
    codeArray: [
        {
            name: '逆向行驶',
            id: 'nxxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'nxxx_zfcode',
            zfcode: '0'
        },
        {
            name: '违章掉头',
            id: 'wzdt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wzdt_zfcode',
            zfcode: '0'
        },
        {
            name: '闯红灯',
            id: 'chd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chd_zfcode',
            zfcode: '0'
        },
        {
            name: '闯红灯（左转）',
            id: 'chdl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chdl_zfcode',
            zfcode: '0'
        },
        {
            name: '闯红灯（右转）',
            id: 'chdr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'chdr_zfcode',
            zfcode: '0'
        },
        {
            name: '压线',
            id: 'yx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yx_zfcode',
            zfcode: '0'
        },
        {
            name: '违反车速规定',
            id: 'wfcsgd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfcsgd_zfcode',
            zfcode: '0'
        },
        {
            name: '变道',
            id: 'bd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bd_zfcode',
            zfcode: '0'
        },
        {
            name: '借道行驶',
            id: 'jdxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdxx_zfcode',
            zfcode: '0'
        },
        {
            name: '违停',
            id: 'wt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wt_zfcode',
            zfcode: '0'
        },
        {
            name: '机动车不在机动车道内行驶',
            id: 'jdcbzjdcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcbzjdcd_zfcode',
            zfcode: '0'
        },
        {
            name: '机动车违反规定使用专用车道',
            id: 'jdcwfgd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcwfgd_zfcode',
            zfcode: '0'
        },
        {
            name: '路口滞留',
            id: 'lkzl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lkzl_zfcode',
            zfcode: '0'
        },
        {
            name: '正常行驶',
            id: 'zcxx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zcxx_zfcode',
            zfcode: '0'
        },
        {
            name: '禁止左拐',
            id: 'jzzg_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzzg_zfcode',
            zfcode: '0'
        },
        {
            name: '禁止右拐',
            id: 'jzyg_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzyg_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(外省市号牌小型车)',
            id: 'cjlwss_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwss_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(沪C)',
            id: 'cjlhc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlhc_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(教练车)',
            id: 'cjljlc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjljlc_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型1)',
            id: 'cjlwf1_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf1_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型2)',
            id: 'cjlwf2_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf2_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型3)',
            id: 'cjlwf3_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf3_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型4)',
            id: 'cjlwf4_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf4_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型5)',
            id: 'cjlwf5_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf5_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型6)',
            id: 'cjlwf6_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf6_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型7)',
            id: 'cjlwf7_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf7_zfcode',
            zfcode: '0'
        },
        {
            name: '闯禁令(违法类型8)',
            id: 'cjlwf8_code',
            code: '0',
            priority: '00',
            id_zfcode: 'cjlwf8_zfcode',
            zfcode: '0'
        },
        {
            name: '货车闯禁令',
            id: 'hccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'hccjl_zfcode',
            zfcode: '0'
        },
        {
            name: '客车闯禁令',
            id: 'kccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'kccjl_zfcode',
            zfcode: '0'
        },
        {
            name: '大车占用小车道',
            id: 'dczyxcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dczyxcd_zfcode',
            zfcode: '0'
        },
        {
            name: '货车占用客车道',
            id: 'hczykcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'hczykcd_zfcode',
            zfcode: '0'
        },
        {
            name: '礼让行人',
            id: 'lrxr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lrxr_zfcode',
            zfcode: '0'
        },
        {
            name: '人行道违停',
            id: 'rxdwt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'rxdwt_zfcode',
            zfcode: '0'
        },
        {
            name: '网格线违停',
            id: 'wgxwt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wgxwt_zfcode',
            zfcode: '0'
        },
        {
            name: '交替通行',
            id: 'jttx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jttx_zfcode',
            zfcode: '0'
        },
        {
            name: '大弯小转',
            id: 'dwxz_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dwxz_zfcode',
            zfcode: '0'
        },
        {
            name: '左转不让直行',
            id: 'zzbrzx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zzbrzx_zfcode',
            zfcode: '0'
        },
        {
            name: '越线停车',
            id: 'yxtc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yxtc_zfcode',
            zfcode: '0'
        },
        {
            name: '斑马线掉头',
            id: 'bmxdt_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bmxdt_zfcode',
            zfcode: '0'
        },
        {
            name: '掉头影响正常通行',
            id: 'dtyxtx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dtyxtx_zfcode',
            zfcode: '0'
        },
        {
            name: '大车右转未停车让行',
            id: 'yzblr_code',
            code: '0',
            priority: '00',
            id_zfcode: 'yzblr_zfcode',
            zfcode: '0'
        },
        {
            name: '禁止货车上匝道',
            id: 'jzhcszd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzhcszd__zfcode',
            zfcode: '0'
        },
        {
            name: '不开车灯',
            id: 'bkcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bkcd_zfcode',
            zfcode: '0'
        },
        {
            name: '连续变道',
            id: 'lxbd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'lxbd_zfcode',
            zfcode: '0'
        },
        {
            name: '未系安全带',
            id: 'wxaqd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wxaqd_zfcode',
            zfcode: '0'
        },
        {
            name: '打手机',
            id: 'dsj_code',
            code: '0',
            priority: '00',
            id_zfcode: 'dsj_zfcode',
            zfcode: '0'
        },
        {
            name: '变道不使用转向灯',
            id: 'bdbsyzxd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'bdbsyzxd_zfcode',
            zfcode: '0'
        },
        {
            name: '违法占用应急车道',
            id: 'wfzyyjcd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfzyyjcd_zfcode',
            zfcode: '0'
        },
        {
            name: '机动车占用非机动车道',
            id: 'jzf_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jzf_zfcode',
            zfcode: '0'
        },
        {
            name: '加塞',
            id: 'js_code',
            code: '0',
            priority: '00',
            id_zfcode: 'js_zfcode',
            zfcode: '0'
        },
        {
            name: '转弯不使用转向灯',
            id: 'zwbsyzxd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'zwbsyzxd_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车闯红灯',
            id: 'fjdcchd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcchd_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车逆行',
            id: 'fjdcnx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcnx_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车闯禁令',
            id: 'fjdccjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdccjl_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车不戴头盔',
            id: 'fjdcbdtk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdcbdtk_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车卡口',
            id: 'fjdkk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdkk_zfcode',
            zfcode: '0'
        },
        {
            name: '危化品车',
            id: 'whpc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'whpc_zfcode',
            zfcode: '0'
        },
        {
            name: '借道超车',
            id: 'jdcc_code',
            code: '0',
            priority: '00',
            id_zfcode: 'jdcc_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车不带头盔',
            id: 'fjdc_passenger_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_passenger_zfcode',
            zfcode: '0'
        },
        {
            name: '轻便摩托车载人',
            id: 'fjdc_overload_blue_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_blue_zfcode',
            zfcode: '0'
        },
        {
            name: '客车、货车以外的其他机动车载人超员',
            id: 'fjdc_overload_yellow_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_yellow_zfcode',
            zfcode: '0'
        },
        {
            name: '非机动车载人',
            id: 'fjdc_overload_code',
            code: '0',
            priority: '00',
            id_zfcode: 'fjdc_overload_zfcode',
            zfcode: '0'
        },
        {
            name: '尾号限行',
            id: 'weihaoxianxing_code',
            code: '0',
            priority: '00',
            id_zfcode: 'weihaoxianxing_zfcode',
            zfcode: '0'
        },
        {
            name: '违法载货',
            id: 'wfzh_code',
            code: '0',
            priority: '00',
            id_zfcode: 'wfzh_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车闯禁令',
            id: 'motocjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motocjl_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车卡口',
            id: 'motokk_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motokk_zfcode',
            zfcode: '0'
        },
        {
            name: '外牌摩托车闯禁令',
            id: 'motowpcjl_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motowpcjl_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车闯红灯',
            id: 'motochd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motochd_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车不按导向车道行驶',
            id: 'motojd_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motojd_zfcode',
            zfcode: '0'
        },
        {
            name: '摩托车压线',
            id: 'motoyx_code',
            code: '0',
            priority: '00',
            id_zfcode: 'motoyx_zfcode',
            zfcode: '0'
        }
    ],
    help: {
        briefIntroduction: "简介",
        functionDescription: "功能说明",
        preview: "预览",
        event: "事件",
        search: "搜索",
        snap: "抓拍",
        configuration: "配置",
        system: "系统",
        internet: "网络",
        video: "视频",
        image: "图像",
        disk: "存储",
        algorithm: "算法",
        eventDetection: "事件检测",
        generalFunctions: "通用功能",
        alarm: "多功能电警",
        bayonetPolice: "卡口电警",
        ramp: "匝道电警",
        version: "版本号V1.0",
        helpDocumentation: "Web帮助文档",
        text: {
            texts: "文档"
        }
    },
    helpDescription: {
        text: {
            p: "二、功能说明",
            p2: "在浏览器界面输入相机IP，并加上参数。http://*************，用户名admin，密码12345。",
            p3: "语言可选中文或英文（此处为中文版）.",
            p4: "登录之后首先进入的是预览界面，如果是第一次进入相机，会要求安装插件，只有安装了插件之后才能看到相机抓拍的画面。"
        },
        image: {
            descriptionImg1: "./image001.png",
            descriptionImg2: "./image003.png"
        }
    },
    helpDetect: {
        text: {
            p: "事件检测",
            p2: "算法---事件检测。单独说明事件检测的标准。根据前卡和电警相机的不同功能以及不同场景，勾选需要检测的违法事件。"
        },
        image: {
            detectImg1: "./image052.png",
            detectImg2: "./image053.png",
            detectImg3: "./image054.png"
        }
    },
    helpDetectAccess: {
        text: {
            p: "卡口电警",
            p2: "前卡事件爆闪：可以选择爆闪时间，以及各个车道单独爆闪。",
            p3: "检测违反车速规定：配合“系统配置”—“速度配置”使用。",
            p4: "检测不开车灯：自动检测。",
            p5: "检测安全带：自动检测。",
            p6: "检测打手机：自动检测。",
            p7: "检测大车占用小车道：设置车道属性，可以单独检测某一条车道。",
            p8: "检测货车占用客车道：设置车道属性，可以单独检测某一条车道。",
            p9: "检测车型闯禁令：所有车道都检测车型。可以单独设置牌照类型。"
        },
        image: {
            detectAccessImage1: "./image062.png",
            detectAccessImage2: "./image063.png",
            detectAccessImage3: "./image064.png",
            detectAccessImage4: "./image065.png",
            detectAccessImage5: "./image066.png",
        }
    },
    helpDetectCommon: {
        text: {
            p: "通用功能",
            p2: "检测卡口：车辆通过相机，抓拍一张卡口图片",
            p3: "检测机动车违反规定使用专用车道：在车道线界面，车道线属性里面设置，应急车道，非机动车道，公交车道等车道属性设置，所有经过这个车道的车辆都检测。1，2张图片在车道内，1，2，3张图片都要有位移。",
            p4: "检测无牌车：无牌车包括实际“无牌车”和看不清车牌的“污损牌车”，无牌车只出卡口，没有违法事件。无牌车也要有运动，停止的车辆不检测。",
            p5: "检测压线：根据实际情况，在车道线设置里查看车道线编号，勾选对应需要检测的车道线。车牌触碰车道线检测，检测车牌，车轮或者车身小部分压线不抓。电警压线在停止线下方，不超过停止线。",
        },
        image: {
            commonImage1: "./image055.png"
        }
    },
    helpDetectMultiple: {
        text: {
            d: "多功能电警",
            d2: "检测闯红灯：车辆违反交通指示灯。根据车辆轨迹和指示灯的情况判定，不认车道属性是直行还是左转。三张图都要是红灯。",
            d3: "检测路口滞留：直行车道，第一，第二张图绿灯，第三张图红灯。第一张图在停止线前，第二张图过停止线，不过直行判断线。前方车辆拥堵，滞留车辆影响道路通行。",
            d4: "检测逆行：第一张无要求，第二，第三张在车道内。",
            d5: "检测大弯小转：车辆没有经过路口中心，在斑马线左转。设置违停检测区，种类选择人行道。",
            d6: "检测左转不让直行：检测区域画对向直行检测区域，左转判断线画在对向检测区旁边，左转车辆碰到左转判断线的时候，对向检测区内有直行车辆。",
            d7: "检测斑马线掉头：设置违停检测区，种类为人行道，设置掉头检测区。需保证掉头车辆车牌能通过检测区，且车牌清晰",
            d8: "检测连续变道：车辆从车道1变道车道2，再变道车道3。三张图在三个车道。",
            d9: "检测变道行驶：设置需要抓拍的车道线，车道线左右两个车道检测抓拍。第一张在第一个车道，第二张车辆压线，第三张在旁边的车道内。在实线范围内，以车道线为准，不判定停止线。",
            d10: "检测违法借道：借道左行，借道直行，借道右行，看车道属性，运动方向，不看指示灯。",
            d11: "检测越线停车：三张图都是红灯，车辆没有经过直行分界线，第一张在停止线下方，第二张和第三张在停止线上方。第二，第三张图片车辆没有位移。可以选择第二第三张图片的时间间隔。停止线以上，画面25%高度为检测区域。",
            d12: "检测违章停车：三张图片都在违停检测区。普通，违停。人行道，人行道违停。网格线，网格线违停。可以选择三张图片之间的间隔，勾选拥堵检测，因为拥堵造成的违停不抓拍。"
        },
        image: {
            multipleImg1: "./image056.png",
            multipleImg2: "./image057.png",
            multipleImg3: "./image058.png",
            multipleImg4: "./image059.png",
            multipleImg5: "./image060.png",
            multipleImg6: "./image061.png"
        }
    },
    helpDetectRamp: {
        text: {
            p: "匝道电警",
            p2: "检测闯禁令（沪C）：检测沪C牌照。",
            p3: "检测闯禁令（教练车）：检测教练车。",
            p4: "检测货车禁止上匝道：检测车型货车。",
            p5: "检测闯禁令（外省市小型车）：可以按星期和时间设置。",
            p6: "检测闯禁令（黑名单）：配合工控机或者其他程序使用，可以设置8种类型。"
        },
        image: {
            rampImg1: "./image067.png",
            rampImg2: "./image068.png",
            rampImg3: "./image069.png"
        }
    },
    helpEvent: {
        text: {
            p: "事件",
            P2: "这个界面可以查看相机抓拍的事件图片和车牌号码，勾选右上角“接收事件”，就会接收相机抓拍的事件图片。左上角点击播放按键，可以实时查看相机画面。",
            p3: "左边是车牌特写图，和识别的车牌字符。中间是抓拍的车辆卡口图片和违章图片，如果点击右上角“隐藏图片”，则不会显示图片。下方是抓拍车辆信息，根据时间不断滚动。",
            p4: "注：此功能需要安装插件"
        },
        image: {
            eventImage1: "./image007.png"
        }
    },
    helpPicture: {
        text: {
            p: "抓拍",
            p2: "抓拍界面，点击右下角播放按键，相机会抓拍实时的画面，这个界面主要是在调试焦距的时候方便查看镜头焦距。通过鼠标滚轮或者键盘CTRL+上下，可以放大缩小画面，便于调试的时候使用。"
        },
        image: {
            picturesImage1: "./image011.png"
        }
    },
    helpPreview: {
        text: {
            p: "预览",
            p2: "下载插件，安装插件的时候关闭浏览器，安装完毕打开浏览器输入相机IP。进入预览界面，这个界面就是实时的相机画面。通过这个界面，在安装相机的时候，可以调整相机抓拍的画面角度。",
            p3: "注：此功能需要安装插件"
        },
        image: {
            helpPreviewImage1: "./image005.png"
        }
    },
    helpSearch: {
        text: {
            p: "搜索",
            p2: "这个界面可以查看抓拍的历史事件，根据时间，车牌号码，事件类型，车牌类型等信息，对车辆和车牌进行精准的搜索和导出对应事件的图片",
            p3: "注：此功能需要安装插件"
        },
        image: {
            helpSearchImager1: "./image009.png"
        }
    },
    helpSetting: {
        text: {
            p: "二、功能说明",
            p2: "配置界面，可以配置相机的各个参数。"
        }
    },
    helpSettingAlgorithm: {
        text: {
            p: "算法",
            p2: "检测区",
            p3: "检测区里是配置车牌大小，目标检测区，违停检测区，左直右分界线，掉头检测区，对向直行检测区。点击每个检测区的设置，检测区都有4个蓝色标记表示检测区的范围，可以移动蓝色标记来移动检测区，同时放大和缩小。",
            p4: "目标检测区：该区域为所有违法类型基础的检测区域设置，用以检测车辆轨迹及号牌的识别结果，该区域正常情况下设置范围不得大于图像的 2/3 大小，否则会导致相机无法运行抓拍。确认检测区域是否设置正常，可通过系统---设备状态，查询运行状态或者直接观察是否能够正常捕获卡口图片为设置依据。",
            p5: "违停检测区：违停区域设置，目前主要针对（滞留人行横道线/网格线区域进行设置，路口掉头违法抓拍时，也需根据抓拍要求设置借用人行道掉头的区域范围）， 设置时以实际需要抓拍的人行横道线或网格线大小为依据即可。",
            p6: "左直右分界线（左转线橙色，直行线玫红色，右转线绿色）：左转和右转分界线，其目的为检测车辆的左转和右转，故应尽量能涵盖住左转或右转车辆的转向路线，同时，需确保直行车辆不会触碰到这两个转向分界线，应尽量贴合大多数车辆的运动轨迹，在车辆即将左、右转时车头所在的位置处来绘制左、右转触发线。",
            p7: "直行触发线，其目的为检测车辆直行及是否闯直行红灯，因此，直行触发线需确保满足以下条件：",
            p8: "•\t应尽量涵盖车辆直行通过路口的路线，确保所有直行车辆都能被检测到；",
            p9: "•\t触发线不宜过长，防止左右转车辆触碰到直行触发线；",
            p10: "•\t车辆在直行触发线的位置，车牌像素值和图像质量需满足识别要求，主要是横向像素值不能低于80pix。",
            p11: "掉头检测区： 该区域目前主要针对抓拍路口掉头违法类型时，对掉头车辆驶入对向车道后的检测区域进行框选。",
            p12: "对向直行检测区： 该区域目前主要针对抓拍左转不让直行违法类型时，对向行驶车辆的行经轨迹进行设置，具体效果需要根据实际抓拍效果进行调整。",
            p13: "车道线",
            p14: "大部分情况下，根据当前车道从左至右，从下至上绘制车道线，并根据实际车道情况定义车道 属性（车道类型、车道数和车道号）。",
            p15: "车道线相关属性：按画面上实际车道画线，从画面下沿到停止线。起始车道可以选择任意车道。从左到右画车道线，车道线1没有属性，车道线2定义左边车道属性“左行”，包括行车方向，车道方向属性，车道属性。车道3定义左边车道属性“右直”。车道4定义左边车道属性“非机动”。",
            p16: "虚拟线圈：该区域主要针对视频车检器功能使用时的，虚拟检测线圈进行设置，每条车道根据实际宽度，在当前车道绘制 2 根检测绊线。紫色线段，从下到上，下面是1线，上面是2线。",
            p17: "礼让行人：礼让行人检测区域设置时，应遵循从1车道开始从左至右依次绘制的原则进行选取，每个车道检测区域，应尽量保证左右宽度多出半条车道的范围进行设置，用以保证检测行人的移动轨迹范围，便于违法判定。",
            p18: "红灯停止线：按照实际停止线画z型或者直线停止线。绘制停车线时，针对第二张违法车辆必须捕获在停车线以后的违法取证要求（闯红灯、导向车道违法等），绘制的停止线应尽量高于实际停车线 1 个车牌像素的距离。",
            p19: "信号灯",
            p20: "信号灯信息：",
            p21: "   （1） 根据实际情况配置“使能红绿灯判断-视频检测”或者“外接红绿灯信号-外接红灯信号检测”；",
            p22: "   （2）当设置外接红灯信号时，根据红灯检测器的端口接入情况，配置“左、直、右、行人”红灯的接入端口（目前大部分红灯信号左转接端口 1、直行接端口 2、右转接端口 3、行人红灯接端口）；",
            p23: "   （3）闯红灯延时判断，默认是3，根据实际情况调整。",
            p24: "   （4）红灯信号不明显的场景，可以勾选“红灯加强”，总共4个等级，4级最高。",
            p25: "   （5）夜晚黄灯时间设置，按照实际黄灯时间设置。",
            p26: "绘制信号灯：",
            p27: "点击局部放大，放大信号灯的图像，然后再点击绘制信号灯，检测区包含信号灯。",
            p28: "在图像画面中分别框出红绿灯，填写相应的配置参数后，摄像机就可以通过实况画面来分析判断红绿灯的实时状态。根据现场需要实际和需要设置的红绿灯数量，设置红灯路数，并在下方正确配置红灯属性。",
            p29: "信号灯定义：",
            p30: "按实际情况选择信号灯类型，和信号灯指示方向。对于圆头红灯，应配置类别为“机动车信号灯”、对于箭头灯，应配置类别为“方向指示信号灯”。",
            p31: "红绿灯状态",
            p32: "实时查看相机检测的红绿灯状态。",
            p33: "事件检测",
            p34: "根据前卡或者电警相机，选择需要检测抓拍的事件。所有事件类型，检测条件等选项后面单独说明。",
            p35: "系统配置",
            p36: "地区汉字代码，地区字母代码，根据相机安装地点，选择当地车牌主要的汉字和字母。",
            p37: "路口名称，路口方向，道路编号，路口编号可以根据实际道路名称输入。",
            p38: "速度测试，视频或者雷达测速。相机高度和相机距离检测区的水平距离根据实际距离填写。可以设置每条车道单独的限速大小。",
            p39: "事件图像编码参数配置，编码品质默认80，数值大图片质量就好，图片大小就大，数值小图片质量就差，图片大小就小，可以根据实际需要选择适当的数值。",
            p40: "交通扩展参数",
            p41: "调整相机抓拍事件的各个参数，最好不要改变，推荐使用默认值。\n",
            p42: "违法代码",
            p43: "根据各个地方的要求输入交警平台的违法代码，可以设置违法事件的优先级。",
            p44: "配置预览",
            p45: "所有参数修改，保存之后，必须到配置预览界面点击“下发配置”，才可以把修改保存的参数下发到相机。"
        },
        image: {
            images1: "./image034.png",
            images2: "./image035.png",
            images3: "./image036.png",
            images4: "./image037.png",
            images5: "./image038.png",
            images6: "./image039.png",
            images7: "./image040.png",
            images8: "./image041.png",
            images9: "./image042.png",
            images10: "./image043.png",
            images11: "./image044.png",
            images12: "./image045.png",
            images13: "./image046.png",
            images14: "./image047.png",
            images15: "./image048.png",
            images16: "./image049.png",
            images17: "./image050.png",
            images18: "./image051.png",
        }
    },
    helpSettingDisk: {
        text: {
            P: "存储",
            p2: "磁盘：功能正在开发中......"
        }
    },
    helpSettingImage: {
        text: {
            p: "图像",
            P2: "事件OSD叠字：配置事件的字符叠加"
        },
        image: {
            imagesImage1: "./image033.png"
        }
    },
    helpSettingInternet: {
        text: {
            p: "网络",
            p2: "高级应用",
            p3: "配置服务器地址和FTP。 配置FTP服务器时请确认用户名、密码和端口是否正确，否则将无法正常接收事件。"
        },
        image: {
            internetImg1: "./image029.png"
        }
    },
    helpSettingSystem: {
        text: {
            p: "系统",
            p2: "这个页面显示系统的主要信息，进入的默认页面为系统信息页面。",
            p3: "设备信息",
            p4: "显示相机的软硬件信息，其中包括设备的各种版本信息，授权状态等，还可以修改设备的名称。",
            p5: "设备状态",
            p6: "显示相机的信息",
            p7: "版本信息",
            p8: "显示相机的软件版本",
            p9: "设备维护",
            p10: "查看相机日志，重启相机，导入导出配置文件等功能。",
            p11: "本地配置",
            p12: "设置图片，录像，日志的保存路径。",
            p13: "注：此功能需要安装插件",
            p14: "用户",
            p15: "功能正在开发中......"
        },
        image: {
            system_img1: "./image013.png",
            system_img2: "./image015.png",
            system_img3: "./image017.png",
            system_img4: "./image019.png",
            system_img5: "./image021.png",
            system_img6: "./image023.png",
        }
    },
    helpSettingVideo: {
        text: {
            p: "视频",
            P2: "视频参数",
            p3: "设置视频播放方式"
        },
        image: {
            videos_img1: "./image031.png"
        }
    },
    helpSynopsis: {
        text: {
            p: "一、简介",
            p2: "在浏览器输入IP地址（https://IP地址），登陆成功后操作步骤如下：",
            p3: "步骤1：依次点击“App桌面”>“ENTER”，出现登陆界面。",
            p4: "步骤2：首次登录，默认账号为admin，密码为12345。",
            p5: "步骤3：单击“登录”。",
            p6: "首次登录后，需要手动单击预览框中的“点此下载安装”来下载并安装播放控件，完成后即可查看实况视频。如果控件被浏览器阻止，请根据页面提示进行操作。",
            p7: "最新的相机配置界面已经移植到web界面，可以不使用控制台，直接打开网页就可以在相机的界面中配置相机参数。",
            p8: "进入web界面的方法，直接输入相机IP。例http://*************，用户名admin，密码12345。",
            p9: "浏览器：IE11版本以上、360安全浏览器的兼容模式、360极速浏览器的兼容模式和猎豹浏览器6.5版本以上。",
        },
        image: {
            synopsisImg1: "./image070.png"
        }
    }
}
