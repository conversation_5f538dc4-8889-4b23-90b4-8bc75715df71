var table, form, reloadTime = 0, configInterval;

layui.use(['element', 'layer', 'form', 'table'], function () {
    table = layui.table, form = layui.form;
});
/**
 * 根据传入的页面名称保存配置
 * @param container
 */
var saveConfig = function (container) {
    let alreadyHidden = [];
    let reg = /draw.*/;
    let checkType = reg.test(container);
    if (checkType) {
        let globalInfo = checkGlobal();
        alreadyHidden = hiddenZone("test", container, globalInfo);
        $("button").removeClass('layui-btn-normal');
    }
    let dataArray = getSe("projectConfig").settingShow[container].save;
    let clearArray = getSe("clearArray");
    if (!clearArray) {
        clearArray = {}
    }
    if (clearArray) {
        let keys = Object.keys(clearArray);
        for (let i = 0; i < keys.length; i++) {
            let key = keys[i];
            if (dataArray.indexOf(key) > -1) {
                deleteSession([key, key + "Value"])
            }
        }
    }
    if (container === 'drawSignal') {
        let signalType = {};
        signalType.signalNum = parseInt($("#signalNumber option:selected").val());
        let signals = [];
        let signalReal = [];
        for (let i = 0; i < parseInt(signalType.signalNum); i++) {
            let signal = {};
            let type = $("#type_" + (i + 1) + " select");
            signal.signalCategory = parseInt($(type[0]).find("option:selected").val());
            signal.signalDirection = parseInt($(type[1]).find("option:selected").val());
            signal.signalShape = parseInt($(type[2]).find("option:selected").val());
            signal.signalType = parseInt($(type[3]).find("option:selected").val());
            signals.push(signal);
            let real = [];
            for (let j = 0; j < 2; j++) {
                let p = {};
                p.x = parseInt($($(".control-point .signal-point")[2 * i + j]).find(".input-number")[0].value);
                p.y = parseInt($($(".control-point .signal-point")[2 * i + j]).find(".input-number")[1].value);
                real.push(p)
            }
            signalReal.push(real);
        }
        for (let i = 0; i < signalReal.length; i++) {
            let w = Math.abs(signalReal[i][0].x - signalReal[i][1].x);
            let h = Math.abs(signalReal[i][0].y - signalReal[i][1].y);
            if (w < 5) {
                layer.msg('信号灯宽度不能小于5！', {icon: 2});
                return
            }
        }
        signalType.signals = signals;
        signalType.signal = $("#signalJudge")[0].checked === true ? 1 : 0;
        signalType.extendSignal = parseInt($("#extendSignal option:selected").val());
        if (signalType.extendSignal) {
            signalType.signalLeftNumber = parseInt($("#signalLeftNumber option:selected").val());
            signalType.signalStraightNumber = parseInt($("#signalStraightNumber option:selected").val());
            signalType.signalRightNumber = parseInt($("#signalRightNumber option:selected").val());
            signalType.signalPeopleNumber = parseInt($("#signalPeopleNumber option:selected").val());
        }
        signalType.signalTime = parseInt($("#signalTime").val());
        signalType.signalIP = $("#signalIP").val();
        signalType.signalStronger = parseInt($("#strongerNum").val());
        if (signalType.signalStronger) {
            signalType.signalStrongStartTime = date2Value($("#StrongStartTime").val(), 1);
            signalType.signalStrongEndTime = date2Value($("#StrongEndTime").val(), 1);
        }
        signalType.signalYellow = $("#signalYellow")[0].checked === true ? 1 : 0;
        if (signalType.signalYellow) {
            signalType.yellowStartTime = date2Value($("#YellowStartTime").val(), 30);
            signalType.yellowEndTime = date2Value($("#YellowEndTime").val(), 30);
        }
        signalType.RS485 = parseInt($("#RS485 option:selected").val());
        setSe('signalType', signalType);
        setSe('signalValue', signalReal);
    }
    console.log(getSe('signalType'))
    console.log(getSe('signalValue'))
    let allPositions = [];
    let po = $("#" + container + " .point");
    if (po.length > 0) {
        for (let i = 0; i < po.length; i++) {
            let item = po[i];
            $(item).css({display: 'block', visibility: 'hidden'});
            let newItem = {};
            let position = [];
            let id = item.id;
            // if (id === 'redStop') {
            //     $("#redStop .redStop-can").css({
            //         display: 'block'
            //         , visibility: 'hidden'
            //     });
            // }
            newItem.id = id;
            let classes = item.className;
            classes = classes.split(" ");
            let type = classes[1];
            if (classes[2]) {
                newItem.lineType = classes[2]
            }
            if (type === 'common-rect') {
                position = getCommonPosition(item.id)
            } else if (type === 'fixed-rect') {
                position = getFixedPosition(item.id)
            } else if (type === 'common-line') {
                position = getCommonLinePosition(item.id)
            } else if (type === "common-rect-group") {
                position = getGroupCommonPosition(item.id)
            } else if (type === "common-line-group") {
                if (id === 'redStop') {
                    position = getCommonLinePosition(item.id)
                } else {
                    position = getGroupCommonLinePosition(item.id)
                }

            } else if (type === "fixed-rect-group") {
                position = getGroupFixedPosition(item.id)
            }
            newItem.type = type;
            let specialType = ['redStop', 'turnLeft', 'goStraight', 'turnRight', 'cartTurnRight', 'upCalibration', 'downCalibration'];
            let index = -1;
            index = $.inArray(newItem.id, specialType);
            let nLength;
            if (index > 0) {
                nLength = 0;
                newItem.use = $("#" + specialType[index] + "-signal")[0].checked === true ? 1 : 0;
                if (!newItem.use) {
                    $(item).css({display: 'none'});
                } else {
                    nLength = 1;
                }
            } else if (index === 0) {
                nLength = 0;
                if ($("#redStopType option:selected").val() === '0') {
                    nLength = 1;
                    newItem.use = $("." + specialType[index] + "-signal")[0].checked === true ? 1 : 0;
                } else {
                    let use = [];
                    $("." + specialType[index] + "-signal").each(function (i) {
                        use.push(this.checked === true ? 1 : 0);
                        if (!this.checked) {
                            $("#redStop .redStop-can" + i).css({
                                display: 'none'
                            });
                        } else {
                            nLength += 1;
                        }
                    });
                    newItem.use = use;
                }
            }
            if (index >= -1 && position.length !== nLength) {
                for (let i = 0; i < nLength - position.length; i++) {
                    position.push([{x: 0, y: 0}, {x: 0, y: 0}])
                }
            }
            newItem.position = position;
            if (newItem.id === 'upCalibration') {
                newItem.up_calibration_queue_length = Number($(".upCalibration-queueLength input").val());
            }
            if (newItem.id === 'downCalibration') {
                newItem.down_calibration_queue_length = Number($(".downCalibration-queueLength input").val());
            }

            allPositions.push(newItem);
        }
    }
    for (let i = 0; i < allPositions.length; i++) {
        setSe(allPositions[i].id, allPositions[i]);
        if (allPositions[i].id !== 'signal') {
            let value = coordinate2Value(allPositions[i].position);
            setSe(allPositions[i].id + "Value", value)
        }
    }
    let inputValue = {};
    if (container === 'drawSurvey') {
        inputValue.plateMin = $("#slideMinWidth input").val();
        inputValue.plateMax = $("#slideMaxWidth input").val();
        inputValue.noParkingType = $("#noParkingType option:selected").val();
    }
    if (container === 'drawLine') {
        let lineType = {};
        let groupNum = parseInt($("#lineNumber option:selected").val());
        groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
        lineType.lineNum = groupNum
        // lineType.lineStart = parseInt($("#lineStart option:selected").val());
        lineType.lineStart = 0;
        lineType.lineStartNum = parseInt($("#lineStartNumber option:selected").val());
        let lines = [];
        for (let i = 0; i < parseInt(lineType.lineNum); i++) { // parseInt(lineType.lineNum) + 1
            let line = {};
            line.type = parseInt($("#type_" + i + " option:selected").val());
            if (i >= 1) {
                line.direction = parseInt($("#direction_" + i + " option:selected").val());
                line.define = parseInt($("#define_" + i + " option:selected").val());
                line.special = parseInt($("#special_" + i + " option:selected").val());
                line.leftWait = parseInt($("#leftWait" + i + " option:selected").val());
                // 公交车道和单行道，下面的时间段才起效
                if (parseInt(line.special) === 4 || parseInt(line.special) === 64) {
                    let time1 = $("#line_" + i + "_1").val().split(' - ');
                    let time2 = $("#line_" + i + "_2").val().split(' - ');
                    let time3 = $("#line_" + i + "_3").val().split(' - ');
                    if (time1.length === 2) {
                        line.time1start = date2Value(time1[0].length >= 0 ? time1[0] : 0, 1);
                        line.time1end = date2Value(time1[1].length >= 0 ? time1[1] : 0, 1);
                    }
                    if (time2.length === 2) {
                        line.time2start = date2Value(time2[0].length >= 0 ? time2[0] : 0, 1);
                        line.time2end = date2Value(time2[1].length >= 0 ? time2[1] : 0, 1);
                    }
                    if (time3.length === 2) {
                        line.time3start = date2Value(time3[0].length >= 0 ? time3[0] : 0, 1);
                        line.time3end = date2Value(time3[1].length >= 0 ? time3[1] : 0, 1);
                    }
                }
            }
            lines.push(line)
        }
        lineType.lines = lines;
        setSe('lineType', lineType);
        inputValue.redStopType = $("#redStopType").val();
        // sessionStorage.setItem('redStopType', redStopType);
    }
    if (container === 'drawLineSurvey') {
        let lineType = {};
        let groupNum = parseInt($("#lineNumber option:selected").val());
        groupNum = groupNum === 0 ? groupNum : (groupNum + 1)
        lineType.lineNum = groupNum
        // lineType.lineStart = parseInt($("#lineStart option:selected").val());
        lineType.lineStart = 0;
        lineType.lineStartNum = parseInt($("#lineStartNumber option:selected").val());
        let lines = [];
        for (let i = 0; i < parseInt(lineType.lineNum); i++) { // parseInt(lineType.lineNum) + 1
            let line = {};
            line.type = parseInt($("#type_" + i + " option:selected").val());
            if (i >= 1) {
                line.direction = parseInt($("#direction_" + i + " option:selected").val());
                line.define = parseInt($("#define_" + i + " option:selected").val());
                line.special = parseInt($("#special_" + i + " option:selected").val());
                line.leftWait = parseInt($("#leftWait" + i + " option:selected").val());
                // 公交车道和单行道，下面的时间段才起效
                if (parseInt(line.special) === 4 || parseInt(line.special) === 64) {
                    let time1 = $("#line_" + i + "_1").val().split(' - ');
                    let time2 = $("#line_" + i + "_2").val().split(' - ');
                    let time3 = $("#line_" + i + "_3").val().split(' - ');
                    if (time1.length === 2) {
                        line.time1start = date2Value(time1[0].length >= 0 ? time1[0] : 0, 1);
                        line.time1end = date2Value(time1[1].length >= 0 ? time1[1] : 0, 1);
                    }
                    if (time2.length === 2) {
                        line.time2start = date2Value(time2[0].length >= 0 ? time2[0] : 0, 1);
                        line.time2end = date2Value(time2[1].length >= 0 ? time2[1] : 0, 1);
                    }
                    if (time3.length === 2) {
                        line.time3start = date2Value(time3[0].length >= 0 ? time3[0] : 0, 1);
                        line.time3end = date2Value(time3[1].length >= 0 ? time3[1] : 0, 1);
                    }
                }
            }
            lines.push(line)
        }
        lineType.lines = lines;
        setSe('lineType', lineType);
        inputValue.redStopType = $("#redStopType").val();


        inputValue.plateMin = $("#slideMinWidth input").val();
        inputValue.plateMax = $("#slideMaxWidth input").val();
        inputValue.noParkingType = $("#noParkingType option:selected").val();
    }

    for (let dataIndex in inputValue) {
        if (inputValue[dataIndex] !== '' || inputValue[dataIndex] !== undefined) {
            setSe(dataIndex, inputValue[dataIndex])
        }
    }
    for (let i = 0; i < alreadyHidden.length; i++) {
        $("#" + alreadyHidden[i]).css({
            display: "none"
        })
    }
    layer.msg(langMessage.setting.saveConfigSuc, {icon: 1});
};
/**
 * 根据传入的页面名称显示配置
 * @param type
 */
var showConfig = function (type) {
    let reg = /draw.*/;
    let checkType = reg.test(type);
    if (checkType) {
        $(".draw-container").html("");
        let globalInfo = checkGlobal();
        hiddenZone("test", type, globalInfo);
        $("button").removeClass('layui-btn-normal');
    }
    if (type !== 'videoDisplay' && type !== 'videoSignal') {
        show();
    }
};


var leftStraightRightCheck = function () {
    let dataArray = ['turnLeft', 'turnRight', 'goStraight', 'cartTurnRight', 'upCalibration', 'downCalibration'];
    for (let i in dataArray) {
        let type = dataArray[i];
        let turnLeft = getSe(type);
        if (turnLeft) {
            let use = turnLeft.use;
            if (use === 1) {
                $("#" + type + "-signal")[0].checked = true;
                $("#" + type + "Btn").removeClass('layui-btn-disabled').removeAttr('disabled');
                $("#" + type).css({
                    display: 'block'
                });
                $("#" + type + "Zone").css({
                    display: 'block'
                });
                if (type === 'upCalibration') {
                    $("." + type + "-queueLength input").val(turnLeft.up_calibration_queue_length);
                }
                if (type === 'downCalibration') {
                    $("." + type + "-queueLength input").val(turnLeft.down_calibration_queue_length);
                }
            } else {
                $("#" + type + "-signal")[0].checked = false;
                $("#" + type + "Btn").addClass('layui-btn-disabled').attr('disabled', 'disabled');
                $("#" + type).css({
                    display: 'none'
                });
                $("#" + type + "Zone").css({
                    display: 'none'
                });
            }
        }
    }
};

var clearNowConfig = function () {
    let nowBtn = $(".layui-btn-normal");
    if (nowBtn.length !== 1) {
        console.log("error");
        return
    }
    let nowId = nowBtn[0].id.slice(0, -3);
    let clearArray = getSe("clearArray");
    if (!clearArray) {
        clearArray = {}
    }
    clearArray[nowId] = true;
    setSe("clearArray", clearArray);
    if (nowId === 'line') {
        changeLineNumber(0)
    } else if (nowId === 'signal') {
        $("#" + nowId + "Big").html("");
    }
    $("#" + nowId).html("");
    $("." + nowId + "-point").find("input.input-number").val(0).attr("disabled", true);
    $("." + nowId + "-queueLength input").val(0);

    let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").find(".draw-container")[0].id;
    drawPath(drawCanvas, drawContainer)

};
var setPositionDefault = function (container, pointInfo, alreadyLength) {
    let pI = pointInfo.number;
    let num1 = pI[0];
    let num2 = pI[1];
    let name = pointInfo.name;
    let reg = 'light';
    let lightFlag = reg === name;
    if (num1) {
        for (let i = alreadyLength; i < num1; i++) {
            if (num2) {
                let type = name + i;
                for (let j = 0; j < num2; j++) {
                    let xName = type + "_x" + j;
                    let yName = type + "_y" + j;
                    container[xName] = 0;
                    container[yName] = 0;
                }

            } else {
                let type = name;
                if (lightFlag) {
                    let xName = type + i + '_x0';
                    let yName = type + i + '_y0';
                    let wName = type + i + '_w';
                    let hName = type + i + '_h';
                    container[xName] = 0;
                    container[yName] = 0;
                    container[wName] = 0;
                    container[hName] = 0;
                } else {
                    let xName = type + "_x" + i;
                    let yName = type + "_y" + i;
                    container[xName] = 0;
                    container[yName] = 0;
                }
            }
        }
    }
};
var changeLineNumber = function (num) {
    num = Number(num)
    let lineN = document.getElementsByClassName("line-can").length;
    let optionsNum = num === 0 ? num : (num + 1); //得到被选中的值
    let liNum = parseInt(lineN / 2);
    let drawCanvas = $("#drawContent").children(".draw-canvas")[0].id;
    let drawContainer = $("#drawContent").children(".draw-container")[0].id;
    if (optionsNum > liNum) {
    } else if (optionsNum < liNum) {
        for (let i = 0; i < liNum - optionsNum; i++) {
            $(".line-can" + (liNum - i)).remove();
            $(".line-group" + (liNum - i)).find(".input-number").val(0);
            if ((liNum - i - 1) > 0) {
                $("#floor1 .line-group" + (liNum - i - 1)).remove();
                $("#floor2 .line-group" + (liNum - i - 1)).remove();
                $("#floor3 .line-group" + (liNum - i - 1)).remove();
                $("#floor4 .line-group" + (liNum - i - 1)).remove();
                $("#floor5 .line-group" + (liNum - i - 1)).remove();
                $("#people1 .rect-group" + (liNum - i - 1)).remove();
                $("#people2 .rect-group" + (liNum - i - 1)).remove();
                $("#people3 .rect-group" + (liNum - i - 1)).remove();
                $("#people4 .rect-group" + (liNum - i - 1)).remove();
                $("#people5 .rect-group" + (liNum - i - 1)).remove();
                $("#redStop .redStop-can" + (liNum - i - 1)).remove();

                $(".floor1-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".floor2-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".floor3-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".floor4-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".floor5-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".people1-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".people2-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".people3-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".people4-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".people5-group" + (liNum - i - 1)).find(".input-number").val(0);
                $(".redStop-group" + (liNum - i - 1)).find(".input-number").val(0).attr({disabled: "disabled"});
                // drawAll('line', drawCanvas, drawContainer);
                $($("#lineNumber option")[(liNum - i - 1)]).attr("disabled", true);
                $(".redStop-group" + (liNum - i - 1)).find(".redStop-signal")[0].checked = false;
            }
            drawAll('line', drawCanvas, drawContainer);

            $($(".my-colla-item")[liNum - i - 1]).addClass('common-none');
            $($(".tab-item")[liNum - i - 1]).addClass('common-none1');
        }
    }
    let length = document.getElementsByClassName("line-can").length;
    if (length) {
        for (let i = 0; i < length; i++) {
            for (let j = 0; j < 4; j++) {
                $($(".line-point button[type=button]")[4 * i + j]).attr("disabled", false);
            }
        }
    }
    let redType = $("#redStopType option:selected").val();
    if (redType === '1') {
        let groupNum = optionsNum;
        $("#redStopBtn").attr('onclick', "commonLine('redStop'," + parseInt(groupNum - 1) + ",false)");
        for (let i = 0; i < 5; i++) {
            if (i < (groupNum - 1)) {
                $(".redStop-group" + (i + 1)).removeClass("common-none")
            } else {
                $(".redStop-group" + (i + 1)).addClass("common-none")
            }
        }
    }
    $("#lineNumber").val(optionsNum === 0 ? optionsNum : (optionsNum - 1));
    form.render();
};
