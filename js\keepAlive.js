var monitorActTimeOut = null
var isKeepAlive = true
$(document).ready(function () {
    document.body.addEventListener('mouseup', catchAct)
    // document.body.addEventListener('mousemove', catchAct)
    document.body.addEventListener('keyup', catchAct)
    document.body.addEventListener('click', catchAct)
    document.body.addEventListener('touchend', catchAct)

    var inter = setInterval(function () {
        console.log('1')
        if (!isKeepAlive) {
            console.log('2')
            clearInterval(inter)
        }
        initWebService(webserviceCMD.CMD_GET_THRD_STATE, null, hhhh)


        // SDC也支持，但因为html这种文件，不符合他们的要求，没有走到nginx中的判断
        // let appName = getAppName()
        // let url = "https://" + location.host + "/SDCWEB/" + appName + "/index.html";
        // $.ajax({
        //     type: "GET",
        //     contentType: "text/html; charset=utf-8",
        //     url: url,
        //     dataType: 'html',
        //     timeout: 5000,
        //     success: function (response) {
        //     },
        //     error: function (XMLHttpRequest, textStatus, errorThrown) {
        //         console.log('keep alive error1')
        //     }
        // });

        // https://************/cgi-bin/main.cgi?action=WebSessionAlive
        // action: WebSessionAlive
        // glToken: 65a24520f2827487e082e9440a0f85256d38e461d60883e7
        // stChannelId: 101
        // channelId: 101
        // para:
        // 华为页面本身还会发cgi请求
        // $.ajax({
        //     type: "POST",
        //     contentType: "application/x-www-form-urlencoded; charset=UTF-8",
        //     url: "https://" + location.host + "/cgi-bin/main.cgi?action=WebSessionAlive",
        //     data: {
        //         action: 'WebSessionAlive',
        //         glToken: sessionStorage.getItem('glToken'),
        //         stChannelId: 101,
        //         channelId: 101,
        //         para: ''
        //     },
        //     timeout: 5000,
        //     success: function (response) {
        //     },
        //     error: function (XMLHttpRequest, textStatus, errorThrown) {
        //         console.log('keep alive error2')
        //     }
        // });
    }, 30 * 1000)
})
var catchAct = function (e) {
    console.log('操作类型：' + e.type)
    clearTimeout(monitorActTimeOut)
    monitorActTimeOut = setTimeout(() => {
        isKeepAlive = false
    }, 30 * 60 * 1000)
}

var removeAct = function () {
    console.log('removeAct')
    clearTimeout(monitorActTimeOut)
    document.body.removeEventListener('mouseup', catchAct)
    document.body.removeEventListener('mousemove', catchAct)
    document.body.removeEventListener('keyup', catchAct)
    document.body.removeEventListener('click', catchAct)
    document.body.removeEventListener('touchend', catchAct)
}

var hhhh = function (data) {

}
