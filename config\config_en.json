﻿{
  "index_ocx": {
    "main": 3,
    "sub": 1,
    "revise": 0
  },
  "allowedAuth": [
    "TOPSKY_H_HOLO",
    "TOPSKY_H_HOLO_M",
    "TOPSKY_H_HOLO_X",
    "TOPSKY_T_HOLO",
    "TOPSKY_T_HOLO_M",
    "TOPSKY_T_HOLO_X",
    "TOPSKY_H_HOLOS",
    "TOPSKY_H_HOLOS_M",
    "TOPSKY_H_HOLOS_X",
    "TOPSKY_T_HOLOS",
    "TOPSKY_T_HOLOS_M",
    "TOPSKY_T_HOLOS_X",
    "TOPSKY_T_YZWT",
    "TOPSKY_T_YZWT_M",
    "TOPSKY_T_YZWT_X",
    "TOPSKY_T_HWCS_X",
    "JOYWARE_T_HOLOS_X",
    "JOYWARE_H_HOLOS_X"
  ],
  "protocol": "1",
  "copyright": "© 2019",
  "index_reboot_time": 30,
  "index_auth_time": 30,
  "camera_username": "admin",
  "camera_password": "HuaWei123",
  "camera_stream": "/LiveMedia/ch1/Media",
  "camera_channel": "1",
  "oldEventPort": 60000,
  "newEventPort": 3128,
  "default_color": {
    "surveyColor": "#FF0000",
    "noParkingColor": "#ed0973",
    "aroundColor": "#0193de",
    "faceColor": "#f18800",
    "lineColor": "#00FFFF",
    "lineNumberColor": "#00FFFF",
    "plateColor": "#00ee00",
    "people1Color": "#eec700",
    "people2Color": "#eec700",
    "people3Color": "#eec700",
    "people4Color": "#eec700",
    "people5Color": "#eec700",
    "floor1Color": "#8D00FF",
    "floor2Color": "#8D00FF",
    "floor3Color": "#8D00FF",
    "floor4Color": "#8D00FF",
    "floor5Color": "#8D00FF",
    "redLightColor": "#637acd",
    "turnLeftColor": "#f8810a",
    "goStraightColor": "#1794e7",
    "turnRightColor": "#0cf42b",
    "cartTurnRightColor": "#0443CC",
    "upCalibrationColor": "#fc2a0a",
    "downCalibrationColor": "#fc2a0a",
    "redStopColor": "#e3f211",
    "signalColor": "#4909f9",
    "biggerColor": "#000"
  },
  "logo_size": "s",
  "auth_status": {
    "0": "Unauthorized",
    "10001": "Front compound electric alarm",
    "10002": "Recognition of basic features of vehicle head bayonet",
    "10003": "AI feature recognition of vehicle head",
    "10004": "Abnormal driving detection",
    "10005": "",
    "10071": "Traffic incident detection",
    "10072": "Visibility detection",
    "10081": "Composite electric police at intelligent intersection",
    "10082": "Traffic parameter detection",
    "10083": "Continuous lane changing",
    "10084": "",
    "10085": "Snapshot of traffic not in turn",
    "10086": "Changing lanes without lighting",
    "70001": "Comprehensive algorithm of electric alarm for vehicle head bayonet",
    "70071": "AI event synthesis algorithm",
    "70081": "Intelligent intersection section synthesis algorithm",
    "30081": "Source signal intelligent intersection synthesis algorithm",
    "99999": "Test version"
  },
  "settingShow": {
    "drawSurvey": {
      "show": [
        "survey",
        "line",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ],
      "save": [
        "survey",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ]
    },
    "drawLine": {
      "show": [
        "survey",
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop"
      ],
      "save": [
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop"
      ]
    },
    "drawLineSurvey": {
      "show": [
        "survey",
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop",
        "survey",
        "line",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ],
      "save": [
        "line",
        "floor1",
        "floor2",
        "floor3",
        "floor4",
        "floor5",
        "people1",
        "people2",
        "people3",
        "people4",
        "people5",
        "redStop",
        "survey",
        "noParking",
        "turnLeft",
        "goStraight",
        "turnRight",
        "cartTurnRight",
        "upCalibration",
        "downCalibration",
        "around",
        "face"
      ]
    },
    "drawSignal": {
      "show": [
        "survey",
        "signal"
      ],
      "save": [
        "signal"
      ]
    },
    "drawPreview": {
      "show": [
        "survey",
        "noParking",
        "around",
        "face",
        "line",
        "signal"
      ],
      "save": []
    }
  },
  "ocxMsg": {
    "TRUE": {
      "result": true,
      "msg": "success"
    },
    "0": {
      "result": true,
      "msg": "success"
    },
    "FALSE": {
      "result": false,
      "msg": "fail"
    },
    "-1": {
      "result": false,
      "msg": "fail"
    },
    "INITED": {
      "result": true,
      "msg": "The video stream has been initialized"
    },
    "Protected Mode": {
      "result": false,
      "msg": "The system is in protected mode and cannot use the plug-in. Please turn off the protection mode of the browser.。",
      "offset": "rb"
    },
    "Json Is Error": {
      "result": false,
      "msg": "Parameter error"
    },
    "Create Conf_File Failed": {
      "result": false,
      "msg": "Failed to create file"
    },
    "Conf File Clean Failed": {
      "result": false,
      "msg": "Empty file failed"
    },
    "Add_New_Conf Failed": {
      "result": false,
      "msg": "Failed to add new configuratio"
    },
    "G_VARIABLE MALLOC FAILED": {
      "result": false,
      "msg": "Configuration variable failed to allocate memory"
    },
    "ALREADY INIT": {
      "result": true,
      "msg": "Already initialized"
    },
    "EVENT NOT STOP": {
      "result": false,
      "msg": "Event thread not stopped"
    },
    "ERROR CODE": {
      "result": false,
      "msg": "Failed to read image path"
    },
    "PATH SELECT ERROR": {
      "result": false,
      "msg": "Wrong path selection"
    }
  },
  "newestOcxVersion": {
    "main": 3,
    "sub": 3,
    "revise": 2
  },
  "web_version": "2025年6月24日18:29:54",
  "mode": "local-dev"
}
