<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>settingLine</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/setting.css">
    <link rel="stylesheet" href="../../css/myTab.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/DomOperation.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/settingDraw.js"></script>
    <script src="../../js/drawCanvas.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/myTab.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/settingLine.js"></script>
    <style>
    </style>
</head>
<body class="sub-body custom-style">
<div class="layui-tab-item layui-show">
    <div class="layui-row">
        <div class="" id="drawContent">
            <img id="configImg" src="../../img/404.png" class="draw-img"/>
            <canvas id="lineCan" width="600" height="450" class="draw-canvas">
            </canvas>
            <div id="drawLine" class="draw-container">
            </div>
        </div>
        <div class="layui-col-md4 config-setting  layui-form" style="padding-left: 10px">
            <button class="layui-btn layui-btn-default" id="refreshPic"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">刷新场景
            </button>
            <button id="saveConfig" class="layui-btn layui-btn-default layui-btn-block"
                    onclick="saveConfig('drawLine')" style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">保存配置
            </button>
            <button id="clearNowConfig" class="layui-btn layui-btn-default" onclick="clearNowConfig()"
                    style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">清除当前绘制区域
            </button>
            <button id="showConfig" class="layui-btn layui-btn-default layui-btn-block"
                    onclick="showConfig('drawLine')" style="margin-bottom: 10px;margin-left: 0;margin-right: 10px">
                重置配置
            </button>

            <div class="layui-collapse" lay-accordion style="margin-top: 10px;max-width: 550px;min-width: 450px">
                <div class="layui-colla-item">
                    <h2 id="attributes" class="layui-colla-title">车道线相关属性</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <button class="layui-btn layui-btn-default" id="lineBtn"
                                    onclick="commonLine('line',6)">
                                绘制车道线
                            </button>
                            <div class="layui-form-item">
                                <label id="lane" for="lineNumber">车道数目：</label>
                                <div class="layui-input-inline">
                                    <select id="lineNumber" lay-filter="lineNumber">
                                        <option value="0">0</option>
                                        <option value="1" disabled="disabled">1</option>
                                        <option value="2" disabled="disabled">2</option>
                                        <option value="3" disabled="disabled">3</option>
                                        <option value="4" disabled="disabled">4</option>
                                        <option value="5" disabled="disabled">5</option>
                                        <!--                                        <option value="6" disabled="disabled">6</option>-->
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label id="begin" for="lineStartNumber">起始车道：</label>
                                <div class="layui-input-inline">
                                    <select id="lineStartNumber" lay-filter="lineStartNumber">
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item common-none">
                                <label class="direction" for="lineStart">方向：</label>
                                <div class="layui-input-inline">
                                    <select id="lineStart" lay-filter="lineStart">
                                        <option id="left" value="0">左>右</option>
                                        <option id="right" value="1">右>左</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item  common-none">
                                <label class="kind" for="lineLineType">样式：</label>
                                <div class="layui-input-inline">
                                    <select id="lineLineType">
                                        <option class="real" value="solid">实线</option>
                                        <option class="false" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input color" id="lineColor">
                                </div>
                                <div class="layui-inline">
                                    <div id="lineColorEle"></div>
                                </div>
                            </div>


                        </div>
                        <div class="my-collapse">
                            <div class="my-colla-item my-this">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line0">车道线0属性</h4>
                                    </div>
                                    <div class="my-colla-tab-title">
                                        <ul>
                                            <li class="line0" class="my-this">车道线0属性</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group1">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>

                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_0" class="config-inline"
                                                        lay-filter="type_0">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="my-colla-item common-none">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line1">车道线1属性</h4>
                                    </div>
                                    <ul class="my-colla-tab-title">
                                        <li class="line1 my-this">车道线1属性</li>
                                        <li class="virtualGround common-none">虚拟地感检测线</li>
                                        <li class="pedestrian common-none">礼让行人检测区</li>
                                    </ul>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group2">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_1" class="config-inline"
                                                        lay-filter="type_1">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                        <table class="config-table" id="line_1">
                                            <tr>
                                                <td><label class="direction" for="direction_1">方向：</label></td>
                                                <td><label class="definition" for="define_1">行车定义：</label></td>
                                                <td><label class="waitArea" for="leftWait1">待行区：</label></td>
                                                <td><label class="property" for="special_1">特殊属性：</label></td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="direction_1" class="line-direction"
                                                                lay-filter="direction_1">
                                                            <option class="up" value="0">上行</option>
                                                            <option class="down" value="1">下行</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="define_1" class="line-define config-inline"
                                                                lay-filter="define_1">
                                                            <option class="left1" value="0x0001">左行</option>
                                                            <option class="straight" value="0x0002">直行</option>
                                                            <option class="right1" value="0x0004">右行</option>
                                                            <option class="turnAround" value="0x0008">掉头</option>
                                                            <option class="leftStraight" value="0x0003">左直</option>
                                                            <option class="rightStraight" value="0x0006">右直</option>
                                                            <option class="turnLeft" value="0x0009">左掉头</option>
                                                            <option class="leftRight" value="0x0005">左右</option>
                                                            <option class="turnLeftRightStraight" value="0x000f">左直右掉头
                                                            </option>
                                                            <option class="turnLeftStraight" value="0x000b">左直掉头
                                                            </option>
                                                            <option class="turnLeftRight" value="0x000d">左右掉头</option>
                                                        </select>
                                                    </div>
                                                </td>

                                                <td>
                                                    <div class="config-inline">
                                                        <select name="" id="leftWait1"
                                                                class="line-left-wait config-inline dis"
                                                                disabled="disabled">
                                                            <option class="no" value="0">无</option>
                                                            <option class="have" value="1">有</option>
                                                        </select>
                                                    </div>

                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="special_1" class="line-special config-inline"
                                                                lay-filter="special_1">
                                                            <option id="ordinary" value="0x0000">普通车道</option>
                                                            <option id="emergency" value="0x0001">应急车道</option>
                                                            <option id="nonMotorized" value="0x0002">非机动车道</option>
                                                            <option id="bus" value="0x0004">公交车道</option>
                                                            <option id="diversion" value="0x0008">导流带</option>
                                                            <option id="single" value="0x0040">单行道</option>
                                                            <option id="noLefTurn" value="0x0010">禁止左拐</option>
                                                            <option id="noRightTurn" value="0x0020">禁止右拐</option>
                                                        </select>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                        <!-- #####################################################公交车道和单行道，下面的时间段才起效-->
                                        <table class="time-table">
                                            <tr class="time-1 common-none">
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label">时间段1</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time1" id="line_1_1"
                                                                   placeholder="请选择时间段1">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label">时间段2</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time2" id="line_1_2"
                                                                   placeholder="请选择时间段2">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label">时间段3</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time3" id="line_1_3"
                                                                   placeholder="请选择时间段3 ">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>


                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="floorTips1"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="floor1Btn"
                                                            disabled="disabled"
                                                            onclick="commonLine('floor1',2,'true')">
                                                        虚拟地感
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point floor1-group floor1-group1">
                                            <div class="floor1-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x1："></div>
                                                <div class="input-range"
                                                     data-label="1.y1："></div>
                                            </div>
                                            <div class="floor1-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x2："></div>
                                                <div class="input-range"
                                                     data-label="1.y2："></div>
                                            </div>
                                            <div class="floor1-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x1："></div>
                                                <div class="input-range"
                                                     data-label="2.y1："></div>
                                            </div>
                                            <div class="floor1-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x2："></div>
                                                <div class="input-range"
                                                     data-label="2.y2："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="peopleTips1"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="people1Btn"
                                                            disabled="disabled"
                                                            onclick="CommonRect('people1')">
                                                        礼让行人
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point people1-group people1-group1">
                                            <div class="people1-point index-point">
                                                <div class="input-range"
                                                     data-label="X1："></div>
                                                <div class="input-range"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="people1-point index-point">
                                                <div class="input-range"
                                                     data-label="X2："></div>
                                                <div class="input-range"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="people1-point index-point">
                                                <div class="input-range"
                                                     data-label="X3："></div>
                                                <div class="input-range"
                                                     data-label="Y3："></div>
                                            </div>
                                            <div class="people1-point index-point">
                                                <div class="input-range"
                                                     data-label="X4："></div>
                                                <div class="input-range"
                                                     data-label="Y4："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="my-colla-item common-none">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line2">车道线2属性</h4>
                                    </div>
                                    <ul class="my-colla-tab-title">
                                        <li class="line2" class="my-this">车道线2属性</li>
                                        <li class="virtualGround" class="common-none">虚拟地感检测线</li>
                                        <li class="pedestrian" class="common-none">礼让行人检测区</li>
                                    </ul>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group3">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_2" class="config-inline"
                                                        lay-filter="type_2">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>

                                        <table class="config-table" id="line_2">
                                            <tr>
                                                <td><label class="direction" for="direction_2">方向：</label></td>
                                                <td><label class="definition" for="define_2">行车定义：</label></td>
                                                <td><label class="waitArea" for="leftWait2">待行区：</label></td>
                                                <td><label class="property" for="type_2">特殊属性：</label></td>
                                            </tr>
                                            <tr class="">
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="direction_2" class="line-direction"
                                                                lay-filter="direction_2">
                                                            <option class="up" value="0">上行</option>
                                                            <option class="down" value="1">下行</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="define_2" class="line-define"
                                                                lay-filter="define_2">
                                                            <option class="left1" value="0x0001">左行</option>
                                                            <option class="straight" value="0x0002">直行</option>
                                                            <option class="right1" value="0x0004">右行</option>
                                                            <option class="turnAround" value="0x0008">掉头</option>
                                                            <option class="leftStraight" value="0x0003">左直</option>
                                                            <option class="rightStraight" value="0x0006">右直</option>
                                                            <option class="turnLeft" value="0x0009">左掉头</option>
                                                            <option class="leftRight" value="0x0005">左右</option>
                                                            <option class="turnLeftRightStraight" value="0x000f">左直右掉头
                                                            </option>
                                                            <option class="turnLeftStraight" value="0x000b">左直掉头
                                                            </option>
                                                            <option class="turnLeftRight" value="0x000d">左右掉头</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select name="" id="leftWait2"
                                                                class="line-left-wait config-inline dis"
                                                                disabled="disabled">
                                                            <option class="no" value="0">无</option>
                                                            <option class="have" value="1">有</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="special_2" class="line-special"
                                                                lay-filter="special_2"
                                                                style="width: 80px">
                                                            <option class="ordinary" value="0x0000">普通车道</option>
                                                            <option class="emergency" value="0x0001">应急车道</option>
                                                            <option class="nonMotorized" value="0x0002">非机动车道</option>
                                                            <option class="bus" value="0x0004">公交车道</option>
                                                            <option class="diversion" value="0x0008">导流带</option>
                                                            <option class="single" value="0x0040">单行道</option>
                                                            <option class="noLefTurn" value="0x0010">禁止左拐</option>
                                                            <option class="noRightTurn" value="0x0020">禁止右拐</option>
                                                        </select>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                        <table class="time-table">
                                            <tr class="time-2 common-none">
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段1</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time1" id="line_2_1"
                                                                   placeholder="请选择时间段1">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段2</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time2" id="line_2_2"
                                                                   placeholder="请选择时间段2">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段3</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time3" id="line_2_3"
                                                                   placeholder="请选择时间段3">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>


                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="floorTips2"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="floor2Btn"
                                                            disabled="disabled"
                                                            onclick="commonLine('floor2',2,'true')">
                                                        虚拟地感
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point floor2-group floor2-group2">
                                            <div class="floor2-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x1："></div>
                                                <div class="input-range"
                                                     data-label="1.y1："></div>
                                            </div>
                                            <div class="floor2-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x2："></div>
                                                <div class="input-range"
                                                     data-label="1.y2："></div>
                                            </div>
                                            <div class="floor2-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x1："></div>
                                                <div class="input-range"
                                                     data-label="2.y1："></div>
                                            </div>
                                            <div class="floor2-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x2："></div>
                                                <div class="input-range"
                                                     data-label="2.y2："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="peopleTips2"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="people2Btn"
                                                            disabled="disabled"
                                                            onclick="CommonRect('people2')">
                                                        礼让行人
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point people2-group people2-group2">
                                            <div class="people2-point index-point">
                                                <div class="input-range"
                                                     data-label="X1："></div>
                                                <div class="input-range"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="people2-point index-point">
                                                <div class="input-range"
                                                     data-label="X2："></div>
                                                <div class="input-range"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="people2-point index-point">
                                                <div class="input-range"
                                                     data-label="X3："></div>
                                                <div class="input-range"
                                                     data-label="Y3："></div>
                                            </div>
                                            <div class="people2-point index-point">
                                                <div class="input-range"
                                                     data-label="X4："></div>
                                                <div class="input-range"
                                                     data-label="Y4："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="my-colla-item common-none">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line3">车道线3属性</h4>
                                    </div>
                                    <ul class="my-colla-tab-title">
                                        <li class="line3" class="my-this">车道线3属性</li>
                                        <li class="virtualGround" class="common-none">虚拟地感检测线</li>
                                        <li class="pedestrian" class="common-none">礼让行人检测区</li>
                                    </ul>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group4">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_3" class="config-inline"
                                                        lay-filter="type_3">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                        <table class="config-table" id="line_3">
                                            <tr>
                                                <td><label class="direction" for="direction_3">方向：</label></td>
                                                <td><label class="definition" for="define_3">行车定义：</label></td>
                                                <td><label class="waitArea" for="leftWait3">待行区：</label></td>
                                                <td><label class="property" for="type_3">特殊属性：</label></td>
                                            </tr>
                                            <tr class="">
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="direction_3" class="line-direction"
                                                                lay-filter="direction_3">
                                                            <option class="up" value="0">上行</option>
                                                            <option class="down" value="1">下行</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="define_3" class="line-define"
                                                                lay-filter="define_3">
                                                            <option class="left1" value="0x0001">左行</option>
                                                            <option class="straight" value="0x0002">直行</option>
                                                            <option class="right1" value="0x0004">右行</option>
                                                            <option class="turnAround" value="0x0008">掉头</option>
                                                            <option class="leftStraight" value="0x0003">左直</option>
                                                            <option class="rightStraight" value="0x0006">右直</option>
                                                            <option class="turnLeft" value="0x0009">左掉头</option>
                                                            <option class="leftRight" value="0x0005">左右</option>
                                                            <option class="turnLeftRightStraight" value="0x000f">左直右掉头
                                                            </option>
                                                            <option class="turnLeftStraight" value="0x000b">左直掉头
                                                            </option>
                                                            <option class="turnLeftRight" value="0x000d">左右掉头</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select name="" id="leftWait3"
                                                                class="line-left-wait config-inline dis"
                                                                disabled="disabled">
                                                            <option class="no" value="0">无</option>
                                                            <option class="have" value="1">有</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="special_3" class="line-special"
                                                                lay-filter="special_3"
                                                                style="width: 80px">
                                                            <option class="ordinary" value="0x0000">普通车道</option>
                                                            <option class="emergency" value="0x0001">应急车道</option>
                                                            <option class="nonMotorized" value="0x0002">非机动车道</option>
                                                            <option class="bus" value="0x0004">公交车道</option>
                                                            <option class="diversion" value="0x0008">导流带</option>
                                                            <option class="single" value="0x0040">单行道</option>
                                                            <option class="noLefTurn" value="0x0010">禁止左拐</option>
                                                            <option class="noRightTurn" value="0x0020">禁止右拐</option>
                                                        </select>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                        <table class="time-table">
                                            <tr class="time-3 common-none">
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段1</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time1" id="line_3_1"
                                                                   placeholder="请选择时间段1">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段2</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time2" id="line_3_2"
                                                                   placeholder="请选择时间段2">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段3</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time3" id="line_3_3"
                                                                   placeholder="请选择时间段3">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>

                                    </div>
                                    <div class="my-colla-tab-item">

                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="floorTips3"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="floor3Btn"
                                                            disabled="disabled"
                                                            onclick="commonLine('floor3',2,'true')">
                                                        虚拟地感
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point floor3-group floor3-group3">
                                            <div class="floor3-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x1："></div>
                                                <div class="input-range"
                                                     data-label="1.y1："></div>
                                            </div>
                                            <div class="floor3-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x2："></div>
                                                <div class="input-range"
                                                     data-label="1.y2："></div>
                                            </div>
                                            <div class="floor3-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x1："></div>
                                                <div class="input-range"
                                                     data-label="2.y1："></div>
                                            </div>
                                            <div class="floor3-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x2："></div>
                                                <div class="input-range"
                                                     data-label="2.y2："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="peopleTips3"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="people3Btn"
                                                            disabled="disabled"
                                                            onclick="CommonRect('people3')">
                                                        礼让行人
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point people3-group people3-group3">
                                            <div class="people3-point index-point">
                                                <div class="input-range"
                                                     data-label="X1："></div>
                                                <div class="input-range"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="people3-point index-point">
                                                <div class="input-range"
                                                     data-label="X2："></div>
                                                <div class="input-range"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="people3-point index-point">
                                                <div class="input-range"
                                                     data-label="X3："></div>
                                                <div class="input-range"
                                                     data-label="Y3："></div>
                                            </div>
                                            <div class="people3-point index-point">
                                                <div class="input-range"
                                                     data-label="X4："></div>
                                                <div class="input-range"
                                                     data-label="Y4："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="my-colla-item common-none">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line4">车道线4属性</h4>
                                    </div>
                                    <ul class="my-colla-tab-title">
                                        <li class="my-this line4">车道线4属性</li>
                                        <li class="common-none virtualGround">虚拟地感检测线</li>
                                        <li class="common-none pedestrian">礼让行人检测区</li>
                                    </ul>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group5">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_4" class="config-inline"
                                                        lay-filter="type_4">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                        <table class="config-table" id="line_4">
                                            <tr>
                                                <td><label class="direction" for="direction_4">方向：</label></td>
                                                <td><label class="definition" for="define_4">行车定义：</label></td>
                                                <td><label class="waitArea" for="leftWait4">待行区：</label></td>
                                                <td><label class="property" for="type_4">特殊属性：</label></td>
                                            </tr>
                                            <tr class="">
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="direction_4" class="line-direction"
                                                                lay-filter="direction_4">
                                                            <option class="up" value="0">上行</option>
                                                            <option class="down" value="1">下行</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="define_4" class="line-define"
                                                                lay-filter="define_4">
                                                            <option class="left1" value="0x0001">左行</option>
                                                            <option class="straight" value="0x0002">直行</option>
                                                            <option class="right1" value="0x0004">右行</option>
                                                            <option class="turnAround" value="0x0008">掉头</option>
                                                            <option class="leftStraight" value="0x0003">左直</option>
                                                            <option class="rightStraight" value="0x0006">右直</option>
                                                            <option class="turnLeft" value="0x0009">左掉头</option>
                                                            <option class="leftRight" value="0x0005">左右</option>
                                                            <option class="turnLeftRightStraight" value="0x000f">左直右掉头
                                                            </option>
                                                            <option class="turnLeftStraight" value="0x000b">左直掉头
                                                            </option>
                                                            <option class="turnLeftRight" value="0x000d">左右掉头</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select name="" id="leftWait4"
                                                                class="line-left-wait config-inline dis"
                                                                disabled="disabled">
                                                            <option class="no" value="0">无</option>
                                                            <option class="have" value="1">有</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="special_4" class="line-special"
                                                                lay-filter="special_4"
                                                                style="width: 80px">
                                                            <option class="ordinary" value="0x0000">普通车道</option>
                                                            <option class="emergency" value="0x0001">应急车道</option>
                                                            <option class="nonMotorized" value="0x0002">非机动车道</option>
                                                            <option class="bus" value="0x0004">公交车道</option>
                                                            <option class="diversion" value="0x0008">导流带</option>
                                                            <option class="single" value="0x0040">单行道</option>
                                                            <option class="noLefTurn" value="0x0010">禁止左拐</option>
                                                            <option class="noRightTurn" value="0x0020">禁止右拐</option>
                                                        </select>
                                                    </div>
                                                </td>
                                            </tr>

                                        </table>
                                        <table class="time-table">
                                            <tr class="time-4 common-none">
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段1</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time1" id="line_4_1"
                                                                   placeholder="请选择时间段1">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段2</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time2" id="line_4_2"
                                                                   placeholder="请选择时间段2">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段3</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time3" id="line_4_3"
                                                                   placeholder="请选择时间段3">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>

                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="floorTips4"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="floor4Btn"
                                                            disabled="disabled"
                                                            onclick="commonLine('floor4',2,'true')">
                                                        虚拟地感
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point floor4-group floor4-group4">
                                            <div class="floor4-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x1："></div>
                                                <div class="input-range"
                                                     data-label="1.y1："></div>
                                            </div>
                                            <div class="floor4-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x2："></div>
                                                <div class="input-range"
                                                     data-label="1.y2："></div>
                                            </div>
                                            <div class="floor4-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x1："></div>
                                                <div class="input-range"
                                                     data-label="2.y1："></div>
                                            </div>
                                            <div class="floor4-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x2："></div>
                                                <div class="input-range"
                                                     data-label="2.y2："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="peopleTips4"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="people4Btn"
                                                            disabled="disabled"
                                                            onclick="CommonRect('people4')">
                                                        礼让行人
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point people4-group people4-group4">
                                            <div class="people4-point index-point">
                                                <div class="input-range"
                                                     data-label="X1："></div>
                                                <div class="input-range"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="people4-point index-point">
                                                <div class="input-range"
                                                     data-label="X2："></div>
                                                <div class="input-range"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="people4-point index-point">
                                                <div class="input-range"
                                                     data-label="X3："></div>
                                                <div class="input-range"
                                                     data-label="Y3："></div>
                                            </div>
                                            <div class="people4-point index-point">
                                                <div class="input-range"
                                                     data-label="X4："></div>
                                                <div class="input-range"
                                                     data-label="Y4："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="my-colla-item common-none">
                                <div class="my-colla-title">
                                    <div class="my-colla-colla-title">
                                        <h4 class="line5">车道线5属性</h4>
                                    </div>
                                    <ul class="my-colla-tab-title">
                                        <li class="my-this line5">车道线5属性</li>
                                        <li class="common-none virtualGround">虚拟地感检测线</li>
                                        <li class="common-none pedestrian">礼让行人检测区</li>
                                    </ul>
                                </div>
                                <div class="my-colla-content">
                                    <div class="my-colla-tab-item my-show">
                                        <div class="control-point line-group6">
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X1："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="line-point index-point">
                                                <div class="input-range input-small"
                                                     data-label="X2："></div>
                                                <div class="input-range input-small"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="line-type config-inline">
                                                <select id="type_5" class="config-inline"
                                                        lay-filter="type_5">
                                                    <option class="line-solid" value="0">实线</option>
                                                    <option class="line-dash" value="1">虚线</option>
                                                    <option class="line-dash-solid" value="2">虚实线</option>
                                                    <option class="line-solid-dash" value="3">实虚线</option>
                                                </select>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                        <table class="config-table" id="line_5">
                                            <tr>
                                                <td><label class="direction" for="direction_5">方向：</label></td>
                                                <td><label class="definition" for="define_5">行车定义：</label></td>
                                                <td><label class="waitArea" for="leftWait5">待行区：</label></td>
                                                <td><label class="property" for="type_5">特殊属性：</label></td>
                                            </tr>
                                            <tr class="">
                                                <td>
                                                    <div class="config-inline">
                                                        <select id="direction_5" class="line-direction"
                                                                lay-filter="direction_5">
                                                            <option class="up" value="0">上行</option>
                                                            <option class="down" value="1">下行</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="define_5" class="line-define"
                                                                lay-filter="define_5">
                                                            <option class="left1" value="0x0001">左行</option>
                                                            <option class="straight" value="0x0002">直行</option>
                                                            <option class="right1" value="0x0004">右行</option>
                                                            <option class="turnAround" value="0x0008">掉头</option>
                                                            <option class="leftStraight" value="0x0003">左直</option>
                                                            <option class="rightStraight" value="0x0006">右直</option>
                                                            <option class="turnLeft" value="0x0009">左掉头</option>
                                                            <option class="leftRight" value="0x0005">左右</option>
                                                            <option class="turnLeftRightStraight" value="0x000f">左直右掉头
                                                            </option>
                                                            <option class="turnLeftStraight" value="0x000b">左直掉头
                                                            </option>
                                                            <option class="turnLeftRight" value="0x000d">左右掉头</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="config-inline">
                                                        <select name="" id="leftWait5"
                                                                class="line-left-wait config-inline dis"
                                                                disabled="disabled">
                                                            <option class="no" value="0">无</option>
                                                            <option class="have" value="1">有</option>
                                                        </select>
                                                    </div>
                                                </td>
                                                <td>

                                                    <div class="config-inline">
                                                        <select id="special_5" class="line-special"
                                                                lay-filter="special_5"
                                                                style="width: 80px">
                                                            <option class="ordinary" value="0x0000">普通车道</option>
                                                            <option class="emergency" value="0x0001">应急车道</option>
                                                            <option class="nonMotorized" value="0x0002">非机动车道</option>
                                                            <option class="bus" value="0x0004">公交车道</option>
                                                            <option class="diversion" value="0x0008">导流带</option>
                                                            <option class="single" value="0x0040">单行道</option>
                                                            <option class="noLefTurn" value="0x0010">禁止左拐</option>
                                                            <option class="noRightTurn" value="0x0020">禁止右拐</option>
                                                        </select>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                        <table class="time-table">
                                            <tr class="time-5 common-none">
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段1</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time1" id="line_5_1"
                                                                   placeholder="请选择时间段1">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段2</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time2" id="line_5_2"
                                                                   placeholder="请选择时间段2">
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="layui-inline">
                                                        <!--<label class="layui-form-label dis">时间段3</label>-->
                                                        <div class="layui-input-inline">
                                                            <input type="text" class="layui-input time3" id="line_5_3"
                                                                   placeholder="请选择时间段3">
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>

                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="floorTips5"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="floor5Btn"
                                                            disabled="disabled"
                                                            onclick="commonLine('floor5',2,'true')">
                                                        虚拟地感
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point floor5-group floor5-group5">
                                            <div class="floor5-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x1："></div>
                                                <div class="input-range"
                                                     data-label="1.y1："></div>
                                            </div>
                                            <div class="floor5-point index-point">
                                                <div class="input-range"
                                                     data-label="1.x2："></div>
                                                <div class="input-range"
                                                     data-label="1.y2："></div>
                                            </div>
                                            <div class="floor5-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x1："></div>
                                                <div class="input-range"
                                                     data-label="2.y1："></div>
                                            </div>
                                            <div class="floor5-point index-point">
                                                <div class="input-range"
                                                     data-label="2.x2："></div>
                                                <div class="input-range"
                                                     data-label="2.y2："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                    <div class="my-colla-tab-item">
                                        <table>
                                            <tr>
                                                <td>
                                                    <div style="padding: 8px 5px;"><i class="layui-icon layui-icon-tips"
                                                                                      id="peopleTips5"></i></div>
                                                </td>
                                                <td>
                                                    <button class="layui-btn layui-btn-default layui-btn-disabled"
                                                            id="people5Btn"
                                                            disabled="disabled"
                                                            onclick="CommonRect('people5')">
                                                        礼让行人
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="control-point people5-group people5-group5">
                                            <div class="people5-point index-point">
                                                <div class="input-range"
                                                     data-label="X1："></div>
                                                <div class="input-range"
                                                     data-label="Y1："></div>
                                            </div>
                                            <div class="people5-point index-point">
                                                <div class="input-range"
                                                     data-label="X2："></div>
                                                <div class="input-range"
                                                     data-label="Y2："></div>
                                            </div>
                                            <div class="people5-point index-point">
                                                <div class="input-range"
                                                     data-label="X3："></div>
                                                <div class="input-range"
                                                     data-label="Y3："></div>
                                            </div>
                                            <div class="people5-point index-point">
                                                <div class="input-range"
                                                     data-label="X4："></div>
                                                <div class="input-range"
                                                     data-label="Y4："></div>
                                            </div>
                                            <div class="layui-clear"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title redStopLine">红灯停止线</h2>
                    <div class="layui-colla-content">
                        <div class="config-control">
                            <div class="layui-form-item">
                                <label id="modeSelection" class="layui-form-label">模式选择：</label>
                                <div class="layui-input-inline">
                                    <select id="redStopType" lay-filter="redStopType">
                                        <option id="straightLine" value="0">直线</option>
                                        <option id="ZLine" value="1">Z型线</option>
                                    </select>
                                </div>
                            </div>
                            <button class="layui-btn layui-btn-default redStopLine"
                                    id="redStopBtn"
                                    onclick="commonLine('redStop',1,false)">
                                红灯停止线
                            </button>
                            <div class="layui-form-item common-none">
                                <div class="layui-input-inline">
                                    <select id="redStopLineType">
                                        <option class="real" value="solid">实线</option>
                                        <option class="false" value="dashed">虚线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-inline">
                                    <input type="text" value="" placeholder="颜色"
                                           class="layui-input" id="redStopColor">
                                </div>
                                <div class="layui-inline" style="margin-left: 5px">
                                    <div id="redStopColorEle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="control-point">
                            <div class="redStop-group redStop-group1">
                                <div class="redStop-checkbox" style="padding: 10px 5px">
                                    <input type="checkbox" class="redStop-signal" lay-filter="redStopCheck"
                                           lay-skin="primary" value="1">
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X1："></div>
                                    <div class="input-range"
                                         data-label="Y1："></div>
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X2："></div>
                                    <div class="input-range"
                                         data-label="Y2："></div>
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="redStop-group redStop-group2 common-none">
                                <div class="redStop-checkbox" style="padding: 10px 5px">
                                    <input type="checkbox" class="redStop-signal" lay-filter="redStopCheck"
                                           lay-skin="primary" value="1">
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X1："></div>
                                    <div class="input-range"
                                         data-label="Y1："></div>
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X2："></div>
                                    <div class="input-range"
                                         data-label="Y2："></div>
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="redStop-group redStop-group3 common-none">
                                <div class="redStop-checkbox" style="padding: 10px 5px">
                                    <input type="checkbox" class="redStop-signal" lay-filter="redStopCheck"
                                           lay-skin="primary" value="1">
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X1："></div>
                                    <div class="input-range"
                                         data-label="Y1："></div>
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X2："></div>
                                    <div class="input-range"
                                         data-label="Y2："></div>
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="redStop-group redStop-group4 common-none">
                                <div class="redStop-checkbox" style="padding: 10px 5px">
                                    <input type="checkbox" class="redStop-signal" lay-filter="redStopCheck"
                                           lay-skin="primary" value="1">
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X1："></div>
                                    <div class="input-range"
                                         data-label="Y1："></div>
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X2："></div>
                                    <div class="input-range"
                                         data-label="Y2："></div>
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                            <div class="redStop-group redStop-group5 common-none">
                                <div class="redStop-checkbox" style="padding: 10px 5px">
                                    <input type="checkbox" class="redStop-signal" lay-filter="redStopCheck"
                                           lay-skin="primary" value="1">
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X1："></div>
                                    <div class="input-range"
                                         data-label="Y1："></div>
                                </div>
                                <div class="redStop-point index-point">
                                    <div class="input-range"
                                         data-label="X2："></div>
                                    <div class="input-range"
                                         data-label="Y2："></div>
                                </div>
                                <div class="layui-clear"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
