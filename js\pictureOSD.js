// 叠字位置8字节对齐，图片总高度16字节对齐
// 拖动叠字事件registerOsd

/**
 * Create by Chelly
 * 2019/10/29
 */
var form, slider, layer, colorpicker, element;
var regRGBA = /rgba\((.*)\)/;
var testVersion = 99999;
// var regRGB2S = /(.*),(.*),(.*),(.*)/;
// var regRGB2D = /(.*)\s+(.*)\s+(.*)\s+(.*)/;
// var index_param = {};
$(document).ready(function () {
    layui.use(['form', 'slider', 'layer', 'colorpicker', 'element'], function () {

        form = layui.form, slider = layui.slider, layer = layui.layer, colorpicker = layui.colorpicker, element = layui.element;

        onloadConfig('../../config/osdConfig.json', 'osdConfig', function (status) {
            if (status !== true) {

                layer.msg(langMessage.pictureOSD.getInfoFail, {icon: 2});

            } else {
                let osdConfig = getSe('osdConfig');
                let osdInfo = osdConfig.osdInfo;
                let dateInfo = {
                    dateFormat: 0,
                    timeFormat: 0,
                };
                let showInfo = {
                    filter: "",
                    selected: false
                };
                let osdPosition = 0;
                setSe('osdInfo', osdInfo);
                setSe('dateInfo', dateInfo);
                setSe('showInfo', showInfo);
                setSe('osdPosition', osdPosition);
                mySelectLoad();
                initDefaultSelect();//渲染叠加方式select
                renderColorPicker();
                initWebService(webserviceCMD.CMD_GET_OSD, null, getOSDValueCallback);
            }
        });


        $('#osdContainer [name="title"]').bind('input propertychange', function (e) {
            let index = parseInt(getDataset($("#osdContainer")[0]).value);
            let osdInfo = getSe("osdInfo");
            osdInfo[index].title = this.value;
            setSe('osdInfo', osdInfo);
            osdRect(index);
        });
        form.on('select(fontSize)', function (data) {
            let index = parseInt(getDataset($("#osdContainer")[0]).value);
            let osdInfo = getSe("osdInfo");
            osdInfo[index].fontSize = parseInt(data.value);
            setSe('osdInfo', osdInfo);
            osdRect(index);
        });
        form.on('select(dateFormat)', function (data) {
            let dateInfo = getSe("dateInfo");
            dateInfo.dateFormat = parseInt(data.value);
            setSe('dateInfo', dateInfo);
            osdRect(2);
        });
        form.on('select(timeFormat)', function (data) {
            let dateInfo = getSe("dateInfo");
            dateInfo.timeFormat = parseInt(data.value);
            setSe('dateInfo', dateInfo);
            osdRect(2);
        });
        form.on('select(position)', function (data) {
            let oldP = getSe('osdPosition');
            let osdPosition = parseInt(data.value);
            if (oldP !== osdPosition) {
                clearOSD(null, osdPosition)
            }

        });
        // form.on('select(separator)', function(data){
        //     let dateInfo = getSe("dateInfo");
        //     dateInfo.separator = parseInt(data.value);
        //     setSe('dateInfo',dateInfo);
        //     osdRect(2);
        // });
        form.on('checkbox(info)', function (data) {
            console.log(parseInt(data.value)); //复选框value值，也可以通过data.elem.value得到
        });
        element.on('tab(osdChecked)', function (data) {
            //console.log(this); //得到当前Tab的所在下标
            let value = parseInt(getDataset(this).value);
            setDataset($("#osdContainer")[0], 'value', value);
            setOSDValue(value);
        });
        element.on('collapse(osdCollapse)', function (data) {
            if (data.show) {
                element.render('tab');
            }
        });
    });

    var mySelectLoad = function () {
        let mySelect = $(".my-select");
        let data = {};
        let render = function () {
            let initData = data.filter(function (item) {
                return item.checked === 1
            });
            if (initData.length > 0) {
                $("#osdContainer").removeClass("common-none")
            } else {
                $("#osdContainer").addClass("common-none")
            }
            element.render();
        };
        // $("body").off('click').on('click')   点击叠加信息折叠面板时，具体叠字属性才会渲染；点击叠加内容select框时，才会渲染select列表
        $("body").off('click').on('click', function (e) {
            let event = e || window.event;
            let target = event.target || event.srcElement;
            data = getSe('osdInfo');
            let show = getSe('showInfo');
            if ($(target).hasClass("layui-colla-title")) {
                render();
            }
            let auth = getSe("deviceInfo").auth_status;
            if (auth == testVersion) {
                return
            }
            if ($(target).parents(".my-select").length > 0) {
                if ($(target).parents("dd").length > 0 || target.nodeName.toLowerCase() === 'dd') {
                    target = target.nodeName.toLowerCase() === 'dd' ? $(target) : $(target).parents("dd");
                    let checkbox = target.find(".layui-form-checkbox");
                    let key = target.find('span').html();
                    let value = parseInt(getDataset(checkbox.parents('dd')[0]).value);
                    if (checkbox.hasClass("layui-form-checked")) {//去除某个叠字
                        checkbox.removeClass("layui-form-checked");
                        $(".my-select-selected ul>li[data-value='" + value + "']").remove();
                        $("#osdContainer .layui-tab-title>li[data-value='" + value + "']").remove();
                        $("#osdText" + value).remove();
                        data[value].checked = 0;
                        osdRect();
                    } else {// 增加某个叠字
                        checkbox.addClass("layui-form-checked");
                        $(".my-select-selected ul li:last-child").before('<li class="my-selected-item" data-value="' + value + '"><span>' + key + '</span><i class="layui-icon layui-icon-close"></i></li>');
                        $("#osdContainer .layui-tab-title").append('<li data-value="' + value + '">' + key + '</li>');
                        setTabShow(value);
                        $(".my-selected-search input").focus();
                        data[value].checked = 1;
                        osdRect(value, setTabShow);
                        element.render('tab');
                    }
                    render();
                    setSe('osdInfo', data);
                    return false
                }
                if (target.nodeName.toLowerCase() === 'i' && $(target).hasClass("layui-icon-close")) {
                    let thisLi = $(target).parents("li");
                    let value = parseInt(getDataset(thisLi[0]).value);
                    $(".my-select-selected ul>li[data-value='" + value + "']").remove();
                    $(".my-select-content dd[data-value='" + value + "']>div").removeClass("layui-form-checked");
                    $("#osdContainer .layui-tab-title>li[data-value='" + value + "']").remove();
                    data[value].checked = 0;
                    $("#osdText" + value).remove();
                    osdRect();
                    render();
                    setSe('osdInfo', data);
                    return false
                }
                if ($(target).parents(".my-select-title").length > 0 || $(target).hasClass("my-select-title")) {
                    if (target.nodeName.toLowerCase() === 'input') {
                        return
                    }
                    if ($(target).parents('.my-selected-item').length) {
                        target = $(target).parents('.my-selected-item');
                        let value = parseInt(getDataset(target[0]).value);
                        setTabShow(value);
                        return
                    }
                    target = $(target).hasClass("my-select-title") ? $(target) : $(target).parents(".my-select-title");
                    target = target.parents(".my-select");
                    if (target.hasClass("layui-form-selected")) {
                        target.removeClass("layui-form-selected");
                    } else {
                        reloadSelect(data, show.filter);
                        target.addClass("layui-form-selected")
                    }
                    if (show.filter !== "") {
                        show.filter = "";
                        reloadSelect(data, show.filter);
                        return
                    }

                    show.selected = !show.selected;
                    setSe('showInfo', show);
                    return false
                }
            } else {
                $(".my-select").removeClass("layui-form-selected");
                $(".my-selected-search input").val("");
                setSe('showInfo', show);
            }


        });
        // mySelect.blur(function (e) {
        //     let show = getSe('showInfo');
        //     let event = e || window.event;
        //     let target = event.target || event.srcElement;
        //     target = $(target).parents(".my-select");
        //     if (target.hasClass("layui-form-selected")) {
        //         target.removeClass("layui-form-selected");
        //     }
        //     show.selected = !show.selected;
        //     setSe('showInfo',show)
        // });
        mySelect.find(".my-selected-search input").bind('input propertychange', function (e) {
            let show = getSe('showInfo');
            let data = getSe('osdInfo');
            let text = e.originalEvent.currentTarget.value;
            if (text !== "") {
                mySelect.removeClass('layui-form-selected');
                show.filter = text;
                show.selected = true;
                setSe('showInfo', show);
                reloadSelect(data, show.filter);
                mySelect.addClass('layui-form-selected');
            }
        });
        element.render();
    };
    $("#saveConfig").off('click').on('click', saveConfig);
    $("#refreshPic").off('click').on('click', refreshPic);
    $("#clearConfig").off('click').on('click', function () {
        clearOSD();
    });
    $("#resetConfig").off('click').on('click', resetOSDValue);
    $(".input-position").focus(function (e) {
        $(this).attr("data-oval", $(this).val()); //将当前值存入自定义属性
    }).blur(function (e) {
        let oldVal = ($(this).attr("data-oval")); //获取原值
        let newVal = ($(this).val()); //获取当前值
        if (oldVal !== newVal) {
            //不相同则做操作
            let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
            let drawContainer = $("#drawContent").find(".draw-container")[0].id;
            let {containerW, containerH, pictureW, pictureH} = getContainerParam();
            let v = $(this);
            let osdInfo = getSe('osdInfo');
            let value = 0;
            value = restrictRealPosition(newVal, containerW, pictureW);
            v.val(value);
            let container = $(e.currentTarget).parents('#osdContainer');
            let num = getDataset(container[0]).value;
            let name = 'osdText' + num;
            let nX = container.find('input[name="startX"]').val();
            let nY = container.find('input[name="startY"]').val();
            let osdPosition = getSe("osdPosition");
            if (osdPosition === 2 && nY < pictureH) {
                container.find('input[name="startY"]').val(pictureH);
                nY = pictureH
            }
            if (osdPosition === 2 && nY >= pictureH) {
                nY = (nY - pictureH);
            }
            osdInfo[num].x = parseInt(nX);
            osdInfo[num].y = parseInt(nY);
            setSe('osdInfo', osdInfo);
            let x = getContainerPosition(nX, containerW, pictureW);
            let y = getContainerPosition(nY, containerH, pictureH);
            $("#" + name).css({
                left: x,
                top: y
            });
            drawPath(drawCanvas, drawContainer);
        }
    });
    $(".button-up").off("click").on("click", function (e) {
        let pixel = 8;
        try {
            pixel = getSe('osdConfig').pixel;
        } catch (e) {
            // console.log(e)
        }
        let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
        let drawContainer = $("#drawContent").find(".draw-container")[0].id;
        let {containerW, containerH, pictureW, pictureH} = getContainerParam();
        let v = $(e.currentTarget).parents('.layui-input-inline').find('.layui-input');
        let container = $(e.currentTarget).parents('#osdContainer');
        let num = getDataset(container[0]).value;
        let osdInfo = getSe('osdInfo');
        let name = 'osdText' + num;
        v.val(parseInt(v.val()) + pixel > pictureW ? pictureW : parseInt(v.val()) + pixel);
        let nX = container.find('input[name="startX"]').val();
        let nY = container.find('input[name="startY"]').val();
        let osdPosition = getSe("osdPosition");
        if (osdPosition === 2 && nY >= pictureH) {
            nY = (nY - pictureH);
        }
        osdInfo[num].x = parseInt(nX);
        osdInfo[num].y = parseInt(nY);
        setSe('osdInfo', osdInfo);
        let x = getContainerPosition(nX, containerW, pictureW);
        let y = getContainerPosition(nY, containerH, pictureH);
        $("#" + name).css({
            left: x,
            top: y
        });
        drawPath(drawCanvas, drawContainer);
    });
    $(".button-down").off("click").on("click", function (e) {
        let pixel = 8;
        try {
            pixel = getSe('osdConfig').pixel;
        } catch (e) {
            // console.log(e)
        }
        let drawCanvas = $("#drawContent").find(".draw-canvas")[0].id;
        let drawContainer = $("#drawContent").find(".draw-container")[0].id;
        let {containerW, containerH, pictureW, pictureH} = getContainerParam();
        let v = $(e.currentTarget).parents('.layui-input-inline').find('.layui-input');
        let container = $(e.currentTarget).parents('#osdContainer');
        let num = getDataset(container[0]).value;
        let osdInfo = getSe('osdInfo');
        let name = 'osdText' + num;
        v.val(parseInt(v.val()) - pixel < 0 ? 0 : parseInt(v.val()) - pixel);
        let nX = container.find('input[name="startX"]').val();
        let nY = container.find('input[name="startY"]').val();
        let osdPosition = getSe("osdPosition");
        if (osdPosition === 2 && nY < pictureH) {
            container.find('input[name="startY"]').val(pictureH);
            nY = pictureH
        }
        if (osdPosition === 2 && nY >= pictureH) {
            nY = (nY - pictureH);
        }
        osdInfo[num].x = parseInt(nX);
        osdInfo[num].y = parseInt(nY);
        setSe('osdInfo', osdInfo);
        let x = getContainerPosition(nX, containerW, pictureW);
        let y = getContainerPosition(nY, containerH, pictureH);
        $("#" + name).css({
            left: x,
            top: y
        });
        drawPath(drawCanvas, drawContainer);
    });
});
var showTips = function () {
    layer.tips(langMessage.pictureOSD.osdPositionTips, '#osdPositionTips');
}
var testVersionDisabled = function () {
    disabledConfig(".test-version");
    // $("#saveConfig").off('click').addClass("layui-disabled").attr("disabled","disabled");
    $("#clearConfig").off('click').addClass("layui-disabled").attr("disabled", "disabled");
    $("#resetConfig").off('click').addClass("layui-disabled").attr("disabled", "disabled");
    $(".button-up").off("click").addClass("layui-disabled").attr("disabled", "disabled");
    $(".button-down").off("click").addClass("layui-disabled").attr("disabled", "disabled");
    $(".my-select").find(".my-selected-search input").remove();
    $(".resize")[0].onmousedown = null;
    $(".resize").addClass("layui-disabled");
    $("#osdZone").off('click').off('mousedown').off('mouseup').off('mousemove');
};
// 获取osd信息之后的展示
var show = function () {
    let osdInfo = getSe('osdInfo');
    let osdPosition = getSe('osdPosition');
    let osdEnable = getSe('osdEnable');

    if (osdEnable === 1) {
        $("#checkOSD")[0].checked = true
    }
    $('#osdPosition').val(osdPosition);
    $(".my-select-selected ul li:not(:last)").remove();
    $("#osdContainer .layui-tab-title li").remove();
    if (osdPosition !== 1) {
        let iH = getSe("osdPositionHeight");
        iH < 20 && (iH = 20);
        initOSD(osdPosition, iH);
    }
    Object.keys(osdInfo).forEach(function (key) {
        if (osdInfo[key].checked === 1) {
            key = parseInt(osdInfo[key].value);
            $(".my-select-selected ul li:last-child").before('<li class="my-selected-item" data-value="' + key + '"><span>' + osdInfo[key].type + '</span><i class="layui-icon layui-icon-close"></i></li>');
            $("#osdContainer .layui-tab-title").append('<li data-value="' + key + '">' + osdInfo[key].type + '</li>');
            osdRect(key, setTabShow);
        }
    });

    element.render();
    form.render();
    let auth = getSe("deviceInfo").auth_status;
    if (auth == testVersion) {
        testVersionDisabled()
    }
};
var initOSD = function (osdPosition, iH, osdMainColor) {
    $("#osdPosition").val(osdPosition);
    let drawOsd = $("#drawOsd");
    // let mainBackColor = $(".main-back-color");
    drawOsd.html("");
    let myDrag = $("#myDrag");
    myDrag.length && myDrag.remove();
    // mainBackColor.addClass("common-none");
    let osdHeight = iH, osdTop = 0, osdBottom = 0, dragClass = "", resizeClass = "", isTop = false;
    if (osdPosition === 0) {
        osdBottom = 'auto', dragClass = "resizeT", resizeClass = "resizeB";
    } else if (osdPosition === 1) {
        let imgInfo = getSe("imgInfo");
        osdHeight = imgInfo.containerHeight;
    } else if (osdPosition === 2) {
        osdTop = 'auto', dragClass = "resizeB", resizeClass = "resizeT", isTop = true;
    }
    drawOsd.css({
        height: osdHeight + 'px',
        top: osdTop === 'auto' ? 'auto' : osdTop + 'px',
        bottom: osdBottom === 'auto' ? 'auto' : osdBottom + 'px'
    });
    if (dragClass && resizeClass) {
        setSe("osdPositionHeight", iH);
        $('#configImg').before('<div id="myDrag" class="' + dragClass + '"><div class="resize ' + resizeClass + '"></div></div>');
        //
        resize(drawOsd[0], $(".resizeT")[0], $("#myDrag")[0], false, isTop, true, false, osdRect);
        $("#myDrag").css({
            height: iH + 'px',
        });
        disabledConfig(".main-color");
    } else {
        removeDisabled(".main-color");
    }
};
var removeDisabled = function (container) {
    $(container + ' select').removeClass("layui-disabled");
    $(container + ' select').removeAttr("disabled", "disabled");
    $(container + ' i').removeClass("layui-disabled");
    $(container + ' i').removeAttr("disabled", "disabled");
    $(container + ' input').removeClass("layui-disabled");
    $(container + ' input').removeAttr("disabled", "disabled");
    $(container).removeClass('dis');
    renderColorPicker();
};
var disabledConfig = function (container) {
    $(container + ' select').addClass("layui-disabled");
    $(container + ' select').attr("disabled", "disabled");
    $(container + ' i').addClass("layui-disabled");
    $(container + ' i').attr("disabled", "disabled");
    $(container + ' input').addClass("layui-disabled");
    $(container + ' input').attr("disabled", "disabled");
    $(container).find('.layui-colorpicker').off('click');

    $(container).addClass('dis');
    form.render();
};
var showOSDValue = function () {
    let param = {
        type: 'osd'
    };
    initWebService(webserviceCMD.CMD_CAPTURE_JPG, param)
};
var resetOSDValue = function () {
    initWebService(webserviceCMD.CMD_GET_OSD, null, getOSDValueCallback);
};
var saveConfig = function () {
    console.log('saveConfig')
    let osdPosition = getSe('osdPosition');
    if (osdPosition !== 1) {
        let showMsg = getSe("osdMsg");
        if (!showMsg) {

            layer.open({
                title: langMessage.common.hint
                , shade: 0.8
                , btn: langMessage.common.confirm
                , yes: function (index, layero) {
                    saveOSDConfig();
                    layer.close(index);
                    let osdMsg = ($("#osdMsg")[0].checked === true ? 1 : 0);
                    setSe("osdMsg", osdMsg)
                }
                , icon: 3
                , content: langMessage.pictureOSD.saveMsg
                , anim: 6
            });

            return
        }
    }
    saveOSDConfig();
};
var saveOSDConfig = function () {
    let data = {};
    let osdInfo = getSe('osdInfo');
    let dateInfo = getSe('dateInfo');
    let osdPosition = getSe('osdPosition');
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();

    let realH = pictureH;
    let height = getSe("osdPositionHeight");
    if (osdPosition !== 1) {
        realH += reduceSurplusHeight1(osdInfo, height, osdPosition);
    }
    data.finalHeight = realH;
    data.finalWidth = pictureW;
    //未获取到图片的情况设置宽高为0
    if (containerW === pictureW && containerH === pictureH) {
        data.finalHeight = 0;
        data.finalWidth = 0;
    }

    Object.keys(osdInfo).forEach(function (key) {
        osdInfo[key].value++;
        let font = osdInfo[key].fontColor.replace(/\s/g, "").split(",");
        let back = osdInfo[key].backColor.replace(/\s/g, "").split(",");
        osdInfo[key].fontColor = font[0] + ' ' + font[1] + ' ' + font[2] + ' ' + parseInt(font[3] * 255);
        osdInfo[key].backColor = back[0] + ' ' + back[1] + ' ' + back[2] + ' ' + parseInt(back[3] * 255);
        osdInfo[key].title = osdInfo[key].title.replace(/"/g, "\\\"");
        delete osdInfo[key].default;
        delete osdInfo[key].type;
        delete osdInfo[key].filter;
    });
    data.osdInfo = osdInfo;
    data.dateInfo = dateInfo;
    data.osdPosition = osdPosition;
    data.osdEnable = getCheckboxValue('checkOSD');
    // setSe('osdInfo', osdInfo);
    setSe('dateInfo', dateInfo);
    setSe('osdPosition', osdPosition);
    setSe('osdEnable', data.osdEnable);
    initWebService(webserviceCMD.CMD_SET_OSD, data, setOSDValueCallback)
};
var osdMinHeight = 16;
/**
 * @description      会除去空白的地方
 * @param point      getSe('osdInfo')
 * @param height     getSe("osdPositionHeight")
 * @param position   getSe('osdPosition')
 * */
var reduceSurplusHeight = function (point, height, position) {
    console.log('=======================height' + height)//叠字区域高度(在页面上的高度)
    let tmpPoint = point.filter(function (item) {
        return item.checked === 1
    });
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    console.log('=======================containerW' + containerW)
    console.log('=======================containerH' + containerH)
    console.log('=======================pictureW' + pictureW)
    console.log('=======================pictureH' + pictureH)
    let reduceNum = 0;//叠字区域的空白高度
    let fontSizes = getSe('osdConfig').fontSize;
    tmpPoint = sortPosition(tmpPoint, 'y');//对y进行排序

    let pictureT = 0;
    // accMul  height*pictureW，保留两者的小数点
    let realHeight = accMul(height, pictureW) / containerW;//height转换到真实图片后叠字区域总高度
    console.log('=======================accMul(height, pictureW)' + accMul(height, pictureW))
    console.log('=======================realHeight = accMul(height, pictureW) / containerW' + realHeight)
    let pictureB = accMul(height, pictureW) / containerW;//height转换成真实picture中的叠字区域高度
    if (position === 2) {// 图片下
        pictureT = pictureH;
        pictureB = pictureB + pictureH;
    }
    console.log('=======================pictureT' + pictureT)
    console.log('=======================pictureB' + pictureB)
    let tmpE = tmpPoint[tmpPoint.length - 1];
    console.log('=======================tmpE', tmpE)
    if (tmpE) {
        let tmpET = pictureB - tmpE.y - fontSizes[tmpE.fontSize];
        if (tmpET > 0 && tmpET >= osdMinHeight) {
            for (let j = 0; j < parseInt(tmpET / osdMinHeight); j++) {
                reduceNum += osdMinHeight
            }

        }
    }
    let tmpS = tmpPoint[0];
    console.log('=======================tmpS', tmpS)
    if (tmpS) {
        let tmpST = tmpS.y - pictureT;
        if (tmpST > 0 && tmpST >= osdMinHeight) {
            let reduceS = 0;
            for (let j = 0; j < parseInt(tmpST / osdMinHeight); j++) {
                reduceS += osdMinHeight
            }
            reduceNum += reduceS;
            tmpPoint = tmpPoint.map(function (item) {
                item.y = item.y - reduceS;
                return item
            })
        }
    }

    // 去除第i个叠字到第i+1个叠字之间的空白距离
    for (let i = 0; i < tmpPoint.length - 1; i++) {
        let item = tmpPoint[i];
        let item_ = tmpPoint[i + 1];
        let itemReal = item.y + fontSizes[item.fontSize];
        if (item_.y > itemReal) {
            let subHeight = item_.y - itemReal;
            let reduceH = 0;
            if (subHeight >= osdMinHeight) {
                for (let j = 0; j < parseInt(subHeight / osdMinHeight); j++) {
                    reduceH += osdMinHeight
                }
            }
            reduceNum += reduceH;
            point[item_.value].y = item_.y - reduceH
        }
    }
    console.log('=======================reduceNum' + reduceNum)
    return realHeight - reduceNum
};
var reduceSurplusHeight1 = function (point, height, position) {
    // console.log('=======================height' + height)//叠字区域高度(在页面上的高度)
    let tmpPoint = point.filter(function (item) {
        return item.checked === 1
    });
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    // console.log('=======================containerW,containerH,pictureW,pictureH', containerW, containerH, pictureW, pictureH)
    let fontSizes = getSe('osdConfig').fontSize;
    tmpPoint = sortPosition(tmpPoint, 'y');//对y进行排序
    let realHeight = accMul(height, pictureW) / containerW;//height转换到真实图片后叠字区域总高度
    let tmpE = tmpPoint[tmpPoint.length - 1];
    // console.log('=======================tmpE', tmpE)
    if (tmpE) {
        let lastY = tmpE.y + fontSizes[tmpE.fontSize]
        if (position === 2) {
            lastY = lastY - pictureH
        }
        if (realHeight < lastY) {
            realHeight = lastY
        }
    }
    realHeight = Math.ceil(realHeight / osdMinHeight) * osdMinHeight
    let maxHeight = getSe('osdConfig').maxHeight
    maxHeight = maxHeight > 0 ? maxHeight : parseInt(pictureH / 2)
    if (realHeight > maxHeight) {
        realHeight = maxHeight
    }
    return realHeight
};
/**
 * @param res 获取OSD配置返回的参数
 * */
var getOSDValueCallback = function (res) {
    if (res.ret === 0 && res.desc === 'success') {
        //osd设置
        let enable = parseInt(res.osdEnable);
        let position = parseInt(res.osdPosition);
        let getOSDInfo = res.osdInfo;
        let dateInfo = res.dateInfo;
        // let mainColor = res.picOutsideRGBA;
        // if(mainColor){
        //     let v = mainColor.split(" ");
        //     mainColor = v[0] + ',' + v[1] + ',' + v[2] + ',' + v[3] / 255
        // }
        let {containerW, containerH, pictureW, pictureH} = getContainerParam();
        let height = res.finalHeight ? res.finalHeight : pictureH;//res.finalHeight:叠加osd后图片的高
        let osdInfo = getSe('osdInfo');
        Object.keys(getOSDInfo).forEach(function (key) {
            let item = getOSDInfo[key];
            Object.keys(osdInfo).forEach(function (i) {
                let oItem = osdInfo[i];
                if (parseInt(item.value - 1) === oItem.value) {
                    Object.keys(item).forEach(function (k) {
                        if (k === 'value') {
                            return;
                        }
                        let v = item[k];
                        if (k === 'fontColor' || k === 'backColor') {
                            v = v.split(" ");
                            v = v[0] + ',' + v[1] + ',' + v[2] + ',' + v[3] / 255
                        }
                        osdInfo[i][k] = v
                    })
                }
            })
        });
        if (position !== 1) {
            height = accMul((height - pictureH), containerH) / pictureH
        }
        setSe('osdInfo', osdInfo);
        setSe('dateInfo', dateInfo);
        setSe('osdPosition', position);
        setSe('osdPositionHeight', height);
        setSe('osdEnable', enable);
        // setSe('osdMainColor',mainColor);
        showOSDValue();
    } else {
        layer.msg(langMessage.pictureOSD.getInfoFail, {icon: 2});
    }
};
var setOSDValueCallback = function (res) {
    if (res.ret === 0 && res.desc === 'success') {
        layer.msg(langMessage.common.saveSuc, {icon: 1});
        initWebService(webserviceCMD.CMD_GET_OSD, null, getOSDValueCallback);
    } else {
        layer.msg(langMessage.common.saveFail, {icon: 2});
    }
};
var getContainerPosition = function (position, containerW, pictureW) {
    return accMul(position, containerW) / pictureW;
};
var initDefaultSelect = function () {
    let osdConfig = getSe('osdConfig');
    let dateFormat = osdConfig.dateFormat;
    let timeFormat = osdConfig.timeFormat;
    // let separator = osdConfig.separator;
    let osdPosition = osdConfig.osdPosition;
    let fontSize = osdConfig.fontSize;
    let dateSelect = "", timeSelect = "", speSelect = "", fontSelect = "", osdSelect = "";
    Object.keys(dateFormat).forEach(function (key) {
        dateSelect += '<option value="' + key + '">' + dateFormat[key] + '</option>';
    });
    $('#osdContainer [name="dateFormat"]').append(dateSelect);

    Object.keys(timeFormat).forEach(function (key) {
        timeSelect += '<option value="' + key + '">' + timeFormat[key] + '</option>';
    });
    $('#osdContainer [name="timeFormat"]').append(timeSelect);

    // Object.keys(separator).forEach(function(key){
    //     speSelect+='<option value="'+key+'">'+separator[key]+'</option>';
    // });
    // $('#osdContainer [name="separator"]').append(speSelect);

    Object.keys(fontSize).forEach(function (key) {
        fontSelect += '<option value="' + key + '">' + key + '号字</option>';
    });
    $('#osdContainer [name="fontSize"]').append(fontSelect);

    Object.keys(osdPosition).forEach(function (key) {
        osdSelect += '<option value="' + key + '">' + osdPosition[key] + '</option>';
    });
    $('#osdPosition').append(osdSelect);
    form.render();
};
var setTabShow = function (value) {
    $("#osdContainer .layui-tab-title li").removeClass('layui-this');
    $('#osdContainer .layui-tab-title li[data-value="' + value + '"]').addClass('layui-this');
    $("#osdContainer .layui-tab-content .layui-tab-item").addClass('layui-show');
    setDataset($("#osdContainer")[0], 'value', value);
    setOSDValue(value);
};
var setOSDValue = function (index) {
    let osdInfo = getSe("osdInfo");
    let item = {};
    osdInfo.forEach(function (it, i) {
        if (i === index) {
            item = it;
        }
    });
    renderColorPicker(item.fontColor, item.backColor);
    $('#osdContainer [name="title"]').val(item.title);
    $('#osdContainer [name="startX"]').val(item.x);
    $('#osdContainer [name="startY"]').val(item.y);
    $('#osdContainer [name="fontSize"]').val(item.fontSize);
    $('#osdContainer [name="fontColorValue"]').val('rgba(' + item.fontColor + ')');
    $('#osdContainer [name="backColorValue"]').val('rgba(' + item.backColor + ')');
    $(".time-config").css({
        display: 'none'
    });
    if (index === 2) {
        let dateInfo = getSe("dateInfo");
        $(".time-config").css({
            display: 'table-row'
        });
        // $('#osdContainer [name="separator"]').val(dateInfo.separator);
        $('#osdContainer [name="dateFormat"]').val(dateInfo.dateFormat);
        $('#osdContainer [name="timeFormat"]').val(dateInfo.timeFormat);

    }
    form.render();
};
var colorPickerDone = function (color, type) {
    $('#osdContainer [name="' + type + 'Value"]').val(color);
    let index = getDataset($("#osdContainer")[0]).value;
    let osdInfo = getSe("osdInfo");
    color = color.match(regRGBA)[1];
    osdInfo[index][type] = color;
    setSe('osdInfo', osdInfo);
    osdRect();
};
var renderColorPicker = function (fontColor, backColor) {
    let fColor = 'rgba(' + (fontColor ? fontColor : '255,0,0,1') + ')';
    let bColor = 'rgba(' + (backColor ? backColor : '255,255,255,0') + ')';


    colorpicker.render({
        elem: '#fontColor'
        , color: fColor
        , format: 'rgb'
        , alpha: true
        , done: function (color) {
            colorPickerDone(color, 'fontColor')
        }
    });
    colorpicker.render({
        elem: '#backColor'
        , color: bColor
        , alpha: true
        , format: 'rgb'
        , done: function (color) {
            colorPickerDone(color, 'backColor')
        }
    });
    let osdPosition = getSe("osdPosition");
    if (osdPosition !== 1) {
        disabledConfig('.main-color')
    }
    let auth = getSe("deviceInfo").auth_status;
    if (auth == testVersion) {
        disabledConfig('.test-version')
    }

};
var reloadSelectedContent = function (data, filter) {
    let dd = '';
    for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if ((data.filter !== "" && item.filter.indexOf(filter) > -1) || filter === "") {
            let checked = item.checked ? ' layui-form-checked ' : '';
            dd += '<dd data-value="' + item.value + '">' +
                '<div class="layui-form-checkbox ' + checked + '" lay-skin="primary">' +
                '<span>' + item.type + '</span><i class="layui-icon layui-icon-ok"></i></div>' +
                '</dd>'
        }
    }
    return dd;
};
var reloadSelect = function (data, filter) {
    let dd = reloadSelectedContent(data, filter);
    $("#selectContainer .my-select-content").html(dd);
};
var restrictRealPosition = function (p, containerW, pictureW) {
    let pixel = 8;
    try {
        pixel = getSe('osdConfig').pixel;
    } catch (e) {
        // console.log(e)
    }
    return parseInt(p / pixel) * pixel
};
var clearOSD = function (callback, position) {
    onloadConfig('../../config/osdConfig.json', 'osdConfig', function (status) {
        if (status !== true) {

            layer.msg(langMessage.pictureOSD.getInfoFail, {icon: 2});

        } else {
            // $("#checkOSD")[0].checked = false;
            $("#osdPosition").val(1);
            $(".my-select-selected ul li:not(:last)").remove();
            $("#osdContainer .layui-tab-title li").remove();
            $("#drawOsd").html("");
            drawPath('osdCan', 'drawOsd');
            //setOSDValue(1);
            $("#osdContainer .layui-tab-content .layui-tab-item").removeClass('layui-show');
            $("#osdContainer").addClass("common-none");
            let dateInfo = {
                dateFormat: 0,
                timeFormat: 0,
            };
            let showInfo = {
                filter: "",
                selected: false
            };
            let osdConfig = getSe('osdConfig');
            let osdInfo = osdConfig.osdInfo;
            let defaultP = 1;
            if (position !== undefined) {
                defaultP = position
            }
            if (defaultP === 2) {//图片下
                let {containerW, containerH, pictureW, pictureH} = getContainerParam();
                Object.keys(osdInfo).forEach(function (key) {
                    osdInfo[key].y = osdInfo[key].y + pictureH
                });
            }
            setSe('osdInfo', osdInfo);
            setSe('dateInfo', dateInfo);
            setSe('showInfo', showInfo);
            setSe('osdPosition', defaultP);
            initOSD(defaultP, 20);
            osdRect();
            element.render();
            form.render();
            if (callback) {
                callback()
            }
        }
    });
};

/**
 * @description 改变叠字区域高度
 * @param oParent   $("#drawOsd")
 * @param isLeft    false
 * @param isTop     图片下true，其它false
 * @param lockX     true
 * @param lockY     false
 * @param callback  osdRect
 * */
var resize = function (oParent, handle, nParent, isLeft, isTop, lockX, lockY, callback) {
    console.log('resize')
    let {containerW, containerH, pictureW, pictureH} = getContainerParam();
    let dragMinWidth = containerH;
    let dragMinHeight = 10;
    handle.onmousedown = function (event) {
        console.log('resize-handle.onmousedown')
        var event = event || window.event;
        let disX = event.clientX - handle.offsetLeft;
        let disY = event.clientY - handle.offsetTop;
        let iParentTop = oParent.offsetTop;
        let iParentLeft = oParent.offsetLeft;
        let iParentWidth = oParent.offsetWidth;
        let iParentHeight = oParent.offsetHeight;
        let maxW = containerW;
        let maxH = parseInt(containerH / 2);
        document.onmousemove = function (event) {
            console.log('resize-handle.onmousedown-document.onmousemove')
            var event = event || window.event;
            let iT = event.clientY - disY;
            let iH = isTop ? iParentHeight + (-1 * iT) : iParentHeight + iT;
            iH > maxH && (iH = maxH);
            iH < 20 && (iH = 20);
            iH = restrictPosition(iH, containerW, pictureW, 16);
            oParent.style.height = iH + "px";
            nParent.style.height = iH + "px";
            setSe("osdPositionHeight", iH);
            if (callback) {
                callback()
            }
            if ((isLeft && iW == dragMinWidth) || (isTop && iH == dragMinHeight)) document.onmousemove = null;
            return false;
        };
        document.onmouseup = function (event) {
            console.log('resize-handle.onmousedown-document.onmouseup')
            var event = event || window.event;
            let iT = event.clientY - disY;
            let iH = isTop ? iParentHeight + (iT * -1) : iParentHeight + iT;
            iH > maxH && (iH = maxH);
            iH < 20 && (iH = 20);
            iH = restrictPosition(iH, containerW, pictureW, 16);
            oParent.style.height = iH + "px";
            nParent.style.height = iH + "px";
            setSe("osdPositionHeight", iH);
            if (callback) {
                callback()
            }
            document.onmousemove = null;
            document.onmouseup = null;
        };
        return false;
    }
};


var refreshPic = function () {
    initWebService(webserviceCMD.CMD_GET_OSD, null, getOSDValueCallback);
};
