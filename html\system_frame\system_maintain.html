<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>systemMaintain</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../css/global.css">

    <script src="../../js/dep/polyfill.min.js"></script>
    <script src="../../js/dep/jquery-3.3.1.js"></script>
    <script src="../../js/cookie.js"></script>
    <script src="../../js/common.js"></script>
    <script src="../../js/lang/load_lang.js"></script>
    <script>var langMessage = getLanguage()</script>
    <script src="../../layui/layui.js"></script>
    <script src="../../js/dep/echarts.js"></script>
    <script src="../../js/webService.js"></script>
    <script src="../../js/settingMain.js"></script>
    <script src="../../js/saveTXT.js"></script>
    <script src="../../js/dep/spark-md5.js"></script>
    <script src="../../js/bufferOperation.js"></script>
    <script src="../../js/analysisFile.js"></script>
    <script src="../../js/ocxGlobal.js"></script>
    <script src="../../js/authorization.js"></script>
    <script src="../../js/keepAlive.js"></script>
    <script src="../../js/systemMaintain.js"></script>
    <style type="text/css">
        .setting-table {
            margin: 10px 20px 10px;
        }

        .setting-table tr > td:last-child {
            width: 300px;
        }

        .setting-table td {
            padding: 10px;
        }

        .layui-form-item {
            margin-bottom: 0px;
            clear: both;
        }

        .update-btn {
            display: inline-block;
            margin-right: 10px;
        }

        .maintain-tips .layui-icon {
            padding: 5px;
            color: #c52616;
            font-weight: bold;
        }

        #authContainer object {
            display: none;
        }
    </style>
    <link rel="stylesheet" href="../../css/subTab.css">
</head>
<body class="sub-body custom-style">
<div class="layui-tab" lay-filter="tabChange">
    <ul class="layui-tab-title">
        <li class="layui-this realTimeLog" onclick="changeTag(1)">实时日志</li>
        <li class="residentLog" onclick="changeTag(2)">常驻日志</li>
        <li class="SLog" onclick="changeTag(3)">重启日志分析</li>
        <li id="equipmentMaintenance">设备维护</li>
        <li id="importExport">导入导出</li>
        <li class="exportYUV">导出YUV</li>
    </ul>
    <div class="layui-tab-content layui-form">
        <div class="layui-tab-item layui-show">
            <fieldset class="layui-elem-field" style="width: 700px;">
                <legend class="realTimeLog">实时日志</legend>
                <div class="layui-field-box" style="padding: 0;">
                    <textarea style="width: 700px;padding-top:10px;margin: 20px 0;border: none" id="realTimeLogInfo"
                              class="layui-textarea" cols="15" rows="18">尚未获取到信息</textarea>
                </div>
            </fieldset>
            <button class="layui-btn layui-btn-default" id="saveRealTimeLogInfo">保存信息</button>
            <button class="layui-btn layui-btn-default" id="getRealTimeLog">获取实时日志</button>
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field" style="width: 700px;">
                <legend class="residentLog">常驻日志</legend>
                <div class="layui-field-box" style="padding: 0;">
                    <textarea style="width: 700px;padding-top:10px;margin: 20px 0;border: none" id="logInfo"
                              class="layui-textarea" cols="15" rows="18">尚未获取到信息</textarea>
                </div>
            </fieldset>
            <button class="layui-btn layui-btn-default" id="saveLogInfo">保存信息</button>
            <button class="layui-btn layui-btn-default" id="getLog">获取常驻日志</button>
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend class="SLog">重启日志分析</legend>
                <div class="layui-field-box" style="padding: 0;">
                    <!--                    <textarea style="width: 700px;padding-top:10px;margin: 20px 0;border: none" id="logInfoS"-->
                    <!--                              class="layui-textarea" cols="15" rows="18">尚未获取到信息</textarea>-->
                    <div class="layui-row" style="padding-top: 20px">
                        <div class="layui-col-md6">
                            <!--点击某一扇面，右边展示折线图-->
                            <div id="logS_echarts"></div>
                            <div id="logS_echarts_time" style="padding-left: 20px"></div>
                        </div>
                        <div class="layui-col-md6" id="right-echarts-line" style="display: none">
                            <form class="layui-form" action="">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">统计方式</label>
                                    <div class="layui-input-block">
                                        <input type="radio" lay-filter="brokenLineRadio" name="brokenLineRadio"
                                               value="day" title="按天" checked>
                                        <input type="radio" lay-filter="brokenLineRadio" name="brokenLineRadio"
                                               value="hour" title="按小时">
                                    </div>
                                </div>
                            </form>
                            <div id="echarts_line"></div>
                        </div>
                    </div>
                </div>
            </fieldset>
            <button class="layui-btn layui-btn-default" id="getLogS">获取日志分析</button>
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend id="deviceRestart">设备重启</legend>
                <div class="layui-field-box" style="padding-right: 20px">
                    <table class="setting-table">
                        <tr>
                            <td>
                                <button class="layui-btn layui-btn-default" id="restartDevice">重启算法</button>
                            </td>
                            <td><span id="restart">远程控制设备立即重新启动</span></td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" lay-filter="autoRestart" title="自动重启"
                                       lay-skin="primary" id="autoRestart"></td>
                            <td class="auto-restart">
                                <div class="layui-form-item">
                                    <div class="layui-input-inline">
                                        <input type="text" id="setTimeValue" class="layui-input" placeholder="设置重启时间"
                                               disabled>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <button class="layui-btn layui-btn-default" id="saveReboot">保存</button>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                </div>
            </fieldset>
            <fieldset class="layui-elem-field layui-field-title">
                <legend id="reset">设备配置复位</legend>
                <div class="layui-field-box" style="padding-right: 20px">
                    <table class="setting-table">
                        <tr>
                            <td>
                                <button class="layui-btn layui-btn-default" id="simplyResume">简单算法恢复</button>
                            </td>
                            <td>
                                <div class="maintain-tips">
                                    <i id="defaults" class="layui-icon layui-icon-tips">算法恢复到默认值</i>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </fieldset>
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field layui-field-title">
                <legend id="configurationData">配置数据</legend>
                <div class="layui-field-box" style="padding-right: 20px">
                    <table class="setting-table">
                        <tr>
                            <td>
                                <button class="layui-btn layui-btn-default" id="importConfigBtn">导入配置文件</button>
                                <input type="file" id="importConfig" style="display: none">
                            </td>
                            <td id="configuration">从备份文件中导入设置配置</td>
                        </tr>
                        <tr>
                            <td>
                                <button class="layui-btn layui-btn-default" id="exportConfig">导出配置文件</button>
                            </td>
                            <td id="load">将设备所有配置导出到本地文件（默认保存路径为浏览器下载路径）</td>
                        </tr>

                    </table>
                </div>
            </fieldset>
            <!--<fieldset class="layui-elem-field layui-field-title">-->
            <!--<legend>授权数据</legend>-->
            <!--<div class="layui-field-box" style="padding-right: 20px">-->
            <!--<div class="layui-field-box" style="padding-right: 20px" id="authContainer">-->
            <!--<table class="setting-table">-->
            <!--<tr>-->
            <!--<td>-->
            <!--&lt;!&ndash;<label for="authFile">&ndash;&gt;-->
            <!--<button class="layui-btn layui-btn-default" id="authFile">选择授权码目录</button>-->
            <!--&lt;!&ndash;</label>&ndash;&gt;-->
            <!--<input type="file" id="authFileInput" webkitdirectory="webkitdirectory" multiple="multiple" style="display: none">-->
            <!--<span id="authName"></span>-->
            <!--</td>-->
            <!--<td>-->
            <!--<button class="layui-btn layui-btn-default layui-btn-disabled" id="importAuth"-->
            <!--style="display: none">授权-->
            <!--</button>-->
            <!--</td>-->
            <!--</tr>-->
            <!--<tr>-->
            <!--<td>-->
            <!--<button class="layui-btn layui-btn-default" id="exportAuth">申请授权令牌</button>-->
            <!--</td>-->
            <!--<td >将授权码导出到本地文件（默认保存路径为浏览器下载路径）<span id="exportPath" class="maintain-tips"></span></td>-->
            <!--</tr>-->
            <!--</table>-->
            <!--</div>-->
            <!--</div>-->
            <!--</fieldset>-->
        </div>
        <div class="layui-tab-item">
            <fieldset class="layui-elem-field" style="width: 700px;">
                <legend class="exportYUV">导出YUV</legend>
                <table class="setting-table">
                    <tr>
                        <td>
                            <button class="layui-btn layui-btn-default exportYUV" onclick="exportYUV()" id="exportYUV">
                                导出YUV
                            </button>
                        </td>
                        <td id="load1">将YUV保存到本地文件（默认保存路径为浏览器下载路径）</td>
                    </tr>
                </table>
            </fieldset>
        </div>
    </div>
</div>
</body>
<div id="logSTableDiv" style="display: none;padding: 20px">
    <table id="logSTable"></table>
</div>
</html>
