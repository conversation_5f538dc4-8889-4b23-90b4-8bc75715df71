var laytpl;
var form, table, layer, element, fH, fW;
var eventInterval, tableInterval, clickTimer = null, tableIndex = null, eventArray = [], sqlStatus = 0, eventID = -1;
var eventLength = 20;
var tmpTime = 0;
var carDetailType = new Map();
var interKeepAlive = null
//监听刷新页面
window.onbeforeunload = function () {
};
window.onresize = function () {
    resetWindow();
};
$(document).ready(function () {
    window.parent.setNavValue(1);
    let globalInfo = checkGlobal();
    let outW = '800px', outH = '500px';
    if (globalInfo.w < 800) {
        $(".event-left").css({
            width: '350px',
        });
        $(".event-picture").css({
            width: 'calc(100% - 350px)'
        });
    }
    resetWindow();

    layui.use(['element', 'form', 'table', 'layer', 'laydate', 'laytpl'], function () {
        element = layui.element;
        form = layui.form;
        table = layui.table;
        layer = layui.layer;
        laytpl = layui.laytpl;
        $("#receiveEvent")[0].checked = false;
        let result = initialize_ocx('eventVideo');
        if (!result.result) {
            layer.open({
                title: langMessage.common.error
                , shade: 0.8
                , btn: false
                , content: result.msg
                , anim: 6
            });
            $("#receiveEvent").attr({
                disabled: true
            });
            form.render();
            return
        }
        getCarType('../config/carType.json', function (carStatus) {
            onloadConfig('../config/event.json', 'eventConfig', function (status) {
                if (!status || !carStatus) {
                    layer.msg(langMessage.common.violationError, {icon: 2});
                    $("#receiveEvent").attr({
                        disabled: true
                    });
                    form.render();
                    if (!ThreadStatus()) {
                        stopThread();
                    }
                }
            });
        });
        if (!ThreadStatus()) {
            // console.log('##############ThreadStatus1##################')
            $("#receiveEvent")[0].checked = true;
            eventInterval = setInterval(function () {
                getEvents();
            }, 200);
            tableInterval = setInterval(function () {
                // table.reload('eventTable', {
                //     data: eventArray
                // });
                myEventReload();
            }, 1000);
            clearInterval(interKeepAlive)
            interKeepAlive = setInterval(function () {
                console.log('4')
                initWebService(webserviceCMD.CMD_GET_THRD_STATE, null, hhhh)
            }, 30 * 1000)
            form.render();
        }

        form.on('checkbox(hidePicture)', function (data) {
            if (data.elem.checked) {
                $(".event-picture-4").css({
                    display: 'none'
                });
                $(".right-event-info-div").css({
                    display: 'none'
                });
                // $(".event-info").css({
                //     height: '91vh'
                // });
            } else {
                $(".event-picture-4").css({
                    display: 'block'
                });
                $(".right-event-info-div").css({
                    display: 'block'
                });
                // $(".event-info").css({
                //     height: '31vh'
                // });
            }
        });
        form.on('checkbox(receiveEvent)', function (data) {
            let handleReceive = function () {

                //老版本获取本地时间
                let eventTime = parseInt(Date.parse(new Date()) / 1000);

                //新版本获取相机时间
                // let eventTime = getDeviceTime();
                setSe("eventTime", eventTime);
                tmpTime = eventTime;

                let threadStatus = ThreadStatus();
                if (true !== threadStatus) {
                    if (!threadStatus) {
                        layer.msg(langMessage.event.frequent, {icon: 2});
                        data.elem.checked = false;
                        form.render();
                    } else {
                        layer.msg(langMessage.ocxGlobal.ocxError.ERROR, {icon: 2});
                    }
                    return
                }

                initListen(function (status) {
                    // console.log('##############initListen1##################')
                    if (status === true) {
                        // console.log('##############initListen2##################')
                        eventInterval = setInterval(function () {
                            getEvents();
                        }, 200);
                        tableInterval = setInterval(function () {
                            // table.reload('eventTable', {
                            //     data: eventArray
                            // });
                            myEventReload();
                        }, 1000);
                        clearInterval(interKeepAlive)
                        interKeepAlive = setInterval(function () {
                            console.log('3')
                            initWebService(webserviceCMD.CMD_GET_THRD_STATE, null, hhhh)
                        }, 30 * 1000)
                    } else {
                        // console.log('##############initListen3##################')
                        layer.msg(langMessage.event.createError, {icon: 2});
                        data.elem.checked = false;
                        $("#receiveEvent").attr({
                            disabled: true
                        });
                        form.render();
                        return
                    }
                });
            };
            if (data.elem.checked) {
                //老版本校时
                let checkTime = getSe("noCheckTime");
                // let fW_c = window.parent.document.getElementById("frame_c").offsetWidth;
                if (!checkTime) {
                    layer.open({
                        title: langMessage.common.error
                        , shade: 0.8
                        , btn: [langMessage.common.confirm, langMessage.common.cancel]
                        //, offset: ['30px', (fW_c - 280) + 'px']
                        , success: function (layero, index) {
                            let iframe = "<iframe id='iframebar' src='about:blank' frameBorder=0  marginHeight=0 marginWidth=0 style='position:absolute;visibility:inherit;top:0px;left:0px;height:230px;width:260px;z-index:-1' filter='progid:DXImageTransform.Microsoft.Alpha(style=0,opacity=0)'></iframe>"
                            $('.layui-layer-dialog').append(iframe)
                        }
                        , yes: function (index, layero) {
                            handleReceive();

                            layer.close(index);
                            let noCheckTime = ($("#noCheckTime")[0].checked === true ? 1 : 0);
                            setSe("noCheckTime", noCheckTime)
                        }
                        , btn2: function (index, layero) {
                            $("#receiveEvent")[0].checked = false;
                            form.render();
                            layer.close(index);
                        }
                        , icon: 0
                        , content: langMessage.event.checkTime
                        , anim: 6
                    });
                } else {
                    handleReceive();
                }

                // // 新版本不校时，但是未启用
                // handleReceive();
            } else {
                stopThread();
                clearInterval(eventInterval);
                clearInterval(tableInterval);
                setSe("eventTime", 0);
                // table.reload('eventTable', {
                //     data: eventArray
                // });
                // myTableReload();
                myEventReload();
                clearInterval(interKeepAlive)
            }
        });
        // 已废弃
        // tableIndex = table.render({
        //     elem: '#eventTable'
        //     , cols: [[ //标题栏
        //         {field: 'time', title: '触发时间',minWidth: 200}
        //         , {field: 'id', title: '事件ID', hide: true}
        //         , {field: 'eventType', title: '事件类型', align: 'center'}
        //         , {field: 'laneID', title: '车道号', align: 'center'}
        //         , {field: 'plateType', title: '车牌类型', align: 'center'}
        //         , {field: 'plate', title: '车牌号码', align: 'center'}
        //         , {field: 'speed', title: '速度', align: 'center'}
        //         , {field: 'carColor', title: '车身颜色', align: 'center'}
        //         , {field: 'desc', title: '概述', align: 'center'}]]
        //     , data: eventArray
        //     //,skin: 'line' //表格风格
        //     , even: true
        //     , limit: eventLength
        //     , page: false //是否显示分页
        //     //,limits: [5, 7, 10]
        //     , done: function () {
        //     }
        // });
        tableIndex = table.render({
            elem: '#eventTable'
            , width: fW - 55//globalInfo.w < 800 ? fW - 350 : fW - 500
            , cols: [[ //标题栏
                {field: 'time', title: langMessage.event.eventTable.time, minWidth: 200}
                , {field: 'ID', title: langMessage.event.eventTable.ID, hide: true}
                , {field: 'EVENT_TYPE', title: langMessage.event.eventTable.EVENT_TYPE, align: 'center'}
                , {field: 'LANE_INDEX', title: langMessage.event.eventTable.LANE_INDEX, align: 'center'}
                , {field: 'PLATE_TYPE', title: langMessage.event.eventTable.PLATE_TYPE, align: 'center'}
                , {field: 'PLATE_STRING', title: langMessage.event.eventTable.PLATE_STRING, align: 'center'}
                , {field: 'CAR_SPEED', title: langMessage.event.eventTable.CAR_SPEED, align: 'center'}
                , {field: 'CAR_COLOR', title: langMessage.event.eventTable.CAR_COLOR, align: 'center'}
                , {field: 'CAR_TYPE', title: langMessage.event.eventTable.CAR_TYPE, align: 'center'}
                , {field: 'IMAGE_EVENT_PATH', title: langMessage.event.eventTable.IMAGE_EVENT_PATH, hide: true}
                , {field: 'IMAGE_EVENT_X', title: langMessage.event.eventTable.IMAGE_EVENT_X, hide: true}
                , {field: 'IMAGE_EVENT_Y', title: langMessage.event.eventTable.IMAGE_EVENT_Y, hide: true}
                , {field: 'IMAGE_EVENT_W', title: langMessage.event.eventTable.IMAGE_EVENT_W, hide: true}
                , {field: 'IMAGE_EVENT_H', title: langMessage.event.eventTable.IMAGE_EVENT_H, hide: true}
                , {field: 'CAR_EVENT_X', title: langMessage.event.eventTable.IMAGE_EVENT_X, hide: true}
                , {field: 'CAR_EVENT_Y', title: langMessage.event.eventTable.IMAGE_EVENT_Y, hide: true}
                , {field: 'CAR_EVENT_W', title: langMessage.event.eventTable.IMAGE_EVENT_W, hide: true}
                , {field: 'CAR_EVENT_H', title: langMessage.event.eventTable.IMAGE_EVENT_H, hide: true}
                , {field: 'IMAGE_PRE_PATH', title: langMessage.event.eventTable.IMAGE_PRE_PATH, hide: true}
                , {field: 'IMAGE_PRE_X', title: langMessage.event.eventTable.IMAGE_PRE_X, hide: true}
                , {field: 'IMAGE_PRE_Y', title: langMessage.event.eventTable.IMAGE_PRE_Y, hide: true}
                , {field: 'IMAGE_PRE_W', title: langMessage.event.eventTable.IMAGE_PRE_W, hide: true}
                , {field: 'IMAGE_PRE_H', title: langMessage.event.eventTable.IMAGE_PRE_H, hide: true}
                , {field: 'CAR_PRE_X', title: langMessage.event.eventTable.IMAGE_PRE_X, hide: true}
                , {field: 'CAR_PRE_Y', title: langMessage.event.eventTable.IMAGE_PRE_Y, hide: true}
                , {field: 'CAR_PRE_W', title: langMessage.event.eventTable.IMAGE_PRE_W, hide: true}
                , {field: 'CAR_PRE_H', title: langMessage.event.eventTable.IMAGE_PRE_H, hide: true}
                , {field: 'IMAGE_LAST_PATH', title: langMessage.event.eventTable.IMAGE_LAST_PATH, hide: true}
                , {field: 'IMAGE_LAST_X', title: langMessage.event.eventTable.IMAGE_LAST_X, hide: true}
                , {field: 'IMAGE_LAST_Y', title: langMessage.event.eventTable.IMAGE_LAST_Y, hide: true}
                , {field: 'IMAGE_LAST_W', title: langMessage.event.eventTable.IMAGE_LAST_W, hide: true}
                , {field: 'IMAGE_LAST_H', title: langMessage.event.eventTable.IMAGE_LAST_H, hide: true}
                , {field: 'CAR_LAST_X', title: langMessage.event.eventTable.IMAGE_LAST_X, hide: true}
                , {field: 'CAR_LAST_Y', title: langMessage.event.eventTable.IMAGE_LAST_Y, hide: true}
                , {field: 'CAR_LAST_W', title: langMessage.event.eventTable.IMAGE_LAST_W, hide: true}
                , {field: 'CAR_LAST_H', title: langMessage.event.eventTable.IMAGE_LAST_H, hide: true}
                , {field: 'IMAGE_PLATE_PATH', title: langMessage.event.eventTable.IMAGE_PLATE_PATH, hide: true}
                , {field: 'IMAGE_FEATURE_PATH', title: langMessage.event.eventTable.IMAGE_FEATURE_PATH, hide: true}
                , {field: 'CHE_XING', title: langMessage.event.eventTable.CHE_XING, hide: true}
                , {field: 'preTime', title: langMessage.event.eventTable.preTime, hide: true}
                , {field: 'lastTime', title: langMessage.event.eventTable.lastTime, hide: true}
                , {field: 'featureTime', title: langMessage.event.eventTable.featureTime, hide: true}
                , {field: 'desc', title: langMessage.event.eventTable.desc, align: 'center'}]]
            , data: eventArray
            //,skin: 'line' //表格风格
            , even: true
            , limit: eventLength
            , page: false //是否显示分页
            //,limits: [5, 7, 10]
            , done: function () {
            }
        });
        // 已废弃
        // //双击事件
        // table.on('rowDouble(eventTable)', function (obj) {
        //     clearTimeout(clickTimer);
        //     // let clickData = obj.data;
        //     let clickData = JSON.parse(getDataset(obj.tr[0]).data);
        //     //旧逻辑根据时间查询
        //     // let t = clickData.time;
        //     // let param = date2stamp(t);
        //     let param = clickData.id;
        //     let data = selectTABLE(param);
        //     layer.open({
        //         title: '车牌号码：' + clickData.plate + ' 事件类型：' + clickData.eventType,
        //         type: 1,
        //         area: [outW, outH],
        //         offset: [(((fH - 100) / 2) - 200) + 'px', ((fW - 600) / 2) + 'px'],
        //         success: function (layero, index) {
        //             $(".layer-pic div").addClass("pic-height");
        //             $("#platePic1").html("");
        //             $("#prePic1").html("");
        //             $("#eventPic1").html("");
        //             $("#lastPic1").html("");
        //             $("#extendPic1").html("");
        //             if (data['IMAGE_PLATE_PATH'] !== '') {
        //                 imageCanvas('platePic1', data['IMAGE_PLATE_PATH']);
        //             }
        //             if (data['IMAGE_PRE_PATH'] !== '') {
        //                 imageCanvas('prePic1', data['IMAGE_PRE_PATH'], data['IMAGE_PRE_X'], data['IMAGE_PRE_Y'], data['IMAGE_PRE_W'], data['IMAGE_PRE_H'])
        //             }
        //             if (data['IMAGE_EVENT_PATH'] !== '') {
        //                 if(data['IMAGE_PRE_PATH'] === ''){
        //                     imageCanvas('prePic1', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'])
        //                 }else{
        //                     imageCanvas('eventPic1', data['IMAGE_EVENT_PATH'], data['IMAGE_EVENT_X'], data['IMAGE_EVENT_Y'], data['IMAGE_EVENT_W'], data['IMAGE_EVENT_H'])
        //                 }
        //             }
        //             if (data['IMAGE_LAST_PATH'] !== '') {
        //                 imageCanvas('lastPic1', data['IMAGE_LAST_PATH'], data['IMAGE_LAST_X'], data['IMAGE_LAST_Y'], data['IMAGE_LAST_W'], data['IMAGE_LAST_H'])
        //             }
        //     	    data = null;
        //         },
        //         end: function (layero, index) {
        //             $(".layer-pic div").removeClass("pic-height");
        //             $("#eventPic1").html("");
        //             $("#platePic1").html("");
        //             $("#prePic1").html("");
        //             $("#lastPic1").html("");
        //             $("#extendPic1").html("");
        //         },
        //         content: $('#eventPicture') //这里content是一个DOM，注意：最好该元素要存放在body最外层，否则可能被其它的相对元素所影响
        //     });
        // });
        // //单击事件
        // table.on('row(eventTable)', function (obj) {
        //     clearTimeout(clickTimer);
        //     clickTimer = setTimeout(function () {
        //         // let clickData = obj.data;
        //         let clickData = JSON.parse(getDataset(obj.tr[0]).data);
        //         //旧逻辑根据时间查询
        //         // let t = clickData.time;
        //         // let param = date2stamp(t);
        //         let param = clickData.id;
        //         let data = selectTABLE(param);
        //         $("#plate").html(clickData.plate);
        //         $("#platePic").html("");
        //         $("#prePic").html("");
        //         $("#eventPic").html("");
        //         $("#lastPic").html("");
        //         if (data['IMAGE_PLATE_PATH'] !== '') {
        //             imageCanvas('platePic', data['IMAGE_PLATE_PATH']);
        //         }
        //         if (data['IMAGE_PRE_PATH'] !== '') {
        //             imageCanvas('prePic', data['IMAGE_PRE_PATH']);
        //         }
        //         if (data['IMAGE_EVENT_PATH'] !== '') {
        //             if(data['IMAGE_PRE_PATH'] ===''){
        //                 imageCanvas('prePic', data['IMAGE_EVENT_PATH']);
        //             }else{
        //                 imageCanvas('eventPic', data['IMAGE_EVENT_PATH']);
        //             }
        //         }
        //         if (data['IMAGE_LAST_PATH'] !== '') {
        //             imageCanvas('lastPic', data['IMAGE_LAST_PATH']);
        //         }
        //         data = null;
        //     },300);
        //
        // });
        //双击事件
        table.on('rowDouble(eventTable)', function (obj) {
            clearTimeout(clickTimer);
            // let clickData = obj.data;
            let data = JSON.parse(getDataset(obj.tr[0]).data);
            let _index = getDataset(this).index;
            console.log(data);
            showEventDetail(data, _index, outW, outH);
        });
        //单击事件
        table.on('row(eventTable)', function (obj) {
            clearTimeout(clickTimer);
            clickTimer = setTimeout(function () {
                // let clickData = obj.data;
                let data = JSON.parse(getDataset(obj.tr[0]).data);
                $("#TIME-").html(data['time']);
                $("#EVENT_TYPE-").html(data.EVENT_TYPE);
                $("#LANE_INDEX-").html(data.LANE_INDEX);
                $("#PLATE_TYPE-").html(data.PLATE_TYPE);
                $("#PLATE_STRING-").html(data.PLATE_STRING);
                $("#CAR_SPEED-").html(data.CAR_SPEED);
                $("#CAR_COLOR-").html(data.CAR_COLOR);
                $("#CAR_TYPE-").html(data.CAR_TYPE);
                if (data['CHE_XING']) {
                    $("#CHE_XING-").html(data.PLATE_STRING);
                }

                $("#plate").html(data.PLATE_STRING);
                $("#platePic1").html("");
                $("#prePic1").html("");
                $("#eventPic1").html("");
                $("#lastPic1").html("");
                $("#extendPic1").html("");
                if (data['IMAGE_PLATE_PATH'] !== '') {
                    imageCanvas('platePic1', data['IMAGE_PLATE_PATH']);
                }
                if (data['IMAGE_PRE_PATH'] !== '') {
                    imageCanvas('prePic1', data['IMAGE_PRE_PATH']);
                }
                if (data['IMAGE_EVENT_PATH'] !== '') {
                    // if (data['IMAGE_PRE_PATH'] === '') {
                    //     imageCanvas('prePic1', data['IMAGE_EVENT_PATH']);
                    // } else {
                    //     imageCanvas('eventPic1', data['IMAGE_EVENT_PATH']);
                    // }
                    imageCanvas('eventPic1', data['IMAGE_EVENT_PATH']);
                }
                if (data['IMAGE_LAST_PATH'] !== '') {
                    imageCanvas('lastPic1', data['IMAGE_LAST_PATH']);
                }
                data = null;
            }, 300);

        });
        form.render();
    });

    // 已废弃
    /*$(".video-type span a").off('click').on('click', function () {
        if ($(".video-stop").length) {
            let channel = getDataset($("#playVideo")[0]).channel;
            let ch = getDataset(this).channel;
            if (channel !== ch) {
                stopRTSP(channel);
                initStreamType(ch);
                setDataset($("#playVideo")[0], "channel", ch);
            }
        }
        $(".video-type span a").removeClass('video-selected');
        $(this).addClass('video-selected');
    });
    $("#videoT1Value").off('click').on('click', function () {
        if($(this).parents(".video-type").find(".video-select").css('display')==='block'){
            $(this).parents(".video-type").find(".video-select").css({display: 'none'});
            return
        }
        $(".video-select").css({display: 'none'});
        let v = this.innerText;
        $("#videoT1Select li").removeClass("selected");
        if (v === '主码流') {
            $($("#videoT1Select li")[0]).addClass("selected")
        }else{
            $($("#videoT1Select li")[1]).addClass("selected")
        }
        $("#videoT1C .video-select").css({display: 'block'});
    });
    $("#videoT1Select li>a").off('click').on('click', function () {
        let channel = getDataset($("#videoT1Value")[0]).channel;
        let ch = getDataset(this).channel;
        if (channel !== ch) {
            let vs = ["", "主码流", "子码流"];
            $("#videoT1Value").html(vs[ch]);
            stopRTSP(channel);
            initStreamType(ch);
            setDataset($("#videoT1Value")[0], "channel", ch);
            $(".video-select").css({display: 'none'});
        }
    });*/
    $("#videoT2Value").off('click').on('click', function () {
        if ($(this).parents(".video-type").find(".video-select").css('display') === 'block') {
            $(this).parents(".video-type").find(".video-select").css({display: 'none'});
            return
        }
        $(".video-select").css({display: 'none'});
        let v = this.innerText;
        $("#videoT2Select li").removeClass("selected");
        if (v === 'UDP') {
            $($("#videoT2Select li")[0]).addClass("selected")
        } else {
            $($("#videoT2Select li")[1]).addClass("selected")
        }
        $("#videoT2C .video-select").css({display: 'block'});
    });
    $("#videoT2Select li>a").off('click').on('click', function () {
        let channel = getDataset($("#videoT2Value")[0]).type;
        let ch = getDataset($("#videoT1Value")[0]).channel;
        let v = getDataset(this).type;
        if (channel !== v) {
            let vs = ["UDP", "TCP"];
            setRTSPWay(vs[v], function (result) {
                if (result === true) {
                    if ($(".video-stop").length) {
                        stopRTSP(ch);
                        initStreamType(ch);
                    }
                    setDataset($("#videoT2Value")[0], "type", v);
                    $("#videoT2Value").html(vs[v]);
                    layer.msg(langMessage.common.editSuc, {icon: 1});
                } else {
                    console.log(result);
                    layer.msg(langMessage.common.editFail, {icon: 2});
                }
                $(".video-select").css({display: 'none'});
            });

        }
    });
    $("#playVideo").off('click').on('click', function (e) {
        let ip = location.hostname;
        //ip = '*************'
        let userInfo = getSe('loginUser');
        let channel = getSe("projectConfig").camera_channel;
        let ch = getDataset($("#videoT1Value")[0]).channel;
        if (ch != channel) {
            channel = ch
        }
        setDataset(this, "channel", channel);
        if (e.currentTarget.className === 'video-play') {
            $(e.currentTarget).removeClass('video-play');
            $(e.currentTarget).addClass('video-stop');
            initStreamType(channel);
        } else if (e.currentTarget.className === 'video-stop') {
            $(e.currentTarget).removeClass('video-stop');
            $(e.currentTarget).addClass('video-play');
            stopRTSP(channel);
        }
    })
    // setInterval(function () {
    //     myEventReload({ORDER: "DESC", LIMIT: "0,20"}, true);
    // }, 1000);
});

var hhhh = function (data) {

}
var getEvents = function () {
    // console.log('##############getEvents1##################')
    let getData = getEventStatus();
    if (getData.status === '-1') {
        // console.log('##############getEvents2##################')
        clearInterval(eventInterval);
        clearInterval(tableInterval);
        stopThread();
        setTimeout(function () {
            // $("input#receiveEvent").attr("checked", "false");
            $("#receiveEvent")[0].checked = false;
            form.render();
            layer.msg(langMessage.event.serverDisconnected, {icon: 2});
            console.log(langMessage.event.receptionFail);
            // table.reload('eventTable', {
            //     data: eventArray
            // });
            myEventReload();
        }, 500)
    }
};
// 已废弃
// var getEvent = function (eventArray, ele) {
//     let getData = ocx.eventWeb();
//     // let date = Date.parse(new Date());
//     let data = JSON.parse(getData);
//     getData = "";
//     let eventData = "";
//     if (data.status === '-1') {
//         clearInterval(eventInterval);
//         clearInterval(tableInterval);
//         stopThread();
//         setTimeout(function () {
//             // $("input#receiveEvent").attr("checked", "false");
//             ele.checked = false;
//             form.render();
//             layer.msg('服务器连接断开', {icon: 2});
//             console.log('事件接收失败');
//             // table.reload('eventTable', {
//             //     data: eventArray
//             // });
//             myTableReload();
//         }, 500)
//     }
//     if (data.status === '1') {
//         eventData = data.eventInfo
//     }
//     if (eventData) {
//         let id = insertDB(eventData[0]);
//         eventData[0].id = id;
//         let l = eventArray.length;
//         if (l === eventLength) {
//             eventArray.pop()
//         } else if (l >= eventLength) {
//             eventArray = eventArray.slice(0, eventLength)
//         }
//         let newEvent = insertTABLE(eventData[0]);
//         eventArray.unshift(newEvent);
//         newEvent = null;
//         eventArray = sortEvent(eventArray);
//         eventData = "";
//     }
// };
// var createDB = function (path) {
//     let openDB = ocx.sqlite_open(path);
//     // if(openDB!==0){
//     //     openDB = ocx.sqlite_open(openDB);
//     // }
//     console.log("open database " + openDB);
//     let execDB1 = 0;
//     execDB1 = ocx.sqlite_exec("SELECT COUNT(*) FROM 'EVENT_TBL'");
//     console.log("exists " + execDB1);
//     if (execDB1) {
//         let execDB = ocx.sqlite_exec(
//             'DROP TABLE IF EXISTS "EVENT_TBL";' +
//             'CREATE TABLE "EVENT_TBL" (' +
//             '  "ID" integer PRIMARY KEY AUTOINCREMENT,' +
//             '  "EVENT_TYPE" integer,' +
//             '  "PLATE_TYPE" integer,' +
//             '  "PLATE_COLOR" integer,' +
//             '  "PLATE_STRING" VARCHAR(50),' +
//             '  "LANE_INDEX" integer,' +
//             '  "CAR_COLOR" integer,' +
//             '  "CAR_TYPE" VARCHAR(50),' +
//             '  "CAR_SPEED" integer,' +
//             '  "EVENT_TIME" integer,' +
//             '  "EVENT_TICK" integer,' +
//             '  "IMAGE_COUNT" integer,' +
//             '  "IMAGE_PRE_PATH" VARCHAR(256),' +
//             '  "IMAGE_PRE_X" integer,' +
//             '  "IMAGE_PRE_Y" integer,' +
//             '  "IMAGE_PRE_W" integer,' +
//             '  "IMAGE_PRE_H" integer,' +
//             '  "IMAGE_EVENT_PATH" VARCHAR(256),' +
//             '  "IMAGE_EVENT_X" integer,' +
//             '  "IMAGE_EVENT_Y" integer,' +
//             '  "IMAGE_EVENT_W" integer,' +
//             '  "IMAGE_EVENT_H" integer,' +
//             '  "IMAGE_LAST_PATH" VARCHAR(256),' +
//             '  "IMAGE_LAST_X" integer,' +
//             '  "IMAGE_LAST_Y" integer,' +
//             '  "IMAGE_LAST_W" integer,' +
//             '  "IMAGE_LAST_H" integer,' +
//             '  "IMAGE_PLATE_PATH" VARCHAR(256),' +
//             '  "PARAM_DATA" blob' +
//             ');');
//         console.log("create " + execDB);
//         execDB = "";
//     }
// };
// var selectTABLE = function (param) {
//     //旧逻辑根据时间查询
//     // let sql = 'select IMAGE_COUNT,IMAGE_PRE_PATH,IMAGE_EVENT_PATH,IMAGE_LAST_PATH,IMAGE_PLATE_PATH from EVENT_TBL ' +
//     //     'where EVENT_TIME=' +
//     //     param.EVENT_TIME +
//     //     ' and EVENT_TICK=' +
//     //     param.EVENT_TICK +
//     //     ';';
//     let sql = 'select IMAGE_COUNT,' +
//         'IMAGE_PRE_PATH,IMAGE_PRE_X,IMAGE_PRE_Y,IMAGE_PRE_W,IMAGE_PRE_H,' +
//         'IMAGE_EVENT_PATH,IMAGE_EVENT_X,IMAGE_EVENT_Y,IMAGE_EVENT_W,IMAGE_EVENT_H,' +
//         'IMAGE_LAST_PATH,IMAGE_LAST_X,IMAGE_LAST_Y,IMAGE_LAST_W,IMAGE_LAST_H,IMAGE_PLATE_PATH from EVENT_TBL ' +
//         'where ID=' +
//         param +
//         ';';
//     let useDB = ocx.sqlite_select(sql);
//     let d = useDB.replace(/\\/g, "\\\\");
//     // console.log("select " + useDB);
//     let dataOBJ = JSON.parse(d);
//     // obj.picNum = dataOBJ.IMAGE_COUTN;
//     // obj.plate_path = dataOBJ.IMAGE_PLATE_PATH;
//     // obj.event_image_path = dataOBJ.IMAGE_EVENT_PATH;
//     // obj.pre_image_path = dataOBJ.IMAGE_PRE_PATH;
//     // obj.latter_image_path = dataOBJ.IMAGE_LAST_PATH;
//     return dataOBJ[0]
// };
// var insertTABLE = function (data) {
//     let event = {};
//     let eventConfig = getSe('eventConfig');
//     event.timestamp = data['timestamp'];
//     event.time = getDate(data['timestamp'], data['msecond']);
//     event.id = data['id'];
//     event.eventType = eventConfig.eventType[data['event_type']];
//     event.laneID = data['pass_lane_id'];
//     event.plateType = eventConfig.plateType[data['reg_plate_type']];
//     event.plate = data['cphm'];
//     event.speed = data['speed'];
//     event.carColor = eventConfig.carColor[data['car_color']];
//     eventConfig = null;
//     console.log(event);
//     $("#plate").html(event.plate);
//     showPic(data);
//
//     return event
// };
// var sortEvent = function (eventArray) {
//     let temp;
//     for (let i = 0; i < eventArray.length - 1; i++) {
//         for (let j = 0; j < eventArray.length - 1 - i; j++) {
//             if ((eventArray[j].timestamp)*1000 + parseInt(eventArray[j].msecond) < (eventArray[j + 1].timestamp)*1000 + parseInt(eventArray[j+1].msecond)) {
//                 temp = eventArray[j];
//                 eventArray[j] = eventArray[j + 1];
//                 eventArray[j + 1] = temp;
//                 temp = null;
//             }
//         }
//     }
//     return eventArray
// };
// var showPic = function (data) {
//     if (data['plate_path'] !== '') {
//         imageCanvas('platePic', data['plate_path']);
//     } else {
//         $("#platePic").html("");
//     }
//     if ($("#hidePicture")[0].checked === false) {
//         $("#eventPic").html("");
//         $("#prePic").html("");
//         $("#lastPic").html("");
//         if (data['pre_image_path'] !== '') {
//             imageCanvas('prePic', data['pre_image_path']);
//
//         }
//         if (data['event_image_path'] !== '') {
//             if(data['pre_image_path'] ===''){
//                 imageCanvas('prePic', data['event_image_path'])
//             }else{
//                 imageCanvas('eventPic', data['event_image_path'])
//             }
//         }
//         if (data['latter_image_path'] !== '') {
//             imageCanvas('lastPic', data['latter_image_path']);
//         }
//     }
// };
var showPic = function (data) {
    $("#TIME-").html(data['time']);
    $("#EVENT_TYPE-").html(data.EVENT_TYPE);
    $("#LANE_INDEX-").html(data.LANE_INDEX);
    $("#PLATE_TYPE-").html(data.PLATE_TYPE);
    $("#PLATE_STRING-").html(data.PLATE_STRING);
    $("#CAR_SPEED-").html(data.CAR_SPEED);
    $("#CAR_COLOR-").html(data.CAR_COLOR);
    $("#CAR_TYPE-").html(data.CAR_TYPE);
    if (data['CHE_XING']) {
        $("#CHE_XING-").html(data.PLATE_STRING);
    }

    $("#plate").html(data.PLATE_STRING);
    if (data['IMAGE_PLATE_PATH'] !== '') {
        imageCanvas('platePic1', data['IMAGE_PLATE_PATH']);
    } else {
        $("#platePic1").html("");
    }
    if ($("#hidePicture")[0].checked === false) {
        $("#eventPic1").html("");
        $("#prePic1").html("");
        $("#lastPic1").html("");
        if (data['IMAGE_PRE_PATH'] !== '') {
            imageCanvas('prePic1', data['IMAGE_PRE_PATH']);

        }
        if (data['IMAGE_EVENT_PATH'] !== '') {
            // if (data['IMAGE_PRE_PATH'] === '') {
            //     imageCanvas('prePic1', data['IMAGE_EVENT_PATH'])
            // } else {
            //     imageCanvas('eventPic1', data['IMAGE_EVENT_PATH'])
            // }
            imageCanvas('eventPic1', data['IMAGE_EVENT_PATH'])
        }
        if (data['IMAGE_LAST_PATH'] !== '') {
            imageCanvas('lastPic1', data['IMAGE_LAST_PATH']);
        }
        if (data['IMAGE_FEATURE_PATH'] !== '') {
            imageCanvas('extendPic1', data['IMAGE_FEATURE_PATH']);
        }
    }
};
// 已废弃
// var insertDB = function (data) {
//     let imgCount = parseInt(data.picNum);
//     let imgPath = '';
//     let imgPathValue = '"';
//     if (data['event_image_path']) {
//         imgPath += 'IMAGE_EVENT_PATH,IMAGE_EVENT_X,IMAGE_EVENT_Y,IMAGE_EVENT_W,IMAGE_EVENT_H';
//         imgPathValue += data['event_image_path'] + '",' + data['interTopleft_x1'] + ',' + data['interTopleft_y1'] + ',' + data['interWidth1'] + ',' + data['interHeight1'];
//     }
//     if (data['plate_path']) {
//         imgPath += ',IMAGE_PLATE_PATH';
//         imgPathValue += ',"' + data['plate_path'] + '"'
//     }
//     if (data['pre_image_path']) {
//         imgPath += ',IMAGE_PRE_PATH,IMAGE_PRE_X,IMAGE_PRE_Y,IMAGE_PRE_W,IMAGE_PRE_H';
//         imgPathValue += ',"' + data['pre_image_path'] + '",' + data['interTopleft_x0'] + ',' + data['interTopleft_y0'] + ',' + data['interWidth0'] + ',' + data['interHeight0']
//     }
//     if (data['latter_image_path']) {
//         imgPath += ',IMAGE_LAST_PATH,IMAGE_LAST_X,IMAGE_LAST_Y,IMAGE_LAST_W,IMAGE_LAST_H';
//         imgPathValue += ',"' + data['latter_image_path'] + '",' + data['interTopleft_x2'] + ',' + data['interTopleft_y2'] + ',' + data['interWidth2'] + ',' + data['interHeight2'];
//     }
//     let sql = 'insert into EVENT_TBL (' +
//         'EVENT_TYPE,PLATE_TYPE,PLATE_COLOR,PLATE_STRING,LANE_INDEX,CAR_COLOR,CAR_TYPE,CAR_SPEED,EVENT_TIME,EVENT_TICK,IMAGE_COUNT,' +
//         imgPath +
//         ') values(' +
//         data['event_type'] + ',' + data['reg_plate_type'] + ',' + data['reg_plate_color'] + ',"' + data['cphm'] + '",' + data['pass_lane_id'] + ',' + data['car_color'] + ',"' + data['CLLX'] + '",' + data['speed'] + ',' + data['timestamp'] + ',' + data['msecond'] + ',' + imgCount + ',' +
//         imgPathValue +
//         ');';
//
//     let IDSql = 'select ID from EVENT_TBL order by id DESC limit 1;';
//     let useDB = ocx.sqlite_exec(sql);
//     let result = JSON.parse(ocx.sqlite_select(IDSql));
//     let ID = result[0].ID;
//     console.log("insert " + useDB);
//     useDB = "";
//     imgPath = "";
//     imgPathValue = "";
//     sql = "";
//     IDSql = "";
//     result = null;
//     return ID;
// };
var errorRTSP = function () {
    let msg = langMessage.common.nonsupportVideo;
    layer.msg(msg, {icon: 2});
    $("#eventVideo").html("");
    $("#eventVideo").append("<a id='ocx_down'>" + msg + "</a>");
};
var resetWindow = function () {
    fH = window.parent.document.getElementById("frame_c").offsetHeight;
    fW = window.parent.document.getElementById("frame_c").offsetWidth;
    let h = parseInt(fH * 0.6);
    $(".video-style").css("height", h);
    $(".event-plate").css({
        height: fH * 0.2 + 'px'
    });
    $("#plate").css({
        lineHeight: fH * 0.2 + 'px',
        fontSize: fH * 0.2 * 0.3 + 'px'
    });
    $(".event-picture-4 div").css("height", parseInt(fH * 0.3));
    $(".right-event-info-div").css("height", parseInt(fH * 0.3 * 2 - 1));
    if (tableIndex !== null) {
        let globalInfo = checkGlobal();
        tableIndex.reload({
            width: globalInfo.w < 800 ? fW - 350 : fW - 500
        });
        myEventReload(null, true);
    }
    $("#eventVideo").css({
        height: h,
    });
    $("#ocx").attr({
        height: h - 30,
        width: $("#eventVideo")[0].offsetWidth
    });

};
var myTableReload = function () {
    let roleListDiv = $("div[lay-id='eventTable'] .layui-table-body .layui-table tbody");
    if (roleListDiv.length > 0) {
        roleListDiv.remove();
    }
    roleListDiv = null;
    let appendTable = "";
    let config = tableIndex.config;
    // "{\"cols\":[[{\"field\":\"time\",\"title\":\"触发时间\",\"minWidth\":200,\"key\":\"0-0\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"id\",\"title\":\"事件ID\",\"hide\":true,\"key\":\"0-1\",\"type\":\"normal\"},{\"field\":\"eventType\",\"title\":\"事件类型\",\"align\":\"center\",\"key\":\"0-2\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"laneID\",\"title\":\"车道号\",\"align\":\"center\",\"key\":\"0-3\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"plateType\",\"title\":\"车牌类型\",\"align\":\"center\",\"key\":\"0-4\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"plate\",\"title\":\"车牌号码\",\"align\":\"center\",\"key\":\"0-5\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"speed\",\"title\":\"速度\",\"align\":\"center\",\"key\":\"0-6\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"carColor\",\"title\":\"车身颜色\",\"align\":\"center\",\"key\":\"0-7\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"desc\",\"title\":\"概述\",\"align\":\"center\",\"key\":\"0-8\",\"hide\":false,\"type\":\"normal\",\"width\":0}]],\"data\":[],\"even\":true,\"page\":false,\"where\":{},\"id\":\"eventTable\",\"request\":{\"pageName\":\"page\",\"limitName\":\"limit\"},\"response\":{\"statusName\":\"code\",\"statusCode\":0,\"msgName\":\"msg\",\"dataName\":\"data\",\"countName\":\"count\"},\"clientWidth\":1623,\"index\":1}"
    if (eventArray.length) {
        $(".layui-none").remove();
        appendTable += '<tbody>';
        for (let i = 0; i < eventArray.length; i++) {
            appendTable += "<tr data-index='" + i + "' data-data='" + JSON.stringify(eventArray[i]) + "'>";
            let cols = config.cols[0];
            for (let j = 0; j < cols.length; j++) {
                let col = cols[j];
                let key = config.index + '-' + col.key;
                let style = col.hide === true ? "layui-hide" : "";
                let width = col.minWidth ? "data-minwidth=\"" + col.minWidth + "\"" : "";
                appendTable += '<td class="' + style + '" ' + width + ' data-key="' + key + '" data-field="' + col.field + '"><div class="layui-table-cell laytable-cell-' + key + '">' + (eventArray[i][col.field] === undefined ? "" : eventArray[i][col.field]) + '</div></td>'
            }
            appendTable += '</tr>'
        }
        appendTable += '</tbody>';
    } else {
        appendTable += "";
    }
    $("div[lay-id='eventTable'] .layui-table-body .layui-table").html(appendTable);
    appendTable = "";
};
var myEventReload = function (param, reload) {
    // console.log('##############myEventReload1##################')
    if (!param) {
        param = {};
        let eventTime = getSe("eventTime");
        if (reload) {
            eventTime = tmpTime;
        }
        if (eventTime !== 0) {
            param.BEGIN_TIME = eventTime
        } else {
            return
        }
    }
    // console.log('##############myEventReload2##################')
    param.ORDER = "DESC";
    param.LIMIT = "0,20";
    let event = ocxSQL(param);
    // console.log('##############接收事件############')
    // console.log(JSON.stringify(event))
    if (event.length > 0) {
        let nowID = event[0].ID;
        if (nowID !== eventID || reload) {
            eventID = nowID;
            let roleListDiv = $("div[lay-id='eventTable'] .layui-table-body .layui-table tbody");
            if (roleListDiv.length > 0) {
                roleListDiv.remove();
            }
            roleListDiv = null;
            let appendTable = "";
            let config = tableIndex.config;
            // "{\"cols\":[[{\"field\":\"time\",\"title\":\"触发时间\",\"minWidth\":200,\"key\":\"0-0\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"id\",\"title\":\"事件ID\",\"hide\":true,\"key\":\"0-1\",\"type\":\"normal\"},{\"field\":\"eventType\",\"title\":\"事件类型\",\"align\":\"center\",\"key\":\"0-2\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"laneID\",\"title\":\"车道号\",\"align\":\"center\",\"key\":\"0-3\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"plateType\",\"title\":\"车牌类型\",\"align\":\"center\",\"key\":\"0-4\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"plate\",\"title\":\"车牌号码\",\"align\":\"center\",\"key\":\"0-5\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"speed\",\"title\":\"速度\",\"align\":\"center\",\"key\":\"0-6\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"carColor\",\"title\":\"车身颜色\",\"align\":\"center\",\"key\":\"0-7\",\"hide\":false,\"type\":\"normal\",\"width\":0},{\"field\":\"desc\",\"title\":\"概述\",\"align\":\"center\",\"key\":\"0-8\",\"hide\":false,\"type\":\"normal\",\"width\":0}]],\"data\":[],\"even\":true,\"page\":false,\"where\":{},\"id\":\"eventTable\",\"request\":{\"pageName\":\"page\",\"limitName\":\"limit\"},\"response\":{\"statusName\":\"code\",\"statusCode\":0,\"msgName\":\"msg\",\"dataName\":\"data\",\"countName\":\"count\"},\"clientWidth\":1623,\"index\":1}"
            if (event.length > 0) {
                $(".layui-none").remove();
                appendTable += '<tbody>';
                for (let i = 0; i < event.length; i++) {
                    // 列表模式#######################################################
                    appendTable += "<tr data-index='" + i + "' data-data='" + JSON.stringify(event[i]) + "'>";
                    let cols = config.cols[0];
                    for (let j = 0; j < cols.length; j++) {
                        let col = cols[j];
                        let key = config.index + '-' + col.key;
                        let style = col.hide === true ? "layui-hide" : "";
                        let width = col.minWidth ? "data-minwidth=\"" + col.minWidth + "\"" : "";
                        appendTable += '<td class="' + style + '" ' + width + ' data-key="' + key + '" data-field="' + col.field + '"><div class="layui-table-cell laytable-cell-' + key + '">' + (event[i][col.field] === undefined ? "" : event[i][col.field]) + '</div></td>'
                    }
                    appendTable += '</tr>'
                    // 图片模式#########################################################
                    if (i < 10) {
                        // let id = event[i].ID;
                        imageCanvas_pictureMode('td_eventPic_' + i, event[i]['IMAGE_EVENT_PATH'], null, null, 155, 75)
                        $('#td_eventPic_' + i).off('dblclick').on('dblclick', function () {
                            console.log(event[i])
                            pictureModeDbclick(event[i], i, '800px', '500px')
                        });
                        $('#td_eventPic_' + i).off('click').on('click', function () {
                            console.log(event[i])
                            pictureModeClick(event[i])
                        });
                        // imageCanvas_pictureMode('td_platePic_' + i, event[i]['IMAGE_PLATE_PATH'], null, null, 155, 30);
                        // $('#td_platePic_' + i).off('dblclick').on('dblclick', function () {
                        //     console.log(event[i])
                        //     pictureModeDbclick(event[i], i, '800px', '500px')
                        // });
                        // $('#td_platePic_' + i).off('click').on('click', function () {
                        //     console.log(event[i])
                        //     pictureModeClick(event[i])
                        // });
                        $('#td_plateNum_' + i).html(event[i].PLATE_STRING);
                        $('#td_plateNum_' + i).off('dblclick').on('dblclick', function () {
                            console.log(event[i])
                            pictureModeDbclick(event[i], i, '800px', '500px')
                        });
                        $('#td_plateNum_' + i).off('click').on('click', function () {
                            console.log(event[i])
                            pictureModeClick(event[i])
                        });


                        $('#td_eventType_' + i).html(event[i].EVENT_TYPE);
                        $('#td_eventType_' + i).off('dblclick').on('dblclick', function () {
                            console.log(event[i])
                            pictureModeDbclick(event[i], i, '800px', '500px')
                        });
                        $('#td_eventType_' + i).off('click').on('click', function () {
                            console.log(event[i])
                            pictureModeClick(event[i])
                        });
                    }
                }
                appendTable += '</tbody>';
            } else {
                appendTable += "";
            }
            $("div[lay-id='eventTable'] .layui-table-body .layui-table").html(appendTable);
            appendTable = "";
            showPic(event[0]);
        }

    }
    // else if(event.status===-1){
    //     layer.msg("数据查询出错！", {icon: 2});
    // }else if(event.status===-1){
    //     layer.msg('无法创建文件！请联系开发人员。', {icon: 2});
    //     $("#receiveEvent")[0].checked = false;
    //     $("#receiveEvent").attr({
    //         disabled: true
    //     });
    //     form.render();
    // }
};
var pictureModeDbclick = function (data, _index, outW, outH) {
    clearTimeout(clickTimer);
    showEventDetail(data, _index, outW, outH);
};
var pictureModeClick = function (data) {
    clearTimeout(clickTimer);
    clickTimer = setTimeout(function () {
        $("#TIME-").html(data['time']);
        $("#EVENT_TYPE-").html(data.EVENT_TYPE);
        $("#LANE_INDEX-").html(data.LANE_INDEX);
        $("#PLATE_TYPE-").html(data.PLATE_TYPE);
        $("#PLATE_STRING-").html(data.PLATE_STRING);
        $("#CAR_SPEED-").html(data.CAR_SPEED);
        $("#CAR_COLOR-").html(data.CAR_COLOR);
        $("#CAR_TYPE-").html(data.CAR_TYPE);
        if (data['CHE_XING']) {
            $("#CHE_XING-").html(data.PLATE_STRING);
        }

        $("#plate").html(data.PLATE_STRING);
        $("#platePic1").html("");
        $("#prePic1").html("");
        $("#eventPic1").html("");
        $("#lastPic1").html("");
        $("#extendPic1").html("");
        if (data['IMAGE_PLATE_PATH'] !== '') {
            imageCanvas('platePic1', data['IMAGE_PLATE_PATH']);
        }
        if (data['IMAGE_PRE_PATH'] !== '') {
            imageCanvas('prePic1', data['IMAGE_PRE_PATH']);
        }
        if (data['IMAGE_EVENT_PATH'] !== '') {
            // if (data['IMAGE_PRE_PATH'] === '') {
            //     imageCanvas('prePic1', data['IMAGE_EVENT_PATH']);
            // } else {
            //     imageCanvas('eventPic1', data['IMAGE_EVENT_PATH']);
            // }
            imageCanvas('eventPic1', data['IMAGE_EVENT_PATH']);
        }
        if (data['IMAGE_LAST_PATH'] !== '') {
            imageCanvas('lastPic1', data['IMAGE_LAST_PATH']);
        }
        if (data['IMAGE_FEATURE_PATH'] !== '') {
            imageCanvas('extendPic1', data['IMAGE_FEATURE_PATH']);
        }
        data = null;
    }, 300);
};
