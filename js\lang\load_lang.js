/**
 * 动态插入语言包
 * @param src 需要返回的上级的次数
 */
var loadLanguage = function () {
  let i18n = getCookie('i18ns');
  if (!i18n) {
    i18n = "zh";
  }
  document.write("<script src='./js/lang/lang_" + i18n + ".js'></script>");
};

var setLanguage = function (langMessage) {
  setSe("langMessage", langMessage);
};

var getLanguage = function () {
  return getSe("langMessage");
};

/**
 * 根据语言包 修改页面上的语言
 * @param obj
 * @param common
 */
var initLang = function (obj, common) {

  let text = obj.text;
  initText(text);
  let clazz = obj.clazz;
  let placeholder = obj.placeholder;
  let title = obj.title;
  let images = obj.image;
  let placeholder2 = obj.placeholder2;

  initClazz(clazz);
  initClass(placeholder2, "placeholder");
  initAttr(title, "title");
  initAttr(placeholder, "placeholder");
  initAttr(images, "src");
  initCommon(common);
};

/**
 * 根据id修改页面上内容
 * @param obj
 */
var initText = function (obj) {
  for (let key in obj) {
    $("#" + key).html(obj[key])
  }
};

/**
 * 根据class修改页面上text内容
 * @param obj
 */

var initClazz = function (obj) {
  for (let key in obj) {
    $("." + key).html(obj[key])
  }
};

/**
 * 根据id修改页面上的type类型内容
 * @param obj 传入的id
 * @param type 需要修改的属性类型
 */

var initAttr = function (obj, type) {
  for (let key in obj) {
    $("#" + key).attr(type, obj[key])
  }
};

/**
 * 根据class修改页面上的type类型内容
 * @param obj 传入的class
 * @param type 需要修改的属性类型
 */
var initClass = function (obj, type) {
  for (let key in obj) {
    $("." + key).attr(type, obj[key])
  }
};

var getAllId = function () {
  let idArray = [];
  let _docc = window.document.all;
  // 遍历每一个对象
  for (let i = 0; i < _docc.length; i++) {
    let _dc = _docc[i];
    // 获取的每个id的node节点
    let node = _dc.getAttributeNode("id");
    // 当前标签的id的值
    let id = _dc.getAttribute("id");
    if (id) {
      idArray.push(id);
    }
  }
  return idArray;
};

/**
 * 修改共管数据的
 * @param common
 */
var initCommon = function (common) {
  let idArray = getAllId();
  idArray.map(function (item) {
    let v = common[item.toUpperCase()];
    if (v) {
      $("#" + item).html(v)
    }
  })
};

document.ready(function () {
  let title = document.getElementsByTagName('title')[0].innerHTML;
  let lang = getLanguage();
  if (title && title !== 'index' && lang[title]) {
    initLang(lang[title], lang.common);
  }
  console.log('ready')
});
