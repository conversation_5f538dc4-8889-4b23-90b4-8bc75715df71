var layer, form;
//监听刷新页面
window.onbeforeunload = function () {
};
window.onresize = function () {
  setContainer();
};
$(document).ready(function () {
  // let w = $("#ocxContainer")[0].clientWidth;
  // let h = parseInt(w / 16 * 9)
  window.parent.setNavValue(0);//菜单样式
  setContainer();//设置#indexVideo、#ocx容器高度宽度
  layui.use(['layer', 'form'], function () {
    layer = layui.layer;
    form = layui.form;
    form.on('submit(RTSPForm)', function (data) {
      console.log(JSON.stringify(data.field));
    });
    let result = initialize_ocx('indexVideo');
    if (!result.result) {
      layer.open({
        title: langMessage.common.error
        , shade: 0.8
        , btn: false
        // ,id: 'LAY_layuipro' //设定一个id，防止重复弹出
        // ,moveType: 1 //拖拽模式，0或者1
        , content: result.msg
        , anim: 6
        // ,success: function(layero,index){
        //     layer.close(index)
        // }
      });
    }
    $("#videoT2Value").off('click').on('click', function () {
      if ($(this).parents(".video-type").find(".video-select").css('display') === 'block') {
        $(this).parents(".video-type").find(".video-select").css({display: 'none'});
        return
      }
      $(".video-select").css({display: 'none'});
      let v = this.innerText;
      $("#videoT2Select li").removeClass("selected");
      if (v === 'UDP') {
        $($("#videoT2Select li")[0]).addClass("selected")
      } else {
        $($("#videoT2Select li")[1]).addClass("selected")
      }
      $("#videoT2C .video-select").css({display: 'block'});
    });
    $("#videoT2Select li>a").off('click').on('click', function () {
      let channel = getDataset($("#videoT2Value")[0]).type;
      let ch = getDataset($("#videoT1Value")[0]).channel;
      let v = getDataset(this).type;
      if (channel !== v) {
        let vs = ["UDP", "TCP"];
        setRTSPWay(vs[v], function (result) {
          if (result === true) {
            if ($(".video-stop").length) {
              stopRTSP(ch);
              initStreamType(ch);
            }
            setDataset($("#videoT2Value")[0], "type", v);
            $("#videoT2Value").html(vs[v]);
            layer.msg(langMessage.common.editSuc, {icon: 1});
          } else {
            console.log(result);
            layer.msg(langMessage.common.editFail, {icon: 2});
          }
          $(".video-select").css({display: 'none'});
        });

      }
    });
    $("#playVideo").off('click').on('click', function (e) {
      let ip = location.hostname;
      //ip = '*************'
      let userInfo = getSe('loginUser');
      let channel = getSe("projectConfig").camera_channel;
      let ch = getDataset($("#videoT1Value")[0]).channel;
      if (ch != channel) {
        channel = ch
      }
      setDataset(this, "channel", channel);
      if (e.currentTarget.className === 'video-play') {
        $(e.currentTarget).removeClass('video-play');
        $(e.currentTarget).addClass('video-stop');
        initStreamType(channel);
      } else if (e.currentTarget.className === 'video-stop') {
        $(e.currentTarget).removeClass('video-stop');
        $(e.currentTarget).addClass('video-play');
        stopRTSP(channel);
      }
    })
  })
});
var setContainer = function () {
  let fH = $("body")[0].offsetHeight;
  let fW = $("body")[0].offsetWidth;
  $("#indexVideo").css({
    height: fH,
    width: fW
  });
  $("#ocx").attr({//ocx 在ocxGlobal.js中创建
    height: fH - 30,
    width: fW
  });
};
var errorRTSP = function () {
  let msg = langMessage.common.nonsupportVideo;
  layer.msg(msg, {icon: 2});
  $("#indexVideo").html("");
  $("#indexVideo").append("<a id='ocx_down'>" + msg + "</a>");
};
